# 基于静态提取代码特征的过拟合补丁自动分类

这是论文 [Automated Classification of Overfitting Patches with Statically Extracted Code Features](http://arxiv.org/pdf/1910.12057) ([doi:10.1109/tse.2021.3071750](https://doi.org/10.1109/tse.2021.3071750)) 的代码仓库

```
@article{ye2021ods,
 title = {Automated Classification of Overfitting Patches with Statically Extracted Code Features},
 author = {<PERSON> and <PERSON><PERSON> and <PERSON><PERSON> and <PERSON> and <PERSON>},
 journal = {IEEE Transactions on Software Engineering},
 year = {2021},
 doi = {10.1109/tse.2021.3071750},
}
```



## 文件夹结构
 ```bash
├── Experiment: csv特征数据和用于复现我们实验的脚本
│ 
├── Features: ODS代码特征
│   └── Code: JSON格式的ODS代码描述特征
│   └── Patterns: JSON格式的ODS修复模式特征
│   └── Context: JSON格式的ODS上下文特征 
├── Source: 可以作为Coming输入生成ODS特征的源程序文件
│
├── Tests: 为Bugs.jar和Bears生成的Evosuite测试，用于标记RepairThemAll补丁的正确性
│
└── RawRepairThemAllPatches: 来自RepairThemAll实验的原始补丁

```


## ODS特征提取

我们已经将ODS特征提取与开源工具 [Coming](https://github.com/SpoonLabs/coming) 集成。
要提取代码特征，您可以解析Source文件夹中的源文件和目标文件对。
使用Coming的特征模式来获得ODS特征。

## 参数
我们使用XGBoost的默认参数（即learning_rate设置为0.3，max_depth设置为6），仅将gamma调整为0.5。所有参数都可以在我们的notebook中找到。


# 如何使用ODS预测新的未见过的补丁：

## 检出Coming仓库并使用maven命令构建。请注意Java版本为1.8。
```
https://github.com/SpoonLabs/coming.git
mvn install -DskipTests
```

## 使用Coming项目中的演示样本执行以下脚本。您将获得一个名为test.csv的生成csv文件和输出路径中JSON格式的代码特征。
```
java -classpath ./target/coming-0-SNAPSHOT-jar-with-dependencies.jar fr.inria.coming.main.ComingMain -input files -mode features -location ./src/main/resources/pairsD4j -output ./out
```
请注意，Coming项目需要特定的输入源文件和目标文件结构：
```
<location_arg>
├── <diff_folder>
│   └── <modif_file>
│       ├── <diff_folder>_<modif_file>_s.java
│       └── <diff_folder>_<modif_file>_t.java
```
## 准备好test.csv并使用以下代码进行预测。您将在prediction.csv中找到生成的预测结果。
```
python3 predict.py

您可能还需要以下依赖：
python3 -m pip install  xgboost
python3 -m pip install scikit-learn
python3 -m pip install imblearn
python3 -m pip install matplotlib
``` 