/*
 * This file was automatically generated by EvoSuite
 * Wed Dec 25 21:06:08 GMT 2019
 */

package org.apache.accumulo.core.client.mock;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.nio.ByteBuffer;
import org.apache.accumulo.core.client.admin.TimeType;
import org.apache.accumulo.core.client.mock.MockTable;
import org.apache.accumulo.core.data.Key;
import org.apache.accumulo.core.data.thrift.TKey;
import org.apache.hadoop.io.Text;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MockTable_ESTest extends MockTable_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      ByteBuffer byteBuffer0 = ByteBuffer.allocate(1731);
      TKey tKey0 = new TKey(byteBuffer0, byteBuffer0, byteBuffer0, byteBuffer0, 1731);
      Key key0 = new Key(tKey0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 410);
      Text text0 = new Text();
      Key key1 = new Key(text0);
      int int0 = mockTable_MockMemKey0.compareTo(key1);
      assertEquals(1731, int0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      StringBuilder stringBuilder0 = new StringBuilder(507);
      Key key0 = new Key(stringBuilder0, stringBuilder0, stringBuilder0, stringBuilder0, 507);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 507);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(key0, ':');
      boolean boolean0 = mockTable_MockMemKey1.equals((Object) mockTable_MockMemKey0);
      assertFalse(boolean0);
      assertFalse(mockTable_MockMemKey0.equals((Object)mockTable_MockMemKey1));
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      TimeType timeType0 = TimeType.LOGICAL;
      MockTable mockTable0 = null;
      try {
        mockTable0 = new MockTable(false, timeType0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // Could not initialize class org.apache.accumulo.core.iterators.IteratorUtil
         //
         verifyException("org.apache.accumulo.core.client.mock.MockTable", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      StringBuilder stringBuilder0 = new StringBuilder();
      Key key0 = new Key(stringBuilder0, stringBuilder0, stringBuilder0, stringBuilder0, 507);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 507);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(mockTable_MockMemKey0, 1048576);
      int int0 = mockTable_MockMemKey0.compareTo((Key) mockTable_MockMemKey1);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      Key key0 = new Key(byteArray0, byteArray0, byteArray0, byteArray0, (byte)0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-114));
      int int0 = mockTable_MockMemKey0.compareTo(key0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      StringBuilder stringBuilder0 = new StringBuilder();
      Key key0 = new Key(stringBuilder0, stringBuilder0, stringBuilder0, stringBuilder0, 507);
      Text text0 = key0.getColumnVisibility();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 507);
      Key key1 = new Key(text0, text0, text0, 'a');
      int int0 = mockTable_MockMemKey0.compareTo(key1);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      StringBuilder stringBuilder0 = new StringBuilder();
      Key key0 = new Key(stringBuilder0, stringBuilder0, stringBuilder0, stringBuilder0, 507);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 507);
      int int0 = mockTable_MockMemKey0.compareTo((Key) mockTable_MockMemKey0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      StringBuilder stringBuilder0 = new StringBuilder();
      Key key0 = new Key(stringBuilder0, stringBuilder0, stringBuilder0, stringBuilder0, 507);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 507);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(mockTable_MockMemKey0, 1048576);
      boolean boolean0 = mockTable_MockMemKey1.equals((Object) mockTable_MockMemKey0);
      assertFalse(mockTable_MockMemKey0.equals((Object)mockTable_MockMemKey1));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      StringBuilder stringBuilder0 = new StringBuilder();
      Key key0 = new Key(stringBuilder0, stringBuilder0, stringBuilder0, stringBuilder0, 507);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 507);
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) mockTable_MockMemKey0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      Key key0 = new Key(byteArray0, byteArray0, byteArray0, byteArray0, (byte)0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-114));
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      Key key0 = new Key(byteArray0, byteArray0, byteArray0, byteArray0, (byte)0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-114));
      String string0 = mockTable_MockMemKey0.toString();
      assertEquals("%00;%00; %00;%00;:%00;%00; [%00;%00;] 0 false count=-114", string0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 560);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(mockTable_MockMemKey0, 74);
      int int0 = mockTable_MockMemKey0.compareTo((Key) mockTable_MockMemKey1);
      assertEquals((-1), int0);
  }
}
