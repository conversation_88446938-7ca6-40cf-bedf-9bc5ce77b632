/*
 * This file was automatically generated by EvoSuite
 * Wed Dec 25 21:09:55 GMT 2019
 */

package org.apache.accumulo.core.client.mock;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.accumulo.core.client.admin.TimeType;
import org.apache.accumulo.core.client.mock.MockTable;
import org.apache.accumulo.core.data.Key;
import org.apache.accumulo.core.data.PartialKey;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MockTable_ESTest extends MockTable_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      Key key0 = new Key(byteArray0, byteArray0, byteArray0, byteArray0, (byte)72);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (-2297));
      key0.setDeleted(true);
      int int0 = mockTable_MockMemKey0.compareTo(key0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      Key key0 = new Key(byteArray0, byteArray0, byteArray0, byteArray0, (byte)110);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-122));
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(mockTable_MockMemKey0, (byte)110);
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) mockTable_MockMemKey1);
      assertFalse(boolean0);
      assertFalse(mockTable_MockMemKey1.equals((Object)mockTable_MockMemKey0));
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      TimeType timeType0 = TimeType.LOGICAL;
      MockTable mockTable0 = null;
      try {
        mockTable0 = new MockTable(false, timeType0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // Could not initialize class org.apache.accumulo.core.iterators.IteratorUtil
         //
         verifyException("org.apache.accumulo.core.client.mock.MockTable", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte)0);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(mockTable_MockMemKey0, (byte)8);
      int int0 = mockTable_MockMemKey1.compareTo((Key) mockTable_MockMemKey0);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-114));
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(key0, (byte)0);
      int int0 = mockTable_MockMemKey0.compareTo((Key) mockTable_MockMemKey1);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-114));
      int int0 = mockTable_MockMemKey0.compareTo((Key) mockTable_MockMemKey0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Key key0 = new Key();
      PartialKey partialKey0 = PartialKey.ROW_COLFAM_COLQUAL;
      Key key1 = key0.followingKey(partialKey0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 11);
      int int0 = mockTable_MockMemKey0.compareTo(key1);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-114));
      int int0 = mockTable_MockMemKey0.compareTo(key0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte)0);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(mockTable_MockMemKey0, (byte)8);
      boolean boolean0 = mockTable_MockMemKey1.equals((Object) mockTable_MockMemKey0);
      assertFalse(boolean0);
      assertFalse(mockTable_MockMemKey0.equals((Object)mockTable_MockMemKey1));
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-114));
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) mockTable_MockMemKey0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-114));
      String string0 = mockTable_MockMemKey0.toString();
      assertEquals(" : [] 9223372036854775807 false count=-114", string0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 11);
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) null);
      assertFalse(boolean0);
  }
}
