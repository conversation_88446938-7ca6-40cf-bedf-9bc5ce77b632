/**
 * Scaffolding file used to store all the setups needed to run 
 * tests automatically generated by EvoSuite
 * Wed Dec 25 19:58:55 GMT 2019
 */

package org.apache.accumulo.core.client.mock;

import org.evosuite.runtime.annotation.EvoSuiteClassExclude;
import org.junit.BeforeClass;
import org.junit.Before;
import org.junit.After;
import org.junit.AfterClass;
import org.evosuite.runtime.sandbox.Sandbox;
import org.evosuite.runtime.sandbox.Sandbox.SandboxMode;

@EvoSuiteClassExclude
public class MockTable_ESTest_scaffolding {

  @org.junit.Rule 
  public org.evosuite.runtime.vnet.NonFunctionalRequirementRule nfr = new org.evosuite.runtime.vnet.NonFunctionalRequirementRule();

  private static final java.util.Properties defaultProperties = (java.util.Properties) java.lang.System.getProperties().clone(); 

  private org.evosuite.runtime.thread.ThreadStopper threadStopper =  new org.evosuite.runtime.thread.ThreadStopper (org.evosuite.runtime.thread.KillSwitchHandler.getInstance(), 3000);


  @BeforeClass 
  public static void initEvoSuiteFramework() { 
    org.evosuite.runtime.RuntimeSettings.className = "org.apache.accumulo.core.client.mock.MockTable"; 
    org.evosuite.runtime.GuiSupport.initialize(); 
    org.evosuite.runtime.RuntimeSettings.maxNumberOfThreads = 100; 
    org.evosuite.runtime.RuntimeSettings.maxNumberOfIterationsPerLoop = 10000; 
    org.evosuite.runtime.RuntimeSettings.mockSystemIn = true; 
    org.evosuite.runtime.RuntimeSettings.sandboxMode = org.evosuite.runtime.sandbox.Sandbox.SandboxMode.RECOMMENDED; 
    org.evosuite.runtime.sandbox.Sandbox.initializeSecurityManagerForSUT(); 
    org.evosuite.runtime.classhandling.JDKClassResetter.init();
    setSystemProperties();
    initializeClasses();
    org.evosuite.runtime.Runtime.getInstance().resetRuntime(); 
  } 

  @AfterClass 
  public static void clearEvoSuiteFramework(){ 
    Sandbox.resetDefaultSecurityManager(); 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
  } 

  @Before 
  public void initTestCase(){ 
    threadStopper.storeCurrentThreads();
    threadStopper.startRecordingTime();
    org.evosuite.runtime.jvm.ShutdownHookHandler.getInstance().initHandler(); 
    org.evosuite.runtime.sandbox.Sandbox.goingToExecuteSUTCode(); 
    setSystemProperties(); 
    org.evosuite.runtime.GuiSupport.setHeadless(); 
    org.evosuite.runtime.Runtime.getInstance().resetRuntime(); 
    org.evosuite.runtime.agent.InstrumentingAgent.activate(); 
  } 

  @After 
  public void doneWithTestCase(){ 
    threadStopper.killAndJoinClientThreads();
    org.evosuite.runtime.jvm.ShutdownHookHandler.getInstance().safeExecuteAddedHooks(); 
    org.evosuite.runtime.classhandling.JDKClassResetter.reset(); 
    resetClasses(); 
    org.evosuite.runtime.sandbox.Sandbox.doneWithExecutingSUTCode(); 
    org.evosuite.runtime.agent.InstrumentingAgent.deactivate(); 
    org.evosuite.runtime.GuiSupport.restoreHeadlessMode(); 
  } 

  public static void setSystemProperties() {
 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
    java.lang.System.setProperty("file.encoding", "ANSI_X3.4-1968"); 
    java.lang.System.setProperty("java.awt.headless", "true"); 
    java.lang.System.setProperty("java.io.tmpdir", "/tmp"); 
    java.lang.System.setProperty("user.country", "US"); 
    java.lang.System.setProperty("user.dir", "/home/<USER>/Desktop/research/evosuite-bugjar/tests/bugs-dot-jar_ACCUMULO-218_3d55560a/1"); 
    java.lang.System.setProperty("user.home", "/home/<USER>"); 
    java.lang.System.setProperty("user.language", "en"); 
    java.lang.System.setProperty("user.name", "wasp"); 
    java.lang.System.setProperty("user.timezone", "America/Los_Angeles"); 
  }

  private static void initializeClasses() {
    org.evosuite.runtime.classhandling.ClassStateSupport.initializeClasses(MockTable_ESTest_scaffolding.class.getClassLoader() ,
      "org.apache.thrift.meta_data.ListMetaData",
      "org.apache.accumulo.core.data.thrift.TKey$_Fields",
      "org.apache.hadoop.fs.FileSystem",
      "org.apache.thrift.protocol.TSimpleJSONProtocol",
      "org.apache.accumulo.core.client.admin.InstanceOperations",
      "org.apache.hadoop.io.SequenceFile$CompressionType",
      "org.apache.hadoop.io.compress.CompressionOutputStream",
      "org.apache.hadoop.io.Writable",
      "org.apache.thrift.TBaseHelper",
      "org.apache.thrift.protocol.TBinaryProtocol",
      "org.apache.accumulo.core.client.TableNotFoundException",
      "org.apache.hadoop.io.BytesWritable$Comparator",
      "org.apache.hadoop.io.SequenceFile$Metadata",
      "org.apache.hadoop.conf.Configuration",
      "org.apache.hadoop.io.WritableFactories",
      "org.apache.accumulo.core.data.thrift.IterInfo",
      "org.apache.thrift.protocol.TCompactProtocol",
      "org.apache.thrift.meta_data.EnumMetaData",
      "org.apache.thrift.TBase",
      "org.apache.accumulo.core.data.thrift.TKey",
      "org.apache.hadoop.io.SequenceFile$Sorter",
      "org.apache.hadoop.io.IntWritable",
      "org.apache.accumulo.core.data.KeyExtent$1",
      "org.apache.thrift.protocol.TJSONProtocol",
      "org.apache.accumulo.core.data.Column",
      "org.apache.accumulo.core.security.ColumnVisibility$NodeType",
      "org.apache.thrift.transport.TSeekableFile",
      "org.apache.accumulo.core.iterators.IteratorUtil$IteratorScope",
      "org.apache.accumulo.core.util.BadArgumentException",
      "org.apache.hadoop.io.SequenceFile$RecordCompressWriter",
      "org.apache.hadoop.io.BooleanWritable",
      "org.apache.hadoop.io.compress.Decompressor",
      "org.apache.hadoop.fs.CanSetReadahead",
      "org.apache.accumulo.core.conf.ConfigurationCopy",
      "org.apache.accumulo.core.client.Scanner",
      "org.apache.hadoop.fs.FSDataOutputStream$PositionCache",
      "org.apache.accumulo.core.data.thrift.IterInfo$_Fields",
      "org.apache.hadoop.io.WritableComparable",
      "org.apache.hadoop.io.WritableComparator",
      "org.apache.accumulo.core.iterators.SortedKeyValueIterator",
      "org.apache.accumulo.core.data.Mutation$ByteBuffer",
      "org.apache.thrift.transport.TMemoryInputTransport",
      "org.apache.hadoop.io.SequenceFile$BlockCompressWriter",
      "org.apache.accumulo.core.data.ColumnUpdate",
      "org.apache.hadoop.io.serializer.SerializationFactory",
      "org.apache.hadoop.fs.Syncable",
      "org.apache.thrift.protocol.TJSONProtocol$JSONBaseContext",
      "org.apache.hadoop.fs.CanSetDropBehind",
      "org.apache.hadoop.io.SequenceFile$Writer$Option",
      "org.apache.accumulo.core.iterators.IteratorUtil",
      "org.apache.hadoop.io.DataInputBuffer",
      "org.apache.thrift.meta_data.FieldValueMetaData",
      "org.apache.accumulo.core.data.thrift.TMutation$_Fields",
      "org.apache.thrift.protocol.TJSONProtocol$JSONListContext",
      "org.apache.accumulo.core.data.thrift.TRange",
      "org.apache.accumulo.core.security.thrift.ThriftSecurityException$_Fields",
      "org.apache.accumulo.core.data.thrift.TKeyExtent",
      "org.apache.accumulo.core.conf.AccumuloConfiguration",
      "org.apache.accumulo.core.client.admin.TableOperations",
      "org.apache.accumulo.core.conf.SiteConfiguration",
      "org.apache.hadoop.io.UTF8$1",
      "org.apache.hadoop.io.IntWritable$Comparator",
      "org.apache.hadoop.io.BooleanWritable$Comparator",
      "org.apache.hadoop.util.Progressable",
      "org.apache.thrift.protocol.TSet",
      "org.apache.accumulo.core.data.Value$Comparator",
      "org.apache.accumulo.core.data.ArrayByteSequence",
      "org.apache.hadoop.fs.Seekable",
      "org.apache.thrift.protocol.TList",
      "org.apache.hadoop.io.compress.CompressionCodec",
      "org.apache.thrift.transport.TTransport",
      "org.apache.accumulo.core.data.thrift.TMutation",
      "org.apache.accumulo.core.data.KeyExtent",
      "org.apache.thrift.protocol.TField",
      "org.apache.accumulo.core.data.KeyValue",
      "org.apache.thrift.protocol.TProtocolException",
      "org.apache.hadoop.io.serializer.Deserializer",
      "org.apache.accumulo.core.data.thrift.TRange$_Fields",
      "org.apache.accumulo.core.data.thrift.TKeyExtent$_Fields",
      "org.apache.thrift.transport.TStandardFile",
      "org.apache.thrift.TByteArrayOutputStream",
      "org.apache.accumulo.core.data.thrift.TKeyValue$_Fields",
      "org.apache.hadoop.io.SortedMapWritable",
      "org.apache.accumulo.core.data.thrift.TColumn",
      "org.apache.accumulo.core.client.ScannerBase",
      "org.apache.accumulo.core.security.thrift.AuthInfo",
      "org.apache.hadoop.io.DataOutputBuffer",
      "org.apache.accumulo.core.util.Pair",
      "org.apache.thrift.TFieldIdEnum",
      "org.apache.accumulo.core.util.ByteBufferUtil",
      "org.apache.accumulo.core.data.Key$1",
      "org.apache.hadoop.io.EnumSetWritable",
      "org.apache.hadoop.fs.PathFilter",
      "org.apache.thrift.protocol.TSimpleJSONProtocol$StructContext",
      "org.apache.hadoop.io.MapWritable",
      "org.apache.accumulo.core.client.Connector",
      "org.apache.hadoop.io.MD5Hash$Comparator",
      "org.apache.hadoop.io.compress.Compressor",
      "org.apache.accumulo.core.client.AccumuloSecurityException",
      "org.apache.hadoop.HadoopIllegalArgumentException",
      "org.apache.thrift.protocol.TStruct",
      "org.apache.thrift.meta_data.FieldMetaData",
      "org.apache.hadoop.io.EnumSetWritable$1",
      "org.apache.accumulo.core.security.ColumnVisibility$NodeComparator",
      "org.apache.hadoop.io.DoubleWritable$Comparator",
      "org.apache.hadoop.io.SequenceFile$Reader",
      "org.apache.hadoop.io.compress.CompressionInputStream",
      "org.apache.thrift.TEnum",
      "org.apache.accumulo.core.security.Authorizations",
      "org.apache.accumulo.core.security.thrift.ThriftSecurityException",
      "org.apache.accumulo.core.conf.DefaultConfiguration",
      "org.apache.thrift.TBaseHelper$NestedStructureComparator",
      "org.apache.thrift.protocol.TMessage",
      "org.apache.hadoop.io.RawComparator",
      "org.apache.hadoop.io.SequenceFile$Reader$Option",
      "org.apache.accumulo.core.client.BatchWriter",
      "org.apache.hadoop.io.WritableUtils",
      "org.apache.accumulo.core.data.ByteSequence",
      "org.apache.hadoop.util.Progress",
      "org.apache.hadoop.io.Text",
      "org.apache.accumulo.core.client.Instance",
      "org.apache.hadoop.io.SequenceFile",
      "org.apache.accumulo.core.iterators.conf.ColumnSet",
      "org.apache.accumulo.core.client.BatchScanner",
      "org.apache.accumulo.core.data.thrift.TColumn$_Fields",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder$UnsafeComparer$1",
      "org.apache.accumulo.core.iterators.IteratorUtil$IterInfoComparator",
      "org.apache.hadoop.io.ShortWritable",
      "org.apache.hadoop.io.ArrayWritable",
      "org.apache.accumulo.core.conf.Property",
      "org.apache.hadoop.fs.FSDataInputStream",
      "org.apache.accumulo.core.client.impl.thrift.TableOperationExceptionType",
      "org.apache.accumulo.core.client.MutationsRejectedException",
      "org.apache.accumulo.core.client.admin.TimeType",
      "org.apache.hadoop.io.DataOutputBuffer$Buffer",
      "org.apache.hadoop.fs.ByteBufferReadable",
      "org.apache.thrift.TBaseHelper$1",
      "org.apache.accumulo.core.client.impl.thrift.TableOperation",
      "org.apache.hadoop.fs.ChecksumFileSystem",
      "org.apache.accumulo.core.client.mock.MockTable$MockMemKey",
      "org.apache.hadoop.fs.RemoteIterator",
      "org.apache.accumulo.core.client.TableExistsException",
      "org.apache.hadoop.io.SequenceFile$1",
      "org.apache.hadoop.fs.FSDataOutputStream",
      "org.apache.accumulo.core.security.TablePermission",
      "org.apache.hadoop.fs.FilterFileSystem",
      "org.apache.accumulo.core.data.Key",
      "org.apache.thrift.transport.TSocket",
      "org.apache.thrift.protocol.TSimpleJSONProtocol$ListContext",
      "org.apache.hadoop.io.UTF8$Comparator",
      "org.apache.accumulo.core.client.mock.MockTable",
      "org.apache.hadoop.fs.FileAlreadyExistsException",
      "org.apache.hadoop.io.AbstractMapWritable",
      "org.apache.hadoop.io.Text$2",
      "org.apache.hadoop.conf.Configuration$IntegerRanges",
      "org.apache.thrift.TException",
      "org.apache.accumulo.core.client.impl.thrift.ThriftTableOperationException$_Fields",
      "org.apache.accumulo.core.util.BulkImportHelper$AssignmentStats",
      "org.apache.accumulo.core.data.Range",
      "org.apache.accumulo.core.client.IteratorSetting",
      "org.apache.accumulo.core.security.ColumnVisibility",
      "org.apache.hadoop.io.ShortWritable$Comparator",
      "org.apache.hadoop.io.Text$1",
      "org.apache.thrift.protocol.TJSONProtocol$JSONPairContext",
      "org.apache.accumulo.core.data.PartialKey",
      "org.apache.accumulo.core.client.impl.thrift.ThriftTableOperationException",
      "org.apache.accumulo.core.client.BatchDeleter",
      "org.apache.accumulo.core.iterators.IteratorEnvironment",
      "org.apache.accumulo.core.data.thrift.TKeyValue",
      "org.apache.hadoop.fs.ChecksumException",
      "org.apache.thrift.transport.TTransportException",
      "org.apache.accumulo.core.conf.ConfigSanityCheck$CheckTimeDuration",
      "org.apache.hadoop.io.FastByteComparisons",
      "org.apache.thrift.meta_data.StructMetaData",
      "org.apache.accumulo.core.security.thrift.AuthInfo$_Fields",
      "org.apache.accumulo.core.data.Mutation",
      "org.apache.thrift.transport.TFramedTransport",
      "org.apache.accumulo.core.util.TextUtil",
      "org.apache.hadoop.io.Text$Comparator",
      "org.apache.hadoop.conf.Configured",
      "org.apache.hadoop.fs.Path",
      "org.apache.accumulo.core.data.Value",
      "org.apache.thrift.transport.TIOStreamTransport",
      "org.apache.hadoop.conf.Configurable",
      "org.apache.accumulo.core.security.ColumnVisibility$Node",
      "org.apache.accumulo.core.conf.ConfigSanityCheck",
      "org.apache.accumulo.core.client.MultiTableBatchWriter",
      "org.apache.thrift.protocol.TSimpleJSONProtocol$Context",
      "org.apache.hadoop.io.DataInputBuffer$Buffer",
      "org.apache.thrift.protocol.TMap",
      "org.apache.accumulo.core.data.Mutation$SimpleReader",
      "org.apache.hadoop.fs.HasFileDescriptor",
      "org.apache.accumulo.core.conf.PropertyType",
      "org.apache.hadoop.io.BinaryComparable",
      "org.apache.hadoop.io.UTF8",
      "org.apache.hadoop.io.DoubleWritable",
      "org.apache.hadoop.util.MergeSort",
      "org.apache.hadoop.io.SequenceFile$Sorter$RawKeyValueIterator",
      "org.apache.hadoop.io.SequenceFile$ValueBytes",
      "org.apache.thrift.protocol.TJSONProtocol$LookaheadReader",
      "org.apache.hadoop.conf.Configuration$Resource",
      "org.apache.accumulo.core.iterators.conf.PerColumnIteratorConfig",
      "org.apache.hadoop.fs.ParentNotDirectoryException",
      "org.apache.hadoop.fs.PositionedReadable",
      "org.apache.hadoop.io.VersionMismatchException",
      "org.apache.hadoop.io.MD5Hash$1",
      "org.apache.hadoop.io.BytesWritable",
      "org.apache.hadoop.fs.CreateFlag",
      "org.apache.hadoop.io.FastByteComparisons$Comparer",
      "org.apache.accumulo.core.client.admin.SecurityOperations",
      "org.apache.hadoop.io.WritableFactory",
      "org.apache.accumulo.core.data.thrift.TKey$1",
      "org.apache.accumulo.core.security.ColumnVisibility$ColumnVisibilityParser",
      "org.apache.accumulo.core.client.AccumuloException",
      "org.apache.hadoop.io.MD5Hash",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder$UnsafeComparer",
      "org.apache.thrift.protocol.TProtocol",
      "org.apache.accumulo.core.security.thrift.SecurityErrorCode",
      "org.apache.accumulo.core.security.SystemPermission",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder",
      "org.apache.hadoop.fs.LocalFileSystem",
      "org.apache.hadoop.io.SequenceFile$Writer"
    );
  } 

  private static void resetClasses() {
    org.evosuite.runtime.classhandling.ClassResetter.getInstance().setClassLoader(MockTable_ESTest_scaffolding.class.getClassLoader()); 

    org.evosuite.runtime.classhandling.ClassStateSupport.resetClasses(
      "org.apache.accumulo.core.client.mock.MockTable",
      "org.apache.accumulo.core.data.Key",
      "org.apache.accumulo.core.client.mock.MockTable$MockMemKey",
      "org.apache.accumulo.core.iterators.IteratorUtil$IteratorScope",
      "org.apache.accumulo.core.conf.PropertyType",
      "org.apache.accumulo.core.conf.Property",
      "org.apache.accumulo.core.conf.AccumuloConfiguration",
      "org.apache.accumulo.core.conf.ConfigSanityCheck",
      "org.apache.accumulo.core.client.admin.TimeType",
      "org.apache.accumulo.core.iterators.IteratorUtil",
      "org.apache.hadoop.io.BinaryComparable",
      "org.apache.hadoop.io.Text$1",
      "org.apache.hadoop.io.Text$2",
      "org.apache.hadoop.io.WritableComparator",
      "org.apache.hadoop.io.Text$Comparator",
      "org.apache.hadoop.io.Text",
      "org.apache.hadoop.io.FastByteComparisons",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder$UnsafeComparer$1",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder$UnsafeComparer",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder",
      "org.apache.thrift.protocol.TStruct",
      "org.apache.thrift.protocol.TField",
      "org.apache.accumulo.core.data.thrift.TKey$_Fields",
      "org.apache.thrift.meta_data.FieldMetaData",
      "org.apache.thrift.meta_data.FieldValueMetaData",
      "org.apache.accumulo.core.data.thrift.TKey",
      "org.apache.accumulo.core.util.ByteBufferUtil",
      "org.apache.accumulo.core.data.ByteSequence",
      "org.apache.accumulo.core.data.ArrayByteSequence",
      "org.apache.accumulo.core.data.ColumnUpdate",
      "org.apache.accumulo.core.security.ColumnVisibility",
      "org.apache.hadoop.io.SequenceFile$Metadata",
      "org.apache.accumulo.core.security.ColumnVisibility$ColumnVisibilityParser",
      "org.apache.accumulo.core.security.Authorizations",
      "org.apache.accumulo.core.util.BadArgumentException",
      "org.apache.hadoop.io.MD5Hash$1",
      "org.apache.hadoop.io.MD5Hash$Comparator",
      "org.apache.hadoop.io.MD5Hash",
      "org.apache.accumulo.core.util.TextUtil",
      "org.apache.hadoop.io.WritableUtils",
      "org.apache.hadoop.conf.Configuration",
      "org.apache.accumulo.core.iterators.conf.PerColumnIteratorConfig",
      "org.apache.accumulo.core.iterators.conf.ColumnSet",
      "org.apache.accumulo.core.data.Mutation",
      "org.apache.hadoop.io.ShortWritable$Comparator",
      "org.apache.hadoop.io.ShortWritable",
      "org.apache.accumulo.core.data.thrift.TKey$1",
      "org.apache.thrift.TBaseHelper$NestedStructureComparator",
      "org.apache.thrift.TBaseHelper",
      "org.apache.accumulo.core.data.Mutation$ByteBuffer",
      "org.apache.thrift.meta_data.ListMetaData",
      "org.apache.accumulo.core.data.thrift.TMutation",
      "org.apache.hadoop.io.AbstractMapWritable",
      "org.apache.hadoop.io.MapWritable",
      "org.apache.hadoop.io.BytesWritable$Comparator",
      "org.apache.hadoop.io.BytesWritable",
      "org.apache.accumulo.core.data.Mutation$SimpleReader",
      "org.apache.accumulo.core.data.Key$1",
      "org.apache.thrift.transport.TStandardFile",
      "org.apache.hadoop.io.EnumSetWritable$1",
      "org.apache.hadoop.io.WritableFactories",
      "org.apache.hadoop.io.EnumSetWritable",
      "org.apache.hadoop.io.SortedMapWritable",
      "org.apache.thrift.transport.TTransport",
      "org.apache.thrift.transport.TFramedTransport",
      "org.apache.thrift.TByteArrayOutputStream",
      "org.apache.thrift.transport.TMemoryInputTransport",
      "org.apache.thrift.protocol.TProtocol",
      "org.apache.thrift.protocol.TBinaryProtocol",
      "org.apache.thrift.transport.TIOStreamTransport",
      "org.apache.thrift.transport.TSocket",
      "org.apache.thrift.protocol.TJSONProtocol",
      "org.apache.thrift.protocol.TJSONProtocol$JSONBaseContext",
      "org.apache.thrift.protocol.TJSONProtocol$LookaheadReader",
      "org.apache.thrift.TException",
      "org.apache.thrift.transport.TTransportException",
      "org.apache.accumulo.core.security.ColumnVisibility$Node",
      "org.apache.accumulo.core.security.ColumnVisibility$NodeType",
      "org.apache.thrift.protocol.TMessage",
      "org.apache.thrift.protocol.TSet",
      "org.apache.thrift.protocol.TList",
      "org.apache.thrift.protocol.TMap",
      "org.apache.thrift.protocol.TSimpleJSONProtocol",
      "org.apache.thrift.protocol.TSimpleJSONProtocol$Context",
      "org.apache.accumulo.core.data.Value",
      "org.apache.accumulo.core.data.KeyValue",
      "org.apache.accumulo.core.data.thrift.TKeyValue$_Fields",
      "org.apache.thrift.meta_data.StructMetaData",
      "org.apache.accumulo.core.data.thrift.TKeyValue",
      "org.apache.hadoop.io.BooleanWritable$Comparator",
      "org.apache.hadoop.io.BooleanWritable",
      "org.apache.hadoop.io.ArrayWritable",
      "org.apache.hadoop.io.IntWritable$Comparator",
      "org.apache.hadoop.io.IntWritable",
      "org.apache.hadoop.io.DoubleWritable$Comparator",
      "org.apache.hadoop.io.DoubleWritable",
      "org.apache.accumulo.core.data.PartialKey"
    );
  }
}
