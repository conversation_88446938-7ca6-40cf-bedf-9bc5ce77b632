/*
 * This file was automatically generated by EvoSuite
 * Wed Dec 25 20:00:49 GMT 2019
 */

package org.apache.accumulo.core.client.mock;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import org.apache.accumulo.core.client.admin.TimeType;
import org.apache.accumulo.core.client.mock.MockTable;
import org.apache.accumulo.core.data.Key;
import org.apache.accumulo.core.security.ColumnVisibility;
import org.apache.hadoop.io.Text;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MockTable_ESTest extends MockTable_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      ByteBuffer byteBuffer0 = ByteBuffer.wrap(byteArray0);
      CharBuffer charBuffer0 = byteBuffer0.asCharBuffer();
      ColumnVisibility columnVisibility0 = new ColumnVisibility();
      Key key0 = new Key(charBuffer0, charBuffer0, charBuffer0, columnVisibility0, (byte)83);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 1505);
      Text text0 = new Text();
      Key key1 = new Key(text0, 0L);
      int int0 = mockTable_MockMemKey0.compareTo(key1);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      Key key0 = new Key(byteArray0, byteArray0, byteArray0, byteArray0, (byte)122);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte)1);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(mockTable_MockMemKey0, 6);
      boolean boolean0 = mockTable_MockMemKey1.equals((Object) mockTable_MockMemKey0);
      assertFalse(mockTable_MockMemKey0.equals((Object)mockTable_MockMemKey1));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      TimeType timeType0 = TimeType.LOGICAL;
      MockTable mockTable0 = null;
      try {
        mockTable0 = new MockTable(false, timeType0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // Could not initialize class org.apache.accumulo.core.iterators.IteratorUtil
         //
         verifyException("org.apache.accumulo.core.client.mock.MockTable", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 2077);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(key0, 1);
      int int0 = mockTable_MockMemKey0.compareTo((Key) mockTable_MockMemKey1);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 2077);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(key0, 1);
      int int0 = mockTable_MockMemKey1.compareTo((Key) mockTable_MockMemKey0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 2077);
      int int0 = mockTable_MockMemKey0.compareTo((Key) mockTable_MockMemKey0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      Key key0 = new Key(byteArray0, byteArray0, byteArray0, byteArray0, (byte)0);
      Key key1 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte)1);
      int int0 = mockTable_MockMemKey0.compareTo(key1);
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 2077);
      int int0 = mockTable_MockMemKey0.compareTo(key0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 2077);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(key0, 1);
      boolean boolean0 = mockTable_MockMemKey1.equals((Object) mockTable_MockMemKey0);
      assertFalse(boolean0);
      assertFalse(mockTable_MockMemKey0.equals((Object)mockTable_MockMemKey1));
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-70));
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) mockTable_MockMemKey0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      Key key0 = new Key(byteArray0, byteArray0, byteArray0, byteArray0, (byte)0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte)1);
      String string0 = mockTable_MockMemKey0.toString();
      assertEquals("%00;%00;%00;%00;%00; %00;%00;%00;%00;%00;:%00;%00;%00;%00;%00; [%00;%00;%00;%00;%00;] 0 false count=1", string0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (-116));
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) null);
      assertFalse(boolean0);
  }
}
