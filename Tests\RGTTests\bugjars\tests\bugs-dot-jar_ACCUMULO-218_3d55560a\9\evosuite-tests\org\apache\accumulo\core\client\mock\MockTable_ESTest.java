/*
 * This file was automatically generated by EvoSuite
 * Wed Dec 25 20:14:06 GMT 2019
 */

package org.apache.accumulo.core.client.mock;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.nio.ByteBuffer;
import org.apache.accumulo.core.client.admin.TimeType;
import org.apache.accumulo.core.client.mock.MockTable;
import org.apache.accumulo.core.data.Key;
import org.apache.accumulo.core.data.thrift.TKey;
import org.apache.hadoop.io.Text;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MockTable_ESTest extends MockTable_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      byte[] byteArray0 = new byte[21];
      Key key0 = new Key(byteArray0, byteArray0, byteArray0, byteArray0, 662L, false);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte)114);
      Text text0 = new Text();
      Key key1 = new Key(text0, text0, text0);
      int int0 = mockTable_MockMemKey0.compareTo(key1);
      assertEquals(21, int0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      ByteBuffer byteBuffer0 = Text.encode(" count=", false);
      TKey tKey0 = new TKey(byteBuffer0, byteBuffer0, byteBuffer0, byteBuffer0, 0L);
      Key key0 = new Key(tKey0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (-2859));
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(key0, (-7));
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) mockTable_MockMemKey1);
      assertFalse(mockTable_MockMemKey1.equals((Object)mockTable_MockMemKey0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      TimeType timeType0 = TimeType.MILLIS;
      MockTable mockTable0 = null;
      try {
        mockTable0 = new MockTable(true, timeType0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // Could not initialize class org.apache.accumulo.core.iterators.IteratorUtil
         //
         verifyException("org.apache.accumulo.core.client.mock.MockTable", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      ByteBuffer byteBuffer0 = Text.encode(" countfx=");
      TKey tKey0 = new TKey(byteBuffer0, byteBuffer0, byteBuffer0, byteBuffer0, (-15L));
      Key key0 = new Key(tKey0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 0);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(key0, (-9));
      int int0 = mockTable_MockMemKey0.compareTo((Key) mockTable_MockMemKey1);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      ByteBuffer byteBuffer0 = Text.encode(" countfx=");
      TKey tKey0 = new TKey(byteBuffer0, byteBuffer0, byteBuffer0, byteBuffer0, (-15L));
      Key key0 = new Key(tKey0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 0);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(key0, (-9));
      int int0 = mockTable_MockMemKey1.compareTo((Key) mockTable_MockMemKey0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (byte) (-117));
      int int0 = mockTable_MockMemKey0.compareTo(key0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      ByteBuffer byteBuffer0 = Text.encode(" countfx=");
      TKey tKey0 = new TKey(byteBuffer0, byteBuffer0, byteBuffer0, byteBuffer0, (-15L));
      Key key0 = new Key(tKey0);
      Key key1 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key1, 0);
      int int0 = mockTable_MockMemKey0.compareTo(key0);
      assertEquals((-9), int0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      ByteBuffer byteBuffer0 = Text.encode(" countfx=");
      TKey tKey0 = new TKey(byteBuffer0, byteBuffer0, byteBuffer0, byteBuffer0, (-15L));
      Key key0 = new Key(tKey0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 0);
      int int0 = mockTable_MockMemKey0.compareTo((Key) mockTable_MockMemKey0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      ByteBuffer byteBuffer0 = Text.encode(" countfx=");
      TKey tKey0 = new TKey(byteBuffer0, byteBuffer0, byteBuffer0, byteBuffer0, (-15L));
      Key key0 = new Key(tKey0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 0);
      MockTable.MockMemKey mockTable_MockMemKey1 = new MockTable.MockMemKey(key0, (-9));
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) mockTable_MockMemKey1);
      assertFalse(mockTable_MockMemKey1.equals((Object)mockTable_MockMemKey0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (-2240));
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) mockTable_MockMemKey0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Key key0 = new Key();
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, (-2240));
      String string0 = mockTable_MockMemKey0.toString();
      assertEquals(" : [] 9223372036854775807 false count=-2240", string0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      ByteBuffer byteBuffer0 = Text.encode(" countfx=");
      TKey tKey0 = new TKey(byteBuffer0, byteBuffer0, byteBuffer0, byteBuffer0, (-15L));
      Key key0 = new Key(tKey0);
      MockTable.MockMemKey mockTable_MockMemKey0 = new MockTable.MockMemKey(key0, 0);
      boolean boolean0 = mockTable_MockMemKey0.equals((Object) null);
      assertFalse(boolean0);
  }
}
