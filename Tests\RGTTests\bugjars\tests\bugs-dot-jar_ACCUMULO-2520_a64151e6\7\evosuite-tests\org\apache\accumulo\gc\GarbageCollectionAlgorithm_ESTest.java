/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 18:01:51 GMT 2019
 */

package org.apache.accumulo.gc;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.accumulo.gc.GarbageCollectionAlgorithm;
import org.apache.accumulo.gc.GarbageCollectionEnvironment;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class GarbageCollectionAlgorithm_ESTest extends GarbageCollectionAlgorithm_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      GarbageCollectionAlgorithm garbageCollectionAlgorithm0 = new GarbageCollectionAlgorithm();
      GarbageCollectionEnvironment garbageCollectionEnvironment0 = mock(GarbageCollectionEnvironment.class, new ViolatedAssumptionAnswer());
      // Undeclared exception!
      try { 
        garbageCollectionAlgorithm0.collect(garbageCollectionEnvironment0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/accumulo/trace/instrument/Trace
         //
         verifyException("org.apache.accumulo.gc.GarbageCollectionAlgorithm", e);
      }
  }
}
