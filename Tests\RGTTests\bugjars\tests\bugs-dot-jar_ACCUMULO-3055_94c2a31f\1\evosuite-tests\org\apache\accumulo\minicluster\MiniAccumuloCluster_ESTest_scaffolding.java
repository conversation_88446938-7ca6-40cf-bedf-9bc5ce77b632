/**
 * Scaffolding file used to store all the setups needed to run 
 * tests automatically generated by EvoSuite
 * Fri Dec 27 18:24:37 GMT 2019
 */

package org.apache.accumulo.minicluster;

import org.evosuite.runtime.annotation.EvoSuiteClassExclude;
import org.junit.BeforeClass;
import org.junit.Before;
import org.junit.After;
import org.junit.AfterClass;
import org.evosuite.runtime.sandbox.Sandbox;
import org.evosuite.runtime.sandbox.Sandbox.SandboxMode;

@EvoSuiteClassExclude
public class MiniAccumuloCluster_ESTest_scaffolding {

  @org.junit.Rule 
  public org.evosuite.runtime.vnet.NonFunctionalRequirementRule nfr = new org.evosuite.runtime.vnet.NonFunctionalRequirementRule();

  private static final java.util.Properties defaultProperties = (java.util.Properties) java.lang.System.getProperties().clone(); 

  private org.evosuite.runtime.thread.ThreadStopper threadStopper =  new org.evosuite.runtime.thread.ThreadStopper (org.evosuite.runtime.thread.KillSwitchHandler.getInstance(), 3000);


  @BeforeClass 
  public static void initEvoSuiteFramework() { 
    org.evosuite.runtime.RuntimeSettings.className = "org.apache.accumulo.minicluster.MiniAccumuloCluster"; 
    org.evosuite.runtime.GuiSupport.initialize(); 
    org.evosuite.runtime.RuntimeSettings.maxNumberOfThreads = 100; 
    org.evosuite.runtime.RuntimeSettings.maxNumberOfIterationsPerLoop = 10000; 
    org.evosuite.runtime.RuntimeSettings.mockSystemIn = true; 
    org.evosuite.runtime.RuntimeSettings.sandboxMode = org.evosuite.runtime.sandbox.Sandbox.SandboxMode.RECOMMENDED; 
    org.evosuite.runtime.sandbox.Sandbox.initializeSecurityManagerForSUT(); 
    org.evosuite.runtime.classhandling.JDKClassResetter.init();
    setSystemProperties();
    initializeClasses();
    org.evosuite.runtime.Runtime.getInstance().resetRuntime(); 
  } 

  @AfterClass 
  public static void clearEvoSuiteFramework(){ 
    Sandbox.resetDefaultSecurityManager(); 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
  } 

  @Before 
  public void initTestCase(){ 
    threadStopper.storeCurrentThreads();
    threadStopper.startRecordingTime();
    org.evosuite.runtime.jvm.ShutdownHookHandler.getInstance().initHandler(); 
    org.evosuite.runtime.sandbox.Sandbox.goingToExecuteSUTCode(); 
    setSystemProperties(); 
    org.evosuite.runtime.GuiSupport.setHeadless(); 
    org.evosuite.runtime.Runtime.getInstance().resetRuntime(); 
    org.evosuite.runtime.agent.InstrumentingAgent.activate(); 
  } 

  @After 
  public void doneWithTestCase(){ 
    threadStopper.killAndJoinClientThreads();
    org.evosuite.runtime.jvm.ShutdownHookHandler.getInstance().safeExecuteAddedHooks(); 
    org.evosuite.runtime.classhandling.JDKClassResetter.reset(); 
    resetClasses(); 
    org.evosuite.runtime.sandbox.Sandbox.doneWithExecutingSUTCode(); 
    org.evosuite.runtime.agent.InstrumentingAgent.deactivate(); 
    org.evosuite.runtime.GuiSupport.restoreHeadlessMode(); 
  } 

  public static void setSystemProperties() {
 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
    java.lang.System.setProperty("file.encoding", "ANSI_X3.4-1968"); 
    java.lang.System.setProperty("java.awt.headless", "true"); 
    java.lang.System.setProperty("java.io.tmpdir", "/tmp"); 
    java.lang.System.setProperty("user.country", "US"); 
    java.lang.System.setProperty("user.dir", "/home/<USER>/Desktop/research/evosuite-bugjar/tests/bugs-dot-jar_ACCUMULO-3055_94c2a31f/1"); 
    java.lang.System.setProperty("user.home", "/home/<USER>"); 
    java.lang.System.setProperty("user.language", "en"); 
    java.lang.System.setProperty("user.name", "wasp"); 
    java.lang.System.setProperty("user.timezone", "America/Los_Angeles"); 
    java.lang.System.setProperty("log4j.configuration", "SUT.log4j.properties"); 
  }

  private static void initializeClasses() {
    org.evosuite.runtime.classhandling.ClassStateSupport.initializeClasses(MiniAccumuloCluster_ESTest_scaffolding.class.getClassLoader() ,
      "org.apache.thrift.meta_data.ListMetaData",
      "org.apache.accumulo.core.data.thrift.TKey$_Fields",
      "org.apache.hadoop.fs.FileSystem",
      "org.apache.accumulo.core.client.admin.InstanceOperations",
      "org.apache.hadoop.io.Writable",
      "org.apache.accumulo.core.data.thrift.TKeyExtent$TKeyExtentStandardScheme",
      "org.apache.accumulo.core.client.TableNotFoundException",
      "org.apache.hadoop.io.BytesWritable$Comparator",
      "org.apache.log4j.Level",
      "org.apache.hadoop.conf.Configuration",
      "org.apache.accumulo.core.data.thrift.IterInfo",
      "org.apache.thrift.protocol.TCompactProtocol",
      "org.apache.thrift.meta_data.EnumMetaData",
      "org.apache.thrift.TBase",
      "org.apache.accumulo.core.data.thrift.TKey",
      "org.apache.accumulo.core.data.thrift.TKey$TKeyStandardScheme",
      "org.apache.accumulo.core.data.KeyExtent$1",
      "org.apache.accumulo.core.tabletserver.thrift.IteratorConfig$IteratorConfigTupleScheme",
      "org.apache.accumulo.core.data.Column",
      "org.apache.accumulo.core.security.ColumnVisibility$NodeType",
      "org.apache.thrift.scheme.StandardScheme",
      "org.apache.accumulo.core.iterators.IteratorUtil$IteratorScope",
      "org.apache.log4j.spi.Filter",
      "org.apache.accumulo.core.client.security.tokens.PasswordToken",
      "org.apache.accumulo.core.client.impl.thrift.ThriftSecurityException",
      "org.apache.accumulo.core.conf.ConfigurationCopy",
      "org.apache.log4j.Layout",
      "org.apache.accumulo.core.tabletserver.thrift.TIteratorSetting$_Fields",
      "org.apache.accumulo.core.client.Scanner",
      "org.apache.accumulo.core.tabletserver.thrift.IteratorConfig$_Fields",
      "org.apache.accumulo.core.data.thrift.IterInfo$_Fields",
      "org.apache.hadoop.io.WritableComparable",
      "org.apache.hadoop.io.WritableComparator",
      "org.apache.accumulo.core.iterators.SortedKeyValueIterator",
      "org.apache.log4j.Hierarchy",
      "org.apache.accumulo.core.data.ColumnUpdate",
      "org.apache.accumulo.core.client.impl.thrift.ThriftSecurityException$_Fields",
      "org.apache.accumulo.core.iterators.IteratorUtil",
      "org.apache.accumulo.core.data.thrift.TColumn$TColumnStandardScheme",
      "org.apache.thrift.meta_data.FieldValueMetaData",
      "org.apache.log4j.spi.DefaultRepositorySelector",
      "org.apache.log4j.spi.OptionHandler",
      "org.apache.accumulo.core.data.thrift.TMutation$_Fields",
      "org.apache.accumulo.core.data.thrift.TRange",
      "org.apache.accumulo.core.data.thrift.TKeyExtent",
      "org.apache.accumulo.core.client.impl.thrift.ThriftTableOperationException$ThriftTableOperationExceptionTupleScheme",
      "org.apache.thrift.protocol.TProtocolFactory",
      "org.apache.accumulo.core.conf.AccumuloConfiguration",
      "org.apache.accumulo.core.client.admin.TableOperations",
      "org.apache.accumulo.core.conf.SiteConfiguration",
      "org.apache.accumulo.core.data.thrift.TRange$TRangeStandardSchemeFactory",
      "org.apache.log4j.helpers.OptionConverter",
      "org.apache.accumulo.core.data.thrift.IterInfo$IterInfoStandardScheme",
      "org.apache.thrift.protocol.TSet",
      "org.apache.accumulo.core.data.Value$Comparator",
      "org.apache.log4j.or.ObjectRenderer",
      "org.apache.accumulo.core.data.ArrayByteSequence",
      "org.apache.accumulo.core.tabletserver.thrift.IteratorConfig$IteratorConfigStandardSchemeFactory",
      "org.apache.thrift.protocol.TList",
      "org.apache.thrift.transport.TTransport",
      "org.apache.accumulo.core.data.thrift.TColumn$TColumnTupleSchemeFactory",
      "org.apache.accumulo.core.data.thrift.TMutation",
      "org.apache.accumulo.core.data.KeyExtent",
      "org.apache.thrift.protocol.TField",
      "org.apache.thrift.protocol.TProtocolException",
      "org.apache.log4j.Category",
      "org.apache.accumulo.core.data.thrift.TRange$_Fields",
      "org.apache.accumulo.core.data.thrift.TKey$TKeyStandardSchemeFactory",
      "org.apache.accumulo.core.tabletserver.thrift.IteratorConfig",
      "org.apache.accumulo.core.data.thrift.TKeyExtent$_Fields",
      "org.apache.accumulo.core.client.security.tokens.AuthenticationToken",
      "org.apache.accumulo.core.data.thrift.TColumn",
      "org.apache.hadoop.util.Daemon",
      "org.apache.accumulo.core.tabletserver.thrift.TIteratorSetting$TIteratorSettingStandardSchemeFactory",
      "org.apache.accumulo.core.data.thrift.TColumn$TColumnStandardSchemeFactory",
      "org.apache.accumulo.core.client.ScannerBase",
      "org.apache.hadoop.util.Daemon$DaemonFactory",
      "org.apache.accumulo.core.security.thrift.AuthInfo",
      "org.apache.accumulo.core.data.thrift.TKey$TKeyTupleSchemeFactory",
      "org.apache.log4j.spi.LoggerFactory",
      "org.apache.log4j.spi.Configurator",
      "org.apache.accumulo.server.util.PortUtils",
      "org.apache.thrift.TFieldIdEnum",
      "org.apache.log4j.spi.LocationInfo",
      "org.apache.log4j.PropertyWatchdog",
      "org.apache.accumulo.core.data.thrift.TMutation$TMutationTupleScheme",
      "org.apache.hadoop.fs.PathFilter",
      "org.apache.accumulo.core.client.Connector",
      "org.apache.accumulo.minicluster.MiniAccumuloCluster$1",
      "org.apache.accumulo.minicluster.MiniAccumuloCluster$2",
      "org.apache.accumulo.core.conf.PropertyType$1",
      "org.apache.accumulo.core.client.AccumuloSecurityException",
      "org.apache.accumulo.core.tabletserver.thrift.TIteratorSetting",
      "org.apache.hadoop.HadoopIllegalArgumentException",
      "org.apache.accumulo.core.util.UtilWaitThread",
      "org.apache.thrift.protocol.TStruct",
      "org.apache.thrift.meta_data.FieldMetaData",
      "org.apache.accumulo.core.security.ColumnVisibility$NodeComparator",
      "org.apache.accumulo.core.tabletserver.thrift.TIteratorSetting$TIteratorSettingTupleSchemeFactory",
      "org.apache.accumulo.core.data.thrift.TKeyExtent$TKeyExtentStandardSchemeFactory",
      "org.apache.log4j.spi.AppenderAttachable",
      "org.apache.thrift.TEnum",
      "org.apache.accumulo.core.security.Authorizations",
      "org.apache.accumulo.core.conf.DefaultConfiguration",
      "org.apache.accumulo.core.data.thrift.TMutation$TMutationStandardScheme",
      "org.apache.accumulo.core.client.impl.thrift.ThriftSecurityException$ThriftSecurityExceptionStandardScheme",
      "org.apache.thrift.protocol.TMessage",
      "org.apache.hadoop.io.RawComparator",
      "org.apache.log4j.Priority",
      "org.apache.log4j.LogManager",
      "org.apache.accumulo.core.client.BatchWriter",
      "org.apache.accumulo.core.client.impl.thrift.ThriftSecurityException$ThriftSecurityExceptionTupleScheme",
      "org.apache.accumulo.core.data.ByteSequence",
      "org.apache.accumulo.core.client.security.tokens.AuthenticationToken$Properties",
      "org.apache.hadoop.io.Text",
      "org.apache.log4j.DefaultCategoryFactory",
      "org.apache.accumulo.core.client.Instance",
      "org.apache.accumulo.core.data.thrift.TKeyExtent$TKeyExtentTupleScheme",
      "org.apache.log4j.or.RendererMap",
      "org.apache.accumulo.core.data.thrift.TColumn$_Fields",
      "org.apache.accumulo.core.client.BatchWriterConfig",
      "org.apache.accumulo.core.client.BatchScanner",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder$UnsafeComparer$1",
      "org.apache.accumulo.core.iterators.IteratorUtil$IterInfoComparator",
      "org.apache.accumulo.core.conf.Property",
      "org.apache.accumulo.core.client.impl.thrift.SecurityErrorCode",
      "org.apache.accumulo.core.client.impl.thrift.TableOperationExceptionType",
      "org.apache.accumulo.minicluster.MiniAccumuloCluster$LogWriter$1",
      "org.apache.accumulo.core.client.MutationsRejectedException",
      "org.apache.accumulo.core.data.Mutation$SERIALIZED_FORMAT",
      "org.apache.thrift.protocol.TTupleProtocol",
      "org.apache.accumulo.core.client.admin.TimeType",
      "org.apache.accumulo.core.tabletserver.thrift.TIteratorSetting$TIteratorSettingStandardScheme",
      "org.apache.accumulo.core.client.impl.thrift.ThriftTableOperationException$ThriftTableOperationExceptionStandardScheme",
      "org.apache.accumulo.core.client.impl.thrift.TableOperation",
      "org.apache.log4j.CategoryKey",
      "org.apache.hadoop.fs.ChecksumFileSystem",
      "org.apache.hadoop.fs.RemoteIterator",
      "org.apache.accumulo.core.security.thrift.AuthInfo$AuthInfoTupleSchemeFactory",
      "org.apache.accumulo.core.client.TableExistsException",
      "org.apache.log4j.helpers.Loader",
      "org.apache.log4j.ProvisionNode",
      "org.apache.accumulo.minicluster.MiniAccumuloCluster$LogWriter",
      "org.apache.accumulo.core.security.TablePermission",
      "org.apache.log4j.helpers.FileWatchdog",
      "org.apache.accumulo.core.data.thrift.IterInfo$IterInfoTupleScheme",
      "org.apache.hadoop.fs.FilterFileSystem",
      "org.apache.accumulo.core.data.Key",
      "org.apache.accumulo.core.client.impl.thrift.ThriftSecurityException$ThriftSecurityExceptionTupleSchemeFactory",
      "org.apache.accumulo.core.security.thrift.AuthInfo$AuthInfoTupleScheme",
      "org.apache.hadoop.fs.FileAlreadyExistsException",
      "org.apache.log4j.spi.RootLogger",
      "org.apache.hadoop.io.Text$2",
      "org.apache.accumulo.core.tabletserver.thrift.IteratorConfig$IteratorConfigTupleSchemeFactory",
      "org.apache.log4j.spi.ErrorHandler",
      "org.apache.thrift.TException",
      "org.apache.accumulo.core.client.impl.thrift.ThriftTableOperationException$_Fields",
      "org.apache.log4j.spi.RendererSupport",
      "org.apache.accumulo.core.data.Range",
      "org.apache.accumulo.core.tabletserver.thrift.TIteratorSetting$TIteratorSettingTupleScheme",
      "org.apache.accumulo.minicluster.MiniAccumuloCluster",
      "org.apache.accumulo.core.client.IteratorSetting",
      "org.apache.accumulo.core.security.ColumnVisibility",
      "org.apache.hadoop.io.Text$1",
      "org.apache.accumulo.core.data.PartialKey",
      "org.apache.accumulo.core.client.impl.thrift.ThriftTableOperationException",
      "org.apache.accumulo.core.client.BatchDeleter",
      "org.apache.accumulo.core.iterators.IteratorEnvironment",
      "org.apache.accumulo.core.data.thrift.IterInfo$IterInfoStandardSchemeFactory",
      "org.apache.accumulo.start.Main",
      "org.apache.thrift.transport.TTransportException",
      "org.apache.hadoop.io.FastByteComparisons",
      "org.apache.thrift.meta_data.StructMetaData",
      "org.apache.accumulo.core.security.thrift.AuthInfo$_Fields",
      "org.apache.accumulo.core.data.Mutation",
      "org.apache.accumulo.core.security.thrift.AuthInfo$AuthInfoStandardScheme",
      "org.apache.log4j.Logger",
      "org.apache.accumulo.core.data.thrift.TRange$TRangeStandardScheme",
      "org.apache.hadoop.io.Text$Comparator",
      "org.apache.hadoop.conf.Configured",
      "org.apache.accumulo.core.data.thrift.TKey$TKeyTupleScheme",
      "org.apache.accumulo.core.client.impl.thrift.ThriftTableOperationException$ThriftTableOperationExceptionTupleSchemeFactory",
      "org.apache.hadoop.fs.Path",
      "org.apache.accumulo.core.data.Value",
      "org.apache.thrift.transport.TIOStreamTransport",
      "org.apache.hadoop.conf.Configurable",
      "org.apache.log4j.helpers.LogLog",
      "org.apache.accumulo.core.data.thrift.TRange$TRangeTupleSchemeFactory",
      "org.apache.accumulo.core.security.ColumnVisibility$Node",
      "org.apache.thrift.scheme.IScheme",
      "org.apache.thrift.meta_data.MapMetaData",
      "org.apache.accumulo.core.data.thrift.TMutation$TMutationTupleSchemeFactory",
      "org.apache.accumulo.core.client.MultiTableBatchWriter",
      "org.apache.accumulo.core.tabletserver.thrift.IteratorConfig$IteratorConfigStandardScheme",
      "org.apache.accumulo.core.client.security.SecurityErrorCode",
      "org.apache.log4j.spi.RepositorySelector",
      "org.apache.log4j.or.DefaultRenderer",
      "org.apache.thrift.protocol.TMap",
      "org.apache.accumulo.core.security.thrift.AuthInfo$AuthInfoStandardSchemeFactory",
      "org.apache.thrift.scheme.SchemeFactory",
      "org.apache.accumulo.core.data.Mutation$SimpleReader",
      "org.apache.accumulo.core.conf.PropertyType",
      "org.apache.log4j.spi.ThrowableRendererSupport",
      "org.apache.log4j.PropertyConfigurator",
      "org.apache.hadoop.io.BinaryComparable",
      "org.apache.log4j.spi.ThrowableRenderer",
      "org.apache.log4j.Appender",
      "org.apache.hadoop.fs.ParentNotDirectoryException",
      "org.apache.hadoop.io.BytesWritable",
      "org.apache.thrift.scheme.TupleScheme",
      "org.apache.hadoop.fs.CreateFlag",
      "org.apache.accumulo.core.data.thrift.TKeyExtent$TKeyExtentTupleSchemeFactory",
      "org.apache.hadoop.io.FastByteComparisons$Comparer",
      "org.apache.accumulo.core.client.admin.SecurityOperations",
      "org.apache.accumulo.core.data.thrift.IterInfo$IterInfoTupleSchemeFactory",
      "org.apache.log4j.spi.HierarchyEventListener",
      "org.apache.accumulo.core.data.thrift.TMutation$TMutationStandardSchemeFactory",
      "org.apache.accumulo.core.client.impl.thrift.ThriftSecurityException$ThriftSecurityExceptionStandardSchemeFactory",
      "org.apache.log4j.spi.LoggingEvent",
      "org.apache.accumulo.core.Constants",
      "org.apache.accumulo.core.client.AccumuloException",
      "org.apache.log4j.spi.ThrowableInformation",
      "org.apache.accumulo.core.client.impl.thrift.ThriftTableOperationException$ThriftTableOperationExceptionStandardSchemeFactory",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder$UnsafeComparer",
      "org.apache.thrift.protocol.TProtocol",
      "org.apache.accumulo.core.security.thrift.SecurityErrorCode",
      "org.apache.accumulo.core.data.thrift.TRange$TRangeTupleScheme",
      "org.apache.accumulo.minicluster.MiniAccumuloConfig",
      "org.apache.accumulo.core.security.SystemPermission",
      "org.apache.log4j.spi.LoggerRepository",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder",
      "org.apache.hadoop.fs.LocalFileSystem",
      "org.apache.accumulo.core.data.thrift.TColumn$TColumnTupleScheme"
    );
  } 

  private static void resetClasses() {
    org.evosuite.runtime.classhandling.ClassResetter.getInstance().setClassLoader(MiniAccumuloCluster_ESTest_scaffolding.class.getClassLoader()); 

    org.evosuite.runtime.classhandling.ClassStateSupport.resetClasses(
      "org.apache.log4j.Category",
      "org.apache.log4j.Logger",
      "org.apache.log4j.Hierarchy",
      "org.apache.log4j.spi.RootLogger",
      "org.apache.log4j.Priority",
      "org.apache.log4j.Level",
      "org.apache.log4j.or.DefaultRenderer",
      "org.apache.log4j.or.RendererMap",
      "org.apache.log4j.DefaultCategoryFactory",
      "org.apache.log4j.spi.DefaultRepositorySelector",
      "org.apache.log4j.helpers.OptionConverter",
      "org.apache.log4j.helpers.Loader",
      "org.apache.log4j.helpers.LogLog",
      "org.apache.log4j.PropertyConfigurator",
      "org.apache.log4j.LogManager",
      "org.apache.log4j.CategoryKey",
      "org.apache.log4j.ProvisionNode",
      "org.apache.accumulo.minicluster.MiniAccumuloCluster",
      "org.apache.accumulo.minicluster.MiniAccumuloCluster$LogWriter",
      "org.apache.accumulo.minicluster.MiniAccumuloCluster$LogWriter$1",
      "org.apache.accumulo.minicluster.MiniAccumuloCluster$1",
      "org.apache.accumulo.minicluster.MiniAccumuloCluster$2",
      "org.apache.accumulo.core.data.Key",
      "org.apache.hadoop.io.BinaryComparable",
      "org.apache.hadoop.io.Text$1",
      "org.apache.hadoop.io.Text$2",
      "org.apache.hadoop.io.WritableComparator",
      "org.apache.hadoop.io.Text$Comparator",
      "org.apache.hadoop.io.Text",
      "org.apache.accumulo.core.data.Range",
      "org.apache.accumulo.core.data.PartialKey",
      "org.apache.hadoop.io.FastByteComparisons",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder$UnsafeComparer$1",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder$UnsafeComparer",
      "org.apache.hadoop.io.FastByteComparisons$LexicographicalComparerHolder",
      "org.apache.accumulo.core.Constants",
      "org.apache.accumulo.core.conf.PropertyType",
      "org.apache.accumulo.core.iterators.IteratorUtil$IteratorScope",
      "org.apache.accumulo.core.util.UtilWaitThread",
      "org.apache.accumulo.minicluster.MiniAccumuloConfig",
      "org.apache.accumulo.server.util.PortUtils",
      "org.apache.hadoop.util.Daemon",
      "org.apache.hadoop.util.Daemon$DaemonFactory",
      "org.apache.accumulo.start.Main"
    );
  }
}
