/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 18:40:13 GMT 2019
 */

package org.apache.accumulo.minicluster;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.File;
import java.io.FileNotFoundException;
import org.apache.accumulo.minicluster.MiniAccumuloCluster;
import org.apache.accumulo.minicluster.MiniAccumuloConfig;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MiniAccumuloCluster_ESTest extends MiniAccumuloCluster_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster((MiniAccumuloConfig) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test1()  throws Throwable  {
      File file0 = MockFile.createTempFile("CRYPTO_DEFAULT_KEY_STRATEGY_KEY_LOCATION", "CRYPTO_DEFAULT_KEY_STRATEGY_KEY_LOCATION");
      MockFile mockFile0 = new MockFile(file0, "CRYPTO_DEFAULT_KEY_STRATEGY_KEY_LOCATION");
      MiniAccumuloConfig miniAccumuloConfig0 = new MiniAccumuloConfig(mockFile0, "CRYPTO_DEFAULT_KEY_STRATEGY_KEY_LOCATION");
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(miniAccumuloConfig0);
        fail("Expecting exception: FileNotFoundException");
      
      } catch(Throwable e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockFileOutputStream", e);
      }
  }

  @Test(timeout = 4000)
  public void test2()  throws Throwable  {
      MockFile mockFile0 = new MockFile("", "");
      MiniAccumuloConfig miniAccumuloConfig0 = new MiniAccumuloConfig(mockFile0, (String) null);
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(miniAccumuloConfig0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Directory / is not empty
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test3()  throws Throwable  {
      MockFile mockFile0 = new MockFile("The truststore for enabling monitor SSL.", "");
      MiniAccumuloConfig miniAccumuloConfig0 = new MiniAccumuloConfig(mockFile0, (String) null);
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(miniAccumuloConfig0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // Could not initialize class org.apache.accumulo.core.Constants
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test4()  throws Throwable  {
      File file0 = MockFile.createTempFile("8ITkvFr%:0iV", "8ITkvFr%:0iV");
      MiniAccumuloConfig miniAccumuloConfig0 = new MiniAccumuloConfig(file0, "");
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(miniAccumuloConfig0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Must pass in directory, /tmp/8ITkvFr%:0iV08ITkvFr%:0iV is a file
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test5()  throws Throwable  {
      MockFile mockFile0 = new MockFile("");
      MiniAccumuloConfig miniAccumuloConfig0 = new MiniAccumuloConfig(mockFile0, "");
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(miniAccumuloConfig0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // Could not initialize class org.apache.accumulo.core.Constants
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test6()  throws Throwable  {
      MockFile mockFile0 = new MockFile("", "");
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(mockFile0, "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Directory / is not empty
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test7()  throws Throwable  {
      MockFile mockFile0 = new MockFile("");
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(mockFile0, "");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // Could not initialize class org.apache.accumulo.core.Constants
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test8()  throws Throwable  {
      File file0 = MockFile.createTempFile("Error reading merge state, it probably just finished", "Error reading merge state, it probably just finished");
      MockFile mockFile0 = new MockFile(file0, "Error reading merge state, it probably just finished");
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(mockFile0, "Error reading merge state, it probably just finished");
        fail("Expecting exception: FileNotFoundException");
      
      } catch(Throwable e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockFileOutputStream", e);
      }
  }

  @Test(timeout = 4000)
  public void test9()  throws Throwable  {
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster((File) null, ", properties:");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }
}
