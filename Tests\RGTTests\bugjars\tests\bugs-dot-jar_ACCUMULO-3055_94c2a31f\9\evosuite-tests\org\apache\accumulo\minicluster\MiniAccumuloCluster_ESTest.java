/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 18:53:29 GMT 2019
 */

package org.apache.accumulo.minicluster;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.File;
import java.io.FileNotFoundException;
import java.util.HashMap;
import java.util.concurrent.ForkJoinTask;
import org.apache.accumulo.minicluster.MiniAccumuloCluster;
import org.apache.accumulo.minicluster.MiniAccumuloConfig;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.mock.java.io.MockFile;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MiniAccumuloCluster_ESTest extends MiniAccumuloCluster_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      ForkJoinTask.getPool();
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster((MiniAccumuloConfig) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      MockFile mockFile0 = new MockFile("");
      MiniAccumuloConfig miniAccumuloConfig0 = new MiniAccumuloConfig(mockFile0, "");
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(miniAccumuloConfig0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // Could not initialize class org.apache.accumulo.core.Constants
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      File file0 = MockFile.createTempFile("p$tevdq'vy2O%", "p$tevdq'vy2O%");
      MiniAccumuloConfig miniAccumuloConfig0 = new MiniAccumuloConfig(file0, "");
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(miniAccumuloConfig0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Must pass in directory, /tmp/p$tevdq'vy2O%0p$tevdq'vy2O% is a file
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      File file0 = MockFile.createTempFile("2VHg", "2VHg");
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(file0, "2VHg");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Must pass in directory, /tmp/2VHg02VHg is a file
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      MockFile mockFile0 = new MockFile("", "");
      String string0 = "";
      MiniAccumuloConfig miniAccumuloConfig0 = new MiniAccumuloConfig(mockFile0, "");
      miniAccumuloConfig0.setNumTservers(30);
      HashMap<String, String> hashMap0 = new HashMap<String, String>();
      MiniAccumuloConfig miniAccumuloConfig1 = miniAccumuloConfig0.setSiteConfig(hashMap0);
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(miniAccumuloConfig1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Directory / is not empty
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      File file0 = MockFile.createTempFile("t;]`H'MLT7E", "t;]`H'MLT7E");
      MockFile mockFile0 = new MockFile(file0, "~|m7bl5f%&");
      MiniAccumuloConfig miniAccumuloConfig0 = new MiniAccumuloConfig(mockFile0, "t;]`H'MLT7E");
      MiniAccumuloConfig miniAccumuloConfig1 = miniAccumuloConfig0.setNumTservers(5);
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(miniAccumuloConfig1);
        fail("Expecting exception: FileNotFoundException");
      
      } catch(Throwable e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockFileOutputStream", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      File file0 = MockFile.createTempFile("2VHg", "2VHg");
      MiniAccumuloConfig miniAccumuloConfig0 = new MiniAccumuloConfig(file0, "");
      HashMap<String, String> hashMap0 = new HashMap<String, String>();
      MiniAccumuloConfig miniAccumuloConfig1 = miniAccumuloConfig0.setSiteConfig(hashMap0);
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(miniAccumuloConfig1);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Must pass in directory, /tmp/2VHg02VHg is a file
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      MockFile mockFile0 = new MockFile("");
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(mockFile0, "");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // Could not initialize class org.apache.accumulo.core.Constants
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      MockFile mockFile0 = new MockFile("</configuration>\n");
      File file0 = MockFile.createTempFile("</configuration>\n", "I\"A+S", (File) mockFile0);
      MockFile mockFile1 = new MockFile(file0, "I\"A+S");
      mockFile0.setWritable(true);
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster(mockFile1, "I\"A+S");
        fail("Expecting exception: FileNotFoundException");
      
      } catch(Throwable e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.evosuite.runtime.mock.java.io.MockFileOutputStream", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      MiniAccumuloCluster miniAccumuloCluster0 = null;
      try {
        miniAccumuloCluster0 = new MiniAccumuloCluster((File) null, "");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.accumulo.minicluster.MiniAccumuloCluster", e);
      }
  }
}
