/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 00:11:57 GMT 2019
 */

package org.apache.commons.math3.fitting.leastsquares;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresAdapter;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresProblem;
import org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer;
import org.apache.commons.math3.linear.ArrayRealVector;
import org.apache.commons.math3.linear.OpenMapRealVector;
import org.apache.commons.math3.optim.ConvergenceChecker;
import org.apache.commons.math3.util.Incrementor;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class LevenbergMarquardtOptimizer_ESTest extends LevenbergMarquardtOptimizer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((LeastSquaresProblem.Evaluation) null).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(2602).when(leastSquaresProblem0).getObservationSize();
      doReturn(2602).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0, (Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getIterationCounter();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(0, 0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(70).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((LeastSquaresProblem.Evaluation) null).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(2602).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance((-1333.0));
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1333.0), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold(1426.455027094);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withParameterRelativeTolerance(1426.455027094);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1426.455027094, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals(1426.455027094, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1426.455027094, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-184.71236053568), (-184.71236053568), (-184.71236053568), (-1342.0), (-184.71236053568));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance((-1342.0));
      assertEquals((-1342.0), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1342.0), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1342.0), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2010.888984207743), (-2010.888984207743), 0.0, 2099.95738, 0.25);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold((-2010.888984207743));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(2099.95738);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(2099.95738, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.25, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2010.888984207743), levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2010.888984207743), levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals((-2010.888984207743), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2010.888984207743), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2099.95738, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-2010.888984207743), levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance((-595.800172));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-595.800172));
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-595.800172), levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals((-595.800172), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-595.800172), levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance((-595.800172));
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-595.800172), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(0.0);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(0.0);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor((-1081.371602766807));
      assertEquals(100.0, levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer4.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1081.371602766807), levenbergMarquardtOptimizer4.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.006);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.006, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor((-588.74392054));
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(630.084156394134);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(630.084156394134, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-1267.6430295878));
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1267.6430295878), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold((-1725.107299));
      double double0 = levenbergMarquardtOptimizer1.getRankingThreshold();
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1725.107299), double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054));
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals((-588.74392054), double0, 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-184.71236053568), (-184.71236053568), (-184.71236053568), (-1342.0), (-184.71236053568));
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-184.71236053568), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-1342.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(1.0, 1.0, 0.0, 0.0, 0.006);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      double double0 = levenbergMarquardtOptimizer1.getInitialStepBoundFactor();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.006, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.006, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054));
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), double0, 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054), (-588.74392054));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      double double0 = levenbergMarquardtOptimizer1.getCostRelativeTolerance();
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-588.74392054), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(1309.84326307989, (-401.1), 645.78, 0.0, (-2742.0));
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals(645.78, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2742.0), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1309.84326307989, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-401.1), double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(1540, 4516);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((-2838)).when(leastSquaresProblem0).getObservationSize();
      doReturn(3699).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[7];
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(doubleArray0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn((-2274)).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(3282.3605929314735, 3282.3605929314735, 3282.3605929314735, 3282.3605929314735, 3282.3605929314735);
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals(3282.3605929314735, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(3282.3605929314735, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(3282.3605929314735, double0, 0.01);
      assertEquals(3282.3605929314735, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(3282.3605929314735, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(3282.3605929314735, 3282.3605929314735, 3282.3605929314735, 3282.3605929314735, 3282.3605929314735);
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals(3282.3605929314735, double0, 0.01);
      assertEquals(3282.3605929314735, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(3282.3605929314735, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(3282.3605929314735, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(3282.3605929314735, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(100.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withInitialStepBoundFactor(0.0);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      double double0 = levenbergMarquardtOptimizer1.getRankingThreshold();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }
}
