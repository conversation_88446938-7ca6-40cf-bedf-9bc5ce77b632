/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 00:16:31 GMT 2019
 */

package org.apache.commons.math3.fitting.leastsquares;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresAdapter;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresProblem;
import org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer;
import org.apache.commons.math3.linear.ArrayRealVector;
import org.apache.commons.math3.linear.OpenMapRealVector;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.optim.ConvergenceChecker;
import org.apache.commons.math3.util.Incrementor;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class LevenbergMarquardtOptimizer_ESTest extends LevenbergMarquardtOptimizer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[2];
      doubleArray0[1] = 394552.35;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0);
      OpenMapRealVector openMapRealVector1 = new OpenMapRealVector(3901, 291, 291.0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(291, (-1355.85));
      RealMatrix realMatrix0 = arrayRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((double)3901).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(291).when(leastSquaresProblem0).getObservationSize();
      doReturn(3901).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector1).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[2];
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((double)3901).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(302).when(leastSquaresProblem0).getObservationSize();
      doReturn(3901).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[2];
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0);
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor1 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((LeastSquaresProblem.Evaluation) null).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(302).when(leastSquaresProblem0).getObservationSize();
      doReturn(3901).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker<LeastSquaresProblem.Evaluation>) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor1).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-624.0083745026864), (-624.0083745026864), 6.283185307179586, (-624.0083745026864), (-624.0083745026864));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold((-624.0083745026864));
      assertEquals((-624.0083745026864), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(6.283185307179586, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(6.283185307179586, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-624.0083745026864), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-624.0083745026864), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-624.0083745026864), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-624.0083745026864), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-624.0083745026864), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-624.0083745026864), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(1.0E-12);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-12, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold(0.0);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1146.6), 0.0, (-1146.6), 0.5, (-699.3205));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(131.648);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(131.648, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1146.6), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1146.6), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-1146.6), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1146.6), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1146.6), 0.0, (-1146.6), 0.5, (-699.3205));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(131.648);
      assertEquals((-699.3205), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(131.648, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1146.6), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1146.6), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-699.3205), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-1631.449665139));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(Double.POSITIVE_INFINITY);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1631.449665139), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(Double.POSITIVE_INFINITY, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1631.449665139), levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-3.147665), 2100.84, 2100.84, 2100.84, (-3.147665));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance((-3.147665));
      assertEquals(2100.84, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-3.147665), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(2100.84, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-3.147665), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-3.147665), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2100.84, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-3.147665), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(2100.84, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-3.147665), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(2100.84, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 2590.2, 6.283185307179586);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(2590.2);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2590.2, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(6.283185307179586, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(6.283185307179586, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2615.8146152), (-2615.8146152), (-2615.8146152), (-2615.8146152), 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance((-2615.8146152));
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1146.6), 0.0, (-1146.6), 0.5, (-699.3205));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(345.4862201374842);
      assertEquals(0.5, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-1146.6), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(345.4862201374842, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-699.3205), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-699.3205), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-1146.6), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2615.8146152), (-2615.8146152), (-2615.8146152), (-2615.8146152), 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor((-2615.8146152));
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-3.147665), 2100.84, 2100.84, 2100.84, (-3.147665));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withCostRelativeTolerance(2100.84);
      assertEquals(2100.84, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals((-3.147665), levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(2100.84, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(2100.84, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(2100.84, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2967.4194077029), (-2967.4194077029), (-1.0), 0.0, (-5795.510342978));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.75);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-2967.4194077029), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.75, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-5795.510342978), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2967.4194077029), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-5795.510342978), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1359.4), 3.0, (-1359.4), 3.0, 0.5);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance((-1359.4));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance((-1359.4));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withCostRelativeTolerance(1841.82095);
      assertEquals((-1359.4), levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals((-1359.4), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1359.4), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1359.4), levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals(1841.82095, levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-1359.4), levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals((-1359.4), levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1359.4), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withCostRelativeTolerance((-1906.3));
      assertEquals(0.0, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-1906.3), levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-871.332), (-871.332), (-871.332), (-871.332), (-871.332));
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals((-871.332), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-871.332), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-871.332), double0, 0.01);
      assertEquals((-871.332), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-871.332), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 1934.1);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1934.1, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1359.4), 3.0, (-1359.4), 3.0, 0.5);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals((-1359.4), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-1359.4), double0, 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 1934.1);
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1934.1, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(Double.NEGATIVE_INFINITY);
      double double0 = levenbergMarquardtOptimizer1.getOrthoTolerance();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      double double0 = levenbergMarquardtOptimizer1.getInitialStepBoundFactor();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2615.8146152), (-2615.8146152), (-2615.8146152), (-2615.8146152), 0.0);
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2615.8146152), double0, 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2615.8146152), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2967.4194077029), (-2967.4194077029), (-1.0), 0.0, (-5795.510342978));
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals((-1.0), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2967.4194077029), double0, 0.01);
      assertEquals((-5795.510342978), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2967.4194077029), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(3901, 3901, 2405.5730638414207);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(302).when(leastSquaresProblem0).getObservationSize();
      doReturn(3901).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-624.0083745026864), (-624.0083745026864), 6.283185307179586, (-624.0083745026864), (-624.0083745026864));
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(894).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector((-1157), 0.0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((-1157)).when(leastSquaresProblem0).getObservationSize();
      doReturn((-1157)).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.linear.OpenMapRealVector", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 291;
      doubleArray0[1] = 394552.35;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(291, (-1355.85));
      RealMatrix realMatrix0 = arrayRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((double)3901).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(291).when(leastSquaresProblem0).getObservationSize();
      doReturn(3901).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals(2.2250738585072014E-308, double0, 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      double double0 = levenbergMarquardtOptimizer1.getRankingThreshold();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withCostRelativeTolerance((-1631.449665139));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withParameterRelativeTolerance(Double.POSITIVE_INFINITY);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1631.449665139), levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals((-1631.449665139), levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(Double.POSITIVE_INFINITY, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }
}
