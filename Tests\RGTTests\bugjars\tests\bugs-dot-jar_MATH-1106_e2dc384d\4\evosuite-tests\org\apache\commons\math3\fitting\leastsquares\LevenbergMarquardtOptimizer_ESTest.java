/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 00:22:04 GMT 2019
 */

package org.apache.commons.math3.fitting.leastsquares;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresAdapter;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresProblem;
import org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer;
import org.apache.commons.math3.linear.ArrayRealVector;
import org.apache.commons.math3.linear.OpenMapRealVector;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.optim.ConvergenceChecker;
import org.apache.commons.math3.util.Incrementor;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class LevenbergMarquardtOptimizer_ESTest extends LevenbergMarquardtOptimizer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(860, 860);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      Double[] doubleArray0 = new Double[8];
      openMapRealVector0.set(860);
      doubleArray0[0] = (Double) 1.0E-12;
      doubleArray0[1] = (Double) 1.0E-12;
      doubleArray0[2] = (Double) 1.0E-12;
      doubleArray0[3] = (Double) 1.0E-12;
      doubleArray0[4] = (Double) 1.0E-12;
      doubleArray0[5] = (Double) 1.0E-12;
      doubleArray0[6] = (Double) 1.0E-12;
      doubleArray0[7] = (Double) 1.0E-12;
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(doubleArray0);
      RealMatrix realMatrix0 = arrayRealVector0.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((double)860).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(860).when(leastSquaresProblem0).getObservationSize();
      doReturn(860).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 8
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(890, 890);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      Double[] doubleArray0 = new Double[8];
      doubleArray0[0] = (Double) 1.0E-12;
      doubleArray0[1] = (Double) 1.0E-12;
      doubleArray0[2] = (Double) 1.0E-12;
      doubleArray0[3] = (Double) 1.0E-12;
      doubleArray0[4] = (Double) 1.0E-12;
      doubleArray0[5] = (Double) 1.0E-12;
      doubleArray0[6] = (Double) 1.0E-12;
      doubleArray0[7] = (Double) 1.0E-12;
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(doubleArray0);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(arrayRealVector0, arrayRealVector0);
      RealMatrix realMatrix0 = arrayRealVector1.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((double)890).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(890).when(leastSquaresProblem0).getObservationSize();
      doReturn(890).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 8
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(890, 890);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((double)890).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(890).when(leastSquaresProblem0).getObservationSize();
      doReturn(890).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0, (Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getIterationCounter();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(491).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(1245.8631, 1245.8631, 1245.8631, (-2152.69526389287), 1245.8631);
      Double[] doubleArray0 = new Double[8];
      Double double0 = new Double((-1264.09772));
      doubleArray0[0] = double0;
      doubleArray0[1] = doubleArray0[0];
      doubleArray0[2] = double0;
      doubleArray0[3] = doubleArray0[0];
      doubleArray0[4] = doubleArray0[1];
      doubleArray0[5] = doubleArray0[0];
      doubleArray0[6] = doubleArray0[4];
      doubleArray0[7] = doubleArray0[1];
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(doubleArray0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(751).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-641.397824451), 0.75, 0.0, (-641.397824451), 0.75);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(1.0E-4);
      assertEquals((-641.397824451), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-641.397824451), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-641.397824451), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-4, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-641.397824451), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.75, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.75, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, (-2523.792), (-2523.792), 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2523.792), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-2523.792), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2523.792), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2523.792), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold((-3065.0723444963196));
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-3065.0723444963196), levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.5);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2193.13110292), (-2193.13110292), (-2193.13110292), (-2193.13110292), (-2193.13110292));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.001);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.001, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2148.717022604548), (-2148.717022604548), (-1949.396504590046), 0.1, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(10.0);
      assertEquals((-1949.396504590046), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1949.396504590046), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2148.717022604548), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2148.717022604548), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2148.717022604548), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2148.717022604548), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2193.13110292), (-2193.13110292), (-2193.13110292), (-2193.13110292), (-2193.13110292));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor((-2193.13110292));
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2148.717022604548), (-2148.717022604548), (-1949.396504590046), 0.1, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(6.283185307179586);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2148.717022604548), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.1, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2148.717022604548), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.1, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(6.283185307179586, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1949.396504590046), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1949.396504590046), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withCostRelativeTolerance((-3065.0723444963196));
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals((-3065.0723444963196), levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2193.13110292), (-2193.13110292), (-2193.13110292), (-2193.13110292), (-2193.13110292));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold(0.001);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.001, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(2974.887742527026);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(2974.887742527026, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      double double0 = levenbergMarquardtOptimizer1.getRankingThreshold();
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2193.13110292), (-2193.13110292), (-2193.13110292), (-2193.13110292), (-2193.13110292));
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals((-2193.13110292), double0, 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2193.13110292), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 2614.120497585632, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(2614.120497585632, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2148.717022604548), (-2148.717022604548), (-1949.396504590046), 0.1, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals((-2148.717022604548), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.1, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-1949.396504590046), double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2148.717022604548), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-616.0), 994.013104852, (-616.0), (-616.0), (-1879.858792834));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.0);
      double double0 = levenbergMarquardtOptimizer1.getOrthoTolerance();
      assertEquals(0.0, double0, 0.01);
      assertEquals((-616.0), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(994.013104852, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-616.0), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-616.0), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1879.858792834), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-616.0), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(994.013104852, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-1879.858792834), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      double double0 = levenbergMarquardtOptimizer1.getInitialStepBoundFactor();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1719.878), 0.0, 2.0, Double.NEGATIVE_INFINITY, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals((-1719.878), double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(2.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.5, 0.0, 0.0, (-1483.96), 880.0);
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals(880.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals((-1483.96), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2148.717022604548), (-2148.717022604548), (-1949.396504590046), 0.1, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-1949.396504590046), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2148.717022604548), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.1, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2148.717022604548), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((-1190)).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(997.0, 997.0, 997.0, 997.0, 997.0);
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals(997.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(997.0, double0, 0.01);
      assertEquals(997.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(997.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(997.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(100.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(3543.0087249, 1.0E-12, 1.0E-4, Double.NEGATIVE_INFINITY, 1.0E-4);
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(1.0E-12, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-4, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(3543.0087249, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(1.0E-4, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(997.0, 997.0, 997.0, 997.0, 997.0);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(997.0, double0, 0.01);
      assertEquals(997.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(997.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(997.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(997.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance((-2318.49233450198));
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2318.49233450198), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }
}
