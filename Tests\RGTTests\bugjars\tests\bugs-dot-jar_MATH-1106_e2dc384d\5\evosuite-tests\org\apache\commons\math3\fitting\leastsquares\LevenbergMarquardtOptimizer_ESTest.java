/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 00:25:41 GMT 2019
 */

package org.apache.commons.math3.fitting.leastsquares;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresAdapter;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresProblem;
import org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer;
import org.apache.commons.math3.linear.ArrayRealVector;
import org.apache.commons.math3.linear.OpenMapRealVector;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.optim.ConvergenceChecker;
import org.apache.commons.math3.util.Incrementor;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class LevenbergMarquardtOptimizer_ESTest extends LevenbergMarquardtOptimizer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(32, 32, 0.1);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(32, (-3994)).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.1).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(32).when(leastSquaresProblem0).getObservationSize();
      doReturn(32).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) 1;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.1).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(1).when(leastSquaresProblem0).getObservationSize();
      doReturn(1).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[9];
      doubleArray0[1] = 6.283185307179586;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.1).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(1).when(leastSquaresProblem0).getObservationSize();
      doReturn(1).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[9];
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(doubleArray0, doubleArray0);
      RealMatrix realMatrix0 = arrayRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.0).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[9];
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((LeastSquaresProblem.Evaluation) null).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(2).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      Double[] doubleArray0 = new Double[1];
      Double double0 = new Double(0.0);
      doubleArray0[0] = double0;
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(doubleArray0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(2249).when(leastSquaresProblem0).getObservationSize();
      doReturn(341).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2076.43167588942), (-2076.43167588942), (-2076.43167588942), 0.0, (-2076.43167588942));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, (-15.94745094745), (-15.94745094745));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold((-15.94745094745));
      assertEquals((-15.94745094745), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-15.94745094745), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-15.94745094745), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1.0), (-1.0), (-1.0), (-1645.2550151), 2.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(2.0);
      assertEquals((-1645.2550151), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1645.2550151), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(2.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1060.26990011), (-1060.26990011), (-1060.26990011), (-2423.0), (-2423.0));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance((-2423.0));
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2423.0), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2423.0), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-2423.0), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2423.0), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2423.0), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1.0), (-1.0), (-1.0), (-1645.2550151), 2.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(0.0);
      assertEquals((-1645.2550151), levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-795.3066098511), (-795.3066098511), (-795.3066098511), (-1.0), 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals((-795.3066098511), levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-795.3066098511), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-795.3066098511), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(0.5);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-1205.8956));
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1205.8956), levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(3902.080907);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(3902.080907, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, (-809.86), (-922.95397200347), 44.52977692343721);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(772.6082768);
      assertEquals((-809.86), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(772.6082768, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(44.52977692343721, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(44.52977692343721, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-809.86), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1060.26990011), (-1060.26990011), (-1060.26990011), (-2423.0), (-2423.0));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance((-1060.26990011));
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2423.0), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2423.0), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1060.26990011), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2423.0), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2076.43167588942), (-2076.43167588942), (-2076.43167588942), 0.0, (-2076.43167588942));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor((-2076.43167588942));
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, (-15.94745094745), (-15.94745094745));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor((-700.1359970348443));
      assertEquals((-15.94745094745), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-15.94745094745), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-15.94745094745), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-15.94745094745), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-700.1359970348443), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2122.545745958468), (-2122.545745958468), 0.0, (-2122.545745958468), 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-2122.545745958468));
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2122.545745958468), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2122.545745958468), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2122.545745958468), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-2122.545745958468), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2122.545745958468), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2076.43167588942), (-2076.43167588942), (-2076.43167588942), 0.0, (-2076.43167588942));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-795.3066098511), (-795.3066098511), (-795.3066098511), (-1.0), 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withCostRelativeTolerance(0.0);
      assertEquals((-795.3066098511), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-795.3066098511), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals((-795.3066098511), levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1944.36915526), (-1944.36915526), (-1944.36915526), (-1944.36915526), 20.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-44.8308576028));
      assertEquals(20.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1944.36915526), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1944.36915526), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1944.36915526), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1944.36915526), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-1944.36915526), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-1944.36915526), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-44.8308576028), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(1, 1);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(0, 0).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.1).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(1).when(leastSquaresProblem0).getObservationSize();
      doReturn(1).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(1, 1, 0.1);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn((-1424), 1).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.1).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(1).when(leastSquaresProblem0).getObservationSize();
      doReturn(1).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2122.545745958468), (-2122.545745958468), 0.0, (-2122.545745958468), 0.0);
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals(0.0, double0, 0.01);
      assertEquals((-2122.545745958468), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2122.545745958468), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2122.545745958468), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-241.81833), (-241.81833), (-241.81833), (-241.81833), (-241.81833));
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals((-241.81833), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-241.81833), double0, 0.01);
      assertEquals((-241.81833), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-241.81833), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-241.81833), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(Double.NEGATIVE_INFINITY, 1.2285363567532743, 0.0, 1948.1990734191907, 1.2285363567532743);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(1.2285363567532743, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.2285363567532743, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1948.1990734191907, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, (-809.86), (-922.95397200347), 44.52977692343721);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-809.86), double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-922.95397200347), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(44.52977692343721, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2076.43167588942), (-2076.43167588942), (-2076.43167588942), 0.0, (-2076.43167588942));
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2076.43167588942), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1944.36915526), (-1944.36915526), (-1944.36915526), (-1944.36915526), 20.0);
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals((-1944.36915526), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-1944.36915526), double0, 0.01);
      assertEquals((-1944.36915526), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1944.36915526), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 10.0, 2170.352034, 0.0, 1.0);
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(2170.352034, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1.0), (-1.0), (-1.0), (-1645.2550151), 2.0);
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals((-1645.2550151), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-1.0), double0, 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      double double0 = levenbergMarquardtOptimizer1.getCostRelativeTolerance();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2528.419811435829), (-2528.419811435829), (-2528.419811435829), (-2528.419811435829), (-2528.419811435829));
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals((-2528.419811435829), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2528.419811435829), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2528.419811435829), double0, 0.01);
      assertEquals((-2528.419811435829), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-2528.419811435829), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(5.0791825488, 5.0791825488, 5.0791825488, (-1435.1323958), (-1435.1323958));
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(931, 4715);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(931).when(leastSquaresProblem0).getObservationSize();
      doReturn(931).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector((-10), (-314), 0.0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((-154)).when(leastSquaresProblem0).getObservationSize();
      doReturn((-154)).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.linear.OpenMapRealVector", e);
      }
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) 19;
      doubleArray0[1] = 6.283185307179586;
      doubleArray0[2] = (double) 19;
      doubleArray0[3] = (double) 19;
      doubleArray0[4] = 0.1;
      doubleArray0[5] = 1.0E-10;
      doubleArray0[6] = 100.0;
      doubleArray0[7] = 9.253200356897354;
      doubleArray0[8] = (double) 19;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.1).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(19).when(leastSquaresProblem0).getObservationSize();
      doReturn(19).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 9
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(1, 1, 0.0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(1, 1).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.0).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(openMapRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(1).when(leastSquaresProblem0).getObservationSize();
      doReturn(1).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(2432.6691, 2432.6691, 2432.6691, 2432.6691, 2432.6691);
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals(2432.6691, double0, 0.01);
      assertEquals(2432.6691, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(2432.6691, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(2432.6691, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(2432.6691, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(5.3351092848624024E-51);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(5.3351092848624024E-51, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.75, 1414.539669, 948.0232020686715, 0.001, 2465.249191693028);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(1414.539669, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.75, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(948.0232020686715, double0, 0.01);
      assertEquals(2465.249191693028, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.001, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.1);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.1, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
  }
}
