/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 00:30:06 GMT 2019
 */

package org.apache.commons.math3.fitting.leastsquares;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresAdapter;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresProblem;
import org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer;
import org.apache.commons.math3.linear.ArrayRealVector;
import org.apache.commons.math3.linear.OpenMapRealVector;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.linear.RealVector;
import org.apache.commons.math3.optim.ConvergenceChecker;
import org.apache.commons.math3.util.Incrementor;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class LevenbergMarquardtOptimizer_ESTest extends LevenbergMarquardtOptimizer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(179, 510.8861987648);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector();
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(5, 5).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector2).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector2).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-966.148811));
      levenbergMarquardtOptimizer2.withParameterRelativeTolerance((-76.2545));
      levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(2383.5208480415454);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor((-3586.2909823));
      levenbergMarquardtOptimizer4.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      levenbergMarquardtOptimizer1.withCostRelativeTolerance((-3586.2909823));
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(179, 510.8861987648);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector();
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(179, 5).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector2).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector2).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-966.148811));
      levenbergMarquardtOptimizer2.withParameterRelativeTolerance((-76.2545));
      levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(2383.5208480415454);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor((-3586.2909823));
      levenbergMarquardtOptimizer4.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = new LevenbergMarquardtOptimizer((-966.148811), 179, 510.8861987648, 510.8861987648, 1.0E-10);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(179, 510.8861987648);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector();
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(5, (-2147483647)).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector2).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector2).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-966.148811));
      levenbergMarquardtOptimizer2.withParameterRelativeTolerance((-76.2545));
      levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(2383.5208480415454);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor((-3586.2909823));
      levenbergMarquardtOptimizer4.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      levenbergMarquardtOptimizer1.withCostRelativeTolerance((-3586.2909823));
      levenbergMarquardtOptimizer3.withCostRelativeTolerance((-3.7269289985326055E-143));
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(179, 510.8861987648);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector();
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn((-1234), 179).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector2).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector2).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-966.148811));
      levenbergMarquardtOptimizer2.withParameterRelativeTolerance((-76.2545));
      levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(2383.5208480415454);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor((-3586.2909823));
      levenbergMarquardtOptimizer4.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      levenbergMarquardtOptimizer1.withCostRelativeTolerance((-3586.2909823));
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(179, 510.8861987648);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector();
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(156, 5).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector2).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5, 0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179, 0).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector2, (RealVector) null).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-966.148811));
      levenbergMarquardtOptimizer2.withParameterRelativeTolerance((-76.2545));
      levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(2383.5208480415454);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor((-3586.2909823));
      levenbergMarquardtOptimizer4.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      levenbergMarquardtOptimizer1.withCostRelativeTolerance((-3586.2909823));
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer2.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(179, 510.8861987648);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector();
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(5, 179).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector2).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector2).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-966.148811));
      levenbergMarquardtOptimizer2.withParameterRelativeTolerance((-76.2545));
      levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(2383.5208480415454);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor((-3586.2909823));
      levenbergMarquardtOptimizer4.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      levenbergMarquardtOptimizer1.withCostRelativeTolerance((-3586.2909823));
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(179, 510.8861987648);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector();
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(163, 1).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector2).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector2).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-966.148811));
      levenbergMarquardtOptimizer2.withParameterRelativeTolerance((-76.2545));
      levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(2383.5208480415454);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor((-3586.2909823));
      levenbergMarquardtOptimizer4.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      levenbergMarquardtOptimizer1.withCostRelativeTolerance((-3586.2909823));
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(179, 510.8861987648);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector();
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(0, 0).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector2).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector2).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-966.148811));
      levenbergMarquardtOptimizer2.withParameterRelativeTolerance((-76.2545));
      levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(2383.5208480415454);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = levenbergMarquardtOptimizer4.withOrthoTolerance((-3586.2909823));
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer5.getRankingThreshold(), 0.01);
      assertEquals((-3586.2909823), levenbergMarquardtOptimizer5.getCostRelativeTolerance(), 0.01);
      assertEquals((-3586.2909823), levenbergMarquardtOptimizer5.getInitialStepBoundFactor(), 0.01);
      
      double double0 = levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      assertEquals(1.0E-10, double0, 0.01);
      
      levenbergMarquardtOptimizer1.withCostRelativeTolerance((-3586.2909823));
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double double0 = (-3586.2909823);
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(realVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 5
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      levenbergMarquardtOptimizer0.withRankingThreshold(1.0E-10);
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 6.1407711078356886E265;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = arrayRealVector1.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(6.1407711078356886E265).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer0.withInitialStepBoundFactor(1677.0);
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(arrayRealVector0, arrayRealVector1);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: unable to perform Q.R decomposition on the 7x7 jacobian matrix
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      levenbergMarquardtOptimizer0.withCostRelativeTolerance((-1674.17));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-1674.17);
      doubleArray0[1] = 1.0E-10;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = arrayRealVector1.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-0.34627030724142993)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(1).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer0.withInitialStepBoundFactor(1677.0);
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(realVector0, arrayRealVector1);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double double0 = (-3586.29098229861);
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-3586.29098229861), 2383.5208480415454, 2383.5208480415454, 2383.5208480415454, (-3586.29098229861));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.29098229861));
      Double double2 = new Double((-3586.29098229861));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer0.withOrthoTolerance((-3586.29098229861));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      doubleArray0[1] = (double) double2;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(realVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1, realVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double double0 = (-3586.29098229861);
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-3586.29098229861), 2383.5208480415454, 2383.5208480415454, 2383.5208480415454, (-3586.29098229861));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.29098229861));
      double double2 = new Double((-3586.29098229861));
      double double3 = (-722.0714127);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      doubleArray0[1] = (-3586.29098229861);
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(realVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1, realVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      double double0 = 886.7680107324387;
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(2383.5208480415454, (-1674.17), 886.7680107324387, 2383.5208480415454, 886.7680107324387);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(886.7680107324387);
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      double double2 = (-722.0714127);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-1674.17);
      doubleArray0[1] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = arrayRealVector1.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.0).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1, realVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer0.withInitialStepBoundFactor(1677.0);
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double double0 = 886.7680107324387;
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(886.7680107324387);
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      double double2 = (-722.0714127);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-1674.17);
      doubleArray0[1] = 1.0E-10;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = arrayRealVector1.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.0).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = new LevenbergMarquardtOptimizer();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double double0 = (-3586.29098229861);
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-3586.29098229861), 2383.5208480415454, 2383.5208480415454, 2383.5208480415454, (-3586.29098229861));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.29098229861));
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      double double2 = (-722.0714127);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 2383.5208480415454;
      doubleArray0[1] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(realVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1, realVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: unable to perform Q.R decomposition on the 7x7 jacobian matrix
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      double double0 = 886.7680107324387;
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(886.7680107324387);
      double double2 = new Double(886.7680107324387);
      double double3 = (-722.0714127);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      levenbergMarquardtOptimizer0.withParameterRelativeTolerance(7.855941152939879E12);
      double[] doubleArray0 = new double[7];
      doubleArray0[3] = 2383.5208480415454;
      doubleArray0[1] = 886.7680107324387;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(realVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      levenbergMarquardtOptimizer1.withInitialStepBoundFactor((-722.0714127));
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(openMapRealVector0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      double double0 = 886.7680107324387;
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(886.7680107324387);
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      double double2 = (-722.0714127);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 2383.5208480415454;
      doubleArray0[1] = 1.0E-10;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(realVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(24).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1, realVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 7
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      double double0 = 886.7680107324387;
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(886.7680107324387);
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      double double2 = (-722.0714127);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-1674.17);
      doubleArray0[1] = 1.0E-10;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = arrayRealVector1.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.0).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1, realVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer0.withInitialStepBoundFactor(1677.0);
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      double double0 = 886.7680107324387;
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(886.7680107324387);
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      double double2 = (-722.0714127);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 2383.5208480415454;
      doubleArray0[1] = 1.0E-10;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(realVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1, realVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      double double0 = 886.7680107324387;
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(886.7680107324387);
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      double double2 = (-722.0714127);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-1674.17);
      doubleArray0[1] = 1.0E-10;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn(0.0).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn((RealMatrix) null).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1, realVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(2009.3930063904127, 2009.3930063904127, 2009.3930063904127, 2009.3930063904127, 2009.3930063904127);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.5);
      levenbergMarquardtOptimizer1.withParameterRelativeTolerance(10.0);
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 298;
      doubleArray0[1] = (double) 298;
      doubleArray0[2] = 1918.804;
      doubleArray0[3] = 10.0;
      doubleArray0[4] = 298.0;
      doubleArray0[5] = (double) 298;
      doubleArray0[6] = 0.5;
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(doubleArray0, true);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(arrayRealVector0, true);
      ArrayRealVector arrayRealVector2 = new ArrayRealVector((RealVector) arrayRealVector1);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((LeastSquaresProblem.Evaluation) null).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(23).when(leastSquaresProblem0).getObservationSize();
      doReturn(298).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector2).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = (-2144.37925832503);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-2144.37925832503));
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(0);
      RealVector realVector0 = RealVector.unmodifiableRealVector(arrayRealVector0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getConvergenceChecker();
      LeastSquaresProblem leastSquaresProblem1 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(303).when(leastSquaresProblem1).getObservationSize();
      doReturn(0).when(leastSquaresProblem1).getParameterSize();
      doReturn(realVector0).when(leastSquaresProblem1).getStart();
      doReturn((ConvergenceChecker<LeastSquaresProblem.Evaluation>) null, (ConvergenceChecker) null).when(leastSquaresProblem1).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem1).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem1).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter1 = new LeastSquaresAdapter(leastSquaresProblem1);
      leastSquaresAdapter1.getConvergenceChecker();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double double0 = 2.0;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(1.0, 887.172019894818, 1.0, 2.0, (-1673.468469282));
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2.0;
      doubleArray0[1] = 2.0;
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(doubleArray0, true);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(arrayRealVector0, doubleArray0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((-2557)).when(leastSquaresProblem0).getObservationSize();
      doReturn(1962).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      double double0 = 886.7680107324387;
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(886.7680107324387, 886.7680107324387, 2383.5208480415454, (-1674.17), (-1674.17));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(886.7680107324387);
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      double double2 = (-722.0714127);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-1674.17);
      doubleArray0[1] = (-1674.17);
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(realVector0, arrayRealVector0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1, realVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(100.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer0.withRankingThreshold(1.0E-10);
      levenbergMarquardtOptimizer2.withCostRelativeTolerance(1.0E-10);
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(0, 0, 0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((-1)).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(leastSquaresProblem0).getObservationSize();
      doReturn(2).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0, arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance((-427.64232188));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-427.64232188));
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor((-427.64232188));
      levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer0.withOrthoTolerance(100.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = levenbergMarquardtOptimizer0.withRankingThreshold((-427.64232188));
      assertEquals(1.0E-10, levenbergMarquardtOptimizer5.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer5.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer5.getInitialStepBoundFactor(), 0.01);
      assertEquals((-427.64232188), levenbergMarquardtOptimizer5.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer5.getCostRelativeTolerance(), 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer6 = levenbergMarquardtOptimizer4.withParameterRelativeTolerance((-762.3166938157672));
      levenbergMarquardtOptimizer4.getCostRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer7 = levenbergMarquardtOptimizer4.withOrthoTolerance(5397.6138403597);
      assertEquals(5397.6138403597, levenbergMarquardtOptimizer7.getOrthoTolerance(), 0.01);
      
      double double0 = levenbergMarquardtOptimizer3.getInitialStepBoundFactor();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals((-427.64232188), double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      
      double double1 = levenbergMarquardtOptimizer6.getRankingThreshold();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer4.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer6.getOrthoTolerance(), 0.01);
      assertEquals((-762.3166938157672), levenbergMarquardtOptimizer6.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, double1, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer6.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1187.889527545), (-1187.889527545), (-1368.96011303), (-4765.982), (-1368.96011303));
      levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance((-4765.982));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold((-721.972743224));
      assertEquals((-1187.889527545), levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals((-4765.982), levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1187.889527545), levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals((-1368.96011303), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-4765.982), levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals((-721.972743224), levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals((-1368.96011303), double0, 0.01);
      
      levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(10.0);
      assertEquals((-4765.982), levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals((-1368.96011303), levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals((-1368.96011303), levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1187.889527545), levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 3551.8831406252, 908.11199857);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor((-3660.279));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withRankingThreshold(908.11199857);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withParameterRelativeTolerance(908.11199857);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = levenbergMarquardtOptimizer4.withOrthoTolerance(0.0);
      double double0 = levenbergMarquardtOptimizer5.getOrthoTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(908.11199857, levenbergMarquardtOptimizer5.getRankingThreshold(), 0.01);
      assertEquals(908.11199857, levenbergMarquardtOptimizer5.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals((-3660.279), levenbergMarquardtOptimizer5.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(908.11199857, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(3551.8831406252, levenbergMarquardtOptimizer4.getOrthoTolerance(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer5.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.006, (-1361.454301302413), (-1361.454301302413), (-335.9009211), (-335.9009211));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.006);
      levenbergMarquardtOptimizer1.getOrthoTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(0.006);
      double double0 = levenbergMarquardtOptimizer2.getInitialStepBoundFactor();
      assertEquals(0.006, double0, 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withRankingThreshold((-2361.6099));
      assertEquals((-1361.454301302413), levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals((-2361.6099), levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals(0.006, levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals((-335.9009211), levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals((-1361.454301302413), levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      
      double double1 = levenbergMarquardtOptimizer2.getCostRelativeTolerance();
      assertEquals((-1361.454301302413), double1, 0.01);
      assertEquals(0.006, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.006, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-335.9009211), levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals((-1361.454301302413), levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(20.0, 20.0, 1020.57504, (-116.79288012), 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-116.79288012));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withInitialStepBoundFactor(20.0);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals((-116.79288012), levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals((-116.79288012), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals((-116.79288012), levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(1020.57504, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals((-116.79288012), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(91.45);
      double double0 = levenbergMarquardtOptimizer1.getOrthoTolerance();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(91.45, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, Double.NEGATIVE_INFINITY, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(3.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(Double.NEGATIVE_INFINITY);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withRankingThreshold(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer1.withRankingThreshold(6.283185307179586);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = levenbergMarquardtOptimizer4.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer6 = levenbergMarquardtOptimizer5.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer7 = levenbergMarquardtOptimizer6.withCostRelativeTolerance(3.0);
      assertEquals(3.0, levenbergMarquardtOptimizer7.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer7.getInitialStepBoundFactor(), 0.01);
      
      levenbergMarquardtOptimizer1.withOrthoTolerance(Double.NEGATIVE_INFINITY);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer8 = levenbergMarquardtOptimizer5.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer9 = levenbergMarquardtOptimizer5.withCostRelativeTolerance(0.0);
      assertEquals(6.283185307179586, levenbergMarquardtOptimizer9.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer9.getInitialStepBoundFactor(), 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer10 = levenbergMarquardtOptimizer1.withOrthoTolerance(6.283185307179586);
      levenbergMarquardtOptimizer0.getRankingThreshold();
      double double0 = levenbergMarquardtOptimizer2.getRankingThreshold();
      assertEquals(0.0, double0, 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer11 = levenbergMarquardtOptimizer8.withCostRelativeTolerance(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer11.getParameterRelativeTolerance(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer11.getOrthoTolerance(), 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer12 = levenbergMarquardtOptimizer3.withParameterRelativeTolerance(496.7);
      assertEquals(496.7, levenbergMarquardtOptimizer12.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer12.getInitialStepBoundFactor(), 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer13 = levenbergMarquardtOptimizer8.withInitialStepBoundFactor(0.0);
      assertEquals(3.0, levenbergMarquardtOptimizer13.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer13.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer13.getParameterRelativeTolerance(), 0.01);
      assertEquals(6.283185307179586, levenbergMarquardtOptimizer13.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer13.getCostRelativeTolerance(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer5.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer8.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer5.getCostRelativeTolerance(), 0.01);
      
      double double1 = levenbergMarquardtOptimizer10.getInitialStepBoundFactor();
      assertEquals(0.0, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withCostRelativeTolerance(1850.4);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withParameterRelativeTolerance(100.0);
      assertEquals(1850.4, levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance((-2121.4208));
      levenbergMarquardtOptimizer1.withCostRelativeTolerance((-2121.4208));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withCostRelativeTolerance((-2121.4208));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withParameterRelativeTolerance(0.0);
      assertEquals((-2121.4208), levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-2121.4208), levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals((-2121.4208), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(100.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-165.538), (-165.538), (-165.538), (-165.538), (-165.538));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(1710.8580961629);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance((-165.538));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      assertEquals((-165.538), levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals((-165.538), levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals((-165.538), levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals((-165.538), levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      double double1 = levenbergMarquardtOptimizer2.getCostRelativeTolerance();
      assertEquals((-165.538), levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals((-165.538), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-165.538), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-165.538), levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(1710.8580961629, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals((-165.538), levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(double1, double0, 0.01);
      assertEquals((-165.538), double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(6.283185307179586);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(6.283185307179586, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 2180.51, 0.0, (-869.3813), 120.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(120.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withCostRelativeTolerance((-1971.911384363));
      assertEquals((-869.3813), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals(120.0, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1971.911384363), levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(2180.51, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(0.0);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer1.withInitialStepBoundFactor((-274.5649755009479));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = levenbergMarquardtOptimizer4.withParameterRelativeTolerance((-0.5));
      assertEquals(0.0, levenbergMarquardtOptimizer5.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer4.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer5.getRankingThreshold(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer5.getParameterRelativeTolerance(), 0.01);
      assertEquals((-274.5649755009479), levenbergMarquardtOptimizer5.getInitialStepBoundFactor(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer5.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-929.042), (-929.042), (-929.042), 20.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withCostRelativeTolerance(20.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withRankingThreshold((-929.042));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = levenbergMarquardtOptimizer4.withRankingThreshold(20.0);
      assertEquals((-929.042), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals((-929.042), levenbergMarquardtOptimizer4.getRankingThreshold(), 0.01);
      assertEquals((-929.042), levenbergMarquardtOptimizer4.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer5.getInitialStepBoundFactor(), 0.01);
      assertEquals((-929.042), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer4.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer5.getOrthoTolerance(), 0.01);
      assertEquals((-929.042), levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-4262.255144));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withInitialStepBoundFactor((-4262.255144));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withRankingThreshold(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getInitialStepBoundFactor(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer4.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals((-4262.255144), levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer4.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getRankingThreshold(), 0.01);
      assertEquals((-4262.255144), levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(1962.703813);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1962.703813, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(683.32979950482);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(683.32979950482);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withInitialStepBoundFactor((-1517.0));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withInitialStepBoundFactor((-1517.0));
      assertEquals(683.32979950482, levenbergMarquardtOptimizer4.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer4.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer4.getRankingThreshold(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals((-1517.0), levenbergMarquardtOptimizer4.getInitialStepBoundFactor(), 0.01);
      assertEquals(683.32979950482, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals(683.32979950482, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals(683.32979950482, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(1.0E-10, 0.0, 1.0E-10, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(4369.4208663282);
      levenbergMarquardtOptimizer0.withParameterRelativeTolerance(1.0E-10);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance((-2406.333115618));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withRankingThreshold(0.001);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals((-2406.333115618), levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals(4369.4208663282, levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      
      double double0 = levenbergMarquardtOptimizer2.getCostRelativeTolerance();
      assertEquals(0.0, double0, 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer1.withOrthoTolerance(4369.4208663282);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = levenbergMarquardtOptimizer4.withRankingThreshold(2740.305729481232);
      assertEquals(0.0, levenbergMarquardtOptimizer5.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getRankingThreshold(), 0.01);
      assertEquals(2740.305729481232, levenbergMarquardtOptimizer5.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer4.getParameterRelativeTolerance(), 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer6 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.25);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer7 = levenbergMarquardtOptimizer6.withParameterRelativeTolerance(0.25);
      assertEquals(0.25, levenbergMarquardtOptimizer6.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer6.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer7.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(3642.5926634756593, 3642.5926634756593, 3642.5926634756593, 3642.5926634756593, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(3642.5926634756593, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(3642.5926634756593, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(3642.5926634756593, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(3642.5926634756593, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(3642.5926634756593, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(3642.5926634756593, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(3642.5926634756593, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.5, (-965.4927));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.5);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withCostRelativeTolerance(0.5);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = levenbergMarquardtOptimizer4.withInitialStepBoundFactor((-965.4927));
      double double0 = levenbergMarquardtOptimizer5.getParameterRelativeTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer5.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals((-965.4927), levenbergMarquardtOptimizer5.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer5.getOrthoTolerance(), 0.01);
      assertEquals(0.5, levenbergMarquardtOptimizer5.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(1.0E-12, 1.0E-12, (-4264.861), (-1.0), (-4264.861));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(1.0E-12);
      assertEquals((-4264.861), levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-12, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-12, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-12, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals((-4264.861), levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(2146.441846349, 2146.441846349, 915.8231, 915.8231, 915.8231);
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(915.8231, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(915.8231, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(915.8231, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(2146.441846349, double0, 0.01);
      assertEquals(2146.441846349, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(3819.658503, 1.0E-4, 1450.1414673, 3819.658503, 3819.658503);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(783.0819);
      double double0 = levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      assertEquals(3819.658503, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(783.0819, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(3819.658503, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(3819.658503, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-4, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1450.1414673, double0, 0.01);
      assertEquals(1450.1414673, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(3819.658503, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-4, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.0);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(2.0);
      double double0 = levenbergMarquardtOptimizer1.getCostRelativeTolerance();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = 0.0;
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn((RealVector) null).when(leastSquaresProblem0).getStart();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn((RealVector) null).when(leastSquaresProblem0).getStart();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(2.0, 2.0, 2.0, 2117.04821, 2117.04821);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(826.44);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer2.optimize((LeastSquaresProblem) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      double double0 = (-3586.2909823);
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(2383.5208480415454);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-3586.2909823));
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      double double2 = 572.5;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(realVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0, (ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      leastSquaresAdapter0.getConvergenceChecker();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 5
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      double double0 = (-3586.29098229861);
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-3586.29098229861), 2383.5208480415454, 2383.5208480415454, 2383.5208480415454, (-3586.29098229861));
      levenbergMarquardtOptimizer0.getOrthoTolerance();
      double double2 = (-722.0714127);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-722.0714127);
      doubleArray0[1] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(openMapRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-3586.29098229861)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector0).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0, openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer0.withOrthoTolerance((-722.0714127));
      leastSquaresAdapter0.getStart();
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: unable to perform Q.R decomposition on the 7x7 jacobian matrix
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(10.0, 3.141592653589793, 10.0, 10.0, (-2110.470984887769));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = new LevenbergMarquardtOptimizer();
      Double double0 = new Double((-2110.470984887769));
      levenbergMarquardtOptimizer1.getRankingThreshold();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-2110.470984887769));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer0.withRankingThreshold(2.2250738585072014E-308);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(121.18931172014875);
      double double1 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = levenbergMarquardtOptimizer2.withRankingThreshold(904.2128505567135);
      levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      double double2 = levenbergMarquardtOptimizer2.getParameterRelativeTolerance();
      assertEquals(double2, double1, 0.01);
      
      levenbergMarquardtOptimizer2.withCostRelativeTolerance((double) double0);
      levenbergMarquardtOptimizer0.withInitialStepBoundFactor((-1277.0976270708993));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer6 = levenbergMarquardtOptimizer3.withCostRelativeTolerance(904.2128505567135);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer6.getRankingThreshold(), 0.01);
      
      levenbergMarquardtOptimizer1.withRankingThreshold(10.0);
      levenbergMarquardtOptimizer5.withRankingThreshold(10.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer7 = levenbergMarquardtOptimizer5.withInitialStepBoundFactor(2.2250738585072014E-308);
      assertEquals(10.0, levenbergMarquardtOptimizer7.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2110.470984887769), levenbergMarquardtOptimizer7.getCostRelativeTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer7.getOrthoTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer5.getInitialStepBoundFactor(), 0.01);
      
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer8 = levenbergMarquardtOptimizer4.withInitialStepBoundFactor((-4200.01332013586));
      assertEquals((-4200.01332013586), levenbergMarquardtOptimizer8.getInitialStepBoundFactor(), 0.01);
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer4.getCostRelativeTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer8.getOrthoTolerance(), 0.01);
      assertEquals(121.18931172014875, levenbergMarquardtOptimizer4.getParameterRelativeTolerance(), 0.01);
      assertEquals((-2110.470984887769), levenbergMarquardtOptimizer8.getRankingThreshold(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer4.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.2909823));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(5, 5);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(179, 510.8861987648);
      ArrayRealVector arrayRealVector1 = new ArrayRealVector();
      ArrayRealVector arrayRealVector2 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      doReturn(0, 0).when(incrementor0).getCount();
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(arrayRealVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector2).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5, 0).when(leastSquaresProblem0).getObservationSize();
      doReturn(179, 0).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector2, (RealVector) null).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer0.withOrthoTolerance((-966.148811));
      levenbergMarquardtOptimizer2.withParameterRelativeTolerance((-76.2545));
      levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(2383.5208480415454);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer5 = levenbergMarquardtOptimizer4.withInitialStepBoundFactor((-3586.2909823));
      levenbergMarquardtOptimizer5.withOrthoTolerance((-3586.2909823));
      levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      levenbergMarquardtOptimizer1.withCostRelativeTolerance((-3586.2909823));
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer3.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      double double0 = (-3586.0063336085295);
      double double1 = 2383.5208480415454;
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-3586.0063336085295), 2383.5208480415454, 2383.5208480415454, 2383.5208480415454, (-3586.0063336085295));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3586.0063336085295));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = new LevenbergMarquardtOptimizer();
      levenbergMarquardtOptimizer2.withRankingThreshold(510.8861987648);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 2383.5208480415454;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, 572.5);
      RealVector realVector0 = RealVector.unmodifiableRealVector(openMapRealVector0);
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      ArrayRealVector arrayRealVector1 = new ArrayRealVector(179);
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      RealMatrix realMatrix0 = openMapRealVector0.outerProduct(realVector0);
      LeastSquaresProblem.Evaluation leastSquaresProblem_Evaluation0 = mock(LeastSquaresProblem.Evaluation.class, new ViolatedAssumptionAnswer());
      doReturn((-966.148811)).when(leastSquaresProblem_Evaluation0).getCost();
      doReturn(realMatrix0).when(leastSquaresProblem_Evaluation0).getJacobian();
      doReturn(arrayRealVector1).when(leastSquaresProblem_Evaluation0).getResiduals();
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(leastSquaresProblem_Evaluation0).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(5).when(leastSquaresProblem0).getObservationSize();
      doReturn(179).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector1).when(leastSquaresProblem0).getStart();
      doReturn(convergenceChecker0).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer1.optimize(leastSquaresAdapter0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      ConvergenceChecker<LeastSquaresProblem.Evaluation> convergenceChecker0 = (ConvergenceChecker<LeastSquaresProblem.Evaluation>) mock(ConvergenceChecker.class, new ViolatedAssumptionAnswer());
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
  }
}
