/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 00:32:16 GMT 2019
 */

package org.apache.commons.math3.fitting.leastsquares;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresAdapter;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresProblem;
import org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer;
import org.apache.commons.math3.linear.ArrayRealVector;
import org.apache.commons.math3.linear.OpenMapRealVector;
import org.apache.commons.math3.optim.ConvergenceChecker;
import org.apache.commons.math3.util.Incrementor;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class LevenbergMarquardtOptimizer_ESTest extends LevenbergMarquardtOptimizer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(2142.7814895, 1021.550575419251, 1021.550575419251, 1021.550575419251, 0.0);
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(0, 0);
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((LeastSquaresProblem.Evaluation) null).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(10.0, 10.0, 10.0, 10.0, 0.0);
      double[] doubleArray0 = new double[6];
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(doubleArray0, doubleArray0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(leastSquaresProblem0).getObservationSize();
      doReturn(0).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(684);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(684).when(leastSquaresProblem0).getObservationSize();
      doReturn(2420).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1633.84054676), 352.84397884398, 352.84397884398, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(352.84397884398, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(352.84397884398, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-1633.84054676), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(352.84397884398, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(352.84397884398, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1633.84054676), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 3.0, 0.0, 0.0, 3.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(1303.4536790147324);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1303.4536790147324, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.1, 0.0, 0.1, 0.1, 0.1);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(1506.9333956518826);
      assertEquals(0.1, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.1, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.1, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.1, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.1, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.1, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1506.9333956518826, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-0.5), (-0.5), (-5536.10970499432), (-5536.10970499432), (-3497.9));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold((-5536.10970499432));
      assertEquals((-0.5), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-3497.9), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-0.5), (-0.5), (-5536.10970499432), (-5536.10970499432), (-3497.9));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance((-5536.10970499432));
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-3497.9), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-3497.9), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance((-452.0888460260434));
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-452.0888460260434), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(0.0);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(139);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(139.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1837.50718785853), (-1837.50718785853), (-1837.50718785853), (-1837.50718785853), (-1837.50718785853));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(3.0);
      assertEquals((-1837.50718785853), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-1837.50718785853), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1837.50718785853), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1837.50718785853), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-1837.50718785853), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-1837.50718785853), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1837.50718785853), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1837.50718785853), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(10.0, 10.0, 10.0, 10.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(20.0);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(3.141592653589793, 3.141592653589793, 3.141592653589793, 3.141592653589793, 3.141592653589793);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-577.09103));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance((-577.09103));
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals((-577.09103), levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals((-577.09103), levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-577.09103), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withInitialStepBoundFactor(575.47625);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(575.47625, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-0.5), (-0.5), (-5536.10970499432), (-5536.10970499432), (-3497.9));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor((-348.77696208));
      assertEquals((-3497.9), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-3497.9), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-348.77696208), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(10.0, 10.0, 10.0, 10.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withInitialStepBoundFactor((-976.74));
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals((-976.74), levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, (-2902.008181), 0.0, (-476.0933027078577), (-476.0933027078577));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(543.65);
      assertEquals((-476.0933027078577), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-476.0933027078577), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(543.65, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-476.0933027078577), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-476.0933027078577), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-0.5), (-0.5), (-5536.10970499432), (-5536.10970499432), (-3497.9));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-3497.9));
      assertEquals((-0.5), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-3497.9), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-3497.9), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-3497.9), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1633.84054676), 352.84397884398, 352.84397884398, 0.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(2638.6);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1633.84054676), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(2638.6, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(352.84397884398, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1633.84054676), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(352.84397884398, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(2142.7814895, 1021.550575419251, 1021.550575419251, 1021.550575419251, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals(2142.7814895, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1021.550575419251, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1021.550575419251, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1021.550575419251, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, (-2902.008181), 0.0, (-476.0933027078577), (-476.0933027078577));
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals((-476.0933027078577), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-476.0933027078577), double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-2902.008181), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 3.0, 0.0, 0.0, 3.0);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(437.8057258981618, 0.0, (-75.139), 437.8057258981618, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(437.8057258981618, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-75.139), double0, 0.01);
      assertEquals(437.8057258981618, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 530.2208201246506, 3903.4731, 0.0, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(530.2208201246506, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(3903.4731, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(20.0, 20.0, 20.0, 20.0, (-1429.5856555565415));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance((-1429.5856555565415));
      double double0 = levenbergMarquardtOptimizer1.getOrthoTolerance();
      assertEquals(20.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1429.5856555565415), double0, 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1429.5856555565415), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1429.5856555565415), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, 0.0);
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-0.5), (-0.5), (-5536.10970499432), (-5536.10970499432), (-3497.9));
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals((-3497.9), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-0.5), double0, 0.01);
      assertEquals((-5536.10970499432), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(10.0, 10.0, 10.0, 10.0, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      double double0 = levenbergMarquardtOptimizer1.getCostRelativeTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(3.141592653589793, 3.141592653589793, 3.141592653589793, 3.141592653589793, 3.141592653589793);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-577.09103));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(3.141592653589793);
      double double0 = levenbergMarquardtOptimizer2.getCostRelativeTolerance();
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals((-577.09103), double0, 0.01);
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(3.141592653589793, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2360.759), (-2360.759), 3.141592653589793, 3.141592653589793, (-2360.759));
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(1, 6013);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(leastSquaresProblem0).getObservationSize();
      doReturn(3546).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1633.84054676), 352.84397884398, 352.84397884398, 0.0, 0.0);
      double[] doubleArray0 = new double[2];
      ArrayRealVector arrayRealVector0 = new ArrayRealVector(doubleArray0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn((-1174)).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withInitialStepBoundFactor(575.47625);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(575.47625, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
  }
}
