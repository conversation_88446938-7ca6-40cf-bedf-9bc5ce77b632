/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 00:34:23 GMT 2019
 */

package org.apache.commons.math3.fitting.leastsquares;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresAdapter;
import org.apache.commons.math3.fitting.leastsquares.LeastSquaresProblem;
import org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer;
import org.apache.commons.math3.linear.ArrayRealVector;
import org.apache.commons.math3.linear.OpenMapRealVector;
import org.apache.commons.math3.optim.ConvergenceChecker;
import org.apache.commons.math3.util.Incrementor;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class LevenbergMarquardtOptimizer_ESTest extends LevenbergMarquardtOptimizer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double[] doubleArray0 = new double[3];
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0);
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn(171).when(leastSquaresProblem0).getObservationSize();
      doReturn(2).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 1156.35552084447, (-1299.1781886), 0.0, (-1299.1781886));
      ArrayRealVector arrayRealVector0 = new ArrayRealVector();
      Incrementor incrementor0 = mock(Incrementor.class, new ViolatedAssumptionAnswer());
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((LeastSquaresProblem.Evaluation) null).when(leastSquaresProblem0).evaluate(any(org.apache.commons.math3.linear.RealVector.class));
      doReturn(0).when(leastSquaresProblem0).getObservationSize();
      doReturn(69).when(leastSquaresProblem0).getParameterSize();
      doReturn(arrayRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker<LeastSquaresProblem.Evaluation>) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn(incrementor0).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn(incrementor0).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      Double[] doubleArray0 = new Double[2];
      Double double0 = new Double(1643);
      doubleArray0[0] = double0;
      doubleArray0[1] = double0;
      OpenMapRealVector openMapRealVector0 = new OpenMapRealVector(doubleArray0, (-1277.36423264476));
      LeastSquaresProblem leastSquaresProblem0 = mock(LeastSquaresProblem.class, new ViolatedAssumptionAnswer());
      doReturn((-2010)).when(leastSquaresProblem0).getObservationSize();
      doReturn(1643).when(leastSquaresProblem0).getParameterSize();
      doReturn(openMapRealVector0).when(leastSquaresProblem0).getStart();
      doReturn((ConvergenceChecker) null).when(leastSquaresProblem0).getConvergenceChecker();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getEvaluationCounter();
      doReturn((Incrementor) null).when(leastSquaresProblem0).getIterationCounter();
      LeastSquaresAdapter leastSquaresAdapter0 = new LeastSquaresAdapter(leastSquaresProblem0);
      // Undeclared exception!
      try { 
        levenbergMarquardtOptimizer0.optimize(leastSquaresAdapter0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.fitting.leastsquares.LevenbergMarquardtOptimizer", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance((-788.0679));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold((-788.0679));
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals((-788.0679), levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals((-788.0679), levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals((-788.0679), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold(1608.2887124616373);
      assertEquals(1608.2887124616373, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withRankingThreshold(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer3.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold(0.0);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-672.0), Double.NEGATIVE_INFINITY, 2457.979, (-672.0), (-672.0));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(92.8612234462);
      assertEquals((-672.0), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(92.8612234462, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-672.0), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-672.0), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2457.979, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-672.0), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(2457.979, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withRankingThreshold(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withParameterRelativeTolerance(0.0);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, (-1815.4692049391526));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance((-0.5));
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-0.5), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1815.4692049391526), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-1815.4692049391526), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-126.40586), (-126.40586), (-126.40586), (-126.40586), (-126.40586));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(1831.0);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1831.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-126.40586), (-126.40586), (-126.40586), (-126.40586), (-126.40586));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance((-501.1530873));
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-501.1530873), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(1479.064171);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1479.064171, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withInitialStepBoundFactor((-1.0));
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withInitialStepBoundFactor(3.0);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(3.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-672.0), Double.NEGATIVE_INFINITY, 2457.979, (-672.0), (-672.0));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor((-1701.3961141705));
      assertEquals((-672.0), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-672.0), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-1701.3961141705), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2457.979, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-672.0), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-672.0), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(2457.979, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 4361.759599399174, (-1147.4745746360877), 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(4361.759599399174, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1147.4745746360877), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-1147.4745746360877), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(4361.759599399174, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-126.40586), (-126.40586), (-126.40586), (-126.40586), (-126.40586));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withInitialStepBoundFactor(1.0E-12);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-12, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals((-126.40586), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withRankingThreshold(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer3 = levenbergMarquardtOptimizer2.withCostRelativeTolerance(0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer4 = levenbergMarquardtOptimizer3.withOrthoTolerance(1479.064171);
      assertEquals(100.0, levenbergMarquardtOptimizer4.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer4.getCostRelativeTolerance(), 0.01);
      assertEquals(1479.064171, levenbergMarquardtOptimizer4.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer3.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance((-0.5));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withCostRelativeTolerance((-788.0679));
      assertEquals((-0.5), levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals((-788.0679), levenbergMarquardtOptimizer2.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1563.2117), 0.0, 2194.79042055, 1841.2, 0.0);
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(2194.79042055);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2194.79042055, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1841.2, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-1563.2117), levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1563.2117), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(2194.79042055, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(2194.79042055, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(1841.2, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-1471.64029801), (-1471.64029801), (-1471.64029801), (-1471.64029801), 0.0);
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals((-1471.64029801), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-1471.64029801), levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals((-1471.64029801), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1471.64029801), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, (-1815.4692049391526));
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1815.4692049391526), double0, 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withParameterRelativeTolerance(0.0);
      double double0 = levenbergMarquardtOptimizer1.getParameterRelativeTolerance();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(1166.0, (-1139.6036603395), (-1.0), (-1.0), 1.0);
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals((-1.0), double0, 0.01);
      assertEquals(1.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals((-1139.6036603395), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1166.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(118.0, 118.0, 0.0, 0.0, (-1.0));
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(118.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(118.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1.0), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(20.0, 20.0, 0.0, (-918.386531618682), 20.0);
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-918.386531618682), double0, 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(20.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 0.0, 0.0, 0.0, (-1815.4692049391526));
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(0.0, double0, 0.01);
      assertEquals((-1815.4692049391526), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer((-2902.8), (-2902.8), 10.0, 10.0, 1307.0027);
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(1307.0027, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(10.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals((-2902.8), double0, 0.01);
      assertEquals((-2902.8), levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      double double0 = levenbergMarquardtOptimizer1.getCostRelativeTolerance();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance((-919.541));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer2 = levenbergMarquardtOptimizer1.withOrthoTolerance(1594.10833118);
      double double0 = levenbergMarquardtOptimizer2.getCostRelativeTolerance();
      assertEquals(1594.10833118, levenbergMarquardtOptimizer2.getOrthoTolerance(), 0.01);
      assertEquals((-919.541), double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer2.getParameterRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer2.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer2.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getCostRelativeTolerance();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getRankingThreshold();
      assertEquals(2.2250738585072014E-308, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getInitialStepBoundFactor();
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals(100.0, double0, 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getOrthoTolerance();
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 1156.35552084447, (-1299.1781886), 0.0, (-1299.1781886));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withCostRelativeTolerance(0.0);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1299.1781886), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(1156.35552084447, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-1299.1781886), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals((-1299.1781886), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
      assertEquals((-1299.1781886), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer();
      double double0 = levenbergMarquardtOptimizer0.getParameterRelativeTolerance();
      assertEquals(100.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals(2.2250738585072014E-308, levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(1.0E-10, double0, 0.01);
      assertEquals(1.0E-10, levenbergMarquardtOptimizer0.getOrthoTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer0 = new LevenbergMarquardtOptimizer(0.0, 1156.35552084447, (-1299.1781886), 0.0, (-1299.1781886));
      LevenbergMarquardtOptimizer levenbergMarquardtOptimizer1 = levenbergMarquardtOptimizer0.withOrthoTolerance(0.001);
      assertEquals((-1299.1781886), levenbergMarquardtOptimizer1.getParameterRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer1.getInitialStepBoundFactor(), 0.01);
      assertEquals(1156.35552084447, levenbergMarquardtOptimizer1.getCostRelativeTolerance(), 0.01);
      assertEquals(0.0, levenbergMarquardtOptimizer0.getInitialStepBoundFactor(), 0.01);
      assertEquals((-1299.1781886), levenbergMarquardtOptimizer1.getRankingThreshold(), 0.01);
      assertEquals(1156.35552084447, levenbergMarquardtOptimizer0.getCostRelativeTolerance(), 0.01);
      assertEquals((-1299.1781886), levenbergMarquardtOptimizer0.getRankingThreshold(), 0.01);
      assertEquals(0.001, levenbergMarquardtOptimizer1.getOrthoTolerance(), 0.01);
      assertEquals((-1299.1781886), levenbergMarquardtOptimizer0.getParameterRelativeTolerance(), 0.01);
  }
}
