/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 19:31:05 GMT 2019
 */

package org.apache.commons.math3.geometry.euclidean.threed;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math3.geometry.euclidean.threed.Euclidean3D;
import org.apache.commons.math3.geometry.euclidean.threed.Line;
import org.apache.commons.math3.geometry.euclidean.threed.Plane;
import org.apache.commons.math3.geometry.euclidean.threed.PolyhedronsSet;
import org.apache.commons.math3.geometry.euclidean.threed.Rotation;
import org.apache.commons.math3.geometry.euclidean.threed.RotationOrder;
import org.apache.commons.math3.geometry.euclidean.threed.SubPlane;
import org.apache.commons.math3.geometry.euclidean.threed.Vector3D;
import org.apache.commons.math3.geometry.euclidean.twod.Euclidean2D;
import org.apache.commons.math3.geometry.euclidean.twod.PolygonsSet;
import org.apache.commons.math3.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math3.geometry.partitioning.AbstractSubHyperplane;
import org.apache.commons.math3.geometry.partitioning.BSPTree;
import org.apache.commons.math3.geometry.partitioning.Region;
import org.apache.commons.math3.geometry.partitioning.SubHyperplane;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class PolyhedronsSet_ESTest extends PolyhedronsSet_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-846.1710883136222), (-846.1710883136222), (-846.1710883136222), (-846.1710883136222), (-846.1710883136222), (-846.1710883136222));
      polyhedronsSet0.computeGeometricalProperties();
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1850.3194839145697), (-0.19), 0.0, (-1850.3194839145697), 0.0, 552.3370340798999, (-1850.3194839145697));
      assertEquals((-1850.3194839145697), polyhedronsSet0.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 0.0, 0.0, 117.361211661, (-719.1940817068312), 0.0);
      assertEquals(0.0, polyhedronsSet0.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1303.3184820682), 1.5707963267948966, (-1303.3184820682), 3.0, (-1303.3184820682), (-1303.3184820682), 0.0);
      Vector3D vector3D0 = new Vector3D((-2280.511), (-2552.8431326));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertNotSame(polyhedronsSet1, polyhedronsSet0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
      Vector3D vector3D0 = Vector3D.MINUS_I;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertNotSame(polyhedronsSet1, polyhedronsSet0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 0.0, 0.0, 1572.0, 0.0);
      RotationOrder rotationOrder0 = RotationOrder.XZY;
      Vector3D vector3D0 = rotationOrder0.getA2();
      Rotation rotation0 = Rotation.IDENTITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertFalse(polyhedronsSet1.isFull());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, (Rotation) null);
      assertNotSame(polyhedronsSet0, polyhedronsSet1);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      RotationOrder rotationOrder0 = RotationOrder.YXY;
      Vector3D vector3D0 = rotationOrder0.getA2();
      Plane plane0 = new Plane(vector3D0, vector3D0, 5.0);
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertNotSame(polyhedronsSet1, polyhedronsSet0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 0.0, 0.0, 1572.0, 0.0);
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertEquals(1.0E-10, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), (-1778.534174656), 700.2);
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, 0.0);
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      assertNotSame(polyhedronsSet0, polyhedronsSet1);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1279.38452218), 443.0450567077, 590.5035, 1677.8, 0.0, 590.5035);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      Vector3D vector3D0 = Vector3D.MINUS_J;
      // Undeclared exception!
      try { 
        polyhedronsSet1.translate(vector3D0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      RotationOrder rotationOrder0 = RotationOrder.YXY;
      Vector3D vector3D0 = rotationOrder0.getA2();
      Plane plane0 = new Plane(vector3D0, vector3D0, 5.0);
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      bSPTree0.insertCut(plane0);
      // Undeclared exception!
      try { 
        polyhedronsSet0.translate(vector3D0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.Boolean cannot be cast to org.apache.commons.math3.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), (-1779.1779397682367), (-3264.692));
      Vector3D vector3D0 = new Vector3D((-3823.762), (-3264.692));
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D0, (Rotation) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.threed.Plane", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.NaN;
      Rotation rotation0 = Rotation.IDENTITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, 2146.4344);
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      Vector2D[] vector2DArray0 = new Vector2D[7];
      Vector2D vector2D0 = Vector2D.ZERO;
      vector2DArray0[0] = vector2D0;
      vector2DArray0[1] = vector2DArray0[0];
      vector2DArray0[2] = vector2DArray0[1];
      vector2DArray0[3] = vector2DArray0[0];
      vector2DArray0[4] = vector2DArray0[0];
      vector2DArray0[5] = vector2DArray0[1];
      vector2DArray0[6] = vector2DArray0[3];
      PolygonsSet polygonsSet0 = new PolygonsSet(1.0, vector2DArray0);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, plane0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree1);
      // Undeclared exception!
      try { 
        polyhedronsSet1.rotate(vector3D0, rotation0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math3.geometry.euclidean.threed.Plane cannot be cast to org.apache.commons.math3.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, 2.2860509143963117E-8);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      Line line0 = plane0.intersection(plane0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>(subPlane0);
      BSPTree<Euclidean3D> bSPTree1 = bSPTree0.split(subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree1, 14.101419448852539);
      // Undeclared exception!
      try { 
        polyhedronsSet0.firstIntersection(vector3D0, line0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math3.geometry.euclidean.threed.SubPlane cannot be cast to java.lang.Boolean
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion$BoundaryBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, 2.2860509143963117E-8);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      Region<Euclidean2D> region0 = subPlane0.getRemainingRegion();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      SubPlane subPlane1 = new SubPlane(plane0, region0);
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane1);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0);
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.twod.PolygonsSet", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null, 2396.793691);
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(2146.4344, 314.36559998, 2146.4344, 2146.4344, (-1.0), 1.0, 1.0);
      Vector3D vector3D0 = Vector3D.NaN;
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(false);
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, 314.36559998);
      bSPTree0.insertCut(plane0);
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet((Collection<SubHyperplane<Euclidean3D>>) null, Double.NaN);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, (-1.4043521031201713));
      PolygonsSet polygonsSet0 = new PolygonsSet((-1.4043521031201713), (-1.4043521031201713), (-1.4043521031201713), (-1.4043521031201713), (-1.4043521031201713));
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(linkedList0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.twod.PolygonsSet", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet((Collection<SubHyperplane<Euclidean3D>>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet((-1043.5146720628288), (-1043.5146720628288), 3255.501044344, (-3225.54), 1.0E-10, 547.0, Double.NaN);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), (-1778.534174656), (-3264.692));
      Vector3D vector3D0 = new Vector3D((-3823.762), (-3264.692));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), (-1778.534174656), (-3264.692));
      Vector3D vector3D0 = new Vector3D((-3823.762), (-3264.692));
      Vector3D vector3D1 = new Vector3D((-1778.534174656), vector3D0, 4.71238898038469, vector3D0);
      Line line0 = new Line(vector3D1, vector3D0, (-3264.692));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNotNull(subHyperplane0);
      
      Rotation rotation0 = new Rotation((-1191.12527), (-3264.692), (-545.8663537436365), (-5942.0), false);
      polyhedronsSet0.rotate(vector3D1, rotation0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, 2.2860509143963117E-8);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      AbstractSubHyperplane<Euclidean3D, Euclidean2D> abstractSubHyperplane0 = subPlane0.copySelf();
      linkedList0.add((SubHyperplane<Euclidean3D>) abstractSubHyperplane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0);
      Line line0 = plane0.intersection(plane0);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertNull(subHyperplane0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null, (-3473.13012));
      Vector3D vector3D0 = Vector3D.MINUS_K;
      // Undeclared exception!
      try { 
        polyhedronsSet0.firstIntersection(vector3D0, (Line) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-2390.88287211935));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      assertEquals((-2390.88287211935), polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), 700.2, (-3264.692));
      Vector3D vector3D0 = Vector3D.PLUS_I;
      Vector3D vector3D1 = new Vector3D(700.2, vector3D0, (-3264.692), vector3D0);
      Line line0 = new Line(vector3D1, vector3D0, (-3264.692));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNotNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), 700.2, (-3264.692));
      Vector3D vector3D0 = Vector3D.PLUS_I;
      Vector3D vector3D1 = new Vector3D((-3264.692), 1907.1588625866682, 1907.1588625866682);
      Line line0 = new Line(vector3D1, vector3D0, (-3264.692));
      Rotation rotation0 = new Rotation(vector3D0, (-3264.692));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet1.firstIntersection(vector3D1, line0);
      assertFalse(subHyperplane0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), 700.2, (-3264.692));
      Vector3D vector3D0 = Vector3D.PLUS_I;
      Vector3D vector3D1 = new Vector3D((-3264.692), 1907.1588625866682, 1907.1588625866682);
      Line line0 = new Line(vector3D1, vector3D0, (-3264.692));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNotNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), 700.2, (-3264.692));
      // Undeclared exception!
      polyhedronsSet0.computeGeometricalProperties();
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(3701.55, 3701.55, 3701.55, 3701.55, 3701.55, 7.766415650650269, (-60.64096560400306));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1.0), 2146.4344, 2146.4344, (-1.0), (-1.0), 2146.4344);
      assertEquals(1.0E-10, polyhedronsSet0.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet();
      assertEquals(1.0E-10, polyhedronsSet0.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(2146.4344, 314.36559998, 2146.4344, 2146.4344, (-1.0), 1.0, 1.0);
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(false);
      PolyhedronsSet polyhedronsSet1 = new PolyhedronsSet(bSPTree0);
      assertEquals(1.0E-10, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(Double.NaN, 2491.765, Double.NaN, 2491.765, 2.29E-8, 2491.765);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), (-1778.534174656), 700.2);
      Vector3D vector3D0 = new Vector3D((-3264.692), (-3264.692));
      Vector3D vector3D1 = new Vector3D((-1778.534174656), vector3D0, (-3264.692), vector3D0);
      Line line0 = new Line(vector3D1, vector3D0, (-3264.692));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNotNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), (-1778.534174656), 700.2);
      Vector3D vector3D0 = new Vector3D((-3264.692), (-3264.692));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3264.692), 700.2, (-3264.692), 1907.1588625866682, (-5942.0), (-1778.534174656), 700.2);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isEmpty());
  }
}
