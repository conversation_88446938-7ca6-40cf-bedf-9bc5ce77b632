/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 19:33:11 GMT 2019
 */

package org.apache.commons.math3.geometry.euclidean.threed;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math3.geometry.euclidean.threed.Euclidean3D;
import org.apache.commons.math3.geometry.euclidean.threed.Line;
import org.apache.commons.math3.geometry.euclidean.threed.Plane;
import org.apache.commons.math3.geometry.euclidean.threed.PolyhedronsSet;
import org.apache.commons.math3.geometry.euclidean.threed.Rotation;
import org.apache.commons.math3.geometry.euclidean.threed.RotationOrder;
import org.apache.commons.math3.geometry.euclidean.threed.SubPlane;
import org.apache.commons.math3.geometry.euclidean.threed.Vector3D;
import org.apache.commons.math3.geometry.euclidean.twod.PolygonsSet;
import org.apache.commons.math3.geometry.partitioning.BSPTree;
import org.apache.commons.math3.geometry.partitioning.SubHyperplane;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class PolyhedronsSet_ESTest extends PolyhedronsSet_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-0.19), 0.0, (-3382.2089), 0.0, (-0.19), 0.0);
      RotationOrder rotationOrder0 = RotationOrder.XZX;
      Vector3D vector3D0 = rotationOrder0.getA2();
      Rotation rotation0 = new Rotation(rotationOrder0, 1.0, 1.0, 1.0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-32.999), (-32.999), (-32.999), 1.0E-10, (-32.999), 0.0, (-32.999));
      Vector3D vector3D0 = Vector3D.PLUS_K;
      Vector3D vector3D1 = Vector3D.ZERO;
      Line line0 = new Line(vector3D0, vector3D1, 1.0E-10);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNotNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.PLUS_J;
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Plane plane0 = new Plane(vector3D0, vector3D0, (-2709.6429002613922));
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 0.0, (-1848.0710871185), 0.0, (-1848.0710871185), (-1848.0710871185));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      linkedList0.add((SubHyperplane<Euclidean3D>) null);
      linkedList0.remove();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertTrue(polyhedronsSet1.isFull());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, (-256.17924), (-256.17924), 0.0, Double.NEGATIVE_INFINITY, Double.NEGATIVE_INFINITY, 0.0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertEquals(0.0, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-0.19), (-0.19), (-0.19), (-0.19), 0.0, (-0.19), (-0.19));
      RotationOrder rotationOrder0 = RotationOrder.XZX;
      Vector3D vector3D0 = rotationOrder0.getA2();
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertNotSame(polyhedronsSet0, polyhedronsSet1);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, (-256.17924), (-256.17924), 0.0, Double.NEGATIVE_INFINITY, Double.NEGATIVE_INFINITY, 0.0);
      RotationOrder rotationOrder0 = RotationOrder.ZXZ;
      Rotation rotation0 = new Rotation(rotationOrder0, Double.NEGATIVE_INFINITY, Double.NEGATIVE_INFINITY, Double.NEGATIVE_INFINITY);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertTrue(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      RotationOrder rotationOrder0 = RotationOrder.ZXZ;
      Rotation rotation0 = new Rotation(rotationOrder0, Double.NEGATIVE_INFINITY, Double.NEGATIVE_INFINITY, Double.NEGATIVE_INFINITY);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1234.468421), (-1.0), 0.0, 0.0, (-2443.72), (-1234.468421));
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertFalse(polyhedronsSet1.equals((Object)polyhedronsSet0));
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 3.0);
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertTrue(polyhedronsSet1.isFull());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertNotSame(polyhedronsSet0, polyhedronsSet1);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null);
      RotationOrder rotationOrder0 = RotationOrder.XYZ;
      Vector3D vector3D0 = rotationOrder0.getA1();
      // Undeclared exception!
      try { 
        polyhedronsSet0.translate(vector3D0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Vector3D vector3D0 = new Vector3D((-2642.6), (-2642.6), (-2642.6));
      Plane plane0 = new Plane(vector3D0, vector3D0, (-2642.6));
      SubPlane subPlane0 = plane0.wholeHyperplane();
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>(subPlane0);
      bSPTree0.insertCut(plane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree0);
      // Undeclared exception!
      try { 
        polyhedronsSet0.translate(vector3D0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math3.geometry.euclidean.threed.SubPlane cannot be cast to org.apache.commons.math3.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null, (-1897.3711));
      Vector3D vector3D0 = Vector3D.NaN;
      Rotation rotation0 = Rotation.IDENTITY;
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D0, rotation0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Vector3D vector3D0 = new Vector3D(0.0, (-2642.6), Double.POSITIVE_INFINITY);
      Plane plane0 = new Plane(vector3D0, vector3D0, 0.0);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>(subPlane0);
      bSPTree0.insertCut(plane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree0);
      Rotation rotation0 = new Rotation(vector3D0, vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D0, rotation0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math3.geometry.euclidean.threed.SubPlane cannot be cast to org.apache.commons.math3.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-0.19), 0.0, (-3382.2089), 0.0, (-0.19), 0.0);
      Vector3D vector3D0 = Vector3D.NaN;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      Line line0 = new Line(vector3D0, vector3D0, (-3382.2089));
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, line0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion$BoundaryBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-32.999188246), (-32.999188246), (-32.999188246), 1.0E-10, (-32.999188246), 0.0, (-32.999188246));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Line line0 = new Line(vector3D0, vector3D0, 1.0E-10);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, line0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>("");
      Vector3D vector3D0 = new Vector3D(1463.0, 1463.0, 2983.72912166318);
      Plane plane0 = new Plane(vector3D0, vector3D0, 2983.72912166318);
      PolygonsSet polygonsSet0 = new PolygonsSet(1463.0, 1961.636, 1463.0, 2983.72912166318, 1961.636);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree1 = bSPTree0.split(subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree0, 1463.0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree1);
      Line line0 = plane0.intersection(plane0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, line0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.String cannot be cast to java.lang.Boolean
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion$BoundaryBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null, 970.6426733976);
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      linkedList0.add((SubHyperplane<Euclidean3D>) null);
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion$1", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet((Collection<SubHyperplane<Euclidean3D>>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet((-2819.1552), (-2819.1552), (-2819.1552), 627.0, 627.0, 627.0, (-2819.1552));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(Double.NaN, Double.NaN, Double.NaN, Double.NaN, Double.NaN, Double.NaN);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(4.0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      bSPTree0.setAttribute(polyhedronsSet0);
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, (-0.3945428694152533));
      bSPTree0.insertCut(plane0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.computeGeometricalProperties();
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-32.47390862263721), (-32.47390862263721), (-32.47390862263721), (-0.5105650078930112), (-32.47390862263721), (-0.5105650078930112), (-32.47390862263721));
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Vector3D vector3D1 = new Vector3D((-0.5105650078930112), (-0.5105650078930112));
      Line line0 = new Line(vector3D1, vector3D0, (-0.5105650078930112));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNotNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.001, 0.001, 0.0, 0.001, (-895.7453578), 0.0, (-895.7453578));
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      Line line0 = new Line(vector3D0, vector3D0, 0.0);
      RotationOrder rotationOrder0 = RotationOrder.XYX;
      Vector3D vector3D1 = rotationOrder0.getA3();
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNotNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-0.19), 0.0, (-3382.2089), 0.0, (-0.19), 0.0);
      RotationOrder rotationOrder0 = RotationOrder.XZX;
      Vector3D vector3D0 = rotationOrder0.getA2();
      Vector3D vector3D1 = rotationOrder0.getA1();
      Line line0 = new Line(vector3D1, vector3D0, (-3382.2089));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertFalse(polyhedronsSet0.isEmpty());
      assertNull(subHyperplane0);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-32.47390862263721), (-32.47390862263721), (-32.47390862263721), (-0.5105650078930112), (-32.47390862263721), (-0.5105650078930112), (-32.47390862263721));
      // Undeclared exception!
      polyhedronsSet0.computeGeometricalProperties();
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 0.0, 0.0, 1966.799, (-2043.06329384), 0.0);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-5.7002853692756625), (-5.7002853692756625), (-5.7002853692756625), (-5.7002853692756625), 3275.5255436903876, (-5.7002853692756625), (-5.7002853692756625));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet();
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-32.999), (-32.999), (-32.999), 1.0E-10, (-32.999), 0.0, (-32.999));
      Vector3D vector3D0 = Vector3D.PLUS_K;
      Rotation rotation0 = new Rotation(vector3D0, (-20.218409970981572));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.computeGeometricalProperties();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.twod.PolygonsSet", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-0.19), 0.0, (-0.19), 0.0, (-0.19), 0.0);
      // Undeclared exception!
      polyhedronsSet0.computeGeometricalProperties();
  }
}
