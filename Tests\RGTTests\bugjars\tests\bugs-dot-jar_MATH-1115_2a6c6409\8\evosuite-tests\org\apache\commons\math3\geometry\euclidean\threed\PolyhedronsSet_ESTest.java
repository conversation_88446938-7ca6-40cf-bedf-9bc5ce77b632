/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 19:39:30 GMT 2019
 */

package org.apache.commons.math3.geometry.euclidean.threed;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math3.geometry.euclidean.threed.Euclidean3D;
import org.apache.commons.math3.geometry.euclidean.threed.Line;
import org.apache.commons.math3.geometry.euclidean.threed.Plane;
import org.apache.commons.math3.geometry.euclidean.threed.PolyhedronsSet;
import org.apache.commons.math3.geometry.euclidean.threed.Rotation;
import org.apache.commons.math3.geometry.euclidean.threed.RotationOrder;
import org.apache.commons.math3.geometry.euclidean.threed.SubPlane;
import org.apache.commons.math3.geometry.euclidean.threed.Vector3D;
import org.apache.commons.math3.geometry.euclidean.twod.Euclidean2D;
import org.apache.commons.math3.geometry.euclidean.twod.PolygonsSet;
import org.apache.commons.math3.geometry.partitioning.AbstractSubHyperplane;
import org.apache.commons.math3.geometry.partitioning.BSPTree;
import org.apache.commons.math3.geometry.partitioning.SubHyperplane;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class PolyhedronsSet_ESTest extends PolyhedronsSet_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(1.0, 1.5707963267948966, 0.0, 0.0, 0.0, 0.0, 0.0);
      assertFalse(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 2.0, 2.0, (-172.25), 2.0, 0.0);
      assertEquals(0.0, polyhedronsSet0.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-778.8715789325785), (-778.8715789325785), 299.1034573763833, (-778.8715789325785), 4.71238898038469, (-778.8715789325785));
      Vector3D vector3D0 = Vector3D.MINUS_J;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertTrue(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0);
      Vector3D vector3D0 = Vector3D.PLUS_J;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertEquals(0.0, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, (-1369.33), 1307.0, 0.0, (-1369.33), (-1369.33));
      Rotation rotation0 = new Rotation((-2263.1912), (-2263.1912), 0.9999, 0.0, true);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertNotSame(polyhedronsSet0, polyhedronsSet1);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-778.8715789325785), (-778.8715789325785), 299.1034573763833, (-778.8715789325785), 4.71238898038469, (-778.8715789325785));
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertNotSame(polyhedronsSet1, polyhedronsSet0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, 0.0);
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertTrue(polyhedronsSet1.isFull());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.PLUS_I;
      Plane plane0 = new Plane(vector3D0, 0.0);
      PolygonsSet polygonsSet0 = new PolygonsSet(2229.5555015);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, plane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree1);
      // Undeclared exception!
      try { 
        polyhedronsSet0.translate(vector3D0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math3.geometry.euclidean.threed.Plane cannot be cast to org.apache.commons.math3.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-4071.8), (-6.663542893624021E-14), (-3.4028234663852886E38), (-4071.8), (-4071.8), 3.141592653489793);
      RotationOrder rotationOrder0 = RotationOrder.XZX;
      Vector3D vector3D0 = rotationOrder0.getA1();
      Rotation rotation0 = new Rotation(672.9999999999, 0.0, 3.141592653489793, (-6.663542893624021E-14), false);
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D0, rotation0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // non-invertible affine transform collapses some lines into single points
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.twod.Line$LineTransform", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-105.972160143), 0.0, (-105.972160143), 1963.2832, (-1763.5556573789345), 197.0);
      Vector3D vector3D0 = Vector3D.PLUS_K;
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D0, (Rotation) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.threed.Plane", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.MINUS_J;
      Plane plane0 = new Plane(vector3D0, (-880.3253052724068));
      PolygonsSet polygonsSet0 = new PolygonsSet((-1413.0798994907602), 2970.1903835757053, 66.0, (-880.3253052724068), 105.972160143);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, "_}SL}wSg5V");
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree1);
      Rotation rotation0 = new Rotation(vector3D0, (-1413.0798994907602));
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D0, rotation0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.String cannot be cast to org.apache.commons.math3.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-4071.8), (-6.663542893624021E-14), (-3.4028234663852886E38), (-407.989284017581), (-407.989284017581), 3.141592653489793);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      Line line0 = new Line(vector3D0, vector3D0, (-480.9473623821));
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, line0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion$BoundaryBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = new Vector3D(3705.668, 0.0, 0.0);
      Vector3D vector3D1 = new Vector3D(3705.668, vector3D0);
      Plane plane0 = new Plane(vector3D1, vector3D1, 0.0);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 1.0E-10);
      // Undeclared exception!
      try { 
        polyhedronsSet0.firstIntersection(vector3D0, (Line) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.threed.Plane", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, vector3D0, 932.8489887298999);
      PolygonsSet polygonsSet0 = new PolygonsSet(932.8489887298999, 2113.691, 991.4951, 932.8489887298999, (-1.0E-10));
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, bSPTree0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree1);
      // Undeclared exception!
      try { 
        polyhedronsSet0.firstIntersection(vector3D0, (Line) null);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math3.geometry.partitioning.BSPTree cannot be cast to org.apache.commons.math3.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.threed.PolyhedronsSet", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, (-1369.33));
      PolygonsSet polygonsSet0 = new PolygonsSet(1866.4662611278748);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      AbstractSubHyperplane<Euclidean3D, Euclidean2D> abstractSubHyperplane0 = subPlane0.buildNew(plane0, polygonsSet0);
      linkedList0.add((SubHyperplane<Euclidean3D>) abstractSubHyperplane0);
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0);
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.twod.PolygonsSet", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null);
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(945.94565, 0.0, (-1.0), (-1.0), 945.94565, (-1066.9261491933319));
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      Vector3D vector3D0 = Vector3D.PLUS_I;
      BSPTree<Euclidean3D> bSPTree1 = bSPTree0.pruneAroundConvexCell(vector3D0, vector3D0, vector3D0);
      Plane plane0 = new Plane(vector3D0, 4.71238898038469);
      LinkedList<SubHyperplane<Euclidean2D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean2D>>();
      PolygonsSet polygonsSet0 = new PolygonsSet(linkedList0, (-2744.949077));
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree2 = bSPTree1.split(subPlane0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree2);
      // Undeclared exception!
      try { 
        polyhedronsSet1.computeGeometricalProperties();
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math3.geometry.euclidean.threed.Vector3D cannot be cast to java.lang.Boolean
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion$BoundaryBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      linkedList0.offerFirst((SubHyperplane<Euclidean3D>) null);
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(linkedList0, (-236.1796907662951));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion$1", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, (-1369.33));
      PolygonsSet polygonsSet0 = new PolygonsSet((-1369.33), (-1369.33), 1866.4662611278748, 1866.4662611278748, (-1369.33));
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(linkedList0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.twod.PolygonsSet", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet((Collection<SubHyperplane<Euclidean3D>>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, (-1369.33));
      PolygonsSet polygonsSet0 = new PolygonsSet(1866.4662611278748);
      BSPTree<Euclidean2D> bSPTree0 = new BSPTree<Euclidean2D>("\" K0?-d{V:");
      PolygonsSet polygonsSet1 = polygonsSet0.buildNew(bSPTree0);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet1);
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(linkedList0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.String cannot be cast to java.lang.Boolean
         //
         verifyException("org.apache.commons.math3.geometry.euclidean.twod.PolygonsSet", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet((-2003.386), (-0.1), (-0.1), (-1.0), (-0.1), (-1.0), (-1.0));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.BSPTree", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null, 0.0);
      Vector3D vector3D0 = Vector3D.MINUS_I;
      // Undeclared exception!
      try { 
        polyhedronsSet0.translate(vector3D0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, (-4762.9));
      RotationOrder rotationOrder0 = RotationOrder.YXZ;
      Vector3D vector3D0 = rotationOrder0.getA3();
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-0.1));
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertNotSame(polyhedronsSet1, polyhedronsSet0);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-105.972160143), 0.0, (-105.972160143), 1963.2832, 0.0, 197.0);
      Vector3D vector3D0 = Vector3D.PLUS_I;
      Rotation rotation0 = new Rotation(vector3D0, vector3D0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, (-1369.33));
      PolygonsSet polygonsSet0 = new PolygonsSet(1866.4662611278748);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      AbstractSubHyperplane<Euclidean3D, Euclidean2D> abstractSubHyperplane0 = subPlane0.buildNew(plane0, polygonsSet0);
      linkedList0.add((SubHyperplane<Euclidean3D>) abstractSubHyperplane0);
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0);
      Line line0 = new Line(vector3D0, vector3D0, (-1369.33));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertNull(subHyperplane0);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-2153.57), (-967.27417637704), (-2153.57), (-1963.2832), (-1963.2832), (-1486.099), 106.5983832);
      Vector3D vector3D0 = new Vector3D(1464.0, 1866.4662611277747);
      Vector3D vector3D1 = Vector3D.PLUS_J;
      Line line0 = new Line(vector3D0, vector3D1, (-2153.57));
      polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, (-1369.33));
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0);
      Line line0 = plane0.intersection(plane0);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertFalse(subHyperplane0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-105.972160143), 0.0, (-105.972160143), 1963.2832, (-1762.0346648071998), 197.0);
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      RotationOrder rotationOrder0 = RotationOrder.ZXY;
      Vector3D vector3D1 = rotationOrder0.getA3();
      Line line0 = new Line(vector3D1, vector3D0, 1963.2832);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNotNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-105.972160143), 0.0, (-105.972160143), 1963.2832, 0.0, 197.0);
      // Undeclared exception!
      polyhedronsSet0.computeGeometricalProperties();
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-2153.57), (-967.27417637704), (-2153.57), (-1963.2832), (-1963.2832), (-1486.099), 106.5983832);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-0.1536575840171921), 1866.466, (-0.1536575840171921), 1866.466, 1866.466, 1866.466);
      assertEquals(1.0E-10, polyhedronsSet0.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1504.967054098197), 2196.182872904647, (-1504.967054098197), (-1504.967054098197), (-1504.967054098197), 2196.182872904647);
      assertFalse(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(2195.0, (-3.4028234663852886E38), (-3.4028234663852886E38), 2195.0, (-4071.8), (-6.663542893624021E-14), (-4071.8));
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet();
      assertTrue(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-105.972160143), 0.0, (-105.972160143), 1963.2832, 0.0, 197.0);
      Vector3D vector3D0 = Vector3D.PLUS_I;
      RotationOrder rotationOrder0 = RotationOrder.ZXY;
      Vector3D vector3D1 = rotationOrder0.getA3();
      Line line0 = new Line(vector3D1, vector3D0, 1963.2832);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertFalse(polyhedronsSet0.isEmpty());
      assertNull(subHyperplane0);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, 0.0);
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      Rotation rotation0 = new Rotation((-2263.1912), (-2263.1912), 0.9999, 0.0, true);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertEquals(0.0, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, 0.0);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.addLast(subPlane0);
      SubPlane subPlane1 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane1);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertFalse(polyhedronsSet1.isEmpty());
  }
}
