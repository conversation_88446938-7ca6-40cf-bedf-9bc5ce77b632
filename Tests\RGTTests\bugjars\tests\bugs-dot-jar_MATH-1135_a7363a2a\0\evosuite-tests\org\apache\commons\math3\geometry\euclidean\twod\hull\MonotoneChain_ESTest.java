/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 22:59:11 GMT 2019
 */

package org.apache.commons.math3.geometry.euclidean.twod.hull;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math3.geometry.Vector;
import org.apache.commons.math3.geometry.euclidean.twod.Euclidean2D;
import org.apache.commons.math3.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math3.geometry.euclidean.twod.hull.MonotoneChain;
import org.apache.commons.math3.geometry.spherical.oned.S1Point;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MonotoneChain_ESTest extends MonotoneChain_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      linkedList0.push(vector2D0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      S1Point s1Point0 = new S1Point(2015.3768194844045);
      Vector2D vector2D0 = s1Point0.getVector();
      linkedList0.add(vector2D0);
      linkedList0.addFirst(vector2D0);
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 0.0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 3.9156979141260213);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D(3.9156979141260213, 3.9156979141260213);
      Vector2D vector2D2 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D2);
      linkedList0.offer(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      assertTrue(collection1.equals((Object)collection0));
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 3.9156979141260213);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D(3.9156979141260213, 3.9156979141260213);
      Vector2D vector2D2 = vector2D1.add(3.9156979141260213, (Vector<Euclidean2D>) vector2D1);
      linkedList0.offer(vector2D2);
      linkedList0.offer(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D2));
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D(1225.137898774278, 1225.137898774278);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = vector2D0.add(1225.137898774278, (Vector<Euclidean2D>) vector2D0);
      Vector2D vector2D2 = vector2D1.subtract(1225.137898774278, (Vector<Euclidean2D>) vector2D0);
      linkedList0.offer(vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
      assertFalse(collection0.contains(vector2D2));
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 3.9156979141260213);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D1);
      Vector2D vector2D2 = new Vector2D(3.9156979141260213, vector2D0, (-3301.134272973), vector2D1, 3.9156979141260213, vector2D0, 3.9156979141260213, vector2D1);
      linkedList0.push(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      Collection<Vector2D> collection2 = monotoneChain0.findHullVertices(collection1);
      assertTrue(collection2.equals((Object)collection1));
      assertFalse(collection2.equals((Object)collection0));
      assertTrue(collection2.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D(1225.137898774278, 1225.137898774278);
      Vector2D vector2D1 = new Vector2D(1225.137898774278, vector2D0);
      linkedList0.add(vector2D1);
      linkedList0.offer(vector2D0);
      linkedList0.offer(vector2D0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      MonotoneChain monotoneChain0 = new MonotoneChain();
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      MonotoneChain monotoneChain0 = new MonotoneChain();
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal Capacity: -2
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 1.0);
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices((Collection<Vector2D>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.ArrayList", e);
      }
  }
}
