/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:16:41 GMT 2019
 */

package org.apache.commons.math3.geometry.euclidean.twod.hull;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math3.geometry.Vector;
import org.apache.commons.math3.geometry.euclidean.twod.Euclidean2D;
import org.apache.commons.math3.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math3.geometry.euclidean.twod.hull.MonotoneChain;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MonotoneChain_ESTest extends MonotoneChain_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 207.87381994876148);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D(1.5830993332061267E-10, 207.87381994876148);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D(1.5830993332061267E-10, vector2D0);
      Vector2D vector2D2 = new Vector2D(207.87381994876148, 1.5830993332061267E-10);
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
      assertFalse(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 209.419);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-1.0), 209.419);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = vector2D0.add((Vector<Euclidean2D>) vector2D0);
      linkedList0.add(vector2D0);
      Vector2D vector2D2 = Vector2D.POSITIVE_INFINITY;
      linkedList0.push(vector2D2);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.NEGATIVE_INFINITY;
      linkedList0.addFirst(vector2D0);
      linkedList0.add(vector2D0);
      linkedList0.addAll((Collection<? extends Vector2D>) linkedList0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      assertFalse(collection1.equals((Object)collection0));
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.NEGATIVE_INFINITY;
      Vector2D vector2D1 = new Vector2D(0.0, vector2D0, 0.0, vector2D0, 746.98, vector2D0);
      linkedList0.addFirst(vector2D1);
      linkedList0.add(vector2D0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices((Collection<Vector2D>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-1.0), 0.2);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D((-1.0), vector2D0);
      Vector2D vector2D2 = vector2D1.add((Vector<Euclidean2D>) vector2D0);
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D2);
      MonotoneChain monotoneChain0 = new MonotoneChain();
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
      assertFalse(collection0.contains(vector2D2));
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 209.419);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D(209.419, 209.419);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D(209.419, vector2D0);
      Vector2D vector2D2 = vector2D1.add((Vector<Euclidean2D>) vector2D0);
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 207.87381994876148);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-1.0), 207.87381994876148);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D((-1.0), vector2D0);
      Vector2D vector2D2 = new Vector2D(207.87381994876148, (-1.0));
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertFalse(collection0.contains(vector2D1));
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 207.87381994876148);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-1.0), 207.87381994876148);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D((-1.0), vector2D0);
      Vector2D vector2D2 = new Vector2D(207.87381994876148, (-1.0));
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D2));
      assertFalse(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-1.0), 209.419);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-1.0), 0.2);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      MonotoneChain monotoneChain0 = new MonotoneChain();
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      MonotoneChain monotoneChain0 = new MonotoneChain();
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal Capacity: -2
         //
         verifyException("java.util.ArrayList", e);
      }
  }
}
