/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:22:18 GMT 2019
 */

package org.apache.commons.math3.geometry.euclidean.twod.hull;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math3.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math3.geometry.euclidean.twod.hull.MonotoneChain;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MonotoneChain_ESTest extends MonotoneChain_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 0.0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 1.0E-10);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      assertFalse(collection1.equals((Object)collection0));
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.NaN;
      linkedList0.add(vector2D1);
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 0.0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices((Collection<Vector2D>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      linkedList0.add(vector2D1);
      Vector2D vector2D2 = new Vector2D(2.0, 2.0);
      Vector2D vector2D3 = new Vector2D(689.04538, vector2D2);
      Vector2D vector2D4 = new Vector2D(2.0, vector2D1, 689.04538, vector2D3, 2.0, vector2D2);
      linkedList0.add(vector2D4);
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 1.0E-10);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      assertFalse(collection1.equals((Object)collection0));
      assertTrue(collection1.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D(1.0, 1.0);
      Vector2D vector2D2 = new Vector2D((-861.5987643758504), vector2D1);
      Vector2D vector2D3 = new Vector2D((-861.5987643758504), vector2D0, (-861.5987643758504), vector2D2, 1.0, vector2D1);
      linkedList0.add(vector2D3);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D(2.0, 2.0);
      Vector2D vector2D2 = new Vector2D(689.04538, vector2D1);
      linkedList0.add(vector2D2);
      linkedList0.add(vector2D1);
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 1.0E-10);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D(2.0, 2.0);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D1);
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 1.0E-10);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D1);
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 1.0E-10);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      linkedList0.push(vector2D0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal Capacity: -2
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false);
  }
}
