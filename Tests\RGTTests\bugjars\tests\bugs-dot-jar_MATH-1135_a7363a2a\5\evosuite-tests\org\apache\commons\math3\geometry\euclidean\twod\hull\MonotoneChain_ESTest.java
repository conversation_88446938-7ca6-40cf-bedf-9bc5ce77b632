/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:24:12 GMT 2019
 */

package org.apache.commons.math3.geometry.euclidean.twod.hull;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.ConcurrentModificationException;
import java.util.LinkedList;
import java.util.List;
import org.apache.commons.math3.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math3.geometry.euclidean.twod.hull.MonotoneChain;
import org.apache.commons.math3.geometry.spherical.oned.S1Point;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MonotoneChain_ESTest extends MonotoneChain_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 0.5608914771742644);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      S1Point s1Point0 = new S1Point(0.5608914771742644);
      Vector2D vector2D0 = Vector2D.NEGATIVE_INFINITY;
      linkedList0.push(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      linkedList0.offer(vector2D1);
      Vector2D vector2D2 = s1Point0.getVector();
      linkedList0.add(vector2D2);
      linkedList0.addAll((Collection<? extends Vector2D>) linkedList0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D2));
  }

  @Test(timeout = 4000)
  public void test1()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, (-0.8091851004314081));
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      S1Point s1Point0 = new S1Point((-0.8091851004314081));
      Vector2D vector2D0 = Vector2D.NaN;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      linkedList0.offer(vector2D1);
      Vector2D vector2D2 = s1Point0.getVector();
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      MonotoneChain monotoneChain1 = new MonotoneChain(true, 0.7237250619712411);
      Collection<Vector2D> collection1 = monotoneChain1.findHullVertices(collection0);
      assertTrue(collection1.equals((Object)collection0));
  }

  @Test(timeout = 4000)
  public void test2()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D0);
      List<Vector2D> list0 = linkedList0.subList(1, 1);
      linkedList0.add(vector2D0);
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices(list0);
        fail("Expecting exception: ConcurrentModificationException");
      
      } catch(ConcurrentModificationException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.SubList", e);
      }
  }

  @Test(timeout = 4000)
  public void test3()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true);
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices((Collection<Vector2D>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test4()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 1.0);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-1232.3619572051277), (-1232.3619572051277));
      linkedList0.add(vector2D0);
      S1Point s1Point0 = new S1Point((-1232.3619572051277));
      Vector2D vector2D1 = Vector2D.ZERO;
      linkedList0.offer(vector2D1);
      Vector2D vector2D2 = s1Point0.getVector();
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D2));
  }

  @Test(timeout = 4000)
  public void test5()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 1.0);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-1232.3619572051277), (-1232.3619572051277));
      linkedList0.add(vector2D0);
      S1Point s1Point0 = new S1Point((-1232.3619572051277));
      Vector2D vector2D1 = Vector2D.ZERO;
      linkedList0.offer(vector2D1);
      Vector2D vector2D2 = s1Point0.getVector();
      linkedList0.add(vector2D2);
      linkedList0.addAll((Collection<? extends Vector2D>) linkedList0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test6()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 1.0);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      S1Point s1Point0 = new S1Point(1.0);
      Vector2D vector2D0 = Vector2D.NaN;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      linkedList0.offer(vector2D1);
      Vector2D vector2D2 = s1Point0.getVector();
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      assertTrue(collection1.contains(vector2D1));
      assertTrue(collection1.equals((Object)collection0));
  }

  @Test(timeout = 4000)
  public void test7()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 1.0);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-1232.3619572051277), (-1232.3619572051277));
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test8()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal Capacity: -2
         //
         verifyException("java.util.ArrayList", e);
      }
  }
}
