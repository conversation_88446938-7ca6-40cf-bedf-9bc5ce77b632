/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:26:05 GMT 2019
 */

package org.apache.commons.math3.geometry.euclidean.twod.hull;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math3.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math3.geometry.euclidean.twod.hull.MonotoneChain;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MonotoneChain_ESTest extends MonotoneChain_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      double[] doubleArray0 = new double[2];
      linkedList0.add(vector2D0);
      doubleArray0[1] = (-351.8);
      Vector2D vector2D1 = new Vector2D(doubleArray0);
      Vector2D vector2D2 = new Vector2D((-351.8), vector2D0, 10.630221210746333, vector2D1, 824.91673928, vector2D0);
      linkedList0.offer(vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D(0.1158765332319096, (-4.503599627370524E15));
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      double[] doubleArray0 = new double[2];
      linkedList0.add(vector2D1);
      doubleArray0[1] = 0.1158765332319096;
      Vector2D vector2D2 = new Vector2D(doubleArray0);
      linkedList0.offer(vector2D2);
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 0.1158765332319096);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      double[] doubleArray0 = new double[2];
      linkedList0.add(vector2D0);
      doubleArray0[1] = 1.0E-10;
      Vector2D vector2D1 = new Vector2D(doubleArray0);
      Vector2D vector2D2 = Vector2D.NEGATIVE_INFINITY;
      linkedList0.offer(vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      assertTrue(collection1.contains(vector2D0));
      assertTrue(collection0.contains(vector2D2));
      assertFalse(collection1.equals((Object)collection0));
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.NEGATIVE_INFINITY;
      linkedList0.add(vector2D0);
      linkedList0.offer(vector2D0);
      linkedList0.add(vector2D0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      Collection<Vector2D> collection2 = monotoneChain0.findHullVertices(collection1);
      assertFalse(collection2.equals((Object)collection1));
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true);
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices((Collection<Vector2D>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D(0.1158765332319096, (-4.503599627370496E15));
      linkedList0.add(vector2D0);
      double[] doubleArray0 = new double[2];
      Vector2D vector2D1 = new Vector2D(doubleArray0);
      Vector2D vector2D2 = new Vector2D(0.1158765332319096, vector2D0, 0.1158765332319096, vector2D1, 0.1158765332319096, vector2D0);
      linkedList0.offer(vector2D1);
      linkedList0.add(vector2D2);
      MonotoneChain monotoneChain0 = new MonotoneChain();
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D(0.1158765332319096, (-4.503599627370496E15));
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      double[] doubleArray0 = new double[2];
      linkedList0.add(vector2D1);
      doubleArray0[1] = 0.1158765332319096;
      Vector2D vector2D2 = new Vector2D(doubleArray0);
      Vector2D vector2D3 = new Vector2D(0.1158765332319096, vector2D1, 13.07, vector2D2, 0.1158765332319096, vector2D1);
      linkedList0.add(vector2D3);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
      assertFalse(collection0.contains(vector2D3));
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D(0.1158765332319096, (-4.503599627370496E15));
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      double[] doubleArray0 = new double[2];
      linkedList0.add(vector2D1);
      doubleArray0[1] = 0.1158765332319096;
      Vector2D vector2D2 = new Vector2D(doubleArray0);
      linkedList0.offer(vector2D2);
      MonotoneChain monotoneChain0 = new MonotoneChain();
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertFalse(collection0.contains(vector2D2));
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.NEGATIVE_INFINITY;
      linkedList0.offer(vector2D0);
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D0);
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 807.0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D(0.1158765332319096, (-4.503599627370496E15));
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      MonotoneChain monotoneChain0 = new MonotoneChain();
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      MonotoneChain monotoneChain0 = new MonotoneChain();
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal Capacity: -2
         //
         verifyException("java.util.ArrayList", e);
      }
  }
}
