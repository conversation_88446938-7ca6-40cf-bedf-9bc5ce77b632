/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:27:59 GMT 2019
 */

package org.apache.commons.math3.geometry.euclidean.twod.hull;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math3.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math3.geometry.euclidean.twod.hull.MonotoneChain;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MonotoneChain_ESTest extends MonotoneChain_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 1846.478081508011);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1846.478081508011;
      doubleArray0[1] = 0.9715213896204331;
      Vector2D vector2D0 = new Vector2D(doubleArray0);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      Vector2D vector2D2 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D2);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      assertFalse(collection1.equals((Object)collection0));
      assertTrue(collection1.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 1844.8963914914852);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D((-3523.152369037), 0.0);
      Vector2D vector2D2 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D2);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 1844.8963914914852);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D((-3523.152369037), 0.0);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 1.184154459111628E-8);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      double[] doubleArray0 = new double[2];
      Vector2D vector2D0 = new Vector2D(doubleArray0);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      assertFalse(collection1.equals((Object)collection0));
      assertTrue(collection1.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 1826.4348857771595);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      double[] doubleArray0 = new double[2];
      doubleArray0[1] = 1826.4348857771595;
      Vector2D vector2D0 = new Vector2D(doubleArray0);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false);
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices((Collection<Vector2D>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 1826.4348857771595);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1826.4348857771595;
      Vector2D vector2D0 = new Vector2D(doubleArray0);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      Vector2D vector2D2 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D2);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      assertTrue(collection1.equals((Object)collection0));
      assertTrue(collection1.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 1.184154459111628E-8);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1.184154459111628E-8;
      Vector2D vector2D0 = new Vector2D(doubleArray0);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      Vector2D vector2D2 = new Vector2D(0.0, 1.184154459111628E-8);
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertFalse(collection0.contains(vector2D2));
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 1826.4348857771595);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1826.4348857771595;
      Vector2D vector2D0 = new Vector2D(doubleArray0);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      Vector2D vector2D2 = new Vector2D(0.0, 1826.4348857771595);
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertFalse(collection0.contains(vector2D2));
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, 1.184154459111628E-8);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1.184154459111628E-8;
      Vector2D vector2D0 = new Vector2D(doubleArray0);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.ZERO;
      Vector2D vector2D2 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D2);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      Collection<Vector2D> collection1 = monotoneChain0.findHullVertices(collection0);
      Collection<Vector2D> collection2 = monotoneChain0.findHullVertices(collection1);
      assertTrue(collection2.equals((Object)collection1));
      assertFalse(collection2.equals((Object)collection0));
      assertTrue(collection1.contains(vector2D2));
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, (-1303.226315258));
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal Capacity: -2
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
  }
}
