/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:29:52 GMT 2019
 */

package org.apache.commons.math3.geometry.euclidean.twod.hull;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math3.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math3.geometry.euclidean.twod.hull.MonotoneChain;
import org.apache.commons.math3.geometry.spherical.oned.S1Point;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MonotoneChain_ESTest extends MonotoneChain_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.POSITIVE_INFINITY;
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test1()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-257.31574178436233), 139.4428506862852);
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      MonotoneChain monotoneChain0 = new MonotoneChain(false, 0.0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test2()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true, (-1.0));
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      S1Point s1Point0 = S1Point.NaN;
      Vector2D vector2D0 = s1Point0.getVector();
      Vector2D vector2D1 = new Vector2D((-1.0), vector2D0);
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test3()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false);
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices((Collection<Vector2D>) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test4()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(true);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D(634.293788001, 139.4428506862852);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D(634.293788001, vector2D0, 139.4428506862852, vector2D0);
      linkedList0.add(vector2D1);
      Vector2D vector2D2 = new Vector2D(634.293788001, vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test5()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-257.31574178436233), 139.4428506862852);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D(634.293788001, vector2D0, 139.4428506862852, vector2D0);
      linkedList0.add(vector2D1);
      Vector2D vector2D2 = new Vector2D(634.293788001, vector2D1);
      linkedList0.add(vector2D2);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertFalse(collection0.contains(vector2D1));
      assertTrue(collection0.contains(vector2D2));
  }

  @Test(timeout = 4000)
  public void test6()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain(false);
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = new Vector2D((-257.31574178436233), 139.4428506862852);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = new Vector2D(634.293788001, vector2D0, 139.4428506862852, vector2D0);
      linkedList0.add(vector2D1);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test7()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      Vector2D vector2D1 = Vector2D.NEGATIVE_INFINITY;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D1);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D1));
  }

  @Test(timeout = 4000)
  public void test8()  throws Throwable  {
      MonotoneChain monotoneChain0 = new MonotoneChain();
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      Vector2D vector2D0 = Vector2D.ZERO;
      linkedList0.add(vector2D0);
      linkedList0.add(vector2D0);
      Collection<Vector2D> collection0 = monotoneChain0.findHullVertices(linkedList0);
      assertTrue(collection0.contains(vector2D0));
  }

  @Test(timeout = 4000)
  public void test9()  throws Throwable  {
      LinkedList<Vector2D> linkedList0 = new LinkedList<Vector2D>();
      MonotoneChain monotoneChain0 = new MonotoneChain(true, (-636.42108));
      // Undeclared exception!
      try { 
        monotoneChain0.findHullVertices(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Illegal Capacity: -2
         //
         verifyException("java.util.ArrayList", e);
      }
  }
}
