/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 22:56:37 GMT 2019
 */

package org.apache.commons.math3.distribution;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.distribution.BinomialDistribution;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well44497a;
import org.apache.commons.math3.random.Well44497b;
import org.apache.commons.math3.random.Well512a;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class BinomialDistribution_ESTest extends BinomialDistribution_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1199, 5.0970277930552287E-8);
      double double0 = binomialDistribution0.cumulativeProbability(0);
      assertEquals(0.9999388885025874, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0.0);
      binomialDistribution0.solveInverseCumulativeProbability((-2818.76), 0, 450);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1199, 5.0970277930552287E-8);
      double double0 = binomialDistribution0.logProbability(1199);
      assertEquals((-20133.63576857995), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0.0);
      double double0 = binomialDistribution0.probability((-1));
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution((-1), (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-1)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution((RandomGenerator) null, 0, 0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1199, 5.0970277930552287E-8);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(6.111336012376708E-5, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1199, 5.0970277930552287E-8);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(6.111336323873219E-5, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1008, 0.0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(1008, int0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(1178, 1178);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 1,178 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1199, 5.0970277930552287E-8);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(1199, int0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(2145472139, 1.0);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(2145472139, int0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Well44497a well44497a0 = new Well44497a();
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well44497a0, 0, 0);
      double double0 = binomialDistribution0.cumulativeProbability((-1147));
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      int[] intArray0 = new int[9];
      Well512a well512a0 = new Well512a(intArray0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well512a0, 689, 1.883511811213715E-8);
      double double0 = binomialDistribution0.logProbability((-328));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(1.2977396379262497E-5, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(683, 0.0);
      double double0 = binomialDistribution0.logProbability(1885);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(683, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a(0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937a0, 0, 1.0);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a((-1));
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well19937a0, (-1), (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-1)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a(0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937a0, 0, 1.0);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a(0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937a0, 0, 1.0);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(683, 0.0);
      double double0 = binomialDistribution0.cumulativeProbability(1);
      assertEquals(683, binomialDistribution0.getNumberOfTrials());
      assertEquals(1.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a(0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937a0, 0, 1.0);
      double double0 = binomialDistribution0.cumulativeProbability(0);
      assertEquals(1.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      int[] intArray0 = new int[9];
      Well512a well512a0 = new Well512a(intArray0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well512a0, 689, 1.883511811213715E-8);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals((-1.2977396501477896E-5), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a(0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937a0, 0, 1.0);
      double double0 = binomialDistribution0.logProbability(1);
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a(0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937a0, 0, 1.0);
      double double0 = binomialDistribution0.probability(0);
      assertEquals(1.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution((RandomGenerator) null, 289, 289);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 289 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Well44497b well44497b0 = new Well44497b((-714));
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well44497b0, 236, (-714));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -714 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 1.0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 1.0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(0, int0);
      assertEquals(1.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 1.0);
      boolean boolean0 = binomialDistribution0.isSupportConnected();
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertTrue(boolean0);
  }
}
