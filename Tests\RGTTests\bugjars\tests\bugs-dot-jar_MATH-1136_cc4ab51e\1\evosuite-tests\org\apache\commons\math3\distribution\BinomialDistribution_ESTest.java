/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:32:21 GMT 2019
 */

package org.apache.commons.math3.distribution;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.distribution.BinomialDistribution;
import org.apache.commons.math3.random.JDKRandomGenerator;
import org.apache.commons.math3.random.MersenneTwister;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well19937c;
import org.apache.commons.math3.random.Well44497a;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class BinomialDistribution_ESTest extends BinomialDistribution_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(833, 1.545909820981726E-8);
      double double0 = binomialDistribution0.cumulativeProbability(1, 833);
      assertEquals(8.281386687514214E-11, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(120, 2.2361985518423144E-8);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals((-2.6834382922142815E-6), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a(0L);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937a0, 245, 0L);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(0.0, double0, 0.01);
      assertEquals(245, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a();
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937a0, 0, 0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      MersenneTwister mersenneTwister0 = new MersenneTwister(0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(mersenneTwister0, 1195, 0);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(1195, binomialDistribution0.getNumberOfTrials());
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(120, 2.2361985518423144E-8);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(2.6834382622107775E-6, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      MersenneTwister mersenneTwister0 = new MersenneTwister(0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(mersenneTwister0, 1195, 0);
      double double0 = binomialDistribution0.cumulativeProbability(0);
      assertEquals(1.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(1195, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.cumulativeProbability(1061);
      assertEquals(1.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      double double0 = binomialDistribution0.cumulativeProbability((-3014));
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      double double0 = binomialDistribution0.logProbability(1);
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      JDKRandomGenerator jDKRandomGenerator0 = new JDKRandomGenerator();
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(jDKRandomGenerator0, 1, 1);
      double double0 = binomialDistribution0.logProbability((-2407));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(1, binomialDistribution0.getSupportUpperBound());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.logProbability((-47));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Well44497a well44497a0 = new Well44497a();
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well44497a0, 0, 0);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      JDKRandomGenerator jDKRandomGenerator0 = new JDKRandomGenerator();
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(jDKRandomGenerator0, 1, 1);
      double double0 = binomialDistribution0.logProbability(1736);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(1, binomialDistribution0.getSupportLowerBound());
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Well19937c well19937c0 = new Well19937c();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well19937c0, 1808, (-3303.86850585));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -3,303.869 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well19937a0, 1948, 1871.095532149);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 1,871.096 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well19937a0, (-2274), Double.NEGATIVE_INFINITY);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-2,274)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(833, 1.545909820981726E-8);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(1.2877428609704339E-5, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      JDKRandomGenerator jDKRandomGenerator0 = new JDKRandomGenerator();
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(jDKRandomGenerator0, 1, 1);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 1.0);
      int int0 = binomialDistribution0.sample();
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(0, binomialDistribution0.getSupportUpperBound());
      assertEquals(0, binomialDistribution0.getSupportLowerBound());
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      JDKRandomGenerator jDKRandomGenerator0 = new JDKRandomGenerator();
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(jDKRandomGenerator0, 1, 1);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1122, 0.0);
      double double0 = binomialDistribution0.probability(1122);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(1122, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 1.0);
      double double0 = binomialDistribution0.probability(39);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.probability(0);
      assertEquals(1.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(590, 590);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 590 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution((-15), (-15));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-15)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 1.0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1122, 0.0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(1122, int0);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1122, 0.0);
      boolean boolean0 = binomialDistribution0.isSupportConnected();
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertTrue(boolean0);
      assertEquals(1122, binomialDistribution0.getNumberOfTrials());
  }
}
