/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:36:53 GMT 2019
 */

package org.apache.commons.math3.distribution;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.distribution.BinomialDistribution;
import org.apache.commons.math3.random.Well1024a;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well44497a;
import org.apache.commons.math3.random.Well44497b;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class BinomialDistribution_ESTest extends BinomialDistribution_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      double double0 = binomialDistribution0.cumulativeProbability((-1), 0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1, binomialDistribution0.getSupportLowerBound());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(435, 0.0);
      double double0 = binomialDistribution0.cumulativeProbability(61);
      assertEquals(435, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.cumulativeProbability(0);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.probability(794);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution((-1), (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-1)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Well44497a well44497a0 = new Well44497a(116);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well44497a0, 116, 5.4445606140746644E-8);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(116, int0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      binomialDistribution0.getSupportUpperBound();
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Well44497a well44497a0 = new Well44497a(116);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well44497a0, 116, 5.4445606140746644E-8);
      double double0 = binomialDistribution0.cumulativeProbability(2063);
      assertEquals(1.0, double0, 0.01);
      assertEquals(116, binomialDistribution0.getSupportUpperBound());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      double double0 = binomialDistribution0.logProbability(1);
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Well1024a well1024a0 = new Well1024a((-104));
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well1024a0, 42, 0.0);
      double double0 = binomialDistribution0.logProbability((-1));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(42, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a(0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937a0, 0, 0.0);
      double double0 = binomialDistribution0.logProbability((-1919));
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Well44497a well44497a0 = new Well44497a(116);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well44497a0, 116, 5.4445606140746644E-8);
      double double0 = binomialDistribution0.logProbability(2063);
      assertEquals(116, binomialDistribution0.getSupportUpperBound());
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a((-1L));
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well19937a0, 1174, (-1L));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -1 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well19937a0, (-1), 0.0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-1)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Well44497a well44497a0 = new Well44497a(116);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well44497a0, 116, 5.4445606140746644E-8);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(6.315689968465023E-6, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(2345, 1.0);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(2345.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(527, 0.0);
      assertEquals(0, binomialDistribution0.getSupportUpperBound());
      
      binomialDistribution0.sample(2161);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
      assertEquals(0, binomialDistribution0.getSupportLowerBound());
      assertEquals(527, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(527, 0.0);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(527, binomialDistribution0.getNumberOfTrials());
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(527, 0.0);
      double double0 = binomialDistribution0.cumulativeProbability((-1611));
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
      assertEquals(527, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a();
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937a0, 0, 0);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(527, 0.0);
      double double0 = binomialDistribution0.probability(0);
      assertEquals(527, binomialDistribution0.getNumberOfTrials());
      assertEquals(1.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Well44497b well44497b0 = new Well44497b();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well44497b0, 920, 920);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 920 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(0, (-1.0));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -1 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(2345, 1.0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(1.0, double0, 0.01);
      assertEquals(2345.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(2345, 1.0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(2345, int0);
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(2345, 1.0);
      boolean boolean0 = binomialDistribution0.isSupportConnected();
      assertTrue(boolean0);
      assertEquals(2345.0, binomialDistribution0.getNumericalMean(), 0.01);
  }
}
