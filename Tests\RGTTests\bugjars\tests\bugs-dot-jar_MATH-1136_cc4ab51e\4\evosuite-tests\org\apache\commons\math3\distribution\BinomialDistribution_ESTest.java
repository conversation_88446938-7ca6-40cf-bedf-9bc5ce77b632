/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:39:06 GMT 2019
 */

package org.apache.commons.math3.distribution;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.distribution.BinomialDistribution;
import org.apache.commons.math3.random.MersenneTwister;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well19937c;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class BinomialDistribution_ESTest extends BinomialDistribution_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 5.6656680900216754E-52);
      double double0 = binomialDistribution0.cumulativeProbability(1294);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(4106, 0.14496260389175003);
      double double0 = binomialDistribution0.probability(0);
      assertEquals(5.38096034249751E-280, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.logProbability(799);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1244, Double.NaN);
      double double0 = binomialDistribution0.probability(1244);
      assertEquals(1244, binomialDistribution0.getNumberOfTrials());
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution((-1684), (-1684));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-1,684)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 5.6656680900216754E-52);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
      assertEquals(5.6656680900216754E-52, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(4106, 0.14496260389175003);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(508.93232487934984, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(4106, 0.14496260389175003);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(4106, int0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0.0);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      int[] intArray0 = new int[2];
      Well19937c well19937c0 = new Well19937c(intArray0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well19937c0, 5928, 1);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(5928, int0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1411, 1.4605950309628873E-8);
      double double0 = binomialDistribution0.cumulativeProbability(1092);
      assertEquals(1.0, double0, 0.01);
      assertEquals(1411, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.cumulativeProbability(0);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1244, Double.NaN);
      double double0 = binomialDistribution0.logProbability(1244);
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals(1244, binomialDistribution0.getSupportLowerBound());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 0.0);
      double double0 = binomialDistribution0.logProbability(768);
      assertEquals(1, binomialDistribution0.getNumberOfTrials());
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.logProbability((-1));
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution((RandomGenerator) null, 0, 0.0);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      MersenneTwister mersenneTwister0 = new MersenneTwister(0L);
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(mersenneTwister0, 3102, (-1.0));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -1 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Well19937c well19937c0 = new Well19937c();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well19937c0, (-3065), 0.0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-3,065)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(4106, 0.14496260389175003);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(595.2164515795256, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1244, Double.NaN);
      binomialDistribution0.sample(1244);
      assertEquals(1244, binomialDistribution0.getSupportLowerBound());
      assertEquals(0, binomialDistribution0.getSupportUpperBound());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.cumulativeProbability((-2146648224));
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      double double0 = binomialDistribution0.cumulativeProbability(0, 1);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(494, 0.0);
      double double0 = binomialDistribution0.logProbability((-753));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(494, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.probability((-2146648224));
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a(0);
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well19937a0, 182, 457.04771769316);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 457.048 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(116, (-2570.4401941));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -2,570.44 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(1, int0);
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      boolean boolean0 = binomialDistribution0.isSupportConnected();
      assertTrue(boolean0);
      assertEquals(1, binomialDistribution0.getSupportUpperBound());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }
}
