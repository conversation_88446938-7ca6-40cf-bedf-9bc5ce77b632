/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:45:50 GMT 2019
 */

package org.apache.commons.math3.distribution;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.distribution.BinomialDistribution;
import org.apache.commons.math3.random.MersenneTwister;
import org.apache.commons.math3.random.Well1024a;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well44497b;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class BinomialDistribution_ESTest extends BinomialDistribution_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(671, 3.4529452510568104E-8);
      double double0 = binomialDistribution0.cumulativeProbability(0, 0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(671, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1064, 8.568043768122183E-212);
      double double0 = binomialDistribution0.cumulativeProbability(2531);
      assertEquals(1.0, double0, 0.01);
      assertEquals(1064, binomialDistribution0.getSupportUpperBound());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      double double0 = binomialDistribution0.logProbability(1);
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.probability((-2202));
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution((-3738), (-3738));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-3,738)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(1.0, double0, 0.01);
      assertEquals(1, binomialDistribution0.getSupportUpperBound());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1064, 8.568043768122183E-212);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(9.116398569282003E-209, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      int[] intArray0 = new int[5];
      Well44497b well44497b0 = new Well44497b(intArray0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well44497b0, 3982, 2.9507744508973654E-8);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(1.1749983863473309E-4, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(1237, (-2121.46258177));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -2,121.463 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1064, 8.568043768122183E-212);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(1064, int0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, int0);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(0, int0);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(2621, 1.0);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(2621, int0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Well1024a well1024a0 = new Well1024a(160);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well1024a0, 160, 0.0);
      double double0 = binomialDistribution0.cumulativeProbability(18);
      assertEquals(160, binomialDistribution0.getNumberOfTrials());
      assertEquals(1.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.cumulativeProbability(0);
      assertEquals(1.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.cumulativeProbability((-753));
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 0.0);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals(1, binomialDistribution0.getNumberOfTrials());
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 0.0);
      double double0 = binomialDistribution0.logProbability(1074);
      assertEquals(1, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0.0);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Well1024a well1024a0 = new Well1024a();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well1024a0, (-2294), 0.0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-2,294)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1601, 0.0);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(1601, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1601, 0.0);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(0.0, double0, 0.01);
      assertEquals(1601, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1601, 0.0);
      double double0 = binomialDistribution0.logProbability((-2148));
      assertEquals(1601, binomialDistribution0.getNumberOfTrials());
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.probability(0);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.logProbability(62);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      MersenneTwister mersenneTwister0 = new MersenneTwister();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(mersenneTwister0, 747, 747);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 747 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a((-2658L));
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well19937a0, 0, (-2658L));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -2,658 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1601, 0.0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(0.0, double0, 0.01);
      assertEquals(1601, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1601, 0.0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(1601, int0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Well1024a well1024a0 = new Well1024a(160);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well1024a0, 160, 0.0);
      boolean boolean0 = binomialDistribution0.isSupportConnected();
      assertEquals(160, binomialDistribution0.getNumberOfTrials());
      assertTrue(boolean0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      int int0 = binomialDistribution0.sample();
      assertEquals(1, binomialDistribution0.getSupportUpperBound());
      assertEquals(1.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(1, binomialDistribution0.getSupportLowerBound());
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
      assertEquals(1, int0);
  }
}
