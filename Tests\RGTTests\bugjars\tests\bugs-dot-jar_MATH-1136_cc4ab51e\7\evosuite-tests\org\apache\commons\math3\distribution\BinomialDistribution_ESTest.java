/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:48:06 GMT 2019
 */

package org.apache.commons.math3.distribution;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.distribution.BinomialDistribution;
import org.apache.commons.math3.random.ISAACRandom;
import org.apache.commons.math3.random.MersenneTwister;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well512a;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class BinomialDistribution_ESTest extends BinomialDistribution_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.solveInverseCumulativeProbability(0, (-1), 2992);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 0.0);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals(-0.0, double0, 0.01);
      assertEquals(1, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.logProbability((-648));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 1.0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1967, 0.5979346036911011);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(472.8841359575383, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1967, 0.5979346036911011);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(1176.1373654603958, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(1, 1);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(1.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Well512a well512a0 = new Well512a(0);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well512a0, 0, 0);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(iSAACRandom0, 211, 0.0);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(211, binomialDistribution0.getNumberOfTrials());
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(iSAACRandom0, 211, 0.0);
      double double0 = binomialDistribution0.cumulativeProbability(0);
      assertEquals(211, binomialDistribution0.getNumberOfTrials());
      assertEquals(1.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(iSAACRandom0, 211, 0.0);
      double double0 = binomialDistribution0.cumulativeProbability((-1775086330));
      assertEquals(211, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(iSAACRandom0, 211, 0.0);
      double double0 = binomialDistribution0.logProbability(211);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(211, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Well512a well512a0 = new Well512a();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well512a0, 912, 912);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 912 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(well19937a0, (-1), (-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-1)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(iSAACRandom0, 211, 0.0);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(211, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(iSAACRandom0, 211, 0.0);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(211, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(3652, 1.0);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(3652, int0);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(3652, 1.0);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(3652, int0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(iSAACRandom0, 211, 0.0);
      double double0 = binomialDistribution0.cumulativeProbability(923);
      assertEquals(211, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(3652, 1.0);
      double double0 = binomialDistribution0.cumulativeProbability((-753), 1);
      assertEquals(0.0, double0, 0.01);
      assertEquals(3652.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(iSAACRandom0, 211, 0.0);
      double double0 = binomialDistribution0.logProbability(1550);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(211, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(iSAACRandom0, 211, 0.0);
      double double0 = binomialDistribution0.logProbability((-989));
      assertEquals(211, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.logProbability(4412);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(3652, 1.0);
      double double0 = binomialDistribution0.probability(1706);
      assertEquals(3652, binomialDistribution0.getSupportUpperBound());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.probability(0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(0, 907.657832250073);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 907.658 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      MersenneTwister mersenneTwister0 = new MersenneTwister(0);
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(mersenneTwister0, 1, (-2846.742901827637));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -2,846.743 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution((-1771), (-1771));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-1,771)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(iSAACRandom0, 211, 0.0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(211, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      boolean boolean0 = binomialDistribution0.isSupportConnected();
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertTrue(boolean0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }
}
