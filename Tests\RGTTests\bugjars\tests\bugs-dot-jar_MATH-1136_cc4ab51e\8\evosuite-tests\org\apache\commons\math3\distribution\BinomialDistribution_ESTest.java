/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 23:50:28 GMT 2019
 */

package org.apache.commons.math3.distribution;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math3.distribution.BinomialDistribution;
import org.apache.commons.math3.random.JDKRandomGenerator;
import org.apache.commons.math3.random.MersenneTwister;
import org.apache.commons.math3.random.RandomAdaptor;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well44497a;
import org.apache.commons.math3.random.Well44497b;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class BinomialDistribution_ESTest extends BinomialDistribution_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(104, 0.0);
      int int0 = binomialDistribution0.solveInverseCumulativeProbability(0.0, 0, 104);
      assertEquals(1, int0);
      assertEquals(104, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getNumericalVariance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.cumulativeProbability(0, 1);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Well44497b well44497b0 = new Well44497b(1685L);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well44497b0, 35, 0.0);
      double double0 = binomialDistribution0.probability(35);
      assertEquals(0.0, double0, 0.01);
      assertEquals(35, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.logProbability(200);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution((-2265), (-2265));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-2,265)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(3, 1.5421457732308477E-7);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(3, binomialDistribution0.getSupportUpperBound());
      assertEquals(1.5421457732308477E-7, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(3, 1.5421457732308477E-7);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(4.6264366062284673E-7, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(3, 1.5421457732308477E-7);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(4.626437319692543E-7, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 6.317463237641817E-9);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0, int0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(5728, 1.0);
      double double0 = binomialDistribution0.cumulativeProbability(0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(5728, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.cumulativeProbability(0);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
      assertEquals(1.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(5728, 1.0);
      double double0 = binomialDistribution0.logProbability((-337));
      assertEquals(5728, binomialDistribution0.getSupportLowerBound());
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(104, 0.0);
      double double0 = binomialDistribution0.logProbability(1655);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(104, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Well44497a well44497a0 = new Well44497a(0L);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(well44497a0, 1847, 1.0);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(1847, binomialDistribution0.getSupportUpperBound());
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      JDKRandomGenerator jDKRandomGenerator0 = new JDKRandomGenerator();
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(jDKRandomGenerator0, 0, (-985.958));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -985.958 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      int[] intArray0 = new int[8];
      Well19937a well19937a0 = new Well19937a(intArray0);
      RandomAdaptor randomAdaptor0 = new RandomAdaptor(well19937a0);
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(randomAdaptor0, (-3090), 0.0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // number of trials (-3,090)
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(104, 0.0);
      double double0 = binomialDistribution0.getNumericalVariance();
      assertEquals(104, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(104, 0.0);
      double double0 = binomialDistribution0.getNumericalMean();
      assertEquals(0.0, double0, 0.01);
      assertEquals(104, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(5728, 1.0);
      int int0 = binomialDistribution0.getSupportUpperBound();
      assertEquals(5728, int0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      MersenneTwister mersenneTwister0 = new MersenneTwister(1);
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(mersenneTwister0, 1, 1);
      int int0 = binomialDistribution0.getSupportLowerBound();
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(3, 1.5421457732308477E-7);
      double double0 = binomialDistribution0.cumulativeProbability((-1315));
      assertEquals(0.0, double0, 0.01);
      assertEquals(4.626437319692543E-7, binomialDistribution0.getNumericalMean(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.logProbability(0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(0, 0);
      double double0 = binomialDistribution0.logProbability((-2053));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getNumericalMean(), 0.01);
      assertEquals(0, binomialDistribution0.getNumberOfTrials());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(795, 0);
      double double0 = binomialDistribution0.probability(0);
      assertEquals(795, binomialDistribution0.getNumberOfTrials());
      assertEquals(1.0, double0, 0.01);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      MersenneTwister mersenneTwister0 = new MersenneTwister(2622);
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(mersenneTwister0, 0, 2622);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 2,622 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = null;
      try {
        binomialDistribution0 = new BinomialDistribution(0, (-2349.0));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -2,349 out of [0, 1] range
         //
         verifyException("org.apache.commons.math3.distribution.BinomialDistribution", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(104, 0.0);
      double double0 = binomialDistribution0.getProbabilityOfSuccess();
      assertEquals(104, binomialDistribution0.getNumberOfTrials());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(104, 0.0);
      int int0 = binomialDistribution0.getNumberOfTrials();
      assertEquals(104, int0);
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      BinomialDistribution binomialDistribution0 = new BinomialDistribution(104, 0.0);
      boolean boolean0 = binomialDistribution0.isSupportConnected();
      assertEquals(0.0, binomialDistribution0.getProbabilityOfSuccess(), 0.01);
      assertTrue(boolean0);
      assertEquals(104, binomialDistribution0.getNumberOfTrials());
  }
}
