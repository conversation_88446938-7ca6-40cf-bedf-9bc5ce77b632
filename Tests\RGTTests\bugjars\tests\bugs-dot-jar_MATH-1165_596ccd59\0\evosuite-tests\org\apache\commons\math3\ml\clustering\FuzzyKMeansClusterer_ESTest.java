/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 21:51:27 GMT 2019
 */

package org.apache.commons.math3.ml.clustering;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.ml.clustering.CentroidCluster;
import org.apache.commons.math3.ml.clustering.DoublePoint;
import org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer;
import org.apache.commons.math3.ml.distance.CanberraDistance;
import org.apache.commons.math3.ml.distance.ChebyshevDistance;
import org.apache.commons.math3.ml.distance.DistanceMeasure;
import org.apache.commons.math3.ml.distance.EarthMoversDistance;
import org.apache.commons.math3.ml.distance.EuclideanDistance;
import org.apache.commons.math3.ml.distance.ManhattanDistance;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.Well1024a;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well44497b;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.Random;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class FuzzyKMeansClusterer_ESTest extends FuzzyKMeansClusterer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2, 2);
      double[] doubleArray0 = new double[10];
      doubleArray0[2] = (-1599.0938022);
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 5353.1054005186, 1, canberraDistance0);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(5353.1054005186, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-818), (-818));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -818 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1195, 4.505432606253564E64, 1195, euclideanDistance0, 1195, (RandomGenerator) null);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals(1195.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1195, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(4.505432606253564E64, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2.4106173515319824);
      double[] doubleArray0 = new double[4];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Random.setNextRandom(1);
      fuzzyKMeansClusterer0.cluster(list0);
      doubleArray0[2] = 2.4106173515319824;
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(2.4106173515319824, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(5.811076015507069, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1319.9344359325, 0, chebyshevDistance0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(1319.9344359325, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1228, 3645.3698, 1228, manhattanDistance0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1228, int0);
      assertEquals(3645.3698, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2.4106173515319824);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1, int0);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(2.4106173515319824, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well19937a well19937a0 = new Well19937a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1073741824), 2.4106173515319824, (-2146919020), chebyshevDistance0, 2523.2976230495, well19937a0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(2.4106173515319824, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-2146919020), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals((-1073741824), int0);
      assertEquals(2523.2976230495, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a((long) 0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 5580.569, 0, (DistanceMeasure) null, 0, well19937a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(5580.569, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      Well44497b well44497b0 = new Well44497b(0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1510.3363062415, (-285), euclideanDistance0, (-1.0), well44497b0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals((-1.0), double0, 0.01);
      assertEquals(1510.3363062415, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-285), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 14.54647851401843, 0, earthMoversDistance0);
      assertEquals(0, fuzzyKMeansClusterer0.getK());
      
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      fuzzyKMeansClusterer0.cluster(linkedList0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(14.54647851401843, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2, 2);
      assertEquals(2, fuzzyKMeansClusterer0.getK());
      
      double[] doubleArray0 = new double[10];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.getClusters();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2, list1.size());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 14.54647851401843, 0, earthMoversDistance0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      fuzzyKMeansClusterer0.cluster(linkedList0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.linear.MatrixUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1868.478816621);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      int[] intArray0 = new int[0];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      linkedList0.add(doublePoint0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math3.util.MathArrays", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1152, 1152);
      try { 
        fuzzyKMeansClusterer0.cluster((Collection<DoublePoint>) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math3.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2.4106173515319824);
      double[] doubleArray0 = new double[4];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      centroidCluster0.addPoint((DoublePoint) null);
      Random.setNextRandom(1);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-602), 712.320843);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2, 2);
      double[] doubleArray0 = new double[9];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      int[] intArray0 = new int[1];
      DoublePoint doublePoint1 = new DoublePoint(intArray0);
      centroidCluster0.addPoint(doublePoint1);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-365), (-369.0), 329, earthMoversDistance0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -369 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      Well44497b well44497b0 = new Well44497b(263);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 1, 0, canberraDistance0, 1.1740089468291563E-7, well44497b0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 1 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(3, 3);
      double[] doubleArray0 = new double[10];
      doubleArray0[2] = (-1599.0938022);
      doubleArray0[4] = (double) 3;
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(3.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(3, 3);
      double[] doubleArray0 = new double[10];
      doubleArray0[2] = (-1599.0938022);
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.cluster(list0);
      assertEquals(3, fuzzyKMeansClusterer0.getK());
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(3, list1.size());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well19937a well19937a0 = new Well19937a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2491.492, 1, chebyshevDistance0, 1, well19937a0);
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.cluster(list0);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2491.492, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertFalse(list1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(648, 648);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (648)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2, 2);
      assertEquals(2, fuzzyKMeansClusterer0.getK());
      
      double[] doubleArray0 = new double[10];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.cluster(list0);
      assertEquals(2, list1.size());
      
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      int[] intArray0 = new int[2];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-595), 221.3, (-595), (DistanceMeasure) null, (-595), well1024a0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getObjectiveFunctionValue();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(233, 233);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2, 2);
      double[] doubleArray0 = new double[10];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      RealMatrix realMatrix0 = fuzzyKMeansClusterer0.getMembershipMatrix();
      assertEquals(2, realMatrix0.getRowDimension());
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2, 2);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(2, fuzzyKMeansClusterer0.getK());
      assertEquals(2.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2.4106173515319824);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(2.4106173515319824, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2.4106173515319824);
      fuzzyKMeansClusterer0.getDataPoints();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(2.4106173515319824, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 5352.058398995883);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(5352.058398995883, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0, int0);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2.4106173515319824);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-1), int0);
      assertEquals(2.4106173515319824, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2, 2);
      double double0 = fuzzyKMeansClusterer0.getFuzziness();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(2.0, double0, 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(17, 17, 17, earthMoversDistance0);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(17, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(17.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(17, fuzzyKMeansClusterer0.getK());
  }
}
