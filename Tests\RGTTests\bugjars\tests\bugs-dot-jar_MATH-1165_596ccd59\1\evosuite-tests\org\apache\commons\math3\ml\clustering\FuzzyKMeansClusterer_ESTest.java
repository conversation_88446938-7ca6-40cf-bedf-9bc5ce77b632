/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 21:53:37 GMT 2019
 */

package org.apache.commons.math3.ml.clustering;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.List;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.ml.clustering.CentroidCluster;
import org.apache.commons.math3.ml.clustering.DoublePoint;
import org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer;
import org.apache.commons.math3.ml.distance.ChebyshevDistance;
import org.apache.commons.math3.ml.distance.DistanceMeasure;
import org.apache.commons.math3.ml.distance.EarthMoversDistance;
import org.apache.commons.math3.ml.distance.ManhattanDistance;
import org.apache.commons.math3.random.ISAACRandom;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.Well44497a;
import org.apache.commons.math3.random.Well44497b;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class FuzzyKMeansClusterer_ESTest extends FuzzyKMeansClusterer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(4, 2840.290310409967, 4, manhattanDistance0, 2544.59, well44497b0);
      int[] intArray0 = new int[1];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(188, 188);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(188.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-454), 1155.81363752, (-491), manhattanDistance0, 1155.81363752, (RandomGenerator) null);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals((-491), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1155.81363752, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-2433), 2481.0, 0, earthMoversDistance0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(2481.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2840.290310409967, 1, manhattanDistance0, (-218.464), well44497b0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(2840.290310409967, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-218.464), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1.1207549425651513E188, 2400, manhattanDistance0, 2544.59, well44497b0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(0, int0);
      assertEquals(2544.59, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1.1207549425651513E188, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(2400, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-311), 645, 4833, chebyshevDistance0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-311), int0);
      assertEquals(4833, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(645.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-2433), 2481.0, 0, earthMoversDistance0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(0.001, double0, 0.01);
      assertEquals(2481.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      ISAACRandom iSAACRandom0 = new ISAACRandom(2145564709);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2145564709, 2145564709, 2145564709, (DistanceMeasure) null, (-1.0), iSAACRandom0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals((-1.0), double0, 0.01);
      assertEquals(2.145564709E9, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(2145564709, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1.1207549425651513E188, 2400, manhattanDistance0, 2544.59, well44497b0);
      assertEquals(0, fuzzyKMeansClusterer0.getK());
      
      int[] intArray0 = new int[1];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(2544.59, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(2400, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1.1207549425651513E188, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 1.1207549425651513E188, 2400, manhattanDistance0, 2544.59, well44497b0);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      int[] intArray0 = new int[1];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(1.1207549425651513E188, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(2400, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1.1207549425651513E188, 2400, manhattanDistance0, 2544.59, well44497b0);
      int[] intArray0 = new int[1];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math3.util.MathArrays", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1142), 2189.10912771, 0, manhattanDistance0);
      try { 
        fuzzyKMeansClusterer0.cluster((Collection<DoublePoint>) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math3.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2840.290310409967, 1, manhattanDistance0, 2544.59, well44497b0);
      int[] intArray0 = new int[1];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint((DoublePoint) null);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-454), 1155.81363752, (-491), manhattanDistance0, 1155.81363752, (RandomGenerator) null);
      double[] doubleArray0 = new double[3];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[5];
      Well44497a well44497a0 = new Well44497a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-35), 0.001, 1, chebyshevDistance0, 1804.567140498, well44497a0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0.001 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(4, 4, 4, manhattanDistance0, 2544.59, well44497b0);
      assertEquals(4.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      
      int[] intArray0 = new int[1];
      intArray0[0] = 4;
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2840.290310409967, 1, manhattanDistance0, (-218.464), well44497b0);
      int[] intArray0 = new int[1];
      intArray0[0] = 1;
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      int[] intArray1 = new int[1];
      DoublePoint doublePoint1 = new DoublePoint(intArray1);
      centroidCluster0.addPoint(doublePoint1);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(0.5, double0, 0.01);
      assertEquals(2840.290310409967, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(4, 4);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      
      int[] intArray0 = new int[1];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.cluster(list0);
      assertEquals(4, list1.size());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1.1207549425651513E188, 2400, manhattanDistance0, 2544.59, well44497b0);
      int[] intArray0 = new int[1];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.linear.MatrixUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1457, 925.5577291246324, 1457, manhattanDistance0);
      double[] doubleArray0 = new double[1];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1,457)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-454), 1155.81363752);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getObjectiveFunctionValue();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2147483640, 2147483640);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2840.290310409967, 1, manhattanDistance0, (-218.464), well44497b0);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      int[] intArray0 = new int[1];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.cluster(list0);
      assertFalse(list1.isEmpty());
      
      RealMatrix realMatrix0 = fuzzyKMeansClusterer0.getMembershipMatrix();
      assertEquals(1, realMatrix0.getRowDimension());
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1, realMatrix0.getColumnDimension());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1284), (-1284), (-1284), earthMoversDistance0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -1,284 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2840.290310409967, 1, manhattanDistance0, 1, well44497b0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(1.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2840.290310409967, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1.1207549425651513E188, 0, manhattanDistance0, 0, well44497b0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(0.0, double0, 0.01);
      assertEquals(1.1207549425651513E188, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2840.290310409967, 1, manhattanDistance0, (-218.464), well44497b0);
      fuzzyKMeansClusterer0.getDataPoints();
      assertEquals((-218.464), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(2840.290310409967, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2840.290310409967, 1, manhattanDistance0, 1, well44497b0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(2840.290310409967, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-454), 1155.81363752, (-491), manhattanDistance0, 1155.81363752, (RandomGenerator) null);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(1155.81363752, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-454), fuzzyKMeansClusterer0.getK());
      assertEquals((-491), int0);
      assertEquals(1155.81363752, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well44497b well44497b0 = new Well44497b();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 2840.290310409967, 1, manhattanDistance0, 1, well44497b0);
      double double0 = fuzzyKMeansClusterer0.getFuzziness();
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(2840.290310409967, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-454), 1155.81363752);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals((-454), fuzzyKMeansClusterer0.getK());
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1155.81363752, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }
}
