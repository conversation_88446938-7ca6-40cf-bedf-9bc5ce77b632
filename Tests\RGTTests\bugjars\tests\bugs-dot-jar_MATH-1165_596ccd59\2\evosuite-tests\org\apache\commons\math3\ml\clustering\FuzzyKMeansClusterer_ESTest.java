/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 21:55:48 GMT 2019
 */

package org.apache.commons.math3.ml.clustering;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import org.apache.commons.math3.ml.clustering.CentroidCluster;
import org.apache.commons.math3.ml.clustering.DoublePoint;
import org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer;
import org.apache.commons.math3.ml.distance.CanberraDistance;
import org.apache.commons.math3.ml.distance.DistanceMeasure;
import org.apache.commons.math3.ml.distance.EarthMoversDistance;
import org.apache.commons.math3.ml.distance.EuclideanDistance;
import org.apache.commons.math3.ml.distance.ManhattanDistance;
import org.apache.commons.math3.random.MersenneTwister;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well44497b;
import org.apache.commons.math3.random.Well512a;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.Random;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class FuzzyKMeansClusterer_ESTest extends FuzzyKMeansClusterer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[1] = (double) 1;
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 943.841711707769);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      int[] intArray0 = new int[9];
      intArray0[0] = 1;
      intArray0[2] = 1;
      intArray0[3] = 1;
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      linkedList0.add(doublePoint0);
      DoublePoint doublePoint1 = new DoublePoint(doubleArray0);
      linkedList0.add(doublePoint1);
      Random.setNextRandom(1);
      fuzzyKMeansClusterer0.cluster(linkedList0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(132, 872.0984428, 132, canberraDistance0);
      assertEquals(132, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(872.0984428, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1941), (-1941));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -1,941 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2146334760, 2146334760, 2146334760, manhattanDistance0, 2.9599693109692324E-149, (RandomGenerator) null);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals(2.14633476E9, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(2.9599693109692324E-149, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(2146334760, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[1] = (double) 1;
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 943.841711707769);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      int[] intArray0 = new int[9];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      linkedList0.add(doublePoint0);
      DoublePoint doublePoint1 = new DoublePoint(doubleArray0);
      linkedList0.add(doublePoint1);
      Random.setNextRandom(1);
      fuzzyKMeansClusterer0.cluster(linkedList0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.5, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      Well19937a well19937a0 = new Well19937a(163);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-296), 2174.81358404835, 163, earthMoversDistance0, 2174.81358404835, well19937a0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(163, int0);
      assertEquals(2174.81358404835, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(2174.81358404835, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 2201.07975575363, (-2146110962), earthMoversDistance0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, int0);
      assertEquals((-2146110962), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2201.07975575363, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      MersenneTwister mersenneTwister0 = new MersenneTwister(0L);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-4382), 2408.0, (-4382), (DistanceMeasure) null, (-4382), mersenneTwister0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(2408.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-4382.0), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-4382), int0);
      assertEquals((-4382), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      Well512a well512a0 = new Well512a((long) 535);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(535, 535, 0, earthMoversDistance0, 0.0, well512a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(535.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      Well512a well512a0 = new Well512a((long) 535);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(535, 535, 0, earthMoversDistance0, (-904.20971687441), well512a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals((-904.20971687441), double0, 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(535.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 943.841711707769, 3839, earthMoversDistance0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      fuzzyKMeansClusterer0.cluster(linkedList0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(943.841711707769, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(3839, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      double[] doubleArray0 = new double[12];
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 943.841711707769, 1, earthMoversDistance0);
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      linkedList0.add(doublePoint0);
      Random.setNextRandom(1);
      fuzzyKMeansClusterer0.cluster(linkedList0);
      List<CentroidCluster<DoublePoint>> list0 = fuzzyKMeansClusterer0.getClusters();
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertFalse(list0.isEmpty());
      assertEquals(943.841711707769, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 943.841711707769, 3839, earthMoversDistance0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      fuzzyKMeansClusterer0.cluster(linkedList0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.linear.MatrixUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      double[] doubleArray0 = new double[9];
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 943.841711707769, 3839, earthMoversDistance0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      linkedList0.add(doublePoint0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math3.util.MathArrays", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      Well512a well512a0 = new Well512a((long) 535);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(535, 535, 0, earthMoversDistance0, 0.0, well512a0);
      try { 
        fuzzyKMeansClusterer0.cluster((Collection<DoublePoint>) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math3.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 935.9287026144415, 1, earthMoversDistance0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      DoublePoint doublePoint0 = new DoublePoint((double[]) null);
      linkedList0.add(doublePoint0);
      Random.setNextRandom(1);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-2588), 1393.00398);
      int[] intArray0 = new int[8];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(3, 3);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      int[] intArray0 = new int[6];
      int[] intArray1 = new int[1];
      DoublePoint doublePoint0 = new DoublePoint(intArray1);
      DoublePoint doublePoint1 = new DoublePoint(intArray0);
      linkedList0.add(doublePoint1);
      linkedList0.add(doublePoint0);
      linkedList0.add(doublePoint0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-427), (-3.015972957475025E265), (-427), euclideanDistance0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -30,159,729,574,750,250,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      Well44497b well44497b0 = new Well44497b(0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 74.79, 0, canberraDistance0, 2097.99587, well44497b0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(0, int0);
      assertEquals(74.79, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(2097.99587, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      int[] intArray0 = new int[1];
      Well19937a well19937a0 = new Well19937a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1963), (-1963), (-1963), (DistanceMeasure) null, 0.0, well19937a0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -1,963 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2, 943.841711707769);
      assertEquals(2, fuzzyKMeansClusterer0.getK());
      
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      int[] intArray0 = new int[9];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      linkedList0.add(doublePoint0);
      linkedList0.add(doublePoint0);
      fuzzyKMeansClusterer0.cluster(linkedList0);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(943.841711707769, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 943.841711707769);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 943.841711707769);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      int[] intArray0 = new int[8];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      linkedList0.add(doublePoint0);
      Random.setNextRandom(1);
      List<CentroidCluster<DoublePoint>> list0 = fuzzyKMeansClusterer0.cluster(linkedList0);
      assertEquals(1, list0.size());
      
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(943.841711707769, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(536, 536);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getObjectiveFunctionValue();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(831, 831);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      double[] doubleArray0 = new double[12];
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 935.9287026144415, 1, earthMoversDistance0);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      linkedList0.add(doublePoint0);
      Random.setNextRandom(1);
      List<CentroidCluster<DoublePoint>> list0 = fuzzyKMeansClusterer0.cluster(linkedList0);
      assertFalse(list0.isEmpty());
      
      fuzzyKMeansClusterer0.getMembershipMatrix();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(935.9287026144415, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 943.841711707769);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(943.841711707769, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 943.841711707769, 0, earthMoversDistance0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(0, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, double0, 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(943.841711707769, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(679, 679);
      fuzzyKMeansClusterer0.getDataPoints();
      assertEquals(679, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(679.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(831, 831);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(831.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(831, int0);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(536, 536);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(536.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(536, fuzzyKMeansClusterer0.getK());
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 935.9287026144415, 1, earthMoversDistance0);
      double double0 = fuzzyKMeansClusterer0.getFuzziness();
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(935.9287026144415, double0, 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-71), 325.3);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals((-71), fuzzyKMeansClusterer0.getK());
      assertEquals(325.3, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }
}
