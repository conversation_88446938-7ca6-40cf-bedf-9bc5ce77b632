/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 22:02:21 GMT 2019
 */

package org.apache.commons.math3.ml.clustering;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import org.apache.commons.math3.ml.clustering.CentroidCluster;
import org.apache.commons.math3.ml.clustering.Clusterable;
import org.apache.commons.math3.ml.clustering.DoublePoint;
import org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer;
import org.apache.commons.math3.ml.distance.CanberraDistance;
import org.apache.commons.math3.ml.distance.ChebyshevDistance;
import org.apache.commons.math3.ml.distance.DistanceMeasure;
import org.apache.commons.math3.ml.distance.EarthMoversDistance;
import org.apache.commons.math3.ml.distance.EuclideanDistance;
import org.apache.commons.math3.random.MersenneTwister;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.Well1024a;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well19937c;
import org.apache.commons.math3.random.Well44497a;
import org.apache.commons.math3.random.Well512a;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class FuzzyKMeansClusterer_ESTest extends FuzzyKMeansClusterer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Well512a well512a0 = new Well512a();
      int[] intArray0 = new int[4];
      intArray0[0] = 1;
      DoublePoint doublePoint1 = new DoublePoint(intArray0);
      centroidCluster0.addPoint(doublePoint1);
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 3593.619, 1, chebyshevDistance0, (-926.7396677443127), well512a0);
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.cluster(list0);
      assertEquals(3593.619, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, list1.size());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Well512a well512a0 = new Well512a();
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      centroidCluster0.addPoint(doublePoint0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 3593.619, 1, chebyshevDistance0, (-926.7396677443127), well512a0);
      fuzzyKMeansClusterer0.cluster(list0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(3593.619, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-926.7396677443127), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-2466), 1657.2008790337206);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1657.2008790337206, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1469, 1469, 1469, euclideanDistance0, 1469, (RandomGenerator) null);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals(1469, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1469.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1469.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Well512a well512a0 = new Well512a();
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 3593.619, 1, chebyshevDistance0, (-926.7396677443127), well512a0);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      fuzzyKMeansClusterer0.cluster(list0);
      fuzzyKMeansClusterer0.getMembershipMatrix();
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(3593.619, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(489, 1517.39749661, 0, canberraDistance0, (-1744.6508345595091), (RandomGenerator) null);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(489, fuzzyKMeansClusterer0.getK());
      assertEquals(0, int0);
      assertEquals(1517.39749661, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1744.6508345595091), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 672.624971, 221, earthMoversDistance0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(672.624971, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(221, int0);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 3593.619);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, int0);
      assertEquals(3593.619, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      Well1024a well1024a0 = new Well1024a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-634), 1271.695650354924, 8, earthMoversDistance0, 1271.695650354924, well1024a0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals((-634), int0);
      assertEquals(1271.695650354924, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1271.695650354924, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(8, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      Well44497a well44497a0 = new Well44497a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2979, 2979, 2979, canberraDistance0, 0.0, well44497a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(2979, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2979.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2979, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      MersenneTwister mersenneTwister0 = new MersenneTwister();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2143528822, 2143528822, 2143528822, earthMoversDistance0, (-1304247697), mersenneTwister0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals((-1.304247697E9), double0, 0.01);
      assertEquals(2143528822, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2.143528822E9, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(2143528822, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 3593.619);
      assertEquals(0, fuzzyKMeansClusterer0.getK());
      
      double[] doubleArray0 = new double[28];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(3593.619, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Well512a well512a0 = new Well512a();
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 3593.619, 1, chebyshevDistance0, (-926.7396677443127), well512a0);
      fuzzyKMeansClusterer0.cluster(list0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1299.869235483);
      int[] intArray0 = new int[7];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math3.util.MathArrays", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[0];
      Well19937c well19937c0 = new Well19937c(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-2166), Double.POSITIVE_INFINITY, (-914), chebyshevDistance0, (-2317.16), well19937c0);
      try { 
        fuzzyKMeansClusterer0.cluster((Collection<DoublePoint>) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math3.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint((DoublePoint) null);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Well512a well512a0 = new Well512a();
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 3593.619, 1, chebyshevDistance0, (-926.7396677443127), well512a0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Well19937a well19937a0 = new Well19937a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-621), 4.817319356453926E214, (-621), (DistanceMeasure) null, 265.87, well19937a0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>((Clusterable) null);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Well512a well512a0 = new Well512a();
      int[] intArray0 = new int[4];
      DoublePoint doublePoint1 = new DoublePoint(intArray0);
      centroidCluster0.addPoint(doublePoint1);
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 3593.619, 1, chebyshevDistance0, (-926.7396677443127), well512a0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(714, 0.0, 0, chebyshevDistance0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-467), (-467));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -467 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Well512a well512a0 = new Well512a();
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 3593.619, 1, chebyshevDistance0, 1, well512a0);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.cluster(list0);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertFalse(list1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(280, 280);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (280)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1306, 1306, 1306, euclideanDistance0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getObjectiveFunctionValue();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(552, 1.415745496749878, 177, euclideanDistance0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 3593.619);
      double[] doubleArray0 = new double[10];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.linear.MatrixUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a((long) 524);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(524, 0.0, (-395), chebyshevDistance0, 0.0, well1024a0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1306, 1306, 1306, euclideanDistance0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1306.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1306, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1306, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1306, 1306, 1306, euclideanDistance0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(0.001, double0, 0.01);
      assertEquals(1306, fuzzyKMeansClusterer0.getK());
      assertEquals(1306.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1306, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1306, 1306, 1306, euclideanDistance0);
      fuzzyKMeansClusterer0.getDataPoints();
      assertEquals(1306, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1306.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1306, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(280, 280);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(280, int0);
      assertEquals(280.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 3593.1073858856503);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-1), int0);
      assertEquals(3593.1073858856503, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-3021), 1807.0);
      double double0 = fuzzyKMeansClusterer0.getFuzziness();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-3021), fuzzyKMeansClusterer0.getK());
      assertEquals(1807.0, double0, 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 3593.619);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(3593.619, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }
}
