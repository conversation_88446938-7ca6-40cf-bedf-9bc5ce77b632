/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 22:04:33 GMT 2019
 */

package org.apache.commons.math3.ml.clustering;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.ml.clustering.CentroidCluster;
import org.apache.commons.math3.ml.clustering.DoublePoint;
import org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer;
import org.apache.commons.math3.ml.distance.CanberraDistance;
import org.apache.commons.math3.ml.distance.ChebyshevDistance;
import org.apache.commons.math3.ml.distance.DistanceMeasure;
import org.apache.commons.math3.ml.distance.EarthMoversDistance;
import org.apache.commons.math3.ml.distance.EuclideanDistance;
import org.apache.commons.math3.ml.distance.ManhattanDistance;
import org.apache.commons.math3.random.ISAACRandom;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.Well1024a;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well44497b;
import org.apache.commons.math3.random.Well512a;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.Random;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class FuzzyKMeansClusterer_ESTest extends FuzzyKMeansClusterer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Random.setNextRandom(1);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 253.01739350301514);
      double[] doubleArray0 = new double[1];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      int[] intArray0 = new int[5];
      intArray0[0] = 1;
      DoublePoint doublePoint1 = new DoublePoint(intArray0);
      centroidCluster0.addPoint(doublePoint1);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Random.setNextRandom(1);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 253.01739350301514);
      double[] doubleArray0 = new double[1];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      int[] intArray0 = new int[5];
      intArray0[0] = 1;
      DoublePoint doublePoint1 = new DoublePoint(intArray0);
      centroidCluster0.addPoint(doublePoint1);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(253.01739350301514, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Random.setNextRandom(1);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 253.01739350301514);
      double[] doubleArray0 = new double[0];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.cluster(list0);
      assertEquals(1, list1.size());
      
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(0.0, double0, 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2146105465, 1898.375, 440, manhattanDistance0, (-1162), (RandomGenerator) null);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals((-1162.0), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1898.375, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(440, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 959L);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      fuzzyKMeansClusterer0.cluster(linkedList0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(959.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Random.setNextRandom(1);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 253.01739350301514);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      double[] doubleArray0 = new double[0];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.cluster(list0);
      assertFalse(list1.isEmpty());
      
      RealMatrix realMatrix0 = fuzzyKMeansClusterer0.getMembershipMatrix();
      assertEquals(1, realMatrix0.getColumnDimension());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well512a well512a0 = new Well512a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 948.5137536, 0, chebyshevDistance0, 3216.54389873006, well512a0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(0, int0);
      assertEquals(3216.54389873006, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(948.5137536, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      Well19937a well19937a0 = new Well19937a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(237, 237, 237, euclideanDistance0, 0.0, well19937a0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(0.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(237, int0);
      assertEquals(237.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 5118.62117149121);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, int0);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(5118.62117149121, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Well44497b well44497b0 = new Well44497b();
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-2102), 2392.94586713, 2146884795, manhattanDistance0, (-2102.0), well44497b0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals((-2102.0), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(2146884795, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals((-2102), int0);
      assertEquals(2392.94586713, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(271, 3361.1416835, 271, chebyshevDistance0);
      RandomGenerator randomGenerator0 = fuzzyKMeansClusterer0.getRandomGenerator();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer1 = new FuzzyKMeansClusterer<DoublePoint>(2471, 2471, 2471, chebyshevDistance0, 0.0, randomGenerator0);
      double double0 = fuzzyKMeansClusterer1.getEpsilon();
      assertEquals(2471, fuzzyKMeansClusterer1.getMaxIterations());
      assertEquals(0.0, double0, 0.01);
      assertEquals(2471.0, fuzzyKMeansClusterer1.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      Well1024a well1024a0 = new Well1024a((long) (-882));
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-882), 2.0, (-882), canberraDistance0, (-882), well1024a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals((-882), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-882.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1775.3211694639594, 0, earthMoversDistance0);
      assertEquals(0, fuzzyKMeansClusterer0.getK());
      
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      fuzzyKMeansClusterer0.cluster(linkedList0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1775.3211694639594, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Random.setNextRandom(1);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 253.01739350301514);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      double[] doubleArray0 = new double[0];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.getClusters();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(253.01739350301514, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1, list1.size());
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      int[] intArray0 = new int[3];
      ISAACRandom iSAACRandom0 = new ISAACRandom(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1558.1995535917, (-2817), canberraDistance0, 4.9E-324, iSAACRandom0);
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math3.util.MathArrays", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      Well1024a well1024a0 = new Well1024a((long) (-882));
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-882), 2.0, (-882), canberraDistance0, (-882), well1024a0);
      try { 
        fuzzyKMeansClusterer0.cluster((Collection<DoublePoint>) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math3.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Random.setNextRandom(1);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, Double.POSITIVE_INFINITY);
      double[] doubleArray0 = new double[1];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      centroidCluster0.addPoint((DoublePoint) null);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1), 3173.4199067283);
      double[] doubleArray0 = new double[7];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(Integer.MAX_VALUE, 0.0, (-1), chebyshevDistance0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-42), (-42));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -42 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1958, 1958);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1,958)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Random.setNextRandom(1);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, Double.POSITIVE_INFINITY);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      double[] doubleArray0 = new double[1];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(577, 577, 498, (DistanceMeasure) null);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getObjectiveFunctionValue();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(574, 574, 574, canberraDistance0, 574, (RandomGenerator) null);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1775.321);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      fuzzyKMeansClusterer0.cluster(linkedList0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.linear.MatrixUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well19937a well19937a0 = new Well19937a(0L);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1214, 0.0, 1420, chebyshevDistance0, 0.0, well19937a0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, Double.POSITIVE_INFINITY);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(Double.POSITIVE_INFINITY, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(574, 574, 574, canberraDistance0, 574, (RandomGenerator) null);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(574.0, double0, 0.01);
      assertEquals(574, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(574.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(574, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 959L);
      fuzzyKMeansClusterer0.getDataPoints();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getK());
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(959.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1958, 1958);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(1958, int0);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1958.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1775.321);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals((-1), int0);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1775.321, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 253.01739350301514);
      double double0 = fuzzyKMeansClusterer0.getFuzziness();
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(253.01739350301514, double0, 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }
}
