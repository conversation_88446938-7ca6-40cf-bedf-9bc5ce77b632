/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 22:06:44 GMT 2019
 */

package org.apache.commons.math3.ml.clustering;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import org.apache.commons.math3.ml.clustering.CentroidCluster;
import org.apache.commons.math3.ml.clustering.DoublePoint;
import org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer;
import org.apache.commons.math3.ml.distance.CanberraDistance;
import org.apache.commons.math3.ml.distance.ChebyshevDistance;
import org.apache.commons.math3.ml.distance.EarthMoversDistance;
import org.apache.commons.math3.ml.distance.EuclideanDistance;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.SynchronizedRandomGenerator;
import org.apache.commons.math3.random.Well1024a;
import org.apache.commons.math3.random.Well19937a;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.Random;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class FuzzyKMeansClusterer_ESTest extends FuzzyKMeansClusterer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      double[] doubleArray0 = new double[5];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      int[] intArray0 = new int[9];
      intArray0[0] = (-2146824101);
      DoublePoint doublePoint1 = new DoublePoint(intArray0);
      centroidCluster0.addPoint(doublePoint1);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Random.setNextRandom(1);
      fuzzyKMeansClusterer0.cluster(list0);
      assertEquals(941.9238867934744, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      double[] doubleArray0 = new double[5];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Random.setNextRandom(1);
      fuzzyKMeansClusterer0.cluster(list0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(941.9238867934744, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 11.9, 0, canberraDistance0);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(11.9, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1570), 3620.1875, (-1570), canberraDistance0, (-1570), (RandomGenerator) null);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals((-1570.0), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-1570), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(3620.1875, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      Well1024a well1024a0 = new Well1024a(0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 763.17834744, 0, euclideanDistance0, 1938.25937808137, well1024a0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(763.17834744, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0, int0);
      assertEquals(1938.25937808137, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      Well19937a well19937a0 = new Well19937a((-147L));
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(Integer.MAX_VALUE, 1834.48737388, Integer.MAX_VALUE, canberraDistance0, 0.0, well19937a0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(1834.48737388, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(Integer.MAX_VALUE, int0);
      assertEquals(0.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a();
      SynchronizedRandomGenerator synchronizedRandomGenerator0 = new SynchronizedRandomGenerator(well1024a0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2762, 2762, 2762, chebyshevDistance0, 2762, synchronizedRandomGenerator0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(2762.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(2762, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2762, int0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-2963), Double.POSITIVE_INFINITY);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-2963), int0);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(Double.POSITIVE_INFINITY, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(4362, 4362, 1616, chebyshevDistance0, 0.0, (RandomGenerator) null);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(0.0, double0, 0.01);
      assertEquals(1616, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(4362.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Well1024a well1024a0 = new Well1024a();
      SynchronizedRandomGenerator synchronizedRandomGenerator0 = new SynchronizedRandomGenerator(well1024a0);
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1307152932, 1307152932, (-4342), earthMoversDistance0, (-1.0624985110080394E-7), synchronizedRandomGenerator0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals((-4342), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals((-1.0624985110080394E-7), double0, 0.01);
      assertEquals(1.307152932E9, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 922.9);
      double[] doubleArray0 = new double[5];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(922.9, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      double[] doubleArray0 = new double[5];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Random.setNextRandom(1);
      fuzzyKMeansClusterer0.cluster(list0);
      List<CentroidCluster<DoublePoint>> list1 = fuzzyKMeansClusterer0.getClusters();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertFalse(list1.isEmpty());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 922.9);
      double[] doubleArray0 = new double[5];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math3.util.MathArrays", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1193), 1.2252256870269775);
      try { 
        fuzzyKMeansClusterer0.cluster((Collection<DoublePoint>) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math3.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      double[] doubleArray0 = new double[5];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint((DoublePoint) null);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Random.setNextRandom(1);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1463), 1.7550547122955322, (-1463), earthMoversDistance0);
      int[] intArray0 = new int[8];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      double[] doubleArray0 = new double[5];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      int[] intArray0 = new int[9];
      DoublePoint doublePoint1 = new DoublePoint(intArray0);
      centroidCluster0.addPoint(doublePoint1);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Random.setNextRandom(1);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 5
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, (-625.0), 1073, canberraDistance0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -625 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-118), (-118));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -118 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 922.9);
      double[] doubleArray0 = new double[5];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.linear.MatrixUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(3715, 3715, 3715, canberraDistance0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (3,715)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-351), 2.4875248571153216E73, (-557), euclideanDistance0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getObjectiveFunctionValue();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(577, 577, 577, earthMoversDistance0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      
      double[] doubleArray0 = new double[5];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      Random.setNextRandom(1);
      fuzzyKMeansClusterer0.cluster(list0);
      fuzzyKMeansClusterer0.getMembershipMatrix();
      assertEquals(941.9238867934744, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 2405.0, (-4217), chebyshevDistance0);
      RandomGenerator randomGenerator0 = fuzzyKMeansClusterer0.getRandomGenerator();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer1 = null;
      try {
        fuzzyKMeansClusterer1 = new FuzzyKMeansClusterer<DoublePoint>(0, 0, 0, chebyshevDistance0, 3304.792491, randomGenerator0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(941.9238867934744, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.001, double0, 0.01);
      assertEquals(941.9238867934744, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      fuzzyKMeansClusterer0.getDataPoints();
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(941.9238867934744, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      Well1024a well1024a0 = new Well1024a(0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 763.005289838824, 0, euclideanDistance0, 763.005289838824, well1024a0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(763.005289838824, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0, int0);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(763.005289838824, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-1), int0);
      assertEquals(941.9238867934744, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 941.9238867934744);
      double double0 = fuzzyKMeansClusterer0.getFuzziness();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(941.9238867934744, double0, 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }
}
