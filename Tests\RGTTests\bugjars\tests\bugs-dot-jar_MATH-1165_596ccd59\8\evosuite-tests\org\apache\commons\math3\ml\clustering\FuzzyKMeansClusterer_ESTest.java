/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 22:09:18 GMT 2019
 */

package org.apache.commons.math3.ml.clustering;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.ml.clustering.CentroidCluster;
import org.apache.commons.math3.ml.clustering.DoublePoint;
import org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer;
import org.apache.commons.math3.ml.distance.CanberraDistance;
import org.apache.commons.math3.ml.distance.ChebyshevDistance;
import org.apache.commons.math3.ml.distance.DistanceMeasure;
import org.apache.commons.math3.ml.distance.EuclideanDistance;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.Well1024a;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class FuzzyKMeansClusterer_ESTest extends FuzzyKMeansClusterer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(4, 899.22, 4, chebyshevDistance0, (-1703.0589734), well1024a0);
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      int[] intArray0 = new int[2];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      linkedList0.addLast(doublePoint0);
      linkedList0.add(doublePoint0);
      linkedList0.addLast(doublePoint0);
      linkedList0.add(doublePoint0);
      linkedList0.add(doublePoint0);
      List<CentroidCluster<DoublePoint>> list0 = fuzzyKMeansClusterer0.cluster(linkedList0);
      assertEquals(4, list0.size());
      
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals((-1703.0589734), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1278, 4629.5901, 1278, canberraDistance0);
      assertEquals(1278, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(4629.5901, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(489, 489);
      DistanceMeasure distanceMeasure0 = fuzzyKMeansClusterer0.getDistanceMeasure();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer1 = new FuzzyKMeansClusterer<DoublePoint>(489, 489, (-1090), distanceMeasure0, (-1090), (RandomGenerator) null);
      fuzzyKMeansClusterer1.getRandomGenerator();
      assertEquals(489.0, fuzzyKMeansClusterer1.getFuzziness(), 0.01);
      assertEquals((-1090.0), fuzzyKMeansClusterer1.getEpsilon(), 0.01);
      assertEquals(489, fuzzyKMeansClusterer1.getK());
      assertEquals((-1090), fuzzyKMeansClusterer1.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, (-1703.0589734), well1024a0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      double[] doubleArray0 = new double[6];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      linkedList0.add(doublePoint0);
      List<CentroidCluster<DoublePoint>> list0 = fuzzyKMeansClusterer0.cluster(linkedList0);
      assertFalse(list0.isEmpty());
      
      RealMatrix realMatrix0 = fuzzyKMeansClusterer0.getMembershipMatrix();
      assertEquals(1, realMatrix0.getColumnDimension());
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 899.22, 0, chebyshevDistance0, (-1703.0589734), well1024a0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(0, int0);
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1703.0589734), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 2739.33231798855);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(2739.33231798855, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), int0);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 899.22, 0, chebyshevDistance0, (-1703.0589734), well1024a0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0, int0);
      assertEquals((-1703.0589734), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1), 49.0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals((-1), int0);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(49.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 899.22, 0, chebyshevDistance0, 0, well1024a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.0, double0, 0.01);
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, (-1703.0589734), well1024a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals((-1703.0589734), double0, 0.01);
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 899.22, 0, chebyshevDistance0, 0, well1024a0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      fuzzyKMeansClusterer0.cluster(linkedList0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(0.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a();
      double[] doubleArray0 = new double[2];
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, (-1703.0589734), well1024a0);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      linkedList0.add(doublePoint0);
      fuzzyKMeansClusterer0.cluster(linkedList0);
      List<CentroidCluster<DoublePoint>> list0 = fuzzyKMeansClusterer0.getClusters();
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, list0.size());
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 899.22, 0, chebyshevDistance0, (-1703.0589734), well1024a0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      linkedList0.add(doublePoint0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math3.util.MathArrays", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(4, 899.22, 4, chebyshevDistance0, (-1703.0589734), well1024a0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      int[] intArray0 = new int[16];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      linkedList0.addLast(doublePoint0);
      linkedList0.add(doublePoint0);
      linkedList0.addLast(doublePoint0);
      linkedList0.add(doublePoint0);
      linkedList0.add(doublePoint0);
      linkedList0.add(doublePoint0);
      linkedList0.add(doublePoint0);
      linkedList0.add(doublePoint0);
      linkedList0.add(doublePoint0);
      linkedList0.add(doublePoint0);
      fuzzyKMeansClusterer0.cluster(linkedList0);
      // Undeclared exception!
      fuzzyKMeansClusterer0.cluster(linkedList0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1), 49.0);
      try { 
        fuzzyKMeansClusterer0.cluster((Collection<DoublePoint>) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math3.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, (-1703.0589734), well1024a0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      linkedList0.offerFirst((DoublePoint) null);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1), 131.665333126);
      double[] doubleArray0 = new double[1];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(4, 899.22);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      int[] intArray0 = new int[2];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      linkedList0.addLast(doublePoint0);
      linkedList0.add(doublePoint0);
      linkedList0.addLast(doublePoint0);
      intArray0[1] = 4;
      DoublePoint doublePoint1 = new DoublePoint(intArray0);
      linkedList0.add(doublePoint1);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(Integer.MAX_VALUE, (-1.0), 367, euclideanDistance0, 367, (RandomGenerator) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -1 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, (-1703.0589734), well1024a0);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      int[] intArray0 = new int[2];
      DoublePoint doublePoint0 = new DoublePoint(intArray0);
      linkedList0.addLast(doublePoint0);
      intArray0[1] = 1;
      DoublePoint doublePoint1 = new DoublePoint(intArray0);
      linkedList0.add(doublePoint1);
      fuzzyKMeansClusterer0.cluster(linkedList0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(0.5, double0, 0.01);
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, (-1703.0589734), well1024a0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, 899.22, well1024a0);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      double[] doubleArray0 = new double[6];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      linkedList0.add(doublePoint0);
      fuzzyKMeansClusterer0.cluster(linkedList0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(0.0, double0, 0.01);
      assertEquals(899.22, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(14, 14);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getObjectiveFunctionValue();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(35, 35);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 899.22);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      fuzzyKMeansClusterer0.cluster(linkedList0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.linear.MatrixUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1621), (-1492.2009), (-1621), (DistanceMeasure) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -1,492.201 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a(1);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, 1, well1024a0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, 899.22, well1024a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(899.22, double0, 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, (-1703.0589734), well1024a0);
      fuzzyKMeansClusterer0.getDataPoints();
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1703.0589734), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, 899.22, well1024a0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(1, int0);
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(899.22, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, 899.22, well1024a0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(899.22, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, int0);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[4];
      Well1024a well1024a0 = new Well1024a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, 899.22, well1024a0);
      double double0 = fuzzyKMeansClusterer0.getFuzziness();
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(899.22, double0, 0.01);
      assertEquals(899.22, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well1024a well1024a0 = new Well1024a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 899.22, 1, chebyshevDistance0, (-1703.0589734), well1024a0);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals(1, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(899.22, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1, fuzzyKMeansClusterer0.getK());
      assertEquals((-1703.0589734), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }
}
