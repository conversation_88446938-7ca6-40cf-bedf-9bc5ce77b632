/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 22:11:17 GMT 2019
 */

package org.apache.commons.math3.ml.clustering;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import org.apache.commons.math3.ml.clustering.CentroidCluster;
import org.apache.commons.math3.ml.clustering.DoublePoint;
import org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer;
import org.apache.commons.math3.ml.distance.CanberraDistance;
import org.apache.commons.math3.ml.distance.ChebyshevDistance;
import org.apache.commons.math3.ml.distance.EarthMoversDistance;
import org.apache.commons.math3.ml.distance.EuclideanDistance;
import org.apache.commons.math3.ml.distance.ManhattanDistance;
import org.apache.commons.math3.random.ISAACRandom;
import org.apache.commons.math3.random.RandomGenerator;
import org.apache.commons.math3.random.Well1024a;
import org.apache.commons.math3.random.Well19937a;
import org.apache.commons.math3.random.Well19937c;
import org.apache.commons.math3.random.Well44497b;
import org.apache.commons.math3.random.Well512a;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class FuzzyKMeansClusterer_ESTest extends FuzzyKMeansClusterer_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-6430), 15.719948, 2590, chebyshevDistance0);
      assertEquals((-6430), fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(15.719948, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(2590, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-4739), (-4739));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -4,739 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(2175, 2175, 2175, chebyshevDistance0, 1536.06183, (RandomGenerator) null);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals(2175, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(2175.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1536.06183, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(2175, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      Well44497b well44497b0 = new Well44497b(0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 2.499312400817871, 0, chebyshevDistance0, 7.241626420445137E-206, well44497b0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(0, int0);
      assertEquals(2.499312400817871, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(7.241626420445137E-206, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-2634), 135.0, (-2634), chebyshevDistance0, 2039.831, iSAACRandom0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(135.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(2039.831, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-2634), int0);
      assertEquals((-2634), fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[5];
      Well19937a well19937a0 = new Well19937a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1497.8030486752102, 0, chebyshevDistance0, (-1641.4582152552557), well19937a0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals((-1641.4582152552557), fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0, int0);
      assertEquals(1497.8030486752102, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      Well19937c well19937c0 = new Well19937c();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-1245), 478.0641, (-1245), earthMoversDistance0, 1551.0092631, well19937c0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(478.0641, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals((-1245), int0);
      assertEquals(1551.0092631, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals((-1245), fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well1024a well1024a0 = new Well1024a((int[]) null);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1979, 1979, 0, manhattanDistance0, 0, well1024a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(1979.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.0, double0, 0.01);
      assertEquals(1979, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      int[] intArray0 = new int[5];
      Well19937a well19937a0 = new Well19937a(intArray0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1497.8030486752102, 0, chebyshevDistance0, (-1641.4582152552557), well19937a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals((-1641.4582152552557), double0, 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1497.8030486752102, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, Double.POSITIVE_INFINITY, 0, euclideanDistance0);
      double[] doubleArray0 = new double[0];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(Double.POSITIVE_INFINITY, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      Well19937a well19937a0 = new Well19937a((-409));
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1016, 277.05096386094533, 1016, euclideanDistance0, 1016, well19937a0);
      try { 
        fuzzyKMeansClusterer0.cluster((Collection<DoublePoint>) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math3.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      ChebyshevDistance chebyshevDistance0 = new ChebyshevDistance();
      ISAACRandom iSAACRandom0 = new ISAACRandom((int[]) null);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-2634), 135.0, (-2634), chebyshevDistance0, 2039.831, iSAACRandom0);
      double[] doubleArray0 = new double[3];
      DoublePoint doublePoint0 = new DoublePoint(doubleArray0);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(240, 0.0, 738, canberraDistance0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      CanberraDistance canberraDistance0 = new CanberraDistance();
      Well512a well512a0 = new Well512a();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = null;
      try {
        fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>((-3456), (-3063), (-2195), canberraDistance0, 0.0, well512a0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // -3,063 is smaller than, or equal to, the minimum (1)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1, 1027.9802497162448);
      DoublePoint doublePoint0 = new DoublePoint((double[]) null);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      centroidCluster0.addPoint(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.cluster(list0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math3.util.MathArrays", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(464, 464, 464, earthMoversDistance0);
      LinkedList<DoublePoint> linkedList0 = new LinkedList<DoublePoint>();
      try { 
        fuzzyKMeansClusterer0.cluster(linkedList0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 0 is smaller than, or equal to, the minimum (464)
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      Well19937a well19937a0 = new Well19937a(0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1027.9802497162448, 0, euclideanDistance0, 0, well19937a0);
      assertEquals(0, fuzzyKMeansClusterer0.getK());
      
      DoublePoint doublePoint0 = new DoublePoint((double[]) null);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      double double0 = fuzzyKMeansClusterer0.getObjectiveFunctionValue();
      assertEquals(1027.9802497162448, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(0, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(0.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1891, 1891, 1891, earthMoversDistance0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getObjectiveFunctionValue();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1902, 1902);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state
         //
         verifyException("org.apache.commons.math3.ml.clustering.FuzzyKMeansClusterer", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      Well19937a well19937a0 = new Well19937a(0);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(0, 1027.9802497162448, 0, euclideanDistance0, 0, well19937a0);
      DoublePoint doublePoint0 = new DoublePoint((double[]) null);
      CentroidCluster<DoublePoint> centroidCluster0 = new CentroidCluster<DoublePoint>(doublePoint0);
      List<DoublePoint> list0 = centroidCluster0.getPoints();
      fuzzyKMeansClusterer0.cluster(list0);
      // Undeclared exception!
      try { 
        fuzzyKMeansClusterer0.getMembershipMatrix();
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math3.linear.MatrixUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(464, 464, 464, earthMoversDistance0);
      fuzzyKMeansClusterer0.getClusters();
      assertEquals(464, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(464, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(464.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      ManhattanDistance manhattanDistance0 = new ManhattanDistance();
      Well1024a well1024a0 = new Well1024a((int[]) null);
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1961, 1961, 1961, manhattanDistance0, 1961, well1024a0);
      double double0 = fuzzyKMeansClusterer0.getEpsilon();
      assertEquals(1961.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1961, fuzzyKMeansClusterer0.getK());
      assertEquals(1961.0, double0, 0.01);
      assertEquals(1961, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      EuclideanDistance euclideanDistance0 = new EuclideanDistance();
      Well19937a well19937a0 = new Well19937a((-409));
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1016, 277.05096386094533, 1016, euclideanDistance0, 1016, well19937a0);
      fuzzyKMeansClusterer0.getDataPoints();
      assertEquals(1016, fuzzyKMeansClusterer0.getK());
      assertEquals(277.05096386094533, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1016, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1016.0, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(464, 464, 464, earthMoversDistance0);
      int int0 = fuzzyKMeansClusterer0.getK();
      assertEquals(464, int0);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(464, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(464.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1891, 1891, 1891, earthMoversDistance0);
      int int0 = fuzzyKMeansClusterer0.getMaxIterations();
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1891, int0);
      assertEquals(1891.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
      assertEquals(1891, fuzzyKMeansClusterer0.getK());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1891, 1891, 1891, earthMoversDistance0);
      double double0 = fuzzyKMeansClusterer0.getFuzziness();
      assertEquals(1891.0, double0, 0.01);
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1891, fuzzyKMeansClusterer0.getK());
      assertEquals(1891, fuzzyKMeansClusterer0.getMaxIterations());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      EarthMoversDistance earthMoversDistance0 = new EarthMoversDistance();
      FuzzyKMeansClusterer<DoublePoint> fuzzyKMeansClusterer0 = new FuzzyKMeansClusterer<DoublePoint>(1891, 1891, 1891, earthMoversDistance0);
      fuzzyKMeansClusterer0.getRandomGenerator();
      assertEquals(1891, fuzzyKMeansClusterer0.getMaxIterations());
      assertEquals(1891, fuzzyKMeansClusterer0.getK());
      assertEquals(0.001, fuzzyKMeansClusterer0.getEpsilon(), 0.01);
      assertEquals(1891.0, fuzzyKMeansClusterer0.getFuzziness(), 0.01);
  }
}
