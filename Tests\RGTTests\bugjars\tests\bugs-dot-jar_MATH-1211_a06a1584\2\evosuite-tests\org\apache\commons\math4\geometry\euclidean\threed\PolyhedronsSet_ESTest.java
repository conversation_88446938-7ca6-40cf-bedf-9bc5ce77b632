/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 10:08:15 GMT 2019
 */

package org.apache.commons.math4.geometry.euclidean.threed;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math4.geometry.euclidean.threed.Euclidean3D;
import org.apache.commons.math4.geometry.euclidean.threed.Line;
import org.apache.commons.math4.geometry.euclidean.threed.Plane;
import org.apache.commons.math4.geometry.euclidean.threed.PolyhedronsSet;
import org.apache.commons.math4.geometry.euclidean.threed.Rotation;
import org.apache.commons.math4.geometry.euclidean.threed.RotationOrder;
import org.apache.commons.math4.geometry.euclidean.threed.SubPlane;
import org.apache.commons.math4.geometry.euclidean.threed.Vector3D;
import org.apache.commons.math4.geometry.euclidean.twod.Euclidean2D;
import org.apache.commons.math4.geometry.euclidean.twod.PolygonsSet;
import org.apache.commons.math4.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math4.geometry.partitioning.BSPTree;
import org.apache.commons.math4.geometry.partitioning.SubHyperplane;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class PolyhedronsSet_ESTest extends PolyhedronsSet_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-2803.54972), 1755.4085, 1755.4085, 1755.4085, 0.0, (-2803.54972), (-2803.54972));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1206.0041469), 0.0, 3.0, 3.0, 0.0, (-1891.0672655228), 0.0);
      assertEquals(0.0, polyhedronsSet0.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(950.63719442, 950.63719442, 950.63719442, 1838.3903516637847, (-2568.22), 4.0, 0.0);
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
      RotationOrder rotationOrder0 = RotationOrder.YXZ;
      Vector3D vector3D0 = rotationOrder0.getA2();
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertTrue(polyhedronsSet1.isFull());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, Double.POSITIVE_INFINITY);
      Vector3D vector3D0 = Vector3D.MINUS_I;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
      RotationOrder rotationOrder0 = RotationOrder.YXZ;
      Vector3D vector3D0 = rotationOrder0.getA2();
      Rotation rotation0 = new Rotation(0.0, 0.0, 0.0, 0.0, false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertNotSame(polyhedronsSet1, polyhedronsSet0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-992.259264), 4.0, (-1176.279559370206), (-992.259264), 0.001, (-1176.279559370206), 0.001);
      Vector3D vector3D0 = new Vector3D(4.0, 1.0, 1.0);
      Rotation rotation0 = new Rotation(vector3D0, vector3D0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertTrue(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1.0), 1780.8665118, 1780.8665118, 1780.8665118, (-973.8377), (-1.0), (-1.0));
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      BSPTree<Euclidean3D> bSPTree1 = bSPTree0.getCell(vector3D0, 0.0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree1);
      polyhedronsSet1.translate(vector3D0);
      assertFalse(polyhedronsSet0.isEmpty());
      assertNotSame(bSPTree0, bSPTree1);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Vector3D vector3D0 = new Vector3D(1669.521327, 1669.521327, 1669.521327);
      Plane plane0 = new Plane(vector3D0, 1669.521327);
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertEquals(1669.521327, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(3.141592653489793, (-982.061302), 0.9999999999, (-2651.311), (-982.061302), (-2651.311), (-1.0));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      Vector3D vector3D0 = Vector3D.PLUS_J;
      // Undeclared exception!
      try { 
        polyhedronsSet1.translate(vector3D0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.NaN;
      Plane plane0 = new Plane(vector3D0, 0.0);
      Vector2D vector2D0 = Vector2D.NaN;
      org.apache.commons.math4.geometry.euclidean.twod.Line line0 = new org.apache.commons.math4.geometry.euclidean.twod.Line(vector2D0, vector2D0, 0.0);
      PolygonsSet polygonsSet0 = line0.wholeSpace();
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
      Object object0 = new Object();
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>(object0);
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, object0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree1);
      // Undeclared exception!
      try { 
        polyhedronsSet1.translate(vector3D0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.Object cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null, 1485);
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate((Vector3D) null, (Rotation) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.MINUS_K;
      Plane plane0 = new Plane(vector3D0, vector3D0, (-88.7));
      SubPlane subPlane0 = plane0.wholeHyperplane();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(1807.0, 6.123233995736766E-17, 988.16, 6.123233995736766E-17, 988.16, 1807.0, 1807.0);
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, subPlane0);
      PolyhedronsSet polyhedronsSet1 = new PolyhedronsSet(bSPTree1, 6.123233995736766E-17);
      Rotation rotation0 = new Rotation(vector3D0, vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.rotate(vector3D0, rotation0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.euclidean.threed.SubPlane cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-146.74173875811155), (-146.74173875811155), 1274.8, 0.25, (-146.74173875811155), 1214.62484242, (-1853.4));
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, (-1853.4));
      Line line0 = plane0.intersection(plane0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, line0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.Characterization", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);
      Vector3D vector3D0 = new Vector3D(0.43039572100531, 1755.4085);
      // Undeclared exception!
      try { 
        polyhedronsSet0.firstIntersection(vector3D0, (Line) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.threed.Plane", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(3953.761687696, 3953.761687696, 3068.02484242, 3068.02484242, (-3418.092087), 3068.02484242, 1268.4528992252922);
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      Object object0 = new Object();
      Euclidean2D euclidean2D0 = Euclidean2D.getInstance();
      BSPTree<Euclidean3D> bSPTree1 = bSPTree0.pruneAroundConvexCell(object0, euclidean2D0, polyhedronsSet0);
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 3953.761687696;
      Vector3D vector3D0 = new Vector3D(doubleArray0);
      Plane plane0 = new Plane(vector3D0, 1268.4528992252922);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      BSPTree<Euclidean3D> bSPTree2 = bSPTree1.split(subPlane0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree2);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, (Line) null);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Vector3D vector3D1 = Vector3D.MINUS_J;
      Plane plane0 = new Plane(vector3D1, 4458.27268);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      Plane plane1 = new Plane(vector3D0, vector3D0, (-431.68351627));
      SubPlane subPlane1 = plane1.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane1);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 1.114443679040054);
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: IndexOutOfBoundsException");
      
      } catch(IndexOutOfBoundsException e) {
         //
         // Index: 0, Size: 0
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, (-1559.42742));
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      SubPlane subPlane0 = plane0.wholeHyperplane();
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>(subPlane0);
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, bSPTree0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree1);
      // Undeclared exception!
      try { 
        polyhedronsSet1.computeGeometricalProperties();
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet((Collection<SubHyperplane<Euclidean3D>>) null, 1.0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1.0), 1780.8665118, 1780.8665118, 1780.8665118, (-973.8377), (-1.0), (-1.0));
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.computeGeometricalProperties();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.Characterization", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, (-2803.5497194));
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Rotation rotation0 = new Rotation((-1381.19306), 0.0, 0.0, 1755.4085, false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, vector3D0, (-431.68351627));
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 1.114443679040054);
      Vector3D vector3D1 = Vector3D.MINUS_K;
      Line line0 = new Line(vector3D0, vector3D1, (-320.96611016));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertFalse(subHyperplane0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1627.8736756112157));
      assertTrue(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-0.70710678118655), 4.0, 4.0, 4.0, 4.0, 4.0, (-0.70710678118655));
      Vector3D vector3D0 = Vector3D.MINUS_K;
      Vector3D vector3D1 = Vector3D.crossProduct(vector3D0, vector3D0);
      Line line0 = new Line(vector3D0, vector3D1, (-0.70710678118655));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, (-7.333667393392865));
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.offerLast(subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, (-7.333667393392865));
      Vector3D vector3D1 = Vector3D.MINUS_K;
      Line line0 = new Line(vector3D0, vector3D1, (-320.96611016));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertFalse(subHyperplane0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);
      RotationOrder rotationOrder0 = RotationOrder.ZXZ;
      Vector3D vector3D0 = rotationOrder0.getA1();
      Vector3D vector3D1 = Vector3D.MINUS_K;
      Line line0 = new Line(vector3D0, vector3D1, 1.625);
      polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, vector3D0, 1.114443679040054);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      Plane plane1 = new Plane(vector3D0, 1.114443679040054);
      SubPlane subPlane1 = plane1.wholeHyperplane();
      linkedList0.offerLast(subPlane1);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 1.114443679040054);
      Vector3D vector3D1 = Vector3D.MINUS_K;
      Line line0 = new Line(vector3D0, vector3D1, 1.114443679040054);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertFalse(subHyperplane0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, vector3D0, 1.114443679040054);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      Plane plane1 = new Plane(vector3D0, 1.114443679040054);
      SubPlane subPlane1 = plane1.wholeHyperplane();
      linkedList0.offerLast(subPlane1);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 1.114443679040054);
      Vector3D vector3D1 = Vector3D.MINUS_K;
      Line line0 = new Line(vector3D0, vector3D1, 1.114443679040054);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertFalse(linkedList0.contains(subHyperplane0));
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 1755.4085, (-2803.5497194), 0.0, 0.0, 1755.4085, 0.0);
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.twod.Line", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, vector3D0, (-431.68351627));
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      Plane plane1 = new Plane(vector3D0, (-7.333667393392865));
      SubPlane subPlane1 = plane1.wholeHyperplane();
      linkedList0.offerLast(subPlane1);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, (-7.333667393392865));
      polyhedronsSet0.computeGeometricalProperties();
      assertEquals((-7.333667393392865), polyhedronsSet0.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(1275.5146436923565, 1275.5146436923565, 1275.5146436923565, (-1843.7117338872488), (-1843.7117338872488), 1275.5146436923565, (-1843.7117338872488));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, (-2803.5497194));
      Vector3D vector3D0 = Vector3D.PLUS_J;
      Rotation rotation0 = new Rotation(0.0, 0.0, 0.0, 1755.4085, false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, (-7.333667393392865));
      polyhedronsSet0.computeGeometricalProperties();
      assertTrue(polyhedronsSet0.isFull());
  }
}
