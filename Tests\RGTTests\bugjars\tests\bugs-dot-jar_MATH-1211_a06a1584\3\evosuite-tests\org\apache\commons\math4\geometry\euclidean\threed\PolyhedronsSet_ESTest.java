/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 10:10:19 GMT 2019
 */

package org.apache.commons.math4.geometry.euclidean.threed;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math4.geometry.euclidean.threed.Euclidean3D;
import org.apache.commons.math4.geometry.euclidean.threed.Line;
import org.apache.commons.math4.geometry.euclidean.threed.Plane;
import org.apache.commons.math4.geometry.euclidean.threed.PolyhedronsSet;
import org.apache.commons.math4.geometry.euclidean.threed.Rotation;
import org.apache.commons.math4.geometry.euclidean.threed.RotationOrder;
import org.apache.commons.math4.geometry.euclidean.threed.SubPlane;
import org.apache.commons.math4.geometry.euclidean.threed.Vector3D;
import org.apache.commons.math4.geometry.euclidean.twod.Euclidean2D;
import org.apache.commons.math4.geometry.euclidean.twod.PolygonsSet;
import org.apache.commons.math4.geometry.partitioning.AbstractSubHyperplane;
import org.apache.commons.math4.geometry.partitioning.BSPTree;
import org.apache.commons.math4.geometry.partitioning.SubHyperplane;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class PolyhedronsSet_ESTest extends PolyhedronsSet_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-2714.336999790993), (-3.141592653589793), (-2714.336999790993), 2597.87, (-2714.336999790993), (-1697.9599429782756), 1.0);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(1.0, 0.0, 0.0, 1.0, 0.0, (-1737.38258615), (-1737.38258615));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-2093.0), 724.3, 0.0, (-2093.0), (-2093.0), 0.0, (-2093.0));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
      Vector3D vector3D0 = Vector3D.MINUS_J;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree0, 855.18371);
      Vector3D vector3D0 = new Vector3D(855.18371, 2067.7290238971977, 855.18371);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertNotSame(polyhedronsSet0, polyhedronsSet1);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, (-0.1), (-0.1), 0.0, (-0.1), 0.0);
      Vector3D vector3D0 = Vector3D.NaN;
      Rotation rotation0 = new Rotation(vector3D0, vector3D0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertTrue(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 3.141592653489793);
      Vector3D vector3D0 = Vector3D.MINUS_K;
      Rotation rotation0 = Rotation.IDENTITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertEquals(3.141592653489793, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 3321.517910090334, 0.0, 0.0, 3321.517910090334, (-1032.00198966));
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertTrue(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      RotationOrder rotationOrder0 = RotationOrder.XYX;
      Vector3D vector3D0 = rotationOrder0.getA2();
      Plane plane0 = new Plane(vector3D0, vector3D0, (-1711.13433646));
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertFalse(polyhedronsSet1.equals((Object)polyhedronsSet0));
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertEquals(0.0, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null, 2181.0495977044216);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      assertFalse(polyhedronsSet1.equals((Object)polyhedronsSet0));
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null, (-2722.484060332));
      RotationOrder rotationOrder0 = RotationOrder.ZYX;
      Vector3D vector3D0 = rotationOrder0.getA2();
      // Undeclared exception!
      try { 
        polyhedronsSet0.translate(vector3D0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>(vector3D0);
      Plane plane0 = new Plane(vector3D0, 0.0);
      bSPTree0.insertCut(plane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree0, (-3428.534300291));
      // Undeclared exception!
      try { 
        polyhedronsSet0.translate(vector3D0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.euclidean.threed.Vector3D cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-26.797642517148358), (-26.797642517148358), (-26.797642517148358), (-26.797642517148358), (-26.797642517148358), (-26.797642517148358), (-26.797642517148358));
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Vector3D vector3D1 = new Vector3D(3.4028234663852886E38, vector3D0);
      Rotation rotation0 = new Rotation(3.4028234663852886E38, 0.6409968137741089, (-1216.92528980432), (-1007.3105140391), true);
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D1, rotation0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // non-invertible affine transform collapses some lines into single points
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.twod.Line$LineTransform", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3757.667039), (-2015.32820201922), (-232.15234414580752), 0.0, 0.0, 0.0, (-2055.710223081243));
      RotationOrder rotationOrder0 = RotationOrder.YZY;
      Vector3D vector3D0 = rotationOrder0.getA3();
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D0, (Rotation) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.threed.Plane", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.MINUS_K;
      Plane plane0 = new Plane(vector3D0, (-2118.799803552317));
      PolygonsSet polygonsSet0 = new PolygonsSet((-2118.799803552317), 1741.7213134116953, 0.19454771280288696, 2111.119325616315, 0.19454771280288696);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, plane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree1, 1741.7213134116953);
      Rotation rotation0 = new Rotation(vector3D0, vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D0, rotation0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.euclidean.threed.Plane cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, (-1751.0));
      Vector3D vector3D0 = Vector3D.PLUS_I;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, (Line) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.PLUS_J;
      Plane plane0 = new Plane(vector3D0, (-3564.670712374432));
      PolygonsSet polygonsSet0 = new PolygonsSet(424.5234415808888, 829.632219488478, 424.5234415808888, (-0.7071067811865475), 0.6366197723675814);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, vector3D0);
      BSPTree<Euclidean3D> bSPTree2 = bSPTree1.split(subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree2, 1399.59463796);
      // Undeclared exception!
      try { 
        polyhedronsSet0.firstIntersection(vector3D0, (Line) null);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.euclidean.threed.Vector3D cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.threed.PolyhedronsSet", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet((Collection<SubHyperplane<Euclidean3D>>) null, 2492.801251032566);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = new Vector3D(0.0, 0.0, (-396.1127));
      Plane plane0 = new Plane(vector3D0, 0.0);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      PolygonsSet polygonsSet0 = new PolygonsSet(0.0);
      BSPTree<Euclidean2D> bSPTree0 = new BSPTree<Euclidean2D>(subPlane0);
      PolygonsSet polygonsSet1 = polygonsSet0.buildNew(bSPTree0);
      AbstractSubHyperplane<Euclidean3D, Euclidean2D> abstractSubHyperplane0 = subPlane0.buildNew(plane0, polygonsSet1);
      linkedList0.offerLast(abstractSubHyperplane0);
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.euclidean.threed.SubPlane cannot be cast to java.lang.Boolean
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.twod.PolygonsSet", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1696.6), (-1696.6), (-3.4028234663852886E38), (-1696.6), (-3.4028234663852886E38), (-3.4028234663852886E38), (-3.4028234663852886E38));
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.computeGeometricalProperties();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.Characterization", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1774.82587706), (-1774.82587706), (-1774.82587706), (-1774.82587706), (-1774.82587706), (-1774.82587706), (-1774.82587706));
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Rotation rotation0 = new Rotation(vector3D0, (-1774.82587706));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.PLUS_K;
      Plane plane0 = new Plane(vector3D0, vector3D0, (-2499.0));
      LinkedList<SubHyperplane<Euclidean2D>> linkedList1 = new LinkedList<SubHyperplane<Euclidean2D>>();
      PolygonsSet polygonsSet0 = new PolygonsSet(linkedList1, 0.0);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 5025.9747265);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1.0));
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.PLUS_K;
      Vector3D vector3D1 = new Vector3D(5025.9747265, vector3D0);
      Plane plane0 = new Plane(vector3D1, vector3D1, (-2499.0));
      LinkedList<SubHyperplane<Euclidean2D>> linkedList1 = new LinkedList<SubHyperplane<Euclidean2D>>();
      PolygonsSet polygonsSet0 = new PolygonsSet(linkedList1, 0.0);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      Plane plane1 = new Plane(vector3D0, vector3D0, 5025.9747265);
      SubPlane subPlane1 = plane1.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane1);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 5025.9747265);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, (Line) null);
      assertFalse(linkedList0.contains(subHyperplane0));
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3253.7332126), 425.28837, (-3253.7332126), (-1697.9599429782756), (-1697.9599429782756), 425.28837, 425.28837);
      RotationOrder rotationOrder0 = RotationOrder.ZYX;
      Vector3D vector3D0 = rotationOrder0.getA3();
      Vector3D vector3D1 = Vector3D.MINUS_K;
      Line line0 = new Line(vector3D1, vector3D0, (-3253.7332126));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertFalse(polyhedronsSet0.isEmpty());
      assertNull(subHyperplane0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3253.7332126), 425.28837025238863, (-3253.7332126), 425.28837025238863, (-1697.9599429782756), 425.28837025238863, (-3253.7332126));
      RotationOrder rotationOrder0 = RotationOrder.XYX;
      Vector3D vector3D0 = rotationOrder0.getA3();
      Vector3D vector3D1 = new Vector3D((-3253.7332126), vector3D0, 425.28837025238863, vector3D0);
      Line line0 = new Line(vector3D1, vector3D0, (-3253.7332126));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertNotNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3253.7332126), 425.28837, (-3253.7332126), (-1697.9599429782756), (-1697.9599429782756), 425.28837, 425.28837);
      RotationOrder rotationOrder0 = RotationOrder.ZYX;
      Vector3D vector3D0 = rotationOrder0.getA3();
      Vector3D vector3D1 = Vector3D.MINUS_K;
      Line line0 = new Line(vector3D1, vector3D0, (-3253.7332126));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertFalse(polyhedronsSet0.isEmpty());
      assertNull(subHyperplane0);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3253.7332126), 425.28837025238863, (-3253.7332126), 425.28837025238863, (-1697.9599429782756), 425.28837025238863, (-3253.7332126));
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.PLUS_K;
      Vector3D vector3D1 = new Vector3D(5025.9747265, vector3D0);
      Plane plane0 = new Plane(vector3D1, vector3D1, (-2499.0));
      LinkedList<SubHyperplane<Euclidean2D>> linkedList1 = new LinkedList<SubHyperplane<Euclidean2D>>();
      PolygonsSet polygonsSet0 = new PolygonsSet(linkedList1, 0.0);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      Plane plane1 = new Plane(vector3D0, vector3D0, 5025.9747265);
      SubPlane subPlane1 = plane1.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane1);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 5025.9747265);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-2118.9095), 723.9985416510001, (-2118.9095), 723.9985416510001, 723.9985416510001, (-2118.9095), (-2118.9095));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate((Vector3D) null);
      assertFalse(polyhedronsSet1.isFull());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1695.8362911856634), (-1695.8362911856634), 0.3, (-1695.8362911856634), 0.3, 0.3, 0.3);
      assertFalse(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1911.8917506946), 275.431540399142, (-1911.8917506946), (-1911.8917506946), 3.0, 275.431540399142, (-1911.8917506946));
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Rotation rotation0 = Rotation.IDENTITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      Line line0 = new Line(vector3D0, vector3D0, 1216.92528980432);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, line0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.Characterization", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 5025.9747265);
      polyhedronsSet0.computeGeometricalProperties();
      assertEquals(5025.9747265, polyhedronsSet0.getTolerance(), 0.01);
  }
}
