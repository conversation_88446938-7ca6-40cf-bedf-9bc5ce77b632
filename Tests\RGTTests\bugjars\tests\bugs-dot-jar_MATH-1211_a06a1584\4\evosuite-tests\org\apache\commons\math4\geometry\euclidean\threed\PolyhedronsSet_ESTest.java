/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 10:12:26 GMT 2019
 */

package org.apache.commons.math4.geometry.euclidean.threed;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.LinkedList;
import org.apache.commons.math4.geometry.euclidean.threed.Euclidean3D;
import org.apache.commons.math4.geometry.euclidean.threed.Line;
import org.apache.commons.math4.geometry.euclidean.threed.Plane;
import org.apache.commons.math4.geometry.euclidean.threed.PolyhedronsSet;
import org.apache.commons.math4.geometry.euclidean.threed.Rotation;
import org.apache.commons.math4.geometry.euclidean.threed.RotationOrder;
import org.apache.commons.math4.geometry.euclidean.threed.SubPlane;
import org.apache.commons.math4.geometry.euclidean.threed.Vector3D;
import org.apache.commons.math4.geometry.euclidean.twod.Euclidean2D;
import org.apache.commons.math4.geometry.euclidean.twod.PolygonsSet;
import org.apache.commons.math4.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math4.geometry.partitioning.BSPTree;
import org.apache.commons.math4.geometry.partitioning.SubHyperplane;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class PolyhedronsSet_ESTest extends PolyhedronsSet_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1.0E-10), (-1.0E-10), (-1.0E-10), (-1.0E-10), (-1.0E-10), (-1.0E-10), (-1.0E-10));
      RotationOrder rotationOrder0 = RotationOrder.XYZ;
      Vector3D vector3D0 = rotationOrder0.getA1();
      Rotation rotation0 = new Rotation(vector3D0, vector3D0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 3.0, (-1.0), 1.0, 1.0, 679.5869, 1.0);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-968.59817046), (-968.59817046), (-2336.5380878), (-968.59817046), 0.0, (-2336.5380878), (-2336.5380878));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-4141.0), 0.0, 0.0, (-4141.0), 0.0, (-4141.0), (-4141.0));
      assertEquals((-4141.0), polyhedronsSet0.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0);
      assertFalse(polyhedronsSet0.isFull());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 1707.2575);
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertEquals(1707.2575, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-991.008705353965), 1.0, 460.197186, 1.0, 1896.5607508703, (-991.008705353965), 122.1779);
      Vector3D vector3D0 = Vector3D.PLUS_I;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertEquals(122.1779, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>(linkedList0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree0, 0.0);
      Vector3D vector3D0 = Vector3D.PLUS_I;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertNotSame(polyhedronsSet1, polyhedronsSet0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-991.008705353965), 1.0, 460.197186, 1.0, 1896.5607508703, (-991.008705353965), 122.1779);
      Vector3D vector3D0 = Vector3D.PLUS_I;
      Rotation rotation0 = Rotation.IDENTITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertFalse(polyhedronsSet1.isFull());
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, 0.0);
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      Rotation rotation0 = new Rotation(vector3D0, vector3D0, vector3D0, vector3D0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertTrue(polyhedronsSet1.isFull());
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertEquals(0.0, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-533.0256386211527));
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      // Undeclared exception!
      try { 
        polyhedronsSet1.translate((Vector3D) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.MINUS_K;
      Plane plane0 = new Plane(vector3D0, 75.68467);
      PolygonsSet polygonsSet0 = new PolygonsSet((-1602.808721));
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, polygonsSet0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree1, Double.POSITIVE_INFINITY);
      // Undeclared exception!
      try { 
        polyhedronsSet0.translate(vector3D0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.euclidean.twod.PolygonsSet cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((BSPTree<Euclidean3D>) null, 0.0);
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Rotation rotation0 = new Rotation(vector3D0, (-1.0));
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D0, rotation0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-726.80756994523));
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, vector3D0, (-726.80756994523));
      BSPTree<Euclidean2D> bSPTree0 = new BSPTree<Euclidean2D>();
      PolygonsSet polygonsSet0 = new PolygonsSet(bSPTree0, (-726.80756994523));
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(bSPTree0);
      BSPTree<Euclidean3D> bSPTree2 = new BSPTree<Euclidean3D>(subPlane0, bSPTree1, bSPTree1, bSPTree1);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree2);
      Rotation rotation0 = Rotation.IDENTITY;
      // Undeclared exception!
      try { 
        polyhedronsSet1.rotate(vector3D0, rotation0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.partitioning.BSPTree cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1967.0));
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      Line line0 = new Line(vector3D0, vector3D0, (-1967.0));
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, line0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.PLUS_K;
      Plane plane0 = new Plane(vector3D0, (-679.5869));
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      SubPlane subPlane0 = plane0.wholeHyperplane();
      Vector3D vector3D1 = Vector3D.NEGATIVE_INFINITY;
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>(polyhedronsSet0);
      BSPTree<Euclidean3D> bSPTree1 = bSPTree0.split(subPlane0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree1);
      Line line0 = new Line(vector3D0, vector3D1, 0.0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D1, line0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.euclidean.threed.PolyhedronsSet cannot be cast to java.lang.Boolean
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.Characterization", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.MINUS_J;
      Plane plane0 = new Plane(vector3D0, (-2583.564));
      SubPlane subPlane0 = plane0.wholeHyperplane();
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>(plane0);
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, vector3D0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(bSPTree1, (-2583.564));
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      linkedList0.add((SubHyperplane<Euclidean3D>) null);
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(linkedList0, (-730.9661006475817));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion$1", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.PLUS_K;
      Plane plane0 = new Plane(vector3D0, (-680.2681756457115));
      Vector2D[] vector2DArray0 = new Vector2D[4];
      Vector2D vector2D0 = new Vector2D(1.833049961066805, 0.0);
      vector2DArray0[0] = vector2D0;
      vector2DArray0[1] = vector2DArray0[0];
      vector2DArray0[2] = vector2DArray0[0];
      vector2DArray0[3] = vector2DArray0[1];
      PolygonsSet polygonsSet0 = new PolygonsSet(1037.2998049662, vector2DArray0);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(linkedList0, (-680.2681756457115));
        fail("Expecting exception: IndexOutOfBoundsException");
      
      } catch(IndexOutOfBoundsException e) {
         //
         // Index: 0, Size: 0
         //
         verifyException("java.util.ArrayList", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1.0E-10), (-1.0E-10), (-1.0E-10), (-1.0E-10), (-1.0E-10), (-1.0E-10), (-1.0E-10));
      RotationOrder rotationOrder0 = RotationOrder.XYZ;
      Vector3D vector3D0 = rotationOrder0.getA1();
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-3.4028234663852886E38), (-3.4028234663852886E38), (-3.4028234663852886E38), (-3.4028234663852886E38), (-3.4028234663852886E38), (-3.4028234663852886E38), (-3.4028234663852886E38));
      Vector3D vector3D0 = new Vector3D(2349.4, (-3.4028234663852886E38));
      Vector3D vector3D1 = new Vector3D(2082.0, vector3D0, 9.755392680573412E-9, vector3D0);
      Rotation rotation0 = new Rotation(vector3D1, vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet0.rotate(vector3D1, rotation0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // non-invertible affine transform collapses some lines into single points
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.twod.Line$LineTransform", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-767.7127526616), (-767.7127526616), 1382.6987282, (-767.7127526616), (-3874.148), (-767.7127526616), 70.0);
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertNotSame(polyhedronsSet0, polyhedronsSet1);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.PLUS_J;
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1.0), 3.0, (-1.0), 2.0, 2.0, 679.5869, 2.0);
      Vector3D vector3D1 = new Vector3D((-1.0), 3.0);
      Line line0 = new Line(vector3D0, vector3D1, (-1.0));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.PLUS_J;
      Plane plane0 = new Plane(vector3D0, vector3D0, 2523.6735209);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 2523.6735209);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, (Line) null);
      assertFalse(linkedList0.contains(subHyperplane0));
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.PLUS_J;
      Vector3D vector3D1 = Vector3D.crossProduct(vector3D0, vector3D0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1.0), 3.0, (-1.0), 2.0, 2.0, 679.5869, 2.0);
      Line line0 = new Line(vector3D0, vector3D1, (-1.0));
      polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.PLUS_J;
      Vector3D vector3D1 = new Vector3D((-1.0), 0.0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 3.0, (-1.0), 3.0, 0.0, 3.0, 0.0);
      Line line0 = new Line(vector3D0, vector3D1, (-1.0));
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertNotNull(subHyperplane0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 3.0, (-1.0), 3.0, 0.0, 3.0, 0.0);
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.twod.Line", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(1136.671876838687, 1136.671876838687, 1136.671876838687, 1136.671876838687, 1136.671876838687, (-441.31265438), (-441.31265438));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.MINUS_J;
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(Double.NEGATIVE_INFINITY, 1.0, (-2018.0125), 0.16666666666666666, Double.NEGATIVE_INFINITY, 4.0, 1.0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.computeGeometricalProperties();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.Characterization", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.PLUS_K;
      Plane plane0 = new Plane(vector3D0, (-679.5869));
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 2.0);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1967.0));
      polyhedronsSet0.computeGeometricalProperties();
      assertTrue(polyhedronsSet0.isFull());
  }
}
