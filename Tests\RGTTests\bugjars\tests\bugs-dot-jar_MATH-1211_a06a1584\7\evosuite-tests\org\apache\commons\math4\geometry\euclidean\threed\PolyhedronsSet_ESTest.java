/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 10:18:39 GMT 2019
 */

package org.apache.commons.math4.geometry.euclidean.threed;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import java.util.LinkedList;
import org.apache.commons.math4.geometry.Point;
import org.apache.commons.math4.geometry.euclidean.threed.Euclidean3D;
import org.apache.commons.math4.geometry.euclidean.threed.Line;
import org.apache.commons.math4.geometry.euclidean.threed.Plane;
import org.apache.commons.math4.geometry.euclidean.threed.PolyhedronsSet;
import org.apache.commons.math4.geometry.euclidean.threed.Rotation;
import org.apache.commons.math4.geometry.euclidean.threed.RotationOrder;
import org.apache.commons.math4.geometry.euclidean.threed.SubPlane;
import org.apache.commons.math4.geometry.euclidean.threed.Vector3D;
import org.apache.commons.math4.geometry.euclidean.twod.Euclidean2D;
import org.apache.commons.math4.geometry.euclidean.twod.PolygonsSet;
import org.apache.commons.math4.geometry.euclidean.twod.Vector2D;
import org.apache.commons.math4.geometry.partitioning.AbstractSubHyperplane;
import org.apache.commons.math4.geometry.partitioning.BSPTree;
import org.apache.commons.math4.geometry.partitioning.SubHyperplane;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class PolyhedronsSet_ESTest extends PolyhedronsSet_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, vector3D0, 0.7853981633974483);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.7853981633974483);
      Vector3D vector3D1 = new Vector3D(0.7853981633974483, vector3D0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D1);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, (Line) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.threed.Plane", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(1.0, 95.8904474043277, 95.8904474043277, 2107.441261144661, 740.4618957622074, 2107.441261144661, 1.0);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 1631.50335, 1400.54358, 1631.50335, 0.0, (-1566.76016), (-1566.76016));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-5525.0), (-5525.0), Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, (-78.53956366036), 0.0, (-5525.0));
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1.0E-10), (-1.0E-10), 2295.0, (-1.0E-10), (-1.0), 0.6, (-0.5));
      Vector3D vector3D0 = Vector3D.ZERO;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertTrue(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.0);
      Vector3D vector3D0 = Vector3D.MINUS_K;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertEquals(0.0, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.0, 0.0, 0.0, 0.25, (-911.0), Double.POSITIVE_INFINITY, 0.0);
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Rotation rotation0 = new Rotation(0.0, 0.0, Double.POSITIVE_INFINITY, 0.0, true);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertTrue(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.NaN;
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 1739.902619);
      Rotation rotation0 = new Rotation(vector3D0, vector3D0, vector3D0, vector3D0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      assertEquals(1739.902619, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1.0E-10), (-1.0E-10), 0.0, 0.0, 0.0, (-2212.627712), 0.0);
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertEquals(0.0, polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(1648.6251388545943);
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree0);
      assertNotSame(polyhedronsSet0, polyhedronsSet1);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 1404.926);
      Vector3D vector3D0 = Vector3D.MINUS_J;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      // Undeclared exception!
      try { 
        polyhedronsSet1.translate(vector3D0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1253.20364496));
      Vector3D vector3D0 = Vector3D.PLUS_I;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew((BSPTree<Euclidean3D>) null);
      Rotation rotation0 = Rotation.IDENTITY;
      // Undeclared exception!
      try { 
        polyhedronsSet1.rotate(vector3D0, rotation0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-6089.976209), 2476.232729745583, (-0.999999999999998), 0.0, 0.0, 1326.56, (-2820.95));
      Vector3D vector3D0 = new Vector3D((-399.8079400786), (-0.999999999999998), (-0.999999999999998));
      Plane plane0 = new Plane(vector3D0, vector3D0, 0.0);
      LinkedList<SubHyperplane<Euclidean2D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean2D>>();
      PolygonsSet polygonsSet0 = new PolygonsSet(linkedList0, (-399.8079400786));
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>("");
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, "\"kI%U");
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree1);
      Vector3D vector3D1 = new Vector3D(67.7, (-4666.205313));
      Rotation rotation0 = new Rotation(vector3D1, vector3D0, vector3D1, vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.rotate(vector3D0, rotation0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.String cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-857.7083), (-857.7083), (-857.7083), (-857.7083), (-857.7083), (-854.7273577), (-358.70505357796));
      Vector3D vector3D0 = Vector3D.NaN;
      Rotation rotation0 = Rotation.IDENTITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      Plane plane0 = new Plane(vector3D0, vector3D0, 0.902267575263977);
      Line line0 = plane0.intersection(plane0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, line0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.Characterization", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, vector3D0, 0.7853981633974483);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.7853981633974483);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, (Line) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.threed.Plane", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D0, vector3D0, 1533.18292);
      PolyhedronsSet polyhedronsSet0 = plane0.wholeSpace();
      Vector2D[] vector2DArray0 = new Vector2D[4];
      Vector2D vector2D0 = plane0.toSubSpace((Point<Euclidean3D>) vector3D0);
      vector2DArray0[0] = vector2D0;
      vector2DArray0[1] = vector2DArray0[0];
      vector2DArray0[2] = vector2D0;
      vector2DArray0[3] = vector2DArray0[0];
      PolygonsSet polygonsSet0 = new PolygonsSet(1533.18292, vector2DArray0);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, bSPTree0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree1);
      // Undeclared exception!
      try { 
        polyhedronsSet1.firstIntersection(vector3D0, (Line) null);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.partitioning.BSPTree cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.threed.PolyhedronsSet", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(233.7696192048, 233.7696192048, (-3795.178658), (-3795.178658), 2156.2, 233.7696192048, (-4770.95));
      Vector3D vector3D0 = Vector3D.MINUS_J;
      Rotation rotation0 = new Rotation(4.9E-324, 2156.2, (-964.884868908), 3383.626836948755, false);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.rotate(vector3D0, rotation0);
      PolyhedronsSet polyhedronsSet2 = polyhedronsSet1.translate(vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet2.computeGeometricalProperties();
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // zero norm
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.threed.Line", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(1817.5550649, 1817.5550649, 1817.5550649, 1817.5550649, 0.0, 0.0, 0.0);
      RotationOrder rotationOrder0 = RotationOrder.XYZ;
      Vector3D vector3D0 = rotationOrder0.getA2();
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>(vector3D0);
      Plane plane0 = new Plane(vector3D0, vector3D0, 1668.81573298);
      PolygonsSet polygonsSet0 = new PolygonsSet(1817.5550649);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree1 = bSPTree0.split(subPlane0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree1);
      // Undeclared exception!
      try { 
        polyhedronsSet1.computeGeometricalProperties();
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.euclidean.threed.Vector3D cannot be cast to java.lang.Boolean
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.Characterization", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet((Collection<SubHyperplane<Euclidean3D>>) null, 0.0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      RotationOrder rotationOrder0 = RotationOrder.YZX;
      Vector3D vector3D0 = rotationOrder0.getA2();
      Plane plane0 = new Plane(vector3D0, 0.7853981633974483);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      Vector2D vector2D0 = Vector2D.POSITIVE_INFINITY;
      org.apache.commons.math4.geometry.euclidean.twod.SubLine subLine0 = new org.apache.commons.math4.geometry.euclidean.twod.SubLine(vector2D0, vector2D0, (-1.7168146928204135));
      BSPTree<Euclidean2D> bSPTree0 = new BSPTree<Euclidean2D>();
      Object object0 = new Object();
      BSPTree<Euclidean2D> bSPTree1 = new BSPTree<Euclidean2D>(subLine0, bSPTree0, bSPTree0, object0);
      PolygonsSet polygonsSet0 = new PolygonsSet(bSPTree1, (-3169.945659));
      AbstractSubHyperplane<Euclidean3D, Euclidean2D> abstractSubHyperplane0 = subPlane0.buildNew(plane0, polygonsSet0);
      linkedList0.add((SubHyperplane<Euclidean3D>) abstractSubHyperplane0);
      PolyhedronsSet polyhedronsSet0 = null;
      try {
        polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.7853981633974483);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // java.lang.Object cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.euclidean.twod.PolygonsSet$SegmentsBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      RotationOrder rotationOrder0 = RotationOrder.YXY;
      Vector3D vector3D0 = rotationOrder0.getA3();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(Double.NaN, Double.NaN, Double.NaN, 369.4033264, (-3795.178658), (-1063.975118), 1585.251072364);
      Vector3D vector3D1 = Vector3D.MINUS_I;
      Plane plane0 = new Plane(vector3D1, 369.4033264);
      Plane plane1 = new Plane(vector3D0, vector3D0, 1.986821492305628E-8);
      Line line0 = plane0.intersection(plane1);
      polyhedronsSet0.firstIntersection(vector3D0, line0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      RotationOrder rotationOrder0 = RotationOrder.YXY;
      Vector3D vector3D1 = rotationOrder0.getA3();
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(Double.NaN, Double.NaN, Double.NaN, 369.4033264, (-3795.178658), (-1063.975118), 1585.251072364);
      Plane plane0 = new Plane(vector3D1, 369.4033264);
      Plane plane1 = new Plane(vector3D1, vector3D0, 1.986821492305628E-8);
      Line line0 = plane0.intersection(plane1);
      polyhedronsSet0.firstIntersection(vector3D1, line0);
      assertFalse(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      RotationOrder rotationOrder0 = RotationOrder.YZX;
      Vector3D vector3D0 = rotationOrder0.getA2();
      Plane plane0 = new Plane(vector3D0, vector3D0, 0.7853981633974483);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.7853981633974483);
      SubHyperplane<Euclidean3D> subHyperplane0 = polyhedronsSet0.firstIntersection(vector3D0, (Line) null);
      assertFalse(subHyperplane0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1298.4696117022963), 95.8904474043277, 95.8904474043277, 2107.441261144661, 95.8904474043277, 2107.441261144661, (-1298.4696117022963));
      // Undeclared exception!
      try { 
        polyhedronsSet0.computeGeometricalProperties();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-1277.8730798448873));
      BSPTree<Euclidean3D> bSPTree0 = polyhedronsSet0.getTree(true);
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, vector3D0, (-5.83475));
      LinkedList<SubHyperplane<Euclidean2D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean2D>>();
      PolygonsSet polygonsSet0 = new PolygonsSet(linkedList0, 0.9315965175628662);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree1 = bSPTree0.split(subPlane0);
      PolyhedronsSet polyhedronsSet1 = new PolyhedronsSet(bSPTree1, (-5.83475));
      polyhedronsSet1.computeGeometricalProperties();
      assertEquals((-5.83475), polyhedronsSet1.getTolerance(), 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-2242.48716), (-1949.0), (-2242.48716), 12.566370614359172, (-1949.0), (-2242.48716), 12.566370614359172);
      assertTrue(polyhedronsSet0.isEmpty());
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(0.7853981633974483, 0.7853981633974483, 0.7853981633974483, 0.7853981633974483, 1384.354, 0.7853981633974483, 202.72047802511);
      Vector3D vector3D0 = Vector3D.POSITIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, 1007.15200078);
      PolygonsSet polygonsSet0 = new PolygonsSet(1007.15200078, 248.8185901, 1007.15200078, 0.7853981633974483, 1007.15200078);
      SubPlane subPlane0 = new SubPlane(plane0, polygonsSet0);
      BSPTree<Euclidean3D> bSPTree0 = new BSPTree<Euclidean3D>();
      BSPTree<Euclidean3D> bSPTree1 = new BSPTree<Euclidean3D>(subPlane0, bSPTree0, bSPTree0, vector3D0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.buildNew(bSPTree1);
      // Undeclared exception!
      try { 
        polyhedronsSet1.translate(vector3D0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.commons.math4.geometry.euclidean.threed.Vector3D cannot be cast to org.apache.commons.math4.geometry.partitioning.BoundaryAttribute
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.AbstractRegion", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-2655.6), 502.5550356239, 502.5550356239, 2896.2169525606, (-942.7297171702572), 1.0, 0.1256551444530487);
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      Plane plane0 = new Plane(vector3D0, vector3D0, (-942.7297171702572));
      Line line0 = plane0.intersection(plane0);
      polyhedronsSet0.firstIntersection(vector3D0, line0);
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      assertFalse(polyhedronsSet1.isEmpty());
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet((-2655.6), 502.5550356239, 502.5550356239, 2896.2169525606, (-942.7297171702572), 1.0, 0.1256551444530487);
      Vector3D vector3D0 = Vector3D.NEGATIVE_INFINITY;
      PolyhedronsSet polyhedronsSet1 = polyhedronsSet0.translate(vector3D0);
      // Undeclared exception!
      try { 
        polyhedronsSet1.computeGeometricalProperties();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // illegal state: internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math4.geometry.partitioning.Characterization", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      LinkedList<SubHyperplane<Euclidean3D>> linkedList0 = new LinkedList<SubHyperplane<Euclidean3D>>();
      RotationOrder rotationOrder0 = RotationOrder.YZX;
      Vector3D vector3D0 = rotationOrder0.getA2();
      Plane plane0 = new Plane(vector3D0, vector3D0, 0.7853981633974483);
      SubPlane subPlane0 = plane0.wholeHyperplane();
      linkedList0.add((SubHyperplane<Euclidean3D>) subPlane0);
      PolyhedronsSet polyhedronsSet0 = new PolyhedronsSet(linkedList0, 0.7853981633974483);
      polyhedronsSet0.computeGeometricalProperties();
      assertFalse(polyhedronsSet0.isEmpty());
  }
}
