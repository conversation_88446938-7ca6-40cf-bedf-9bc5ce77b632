/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 10:33:25 GMT 2019
 */

package org.apache.commons.math4.special;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math4.special.Gamma;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class Gamma_ESTest extends Gamma_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double double0 = Gamma.gamma(2.0740270614624023);
      assertEquals(1.0335896935174114, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double double0 = Gamma.gamma(1.0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      double double0 = Gamma.gamma(Double.NEGATIVE_INFINITY);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      double double0 = Gamma.logGamma1p(1.5);
      assertEquals(0.2846828704729192, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      double double0 = Gamma.trigamma((-429.4621));
      assertEquals(10.008531116815709, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      double double0 = Gamma.digamma((-869.0));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(0.0, 0.0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(168.941, 1.011779773453775);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double double0 = Gamma.logGamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(9.926418406727737E-7, 5293.97131615393);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(Double.POSITIVE_INFINITY, 0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double double0 = Gamma.logGamma1p(1.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double double0 = Gamma.logGamma1p((-7.81295459930537E-72));
      assertEquals(4.5097597838835383E-72, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      double double0 = Gamma.logGamma1p(4.139937459513021E-8);
      assertEquals((-2.3896366123802735E-8), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double double0 = Gamma.gamma((-700.660991));
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      double double0 = Gamma.gamma((-0.04200263503409524));
      assertEquals((-24.42846567654193), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(2.175685405731201, 2.175685405731201, (-1321.316154021014), (-1995));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (-1,995) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction diverged to NaN for value \u221E
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1((-2.3896366123802735E-8));
      assertEquals((-1.3793357235411456E-8), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(0.6466736793518066);
      assertEquals(0.11158325738396549, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      double double0 = Gamma.lanczos(550);
      assertEquals(1.0204392198510357, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      double double0 = Gamma.trigamma(5711.562530184373);
      assertEquals(1.7509876527525112E-4, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(1258.23, 0.0, 2.7294301888986675E-195, 1793);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((-0.4919138160976202), (double) 861, 2813.1314, 861);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(Double.NaN, 0.0, Double.NaN, (-519));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(4.005882964287806E-150, 4.005882964287806E-150, 4.005882964287806E-150, 861);
      assertEquals(1.000000000000007, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(0.0072189432466631, (double) 468, 0.0, 468);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction convergents failed to converge (in less than 468 iterations) for value 468
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP((-1728.671119236), 2486.106414178867, 0.0, 1496);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(Double.NaN, Double.NaN, Double.NaN, 80);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      double double0 = Gamma.logGamma(480.78671983125014);
      assertEquals(2486.106414178867, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      double double0 = Gamma.logGamma(2.0458641052246094);
      assertEquals(0.0200625315645244, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      double double0 = Gamma.gamma(272.0);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      double double0 = Gamma.gamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      try { 
        Gamma.logGamma1p(3367);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 3,367 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      try { 
        Gamma.logGamma1p((-1545.0));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -1,545 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(3.141592653589793, 0.03333333333333333, Double.NaN, 1086);
      assertEquals(0.9999969210887667, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(821.11469);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 821.115 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-2407.900190427217));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -2,407.9 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      double double0 = Gamma.trigamma(4.139937459513021E-8);
      assertEquals(5.834618156084539E14, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      double double0 = Gamma.trigamma(Double.NEGATIVE_INFINITY);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      double double0 = Gamma.trigamma(0.0);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      double double0 = Gamma.trigamma(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      double double0 = Gamma.digamma(6.8716741130671986E-9);
      assertEquals((-1.4552494596110642E8), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      double double0 = Gamma.digamma(Double.POSITIVE_INFINITY);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      double double0 = Gamma.digamma(793.0496659224086);
      assertEquals(6.675255240055273, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      double double0 = Gamma.digamma(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(2.0, (-276.944838574), 1413.373267079242, (-2411));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((-891.9454060902427), Double.NaN, 0.0, 0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(2821.7, 1188.299573, (-1663.398424), 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (0) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(595.552, 0.0, 0.0, 196);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(Double.POSITIVE_INFINITY, (-258.672059), 1.679598331451416, (-3742));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(1322.386275, Double.NaN, 9.212343890163774E-4, 1086);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      double double0 = Gamma.logGamma(6.675255240055273);
      assertEquals(5.979308432861266, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(0.6196264058390855, 6.562319473965767E278, 0.6196264058390855, 550);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      double double0 = Gamma.logGamma(2.509050608571019E-106);
      assertEquals(243.1541154193745, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      double double0 = Gamma.logGamma((-1035.56));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      double double0 = Gamma.logGamma(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(418.5970955676, 597.6073684113658);
      assertEquals(0.9999999999999957, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(0.0, (-0.5));
      assertEquals(Double.NaN, double0, 0.01);
  }
}
