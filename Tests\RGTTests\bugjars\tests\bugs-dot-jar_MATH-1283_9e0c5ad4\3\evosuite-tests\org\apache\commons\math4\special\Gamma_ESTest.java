/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 10:41:35 GMT 2019
 */

package org.apache.commons.math4.special;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math4.special.Gamma;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class Gamma_ESTest extends Gamma_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double double0 = Gamma.logGamma(0.08333333333333333);
      assertEquals(2.4422973111828896, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP(0.0, 0.08333333333333333);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.logGamma1p(Double.NaN);
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(Double.NaN, 3704.3792625856795);
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.digamma((-1197.6));
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(6.067726943183879, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaP(6.067726943183879, Double.NaN);
      assertEquals(double5, double3, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertEquals(double5, double1, 0.01);
      assertEquals(double5, double2, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.trigamma(Double.NaN);
      assertEquals(double6, double2, 0.01);
      assertEquals(double6, double5, 0.01);
      assertEquals(double6, double1, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals(double6, double3, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.gamma(0.6862877607345581);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertEquals(1.3203089233517524, double7, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double double0 = Gamma.gamma(571.0);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      int int0 = (-2357);
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, 1213.1923342303946, (-2357));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction convergents failed to converge (in less than -2,357 iterations) for value \u221E
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      double double0 = Gamma.digamma((-971.645518));
      assertEquals(5.3341106912356295, double0, 0.01);
      
      double double1 = Gamma.gamma(1433.48533123259);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.lanczos(6.820161668496171E-10);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(32.946318649911646, double2, 0.01);
      
      double double3 = Gamma.logGamma1p(6.116095104481416E-9);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals((-3.53030587156854E-9), double3, 0.01);
      
      double double4 = Gamma.regularizedGammaP(0.0, 1280.2199175519);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.gamma((-399.56510591));
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(0.0, double5, 0.01);
      
      double double6 = Gamma.lanczos(Double.NaN);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertEquals(double6, double4, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertEquals(double6, double1, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.gamma(4.939449793824468E-4);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertEquals(2023.9402568123942, double7, 0.01);
      
      double double8 = Gamma.digamma(1.280502823881162E-4);
      assertNotEquals(double8, double1, 0.01);
      assertNotEquals(double8, double2, 0.01);
      assertNotEquals(double8, double5, 0.01);
      assertNotEquals(double8, double7, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertNotEquals(double8, double6, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertNotEquals(double8, double4, 0.01);
      assertEquals((-7810.009216760758), double8, 0.01);
      
      double double9 = Gamma.logGamma(5.3341106912356295);
      assertNotEquals(double9, double1, 0.01);
      assertNotEquals(double9, double8, 0.01);
      assertNotEquals(double9, double2, 0.01);
      assertNotEquals(double9, double5, 0.01);
      assertNotEquals(double9, double7, 0.01);
      assertNotEquals(double9, double6, 0.01);
      assertNotEquals(double9, double0, 0.01);
      assertNotEquals(double9, double4, 0.01);
      assertNotEquals(double9, double3, 0.01);
      assertEquals(3.693324358474189, double9, 0.01);
      
      try { 
        Gamma.logGamma1p((-99.2769));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -99.277 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(1.0E-14, 1.0E-14);
      assertEquals(3.1818991885756986E-13, double0, 0.01);
      
      double double1 = Gamma.trigamma(3.1818991885756986E-13);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(9.877048089204116E24, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ(1.0E-14, 3.1818991885756986E-13, 1.0E-14, 6);
      assertEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(2.799982468104645E-13, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP((-1.2504934821426706E-6), (-1.2504934821426706E-6));
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.digamma((-1160.5352046));
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(6.708188195420935, double4, 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      double double0 = Gamma.gamma((-103.8639965));
      assertEquals(1.385217983499965E-165, double0, 0.01);
      
      double double1 = Gamma.gamma(1.385217983499965E-165);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(7.219080404033935E164, double1, 0.01);
      
      double double2 = (-2396.4151);
      double double3 = Gamma.regularizedGammaP((-2396.4151), (-1530.1784778671554));
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.trigamma(0.0);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double4, 0.01);
      
      double double5 = 142.0;
      double double6 = Gamma.logGamma(142.0);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals(560.1690540372731, double6, 0.01);
      
      double double7 = Gamma.regularizedGammaQ((-2780.0), (-2780.0));
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertEquals(double7, double3, 0.01);
      assertEquals(Double.NaN, double7, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(1915.842107, Double.POSITIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction diverged to NaN for value \u221E
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(205.910250736617, (-240.09351692), (-1720.47), 110);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP(Double.NaN, 205.910250736617, 205.910250736617, (-65));
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ(0.0, 0.0);
      assertEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.digamma(0.0);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaP((-240.09351692), (-2299.802472331369), Double.NaN, 2008);
      assertEquals(double4, double2, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaP(205.910250736617, Double.NaN, (-1367.036600929), 110);
      assertEquals(double5, double4, 0.01);
      assertEquals(double5, double0, 0.01);
      assertEquals(double5, double1, 0.01);
      assertEquals(double5, double2, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(3402.122033284051, (-1990.53552));
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP(2081.397113, 3402.122033284051);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      double double2 = Gamma.trigamma(0.0);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double2, 0.01);
      
      double double3 = Gamma.lanczos(505.003846);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(1.0222672205084327, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaQ((-2737.1), 0.0, 0.0, 593);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(double4, double0, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.trigamma(1.4193426880442385E174);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(7.045514859966163E-175, double5, 0.01);
      
      double double6 = Gamma.logGamma(593);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals(3191.1467604677305, double6, 0.01);
      
      double double7 = Gamma.gamma(908.8735240365);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertEquals(double7, double4, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertEquals(double7, double0, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertEquals(Double.NaN, double7, 0.01);
      
      double double8 = Gamma.logGamma(Double.POSITIVE_INFINITY);
      assertNotEquals(double8, double6, 0.01);
      assertEquals(double8, double7, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertNotEquals(double8, double1, 0.01);
      assertEquals(double8, double4, 0.01);
      assertNotEquals(double8, double2, 0.01);
      assertNotEquals(double8, double5, 0.01);
      assertEquals(double8, double0, 0.01);
      assertEquals(Double.NaN, double8, 0.01);
      
      double double9 = Gamma.trigamma(568.834949176642);
      assertNotEquals(double9, double1, 0.01);
      assertNotEquals(double9, double3, 0.01);
      assertNotEquals(double9, double7, 0.01);
      assertNotEquals(double9, double8, 0.01);
      assertNotEquals(double9, double0, 0.01);
      assertEquals(double9, double5, 0.01);
      assertNotEquals(double9, double4, 0.01);
      assertNotEquals(double9, double6, 0.01);
      assertNotEquals(double9, double2, 0.01);
      assertEquals(0.0017595253352881933, double9, 0.01);
      
      double double10 = Gamma.regularizedGammaP((-524.8851), 0.0017595253352881933, (-1195.0620005006976), 593);
      assertNotEquals(double10, double3, 0.01);
      assertNotEquals(double10, double6, 0.01);
      assertNotEquals(double10, double1, 0.01);
      assertEquals(double10, double0, 0.01);
      assertNotEquals(double10, double5, 0.01);
      assertNotEquals(double10, double9, 0.01);
      assertEquals(double10, double4, 0.01);
      assertEquals(double10, double7, 0.01);
      assertNotEquals(double10, double2, 0.01);
      assertEquals(double10, double8, 0.01);
      assertEquals(Double.NaN, double10, 0.01);
      
      double double11 = Gamma.logGamma(0.0);
      assertEquals(double11, double4, 0.01);
      assertEquals(double11, double0, 0.01);
      assertNotEquals(double11, double5, 0.01);
      assertEquals(double11, double7, 0.01);
      assertNotEquals(double11, double2, 0.01);
      assertNotEquals(double11, double3, 0.01);
      assertEquals(double11, double10, 0.01);
      assertNotEquals(double11, double6, 0.01);
      assertNotEquals(double11, double1, 0.01);
      assertEquals(double11, double8, 0.01);
      assertNotEquals(double11, double9, 0.01);
      assertEquals(Double.NaN, double11, 0.01);
      
      double double12 = Gamma.digamma(1.0E-5);
      assertNotEquals(double12, double5, 0.01);
      assertNotEquals(double12, double2, 0.01);
      assertNotEquals(double12, double3, 0.01);
      assertNotEquals(double12, double0, 0.01);
      assertNotEquals(double12, double1, 0.01);
      assertNotEquals(double12, double6, 0.01);
      assertNotEquals(double12, double7, 0.01);
      assertNotEquals(double12, double10, 0.01);
      assertNotEquals(double12, double11, 0.01);
      assertNotEquals(double12, double4, 0.01);
      assertNotEquals(double12, double8, 0.01);
      assertNotEquals(double12, double9, 0.01);
      assertEquals((-100000.57721566489), double12, 0.01);
      
      double double13 = Gamma.gamma((-1195.0620005006976));
      assertNotEquals(double13, double3, 0.01);
      assertNotEquals(double13, double2, 0.01);
      assertNotEquals(double13, double12, 0.01);
      assertEquals(double13, double7, 0.01);
      assertEquals(double13, double8, 0.01);
      assertEquals(double13, double10, 0.01);
      assertNotEquals(double13, double1, 0.01);
      assertEquals(double13, double4, 0.01);
      assertNotEquals(double13, double5, 0.01);
      assertNotEquals(double13, double6, 0.01);
      assertNotEquals(double13, double9, 0.01);
      assertEquals(double13, double0, 0.01);
      assertEquals(double13, double11, 0.01);
      assertEquals(Double.NaN, double13, 0.01);
      
      double double14 = Gamma.trigamma(Double.NaN);
      assertEquals(double14, double8, 0.01);
      assertNotEquals(double14, double3, 0.01);
      assertEquals(double14, double7, 0.01);
      assertEquals(double14, double10, 0.01);
      assertNotEquals(double14, double1, 0.01);
      assertNotEquals(double14, double12, 0.01);
      assertEquals(double14, double11, 0.01);
      assertNotEquals(double14, double9, 0.01);
      assertNotEquals(double14, double5, 0.01);
      assertEquals(double14, double0, 0.01);
      assertEquals(double14, double13, 0.01);
      assertNotEquals(double14, double2, 0.01);
      assertEquals(double14, double4, 0.01);
      assertNotEquals(double14, double6, 0.01);
      assertEquals(Double.NaN, double14, 0.01);
      
      double double15 = Gamma.logGamma(1362.164428);
      assertNotEquals(double15, double9, 0.01);
      assertNotEquals(double15, double0, 0.01);
      assertNotEquals(double15, double7, 0.01);
      assertNotEquals(double15, double4, 0.01);
      assertNotEquals(double15, double13, 0.01);
      assertNotEquals(double15, double10, 0.01);
      assertNotEquals(double15, double12, 0.01);
      assertNotEquals(double15, double6, 0.01);
      assertNotEquals(double15, double14, 0.01);
      assertNotEquals(double15, double5, 0.01);
      assertNotEquals(double15, double8, 0.01);
      assertNotEquals(double15, double11, 0.01);
      assertNotEquals(double15, double2, 0.01);
      assertNotEquals(double15, double1, 0.01);
      assertNotEquals(double15, double3, 0.01);
      assertEquals(8465.655544538618, double15, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(2325.23, 1.4193426880442385E174, 3402.122033284051, (-148));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction convergents failed to converge (in less than -148 iterations) for value 1,419,342,688,044,238,500,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000,000
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      double double0 = Gamma.lanczos((-7.461209161105275E-9));
      assertEquals(32.946319006557886, double0, 0.01);
      
      double double1 = Gamma.digamma(32.946319006557886);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(3.479626569977735, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ(0.0, 3.479626569977735);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ(0.0, (-3456.095982079));
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaP(Double.NaN, Double.NaN);
      assertEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(double4, double3, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaQ(Double.NaN, 3.479626569977735, 0.0, 0);
      assertEquals(double5, double2, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(double5, double4, 0.01);
      assertEquals(double5, double3, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.regularizedGammaQ((-522.962621), Double.NaN, Double.NaN, 0);
      assertEquals(double6, double5, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertEquals(double6, double2, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertEquals(double6, double4, 0.01);
      assertEquals(double6, double3, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.gamma(861.61);
      assertEquals(double7, double3, 0.01);
      assertEquals(double7, double5, 0.01);
      assertEquals(double7, double6, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertEquals(double7, double4, 0.01);
      assertEquals(double7, double2, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertEquals(Double.NaN, double7, 0.01);
      
      double double8 = Gamma.digamma(3257.6976);
      assertNotEquals(double8, double4, 0.01);
      assertNotEquals(double8, double5, 0.01);
      assertNotEquals(double8, double1, 0.01);
      assertNotEquals(double8, double6, 0.01);
      assertNotEquals(double8, double7, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertNotEquals(double8, double2, 0.01);
      assertEquals(8.088622476690885, double8, 0.01);
      
      double double9 = Gamma.trigamma(Double.NaN);
      assertEquals(double9, double5, 0.01);
      assertEquals(double9, double4, 0.01);
      assertEquals(double9, double3, 0.01);
      assertNotEquals(double9, double0, 0.01);
      assertEquals(double9, double2, 0.01);
      assertEquals(double9, double6, 0.01);
      assertNotEquals(double9, double8, 0.01);
      assertEquals(double9, double7, 0.01);
      assertNotEquals(double9, double1, 0.01);
      assertEquals(Double.NaN, double9, 0.01);
      
      double double10 = Gamma.gamma(Double.NaN);
      assertEquals(double10, double4, 0.01);
      assertEquals(double10, double6, 0.01);
      assertNotEquals(double10, double0, 0.01);
      assertEquals(double10, double5, 0.01);
      assertEquals(double10, double9, 0.01);
      assertEquals(double10, double3, 0.01);
      assertNotEquals(double10, double1, 0.01);
      assertEquals(double10, double7, 0.01);
      assertEquals(double10, double2, 0.01);
      assertNotEquals(double10, double8, 0.01);
      assertEquals(Double.NaN, double10, 0.01);
      
      double double11 = Gamma.gamma(3.479626569977735);
      assertNotEquals(double11, double9, 0.01);
      assertNotEquals(double11, double8, 0.01);
      assertNotEquals(double11, double1, 0.01);
      assertNotEquals(double11, double6, 0.01);
      assertNotEquals(double11, double7, 0.01);
      assertNotEquals(double11, double2, 0.01);
      assertNotEquals(double11, double5, 0.01);
      assertNotEquals(double11, double0, 0.01);
      assertNotEquals(double11, double3, 0.01);
      assertNotEquals(double11, double10, 0.01);
      assertNotEquals(double11, double4, 0.01);
      assertEquals(3.249714780462121, double11, 0.01);
      
      double double12 = Gamma.invGamma1pm1((-7.461209161105275E-9));
      assertNotEquals(double12, double9, 0.01);
      assertNotEquals(double12, double0, 0.01);
      assertNotEquals(double12, double8, 0.01);
      assertNotEquals(double12, double1, 0.01);
      assertNotEquals(double12, double11, 0.01);
      assertNotEquals(double12, double7, 0.01);
      assertNotEquals(double12, double6, 0.01);
      assertNotEquals(double12, double10, 0.01);
      assertNotEquals(double12, double2, 0.01);
      assertNotEquals(double12, double3, 0.01);
      assertNotEquals(double12, double5, 0.01);
      assertNotEquals(double12, double4, 0.01);
      assertEquals((-4.306726843409287E-9), double12, 0.01);
      
      double double13 = Gamma.digamma(0.0);
      assertNotEquals(double13, double0, 0.01);
      assertNotEquals(double13, double5, 0.01);
      assertNotEquals(double13, double4, 0.01);
      assertNotEquals(double13, double6, 0.01);
      assertNotEquals(double13, double1, 0.01);
      assertNotEquals(double13, double12, 0.01);
      assertNotEquals(double13, double9, 0.01);
      assertNotEquals(double13, double8, 0.01);
      assertNotEquals(double13, double3, 0.01);
      assertNotEquals(double13, double2, 0.01);
      assertNotEquals(double13, double10, 0.01);
      assertNotEquals(double13, double11, 0.01);
      assertNotEquals(double13, double7, 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double13, 0.01);
      
      double double14 = Gamma.lanczos((-947.44078));
      assertNotEquals(double14, double8, 0.01);
      assertNotEquals(double14, double7, 0.01);
      assertNotEquals(double14, double11, 0.01);
      assertNotEquals(double14, double1, 0.01);
      assertNotEquals(double14, double2, 0.01);
      assertNotEquals(double14, double12, 0.01);
      assertNotEquals(double14, double6, 0.01);
      assertNotEquals(double14, double9, 0.01);
      assertNotEquals(double14, double4, 0.01);
      assertNotEquals(double14, double5, 0.01);
      assertNotEquals(double14, double3, 0.01);
      assertNotEquals(double14, double0, 0.01);
      assertNotEquals(double14, double13, 0.01);
      assertNotEquals(double14, double10, 0.01);
      assertEquals(0.9882001717908823, double14, 0.01);
      
      double double15 = Gamma.regularizedGammaP(Double.NaN, 0.0, (-3456.095982079), (-1431));
      assertEquals(double15, double9, 0.01);
      assertNotEquals(double15, double14, 0.01);
      assertNotEquals(double15, double1, 0.01);
      assertEquals(double15, double2, 0.01);
      assertNotEquals(double15, double12, 0.01);
      assertNotEquals(double15, double11, 0.01);
      assertEquals(double15, double10, 0.01);
      assertEquals(double15, double3, 0.01);
      assertNotEquals(double15, double8, 0.01);
      assertEquals(double15, double6, 0.01);
      assertEquals(double15, double4, 0.01);
      assertEquals(double15, double5, 0.01);
      assertNotEquals(double15, double0, 0.01);
      assertNotEquals(double15, double13, 0.01);
      assertEquals(double15, double7, 0.01);
      assertEquals(Double.NaN, double15, 0.01);
      
      double double16 = Gamma.regularizedGammaQ(4.7976477743232285E-102, 0.0);
      assertNotEquals(double16, double3, 0.01);
      assertNotEquals(double16, double8, 0.01);
      assertNotEquals(double16, double9, 0.01);
      assertNotEquals(double16, double1, 0.01);
      assertNotEquals(double16, double12, 0.01);
      assertNotEquals(double16, double13, 0.01);
      assertNotEquals(double16, double6, 0.01);
      assertNotEquals(double16, double0, 0.01);
      assertNotEquals(double16, double2, 0.01);
      assertNotEquals(double16, double10, 0.01);
      assertNotEquals(double16, double5, 0.01);
      assertNotEquals(double16, double4, 0.01);
      assertNotEquals(double16, double11, 0.01);
      assertNotEquals(double16, double14, 0.01);
      assertNotEquals(double16, double15, 0.01);
      assertNotEquals(double16, double7, 0.01);
      assertEquals(1.0, double16, 0.01);
      
      double double17 = Gamma.regularizedGammaQ((-205.5148985385061), 0.3056961078365221, 0.0, 399);
      assertNotEquals(double17, double1, 0.01);
      assertEquals(double17, double15, 0.01);
      assertEquals(double17, double9, 0.01);
      assertNotEquals(double17, double14, 0.01);
      assertNotEquals(double17, double12, 0.01);
      assertEquals(double17, double3, 0.01);
      assertNotEquals(double17, double11, 0.01);
      assertNotEquals(double17, double16, 0.01);
      assertNotEquals(double17, double0, 0.01);
      assertEquals(double17, double2, 0.01);
      assertEquals(double17, double7, 0.01);
      assertEquals(double17, double5, 0.01);
      assertEquals(double17, double4, 0.01);
      assertNotEquals(double17, double13, 0.01);
      assertEquals(double17, double10, 0.01);
      assertEquals(double17, double6, 0.01);
      assertNotEquals(double17, double8, 0.01);
      assertEquals(Double.NaN, double17, 0.01);
      
      double double18 = Gamma.lanczos(0.0);
      assertNotEquals(double18, double3, 0.01);
      assertNotEquals(double18, double8, 0.01);
      assertNotEquals(double18, double17, 0.01);
      assertNotEquals(double18, double7, 0.01);
      assertNotEquals(double18, double15, 0.01);
      assertNotEquals(double18, double11, 0.01);
      assertNotEquals(double18, double14, 0.01);
      assertNotEquals(double18, double10, 0.01);
      assertNotEquals(double18, double16, 0.01);
      assertNotEquals(double18, double13, 0.01);
      assertNotEquals(double18, double2, 0.01);
      assertNotEquals(double18, double1, 0.01);
      assertEquals(double18, double0, 0.01);
      assertNotEquals(double18, double4, 0.01);
      assertNotEquals(double18, double12, 0.01);
      assertNotEquals(double18, double6, 0.01);
      assertNotEquals(double18, double5, 0.01);
      assertNotEquals(double18, double9, 0.01);
      assertEquals(32.94631867978169, double18, 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double double0 = Gamma.gamma(5229.0);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.lanczos(Double.NaN);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.gamma((-0.5772156649015329));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals((-3.6316427122685804), double2, 0.01);
      
      double double3 = Gamma.lanczos((-0.5772156649015329));
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(99.992732923394, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaP((-3.6316427122685804), Double.NaN);
      assertEquals(double4, double1, 0.01);
      assertEquals(double4, double0, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.logGamma1p(Double.NaN);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(double5, double4, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertEquals(double5, double0, 0.01);
      assertEquals(double5, double1, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-3003.17601897));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -3,003.176 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      double double0 = Gamma.lanczos((-2766.004463011));
      assertEquals(0.9959527586227944, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP((-2766.004463011), (-1.904714978998808E-72), 1.3775582313537598, 178);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.logGamma((-1.904714978998808E-72));
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ((double) 178, Double.NaN, (-1.904714978998808E-72), 178);
      assertEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.logGamma(0.9959527586227944);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(0.0023496299025055367, double4, 0.01);
      
      double double5 = Gamma.invGamma1pm1(1.3775582313537598);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals((-0.1831806172982445), double5, 0.01);
      
      double double6 = Gamma.regularizedGammaQ(Double.NaN, 7.523374196797555E105, (-1.904714978998808E-72), 0);
      assertNotEquals(double6, double4, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertEquals(double6, double1, 0.01);
      assertEquals(double6, double2, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertEquals(double6, double3, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.digamma(1.0E-14);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertEquals((-1.0000000000000058E14), double7, 0.01);
      
      double double8 = Gamma.regularizedGammaQ(1084.91346797219, 2.5066282746310007);
      assertNotEquals(double8, double4, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertNotEquals(double8, double2, 0.01);
      assertNotEquals(double8, double7, 0.01);
      assertNotEquals(double8, double1, 0.01);
      assertNotEquals(double8, double6, 0.01);
      assertNotEquals(double8, double5, 0.01);
      assertEquals(double8, double0, 0.01);
      assertEquals(1.0, double8, 0.01);
      
      double double9 = Gamma.regularizedGammaQ(1.7321703934464356E213, (-298.9255));
      assertEquals(double9, double3, 0.01);
      assertNotEquals(double9, double0, 0.01);
      assertEquals(double9, double2, 0.01);
      assertEquals(double9, double6, 0.01);
      assertNotEquals(double9, double8, 0.01);
      assertNotEquals(double9, double4, 0.01);
      assertNotEquals(double9, double7, 0.01);
      assertEquals(double9, double1, 0.01);
      assertNotEquals(double9, double5, 0.01);
      assertEquals(Double.NaN, double9, 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      double double0 = Gamma.digamma((-1157.2));
      assertEquals(11.378221915475473, double0, 0.01);
      
      double double1 = Gamma.invGamma1pm1(0.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ((-3091.29), Double.NaN, 1.880755298741952E-9, 3252);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double double0 = Gamma.lanczos(0.0);
      assertEquals(32.94631867978169, double0, 0.01);
      
      double double1 = Gamma.lanczos(0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(32.94631867978169, double1, 0.01);
      
      double double2 = Gamma.trigamma(0.0);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double2, 0.01);
      
      double double3 = Gamma.trigamma((-0.6558780715202539));
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(11.852773019391748, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaQ(32.94631867978169, 32.94631867978169);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(0.4768285289147002, double4, 0.01);
      
      int int0 = 0;
      double double5 = Gamma.regularizedGammaQ(0.0, 0.4768285289147002, (-0.5), 0);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.lanczos(0);
      assertEquals(double6, double1, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals(double6, double0, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertEquals(32.94631867978169, double6, 0.01);
      
      double double7 = Gamma.gamma(211.891243443);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertEquals(double7, double2, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double7, 0.01);
      
      double double8 = Gamma.trigamma(Double.POSITIVE_INFINITY);
      assertEquals(double8, double2, 0.01);
      assertNotEquals(double8, double4, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertEquals(double8, double7, 0.01);
      assertNotEquals(double8, double5, 0.01);
      assertNotEquals(double8, double1, 0.01);
      assertNotEquals(double8, double6, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double8, 0.01);
      
      double double9 = Gamma.gamma((-1.8613847754677912E-9));
      assertNotEquals(double9, double0, 0.01);
      assertNotEquals(double9, double8, 0.01);
      assertNotEquals(double9, double4, 0.01);
      assertNotEquals(double9, double7, 0.01);
      assertNotEquals(double9, double2, 0.01);
      assertNotEquals(double9, double5, 0.01);
      assertNotEquals(double9, double3, 0.01);
      assertNotEquals(double9, double6, 0.01);
      assertNotEquals(double9, double1, 0.01);
      assertEquals((-5.372344365624818E8), double9, 0.01);
      
      double double10 = (-0.5772156649015329);
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-0.5772156649015329));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -0.577 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double double0 = Gamma.trigamma(972.279573141);
      assertEquals(0.0010290398557403482, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP(972.279573141, (-2.056338416977607E-7));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      try { 
        Gamma.logGamma1p(972.279573141);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 972.28 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(0.0, 0.0);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = 1577.32;
      double double2 = Gamma.regularizedGammaQ(1577.32, 5014.3847247);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      double double3 = Gamma.logGamma(Double.POSITIVE_INFINITY);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = 0.0;
      double double5 = Gamma.logGamma(0.0);
      assertEquals(double5, double0, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(double5, double3, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = 0.6558387279510498;
      double double7 = Gamma.gamma(0.6558387279510498);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertEquals(1.373833803337357, double7, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(20.0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 20 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double double0 = Gamma.lanczos((-1.0));
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      double double1 = Gamma.logGamma(Double.POSITIVE_INFINITY);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.digamma(Double.NaN);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.gamma((-1888.533));
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.lanczos(1013.418280936);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(1.011075147688007, double4, 0.01);
      
      double double5 = Gamma.digamma(Double.POSITIVE_INFINITY);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(double5, double0, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double5, 0.01);
      
      double double6 = Gamma.regularizedGammaP(Double.NaN, 1013.418280936, 0.0, 0);
      assertNotEquals(double6, double5, 0.01);
      assertEquals(double6, double1, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertEquals(double6, double3, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals(double6, double2, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.regularizedGammaQ(0.0, 0.0);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertEquals(double7, double3, 0.01);
      assertEquals(double7, double6, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertEquals(double7, double1, 0.01);
      assertEquals(double7, double2, 0.01);
      assertEquals(Double.NaN, double7, 0.01);
      
      double double8 = Gamma.logGamma((-210.1643615328922));
      assertNotEquals(double8, double5, 0.01);
      assertEquals(double8, double7, 0.01);
      assertEquals(double8, double6, 0.01);
      assertNotEquals(double8, double4, 0.01);
      assertEquals(double8, double2, 0.01);
      assertEquals(double8, double3, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertEquals(double8, double1, 0.01);
      assertEquals(Double.NaN, double8, 0.01);
      
      double double9 = Gamma.trigamma(0.0);
      assertNotEquals(double9, double8, 0.01);
      assertNotEquals(double9, double3, 0.01);
      assertNotEquals(double9, double6, 0.01);
      assertEquals(double9, double5, 0.01);
      assertNotEquals(double9, double2, 0.01);
      assertNotEquals(double9, double1, 0.01);
      assertNotEquals(double9, double7, 0.01);
      assertNotEquals(double9, double4, 0.01);
      assertEquals(double9, double0, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double9, 0.01);
      
      double double10 = Gamma.regularizedGammaP((-1.0), 0.0, (-6.077618957228252E-8), 0);
      assertNotEquals(double10, double5, 0.01);
      assertEquals(double10, double6, 0.01);
      assertNotEquals(double10, double4, 0.01);
      assertNotEquals(double10, double9, 0.01);
      assertEquals(double10, double2, 0.01);
      assertEquals(double10, double7, 0.01);
      assertEquals(double10, double8, 0.01);
      assertEquals(double10, double1, 0.01);
      assertNotEquals(double10, double0, 0.01);
      assertEquals(double10, double3, 0.01);
      assertEquals(Double.NaN, double10, 0.01);
      
      double double11 = Gamma.gamma(0.0);
      assertEquals(double11, double6, 0.01);
      assertEquals(double11, double1, 0.01);
      assertEquals(double11, double7, 0.01);
      assertNotEquals(double11, double0, 0.01);
      assertNotEquals(double11, double5, 0.01);
      assertNotEquals(double11, double9, 0.01);
      assertNotEquals(double11, double4, 0.01);
      assertEquals(double11, double10, 0.01);
      assertEquals(double11, double2, 0.01);
      assertEquals(double11, double3, 0.01);
      assertEquals(double11, double8, 0.01);
      assertEquals(Double.NaN, double11, 0.01);
      
      double double12 = Gamma.gamma(1.011075147688007);
      assertNotEquals(double12, double1, 0.01);
      assertNotEquals(double12, double6, 0.01);
      assertNotEquals(double12, double5, 0.01);
      assertNotEquals(double12, double7, 0.01);
      assertNotEquals(double12, double11, 0.01);
      assertNotEquals(double12, double2, 0.01);
      assertNotEquals(double12, double8, 0.01);
      assertNotEquals(double12, double10, 0.01);
      assertNotEquals(double12, double4, 0.01);
      assertNotEquals(double12, double9, 0.01);
      assertNotEquals(double12, double0, 0.01);
      assertNotEquals(double12, double3, 0.01);
      assertEquals(0.9937273496097274, double12, 0.01);
      
      double double13 = Gamma.lanczos((-1069.8913724));
      assertNotEquals(double13, double3, 0.01);
      assertNotEquals(double13, double7, 0.01);
      assertNotEquals(double13, double8, 0.01);
      assertNotEquals(double13, double4, 0.01);
      assertNotEquals(double13, double9, 0.01);
      assertNotEquals(double13, double0, 0.01);
      assertNotEquals(double13, double5, 0.01);
      assertEquals(double13, double12, 0.01);
      assertNotEquals(double13, double2, 0.01);
      assertNotEquals(double13, double11, 0.01);
      assertNotEquals(double13, double10, 0.01);
      assertNotEquals(double13, double1, 0.01);
      assertNotEquals(double13, double6, 0.01);
      assertEquals(0.9895482263121972, double13, 0.01);
      
      double double14 = Gamma.digamma(3.5309026109857795E-8);
      assertNotEquals(double14, double12, 0.01);
      assertNotEquals(double14, double4, 0.01);
      assertNotEquals(double14, double10, 0.01);
      assertNotEquals(double14, double11, 0.01);
      assertNotEquals(double14, double0, 0.01);
      assertNotEquals(double14, double1, 0.01);
      assertNotEquals(double14, double6, 0.01);
      assertNotEquals(double14, double7, 0.01);
      assertNotEquals(double14, double13, 0.01);
      assertNotEquals(double14, double8, 0.01);
      assertNotEquals(double14, double2, 0.01);
      assertNotEquals(double14, double9, 0.01);
      assertNotEquals(double14, double5, 0.01);
      assertNotEquals(double14, double3, 0.01);
      assertEquals((-2.83213707812161E7), double14, 0.01);
      
      double double15 = Gamma.digamma(0);
      assertNotEquals(double15, double14, 0.01);
      assertNotEquals(double15, double3, 0.01);
      assertNotEquals(double15, double6, 0.01);
      assertNotEquals(double15, double10, 0.01);
      assertNotEquals(double15, double7, 0.01);
      assertNotEquals(double15, double9, 0.01);
      assertNotEquals(double15, double4, 0.01);
      assertNotEquals(double15, double8, 0.01);
      assertNotEquals(double15, double2, 0.01);
      assertNotEquals(double15, double11, 0.01);
      assertNotEquals(double15, double5, 0.01);
      assertNotEquals(double15, double13, 0.01);
      assertNotEquals(double15, double0, 0.01);
      assertNotEquals(double15, double12, 0.01);
      assertNotEquals(double15, double1, 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double15, 0.01);
      
      double double16 = Gamma.invGamma1pm1((-6.077618957228252E-8));
      assertNotEquals(double16, double8, 0.01);
      assertNotEquals(double16, double15, 0.01);
      assertNotEquals(double16, double1, 0.01);
      assertNotEquals(double16, double14, 0.01);
      assertNotEquals(double16, double7, 0.01);
      assertNotEquals(double16, double9, 0.01);
      assertNotEquals(double16, double3, 0.01);
      assertNotEquals(double16, double12, 0.01);
      assertNotEquals(double16, double11, 0.01);
      assertNotEquals(double16, double2, 0.01);
      assertNotEquals(double16, double5, 0.01);
      assertNotEquals(double16, double4, 0.01);
      assertNotEquals(double16, double10, 0.01);
      assertNotEquals(double16, double0, 0.01);
      assertNotEquals(double16, double6, 0.01);
      assertNotEquals(double16, double13, 0.01);
      assertEquals((-3.5080971096793144E-8), double16, 0.01);
      
      double double17 = Gamma.regularizedGammaQ((-210.1643615328922), 0.0, 1013.418280936, (-125));
      assertNotEquals(double17, double0, 0.01);
      assertNotEquals(double17, double13, 0.01);
      assertEquals(double17, double11, 0.01);
      assertEquals(double17, double2, 0.01);
      assertNotEquals(double17, double4, 0.01);
      assertNotEquals(double17, double5, 0.01);
      assertNotEquals(double17, double9, 0.01);
      assertEquals(double17, double10, 0.01);
      assertEquals(double17, double6, 0.01);
      assertNotEquals(double17, double16, 0.01);
      assertNotEquals(double17, double15, 0.01);
      assertEquals(double17, double7, 0.01);
      assertEquals(double17, double8, 0.01);
      assertNotEquals(double17, double14, 0.01);
      assertEquals(double17, double3, 0.01);
      assertNotEquals(double17, double12, 0.01);
      assertEquals(double17, double1, 0.01);
      assertEquals(Double.NaN, double17, 0.01);
      
      double double18 = Gamma.invGamma1pm1(0.9895482263121972);
      assertNotEquals(double18, double12, 0.01);
      assertNotEquals(double18, double4, 0.01);
      assertNotEquals(double18, double10, 0.01);
      assertNotEquals(double18, double9, 0.01);
      assertNotEquals(double18, double14, 0.01);
      assertNotEquals(double18, double3, 0.01);
      assertNotEquals(double18, double0, 0.01);
      assertNotEquals(double18, double5, 0.01);
      assertNotEquals(double18, double7, 0.01);
      assertNotEquals(double18, double17, 0.01);
      assertNotEquals(double18, double2, 0.01);
      assertNotEquals(double18, double8, 0.01);
      assertNotEquals(double18, double1, 0.01);
      assertNotEquals(double18, double13, 0.01);
      assertNotEquals(double18, double6, 0.01);
      assertNotEquals(double18, double11, 0.01);
      assertNotEquals(double18, double15, 0.01);
      assertEquals(double18, double16, 0.01);
      assertEquals(0.00439316466038106, double18, 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double double0 = Gamma.gamma(1.360180139541626);
      assertEquals(0.8901679974115484, double0, 0.01);
      
      double double1 = Gamma.trigamma(1.360180139541626);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0662394272043294, double1, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(1423.702);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 1,423.702 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(0.0, 0.0, 0.0, 7);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ(1.0607041998938709E-7, Double.NaN);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.lanczos((-237.13298));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(0.9531473197040825, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ((-237.13298), Double.NaN);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(2777.1669811033, 2.4106173515319824, 2777.1669811033, 342);
      assertEquals(1.0, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ((double) 342, 3567.008577029, 2777.1669811033, 10);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      double double2 = Gamma.lanczos(1.6584100723266602);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(9.157089685626795, double2, 0.01);
      
      double double3 = Gamma.digamma(0.0);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaP((-1948.1803), 1.0142320772726397E304);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      double double0 = Gamma.gamma((-1.7766273910482863E198));
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.invGamma1pm1(0.3056961078365221);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.11529637311280126, double1, 0.01);
      
      double double2 = Gamma.invGamma1pm1(Double.NaN);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ(Double.NaN, (-706.8043312), (-1376.6017), 0);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaP((-1347.0), (-1290.0));
      assertEquals(double4, double2, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(double4, double3, 0.01);
      assertEquals(double4, double0, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaQ(0.0, 2740.6055631228646, 0.11529637311280126, 0);
      assertEquals(double5, double2, 0.01);
      assertEquals(double5, double0, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(double5, double4, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.regularizedGammaP((-181.7999811), 2087.0);
      assertNotEquals(double6, double1, 0.01);
      assertEquals(double6, double0, 0.01);
      assertEquals(double6, double4, 0.01);
      assertEquals(double6, double5, 0.01);
      assertEquals(double6, double3, 0.01);
      assertEquals(double6, double2, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      double double0 = (-713.7213811646);
      double double1 = Gamma.trigamma((-713.7213811646));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(16.74306216583486, double1, 0.01);
      
      double double2 = Gamma.gamma((-713.7213811646));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      double double3 = Gamma.trigamma(16.74306216583486);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(0.06154532661677599, double3, 0.01);
      
      double double4 = 1.7966744899749756;
      double double5 = Double.POSITIVE_INFINITY;
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(1.7966744899749756, Double.POSITIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction diverged to NaN for value \u221E
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(761.19, 0.0);
      assertEquals(1.0, double0, 0.01);
      
      double double1 = Gamma.invGamma1pm1(0.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP(1.5392257142577226E220, 761.19);
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ(0.0, (-1705.807613486));
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.invGamma1pm1(0.0);
      assertEquals(double4, double2, 0.01);
      assertEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(0.0, double4, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      double double0 = Gamma.trigamma((-211.2));
      assertEquals(28.562127686146944, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ(3.002095574584687E155, (-211.2), 3896.784967208521, (-169));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ(0.678885817527771, (-2157.074851312083));
      assertNotEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(0.0, (-4.4703076268400615E-80), Double.POSITIVE_INFINITY, (-169));
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaP(Double.NaN, 1.3438240331106108E-153, (-633.0), (-259));
      assertEquals(double4, double1, 0.01);
      assertEquals(double4, double3, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(double4, double2, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaP(Double.NaN, (-4.4703076268400615E-80), 3.002095574584687E155, (-1100));
      assertEquals(double5, double2, 0.01);
      assertEquals(double5, double4, 0.01);
      assertEquals(double5, double1, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.regularizedGammaQ((-633.0), 0.0);
      assertEquals(double6, double1, 0.01);
      assertEquals(double6, double5, 0.01);
      assertEquals(double6, double2, 0.01);
      assertEquals(double6, double3, 0.01);
      assertEquals(double6, double4, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.logGamma(2760.8553749431167);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertEquals(19111.175823043708, double7, 0.01);
      
      double double8 = Gamma.invGamma1pm1((-4.4703076268400615E-80));
      assertNotEquals(double8, double7, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertNotEquals(double8, double4, 0.01);
      assertNotEquals(double8, double5, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertNotEquals(double8, double2, 0.01);
      assertNotEquals(double8, double1, 0.01);
      assertNotEquals(double8, double6, 0.01);
      assertEquals((-2.5803315891408797E-80), double8, 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      double double0 = Gamma.gamma(1.3088328582956644E-7);
      assertEquals(7640394.4026459865, double0, 0.01);
      
      double double1 = Gamma.digamma(1.3088328582956644E-7);
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-7640395.557077186), double1, 0.01);
      
      double double2 = 0.0;
      double double3 = Gamma.regularizedGammaQ(0.0, 39.955705902);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      int int0 = (-1627);
      double double4 = Gamma.regularizedGammaP(7640394.4026459865, Double.NaN, 0.0, (-1627));
      assertNotEquals(double4, double2, 0.01);
      assertEquals(double4, double3, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.logGamma1p(Double.NaN);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(double5, double3, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(double5, double4, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(20.0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 20 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double double0 = Gamma.gamma(0.7853981633974483);
      assertEquals(1.181043114925474, double0, 0.01);
      
      double double1 = Gamma.trigamma(0.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      
      double double2 = Gamma.trigamma(1291.857679444);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(7.743787534207322E-4, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(1291.857679444, 1021.357717063, 2.3472546699189522E-8, 3595);
      assertEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(2.388360692901983E-16, double3, 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(598.04285, 598.04285, 1633.692544727467, 1914);
      assertEquals(0.9836888944333593, double0, 0.01);
      
      double double1 = Gamma.gamma((-1438.3123245));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ((-304.266444534997), (-637.3503542141));
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ((-2081.51947805), 1633.692544727467);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.logGamma(0.9836888944333593);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(0.00963560299241552, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaP((double) 1914, 1894.703419701052);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals(0.33180398734296135, double5, 0.01);
      
      double double6 = Gamma.digamma((-818.9465767777285));
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals((-11.833736034662738), double6, 0.01);
      
      double double7 = Gamma.logGamma(598.04285);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertEquals(3223.363484434624, double7, 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      double double0 = Gamma.lanczos(0.0);
      assertEquals(32.94631867978169, double0, 0.01);
      
      double double1 = Gamma.trigamma(285.4213521506);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.003509736865746305, double1, 0.01);
      
      double double2 = Gamma.gamma((-722.905314));
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(-0.0, double2, 0.01);
      
      double double3 = Gamma.logGamma(0.0);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = (-2700.964692781);
      double double5 = Gamma.regularizedGammaQ((-2700.964692781), (-2700.964692781));
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals(double5, double3, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      int int0 = (-1173);
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(1034.7854056227793, 577.0, (-1988.966), (-1173));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (-1,173) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(2.100525379180908, 699.0, 699.0, (-195));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction convergents failed to converge (in less than -195 iterations) for value 699
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      double double0 = Gamma.trigamma((-1.643181065367639E-4));
      assertEquals(3.703643493935854E7, double0, 0.01);
      
      double double1 = 4.7421875;
      double double2 = Gamma.regularizedGammaP(0.0, 4.7421875, 3.703643493935854E7, (-1847));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ(1879.9342, 4.7421875);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(1.0, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaQ(Double.NaN, 0.0);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(double4, double2, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaQ((double) (-1847), (-845.56726249891), (-5445.7), (-1847));
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertEquals(double5, double4, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(double5, double2, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.logGamma(Double.NaN);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertEquals(double6, double4, 0.01);
      assertEquals(double6, double2, 0.01);
      assertEquals(double6, double5, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.logGamma1p(Double.NaN);
      assertNotEquals(double7, double0, 0.01);
      assertEquals(double7, double6, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertEquals(double7, double4, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertEquals(double7, double5, 0.01);
      assertEquals(double7, double2, 0.01);
      assertEquals(Double.NaN, double7, 0.01);
      
      double double8 = Gamma.regularizedGammaQ(Double.NaN, (-5445.7));
      assertNotEquals(double8, double1, 0.01);
      assertEquals(double8, double4, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertEquals(double8, double7, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertEquals(double8, double2, 0.01);
      assertEquals(double8, double5, 0.01);
      assertEquals(double8, double6, 0.01);
      assertEquals(Double.NaN, double8, 0.01);
      
      double double9 = Gamma.trigamma(0.0);
      assertNotEquals(double9, double8, 0.01);
      assertNotEquals(double9, double5, 0.01);
      assertNotEquals(double9, double1, 0.01);
      assertNotEquals(double9, double6, 0.01);
      assertNotEquals(double9, double4, 0.01);
      assertNotEquals(double9, double7, 0.01);
      assertNotEquals(double9, double0, 0.01);
      assertNotEquals(double9, double3, 0.01);
      assertNotEquals(double9, double2, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double9, 0.01);
      
      double double10 = Gamma.regularizedGammaQ((-204.2), 1879.9342);
      assertEquals(double10, double6, 0.01);
      assertNotEquals(double10, double3, 0.01);
      assertNotEquals(double10, double0, 0.01);
      assertEquals(double10, double7, 0.01);
      assertEquals(double10, double4, 0.01);
      assertEquals(double10, double8, 0.01);
      assertEquals(double10, double5, 0.01);
      assertNotEquals(double10, double9, 0.01);
      assertNotEquals(double10, double1, 0.01);
      assertEquals(double10, double2, 0.01);
      assertEquals(Double.NaN, double10, 0.01);
      
      double double11 = 1980.731094759427;
      double double12 = Gamma.digamma(1980.731094759427);
      assertNotEquals(double12, double4, 0.01);
      assertNotEquals(double12, double7, 0.01);
      assertNotEquals(double12, double8, 0.01);
      assertNotEquals(double12, double10, 0.01);
      assertNotEquals(double12, double5, 0.01);
      assertNotEquals(double12, double9, 0.01);
      assertNotEquals(double12, double1, 0.01);
      assertNotEquals(double12, double11, 0.01);
      assertNotEquals(double12, double0, 0.01);
      assertNotEquals(double12, double6, 0.01);
      assertNotEquals(double12, double2, 0.01);
      assertNotEquals(double12, double3, 0.01);
      assertEquals(7.5909688420286185, double12, 0.01);
      
      double double13 = Gamma.logGamma((-2739.1846024));
      assertNotEquals(double13, double9, 0.01);
      assertEquals(double13, double8, 0.01);
      assertEquals(double13, double4, 0.01);
      assertEquals(double13, double2, 0.01);
      assertEquals(double13, double6, 0.01);
      assertEquals(double13, double5, 0.01);
      assertEquals(double13, double10, 0.01);
      assertNotEquals(double13, double11, 0.01);
      assertNotEquals(double13, double1, 0.01);
      assertEquals(double13, double7, 0.01);
      assertNotEquals(double13, double0, 0.01);
      assertNotEquals(double13, double3, 0.01);
      assertNotEquals(double13, double12, 0.01);
      assertEquals(Double.NaN, double13, 0.01);
      
      double double14 = Gamma.lanczos(Double.NaN);
      assertNotEquals(double14, double9, 0.01);
      assertEquals(double14, double4, 0.01);
      assertEquals(double14, double13, 0.01);
      assertEquals(double14, double2, 0.01);
      assertEquals(double14, double6, 0.01);
      assertEquals(double14, double8, 0.01);
      assertEquals(double14, double5, 0.01);
      assertNotEquals(double14, double11, 0.01);
      assertNotEquals(double14, double1, 0.01);
      assertEquals(double14, double10, 0.01);
      assertNotEquals(double14, double3, 0.01);
      assertNotEquals(double14, double0, 0.01);
      assertNotEquals(double14, double12, 0.01);
      assertEquals(double14, double7, 0.01);
      assertEquals(Double.NaN, double14, 0.01);
      
      double double15 = Gamma.logGamma(1180.746123762334);
      assertNotEquals(double15, double12, 0.01);
      assertNotEquals(double15, double4, 0.01);
      assertNotEquals(double15, double5, 0.01);
      assertNotEquals(double15, double9, 0.01);
      assertNotEquals(double15, double14, 0.01);
      assertNotEquals(double15, double10, 0.01);
      assertNotEquals(double15, double7, 0.01);
      assertNotEquals(double15, double0, 0.01);
      assertNotEquals(double15, double2, 0.01);
      assertNotEquals(double15, double6, 0.01);
      assertNotEquals(double15, double3, 0.01);
      assertNotEquals(double15, double13, 0.01);
      assertNotEquals(double15, double1, 0.01);
      assertNotEquals(double15, double11, 0.01);
      assertNotEquals(double15, double8, 0.01);
      assertEquals(7169.118095253555, double15, 0.01);
      
      double double16 = Gamma.lanczos(Double.NaN);
      assertEquals(double16, double5, 0.01);
      assertEquals(double16, double14, 0.01);
      assertNotEquals(double16, double12, 0.01);
      assertNotEquals(double16, double15, 0.01);
      assertEquals(double16, double10, 0.01);
      assertEquals(double16, double7, 0.01);
      assertNotEquals(double16, double0, 0.01);
      assertNotEquals(double16, double3, 0.01);
      assertEquals(double16, double2, 0.01);
      assertEquals(double16, double6, 0.01);
      assertEquals(double16, double13, 0.01);
      assertNotEquals(double16, double9, 0.01);
      assertNotEquals(double16, double1, 0.01);
      assertEquals(double16, double4, 0.01);
      assertNotEquals(double16, double11, 0.01);
      assertEquals(double16, double8, 0.01);
      assertEquals(Double.NaN, double16, 0.01);
      
      double double17 = 7.358873642076596E195;
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(7.358873642076596E195, 3.703643493935854E7, 3.102559332875688E297, 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (0) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(0.0, 0.0, 0.0, (-1643));
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.logGamma(Double.NaN);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP(Double.NaN, (double) (-1643));
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.logGamma1p(Double.NaN);
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaQ(Double.NaN, 0.0, 942.6, (-1643));
      assertEquals(double4, double3, 0.01);
      assertEquals(double4, double2, 0.01);
      assertEquals(double4, double0, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((-9.837447530487956E-5), (-9.837447530487956E-5), 1.9575583661463974E-10, 1047);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ(0.0, 204.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.digamma(Double.NaN);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.trigamma(Double.NaN);
      assertEquals(double3, double1, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.invGamma1pm1(Double.NaN);
      assertEquals(double4, double0, 0.01);
      assertEquals(double4, double2, 0.01);
      assertEquals(double4, double3, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.trigamma((-1.0));
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double5, 0.01);
      
      double double6 = Gamma.regularizedGammaQ(Double.POSITIVE_INFINITY, 461.9365036121, 0.0, 1047);
      assertEquals(double6, double0, 0.01);
      assertEquals(double6, double4, 0.01);
      assertEquals(double6, double2, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertEquals(double6, double1, 0.01);
      assertEquals(double6, double3, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.regularizedGammaP(0.0, 0.0);
      assertEquals(double7, double0, 0.01);
      assertEquals(double7, double4, 0.01);
      assertEquals(double7, double2, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertEquals(double7, double1, 0.01);
      assertEquals(double7, double3, 0.01);
      assertEquals(double7, double6, 0.01);
      assertEquals(Double.NaN, double7, 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(0.0, 0.0);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP((-806.3061094), (-1631.5711), (-67.494024925633), 1);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP((-67.494024925633), Double.NaN, 0.0, 3700);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ((-1631.5711), (-1.2494415722763663E-13));
      assertEquals(double3, double1, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.gamma(Double.POSITIVE_INFINITY);
      assertEquals(double4, double3, 0.01);
      assertEquals(double4, double2, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(double4, double0, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      double double0 = (-0.04200263503409524);
      double double1 = Gamma.regularizedGammaQ((-0.04200263503409524), (-0.04200263503409524), (-0.04200263503409524), 1981);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.trigamma((-0.04200263503409524));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(568.574263754358, double2, 0.01);
      
      double double3 = Gamma.lanczos(Double.NaN);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(2197.04922, 568.574263754358, (-1683.052035135857), 1981);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (1,981) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP((-753.92245237079), (-753.92245237079));
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP((-2441.449929), Double.NaN);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ((-768.884359036), (-471.5847));
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(2.425402115319395E-188, 2.425402115319395E-188);
      assertEquals((-1.9317880628477724E-14), double0, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(1541.13062532, 2.425402115319395E-188, (-1.9317880628477724E-14), 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (0) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      double double0 = Gamma.trigamma(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP(0.0, (-2314.64705091112), 0.0, 0);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      double double0 = Gamma.logGamma1p(9.453598391231829E-8);
      assertEquals((-5.4567643460638895E-8), double0, 0.01);
      
      double double1 = Gamma.lanczos(9.453598391231829E-8);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(32.94631453941988, double1, 0.01);
      
      double double2 = Gamma.digamma((-5.4567643460638895E-8));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(1.8325877847813297E7, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ(1.8325877847813297E7, 0.0);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(1.0, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaQ(1.8325877847813297E7, (-5.4567643460638895E-8), 0.0, 3890);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaQ((double) 3890, 32.94631453941988);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(1.0, double5, 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(1362.79267375, 1362.79267375, 1362.79267375, 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (0) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      double double0 = Gamma.lanczos((-1903.0));
      assertEquals(0.9941192130856127, double0, 0.01);
      
      double double1 = Gamma.invGamma1pm1(0.9941192130856127);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.00247820445897623, double1, 0.01);
      
      double double2 = Gamma.logGamma((-2050.353496261234));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      double double0 = Gamma.logGamma(279.173);
      assertEquals(1291.1856615946315, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP((-1910.703), (-1910.703), (-1910.703), 241);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.logGamma(2509.4668456381);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(17131.207023584167, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(Double.NaN, 279.173, 0.0, 221);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.logGamma1p(Double.NaN);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(double4, double3, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.gamma(1291.1856615946315);
      assertEquals(double5, double4, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(double5, double1, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.digamma(9.563454814394247E171);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals(395.9999999460211, double6, 0.01);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      double double0 = Gamma.logGamma(1.1898510456085205);
      assertEquals((-0.08237510758548004), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      double double0 = Gamma.logGamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      double double0 = (-8.514194324403149E-6);
      double double1 = Gamma.gamma((-8.514194324403149E-6));
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-117451.50233790769), double1, 0.01);
      
      double double2 = Gamma.logGamma((-8.514194324403149E-6));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = 0.0;
      double double4 = Gamma.gamma(0.0);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      try { 
        Gamma.logGamma1p(2374.7496732755);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 2,374.75 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      double double0 = Gamma.lanczos(0.0);
      assertEquals(32.94631867978169, double0, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(32.94631867978169);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 32.946 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      double double0 = (-463.65);
      try { 
        Gamma.logGamma1p((-463.65));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -463.65 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      double double0 = Gamma.gamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ(0.0, (-1842.0696));
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP((-1513.51776516505), 1.0, 4528.22663133, 0);
      assertEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.lanczos(4528.22663133);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(1.0024749802053856, double3, 0.01);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(89.70910919, 89.70910919, 89.70910919, 155);
      assertEquals(0.04208122433723036, double0, 0.01);
      
      try { 
        Gamma.logGamma1p(89.70910919);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 89.709 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      double double0 = Gamma.logGamma((-1512.32574624));
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.gamma((-1512.32574624));
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.gamma((-1512.32574624));
      assertEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.invGamma1pm1(Double.NaN);
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.trigamma(1024.16626);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(9.76880805346987E-4, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaP((-293.9259), 1024.16626);
      assertEquals(double5, double0, 0.01);
      assertEquals(double5, double1, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(double5, double2, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.invGamma1pm1(9.76880805346987E-4);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertEquals(double6, double4, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertEquals(5.632449626923869E-4, double6, 0.01);
      
      double double7 = Gamma.gamma(1282.457);
      assertEquals(double7, double2, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertEquals(double7, double0, 0.01);
      assertEquals(double7, double5, 0.01);
      assertEquals(double7, double1, 0.01);
      assertEquals(double7, double3, 0.01);
      assertEquals(Double.NaN, double7, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-1845.398889));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -1,845.399 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((-2848.8926), (-2848.8926));
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ(0.0, 0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(6.663175904917432E40);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 66,631,759,049,174,320,000,000,000,000,000,000,000,000 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Gamma.regularizedGammaP((-2196.282079966891), 971.9156, 971.9156, 0);
      double double0 = Gamma.digamma(618.9);
      assertEquals(6.427135606228709, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP((-1673.80154), 971.9156, 618.9, 0);
      double double2 = Gamma.digamma(0);
      assertEquals(Double.NEGATIVE_INFINITY, double2, 0.01);
      
      double double3 = Gamma.logGamma((-1150.5));
      assertEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      Gamma.lanczos((-3396.33039525897));
      Gamma.logGamma1p(0.9967034598086613);
      Gamma.regularizedGammaQ((-3396.33039525897), (-3396.33039525897), 0.0, 2786);
      double double0 = Gamma.regularizedGammaQ((double) 2786, (-954.928497082911));
      assertEquals(Double.NaN, double0, 0.01);
      
      Gamma.lanczos(0.9967034598086613);
      double double1 = Gamma.regularizedGammaP(6.283185307179586, 13.175958825475298);
      assertEquals(0.9874815140530787, double1, 0.01);
      
      Gamma.trigamma(13.175958825475298);
      double double2 = Gamma.gamma(0.9874815140530787);
      double double3 = Gamma.lanczos(0.9874815140530787);
      assertEquals(13.255387581560448, double3, 0.01);
      
      double double4 = Gamma.trigamma(13.175958825475298);
      assertEquals(0.07884867003527354, double4, 0.01);
      
      Gamma.logGamma1p(Double.NaN);
      double double5 = Gamma.regularizedGammaQ(493.106855, 13.255387581560448, 0.9967034598086613, 2);
      assertEquals(double5, double2, 0.01);
      assertEquals(1.0, double5, 0.01);
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      Gamma.regularizedGammaQ((-1202.211), (-1202.211), (-1202.211), (-485));
      Gamma.regularizedGammaP((-1281.194), 0.026620534842894922);
      double double0 = Gamma.invGamma1pm1(0.026620534842894922);
      assertEquals(0.014900290536607573, double0, 0.01);
      
      Gamma.logGamma1p(0.0);
      Gamma.regularizedGammaP(Double.NaN, (-1202.211));
      double double1 = Gamma.regularizedGammaP(1.5, -0.0, 0.0, (-485));
      assertEquals(0.0, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP(0.0, 0.026620534842894922, 0.0, (-485));
      Gamma.regularizedGammaQ(0.0, (-1202.211));
      Gamma.invGamma1pm1(0.0);
      double double3 = Gamma.lanczos(-0.0);
      assertEquals(32.94631867978169, double3, 0.01);
      
      Gamma.regularizedGammaP(-0.0, 0.0);
      Gamma.regularizedGammaQ((-1202.211), (-1485.7149565369), 0.03333333333333333, (-485));
      double double4 = Gamma.digamma(506.7);
      assertEquals(6.226932010719621, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaQ(506.7, (-280.9823883406416));
      assertEquals(double5, double2, 0.01);
      
      double double6 = Gamma.regularizedGammaQ(0.0, (-0.6558780715202539), 0.0, (-485));
      assertEquals(Double.NaN, double6, 0.01);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      double double0 = Gamma.gamma((-9.837447530487956E-5));
      assertEquals((-10165.81577448127), double0, 0.01);
      
      Gamma.logGamma1p((-9.837447530487956E-5));
      double double1 = Gamma.gamma(5.679124800940188E-5);
      assertEquals(17607.769813867377, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP((-9.837447530487956E-5), 8.533262777516794E-94);
      double double3 = Gamma.trigamma(0.0);
      assertEquals(Double.POSITIVE_INFINITY, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaQ(410.372, (-10165.81577448127));
      assertEquals(double4, double2, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
  }
}
