/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 10:45:09 GMT 2019
 */

package org.apache.commons.math4.special;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math4.special.Gamma;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class Gamma_ESTest extends Gamma_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double double0 = Gamma.gamma(1.0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double double0 = Gamma.gamma((-1.3577357192972777E258));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      double double0 = Gamma.trigamma(3.9231656332530014E-68);
      assertEquals(6.4972071229822075E134, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      double double0 = Gamma.trigamma(0.0);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      double double0 = Gamma.digamma((-293.712));
      assertEquals(3.2156608883526823, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((double) 2716, (double) 2716, Double.NaN, 12);
      assertEquals(0.9923452350817967, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((-0.1207822376352452), 0.08333333333333333, (-0.1207822376352452), (-1));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(0.22937411069869995, 0.22937411069869995, 0.22937411069869995, 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (0) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(32.94637344732348, 0.11865071952342987, 0.0, 2716);
      assertEquals(3.9231656332530014E-68, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(0.0, 0.0, 309.91883, 991);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      double double0 = Gamma.logGamma(1.5);
      assertEquals((-0.1207822376352452), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double double0 = Gamma.logGamma((-4979.56798594905));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(136.99, 4.909590673122532E13);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(32.934530634804936, 2.6923694661863613E-4);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(0.16653861138229148, 0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double double0 = Gamma.logGamma1p(1.0032223661142865);
      assertEquals(0.0013657120463627247, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      double double0 = Gamma.logGamma1p(6.820161668496171E-10);
      assertEquals((-3.9367041483912923E-10), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      double double0 = Gamma.gamma((-552.259293));
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      double double0 = Gamma.gamma((-5.207000323380292E-8));
      assertEquals((-1.9204915843109492E7), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(3.141592653589793, 1006.1, 303.7243227, (-1011));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction convergents failed to converge (in less than -1,011 iterations) for value 1,006.1
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      // Undeclared exception!
      Gamma.regularizedGammaP(2.119495681669309E28, 2.119495681669309E28);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(83.0589242, Double.POSITIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction diverged to NaN for value \u221E
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      double double0 = Gamma.logGamma1p(0.0);
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(1.0);
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(0.9922597403387414);
      assertEquals(0.003258406808503053, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      double double0 = Gamma.lanczos(674.62817140526);
      assertEquals(1.016652706048714, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      double double0 = Gamma.digamma(2330.37245);
      assertEquals(7.753568810247864, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(-0.0, 0.0, 1.4198992252349854, 6);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(0.0, Double.NaN, 683.0, 7);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(0.1930064558982849, 77.6, 0.1930064558982849, 14);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP((-1.2504934821426706E-6), (-1.2504934821426706E-6), 0.3056961078365221, 12);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(Double.NaN, (-332.52638018192), 0.0, (-1078));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(99.4533641285768, (-1585.5245), 2592.27618, 857);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      double double0 = Gamma.logGamma(3.141592653589793);
      assertEquals(0.827694592323437, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      double double0 = Gamma.logGamma(8.441822398385275E-5);
      assertEquals(9.379678534031022, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      double double0 = Gamma.gamma((-1.3293244194328255));
      assertEquals(3.0769264939351264, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      double double0 = Gamma.gamma(1.9887375831604004);
      assertEquals(0.9952905499078029, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      double double0 = Gamma.gamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      double double0 = Gamma.gamma(42.0);
      assertEquals(3.345252661316381E49, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      double double0 = Gamma.gamma(1.354355037085258E-262);
      assertEquals(7.383588295666736E261, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      try { 
        Gamma.logGamma1p(Double.POSITIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // \u221E is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      try { 
        Gamma.logGamma1p((-2373.13867593));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -2,373.139 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1((-2.1745347282493023E-299));
      assertEquals((-1.255175509017895E-299), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(1129.3562);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 1,129.356 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-1342.1878));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -1,342.188 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      double double0 = Gamma.trigamma(1.0E-5);
      assertEquals(9.999999999999998E9, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      double double0 = Gamma.trigamma((-1.5683631181406778E-7));
      assertEquals(4.06543253439796E13, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      double double0 = Gamma.trigamma(1253.94426033643);
      assertEquals(7.978016881987029E-4, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      double double0 = Gamma.trigamma(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      double double0 = Gamma.digamma(3.953806005373127E-272);
      assertEquals((-2.5292085616770883E271), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      double double0 = Gamma.digamma(0.0);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      double double0 = Gamma.digamma(Double.POSITIVE_INFINITY);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      double double0 = Gamma.digamma(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(21.10596775291429, 0.0, 0.0, (-2604));
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(4.1719443712336026E278, (-8.569906238528267E-8), 4.958369974640573E193, 987);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(Double.NaN, Double.NaN, (-1.643181065367639E-4), (-2117));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(763.383743189, 1.0098963140419455, 0.0, (-127));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (-127) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(6.989593267473173, 14.136097974741746);
      assertEquals(0.9870294866759609, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(3.399464998481189E-5, 0.0, 1.990680456161499, 13);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(13.051427900193595, Double.NaN, 1627.85, 987);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      double double0 = Gamma.logGamma(8.111475214120304);
      assertEquals(8.750678598354776, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      double double0 = Gamma.logGamma(1.0098963140419455);
      assertEquals((-0.005632143284923553), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      double double0 = Gamma.logGamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      double double0 = Gamma.logGamma(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }
}
