/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 10:49:41 GMT 2019
 */

package org.apache.commons.math4.special;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math4.special.Gamma;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class Gamma_ESTest extends Gamma_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Gamma.trigamma((-697.811006702046));
      int int0 = 15;
      Gamma.regularizedGammaQ(31.530762775087933, 2417.0, 0.5, 15);
      Gamma.invGamma1pm1(1.349595069885254);
      Gamma.gamma(0.0);
      Gamma.lanczos(3015.9);
      Gamma.regularizedGammaP((-697.811006702046), 0.0);
      Gamma.invGamma1pm1(0.0);
      Gamma.logGamma((-3.8207793300052055E-8));
      Gamma.trigamma((-850.684842));
      Gamma.digamma(6.8716741130671986E-9);
      Gamma.logGamma(0.0);
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(621.54);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 621.54 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Gamma.digamma(2.6923694661863613E-4);
      Gamma.trigamma(2.6923694661863613E-4);
      double double0 = (-928.28807);
      Gamma.digamma((-928.28807));
      Gamma.trigamma(9.30140354527745);
      Gamma.logGamma(0.0);
      Gamma.regularizedGammaQ(0.0, 1.8773906230926514);
      Gamma.regularizedGammaP(0.11349656311805996, 2.057887077331543);
      Gamma.digamma(3235.79);
      Gamma.regularizedGammaP(8.081873851411036, 1433.46960731712);
      Gamma.regularizedGammaP((-6.49667713553005E-8), (-3.292458622014749E252), 2537.29063494277, (-3974));
      Gamma.regularizedGammaP(8.081873851411036, (-3.292458622014749E252));
      Gamma.regularizedGammaP(8.081873851411036, Double.NaN, 8.081873851411036, 753);
      try { 
        Gamma.logGamma1p(2060.096);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 2,060.096 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Gamma.regularizedGammaQ(2.5, 2.5);
      Gamma.regularizedGammaQ((-2230.0548), 0.415880186995508, Double.NaN, 0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Gamma.regularizedGammaQ(48.5116, 48.5116);
      Gamma.regularizedGammaQ(0.48090527320259746, 0.48090527320259746);
      int int0 = 3261;
      Gamma.regularizedGammaP(0.31411493469227714, 0.31411493469227714, 0.48090527320259746, 3261);
      try { 
        Gamma.logGamma1p(3261);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 3,261 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Gamma.lanczos(0.0);
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(6.8716741130671986E-9, 32.94631867978169, 6.8716741130671986E-9, (-1132));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction convergents failed to converge (in less than -1,132 iterations) for value 32.946
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Gamma.lanczos((-1.2494415722763663E-13));
      Gamma.logGamma(557.5641969633874);
      Gamma.gamma((-473.579963208029));
      Gamma.gamma(0.0);
      Gamma.regularizedGammaQ((-1.728610646009749E253), 839.0);
      Gamma.logGamma1p(1.0E-14);
      Gamma.regularizedGammaP(5.778891828, 916.820903418);
      Gamma.regularizedGammaP(1.0, (-5.772156649015247E-15));
      Gamma.lanczos(557.5641969633874);
      Gamma.regularizedGammaQ(3273.0472692761655, 0.3750596046447754);
      Gamma.regularizedGammaP(0.0, (-1933.6626153), 32.94631867978716, 0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Gamma.regularizedGammaQ(1696.298805257135, 1696.298805257135, 1696.298805257135, 1);
      Gamma.invGamma1pm1(0.0);
      Gamma.gamma(2336.830866694);
      Gamma.digamma(210.979);
      Gamma.logGamma1p(0.0);
      Gamma.regularizedGammaQ(0.0, 0.0, 5.34938682619028, 1);
      Gamma.invGamma1pm1(0.9903141541645987);
      Gamma.regularizedGammaP(1696.298805257135, 64.17215174642);
      Gamma.invGamma1pm1(0.0);
      Gamma.gamma(0.0);
      Gamma.regularizedGammaP(0.0, (-1.2778508303324259E-8), 75.4465, 1);
      Gamma.lanczos(1);
      Gamma.trigamma(0.0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Gamma.regularizedGammaP(3.5201699899499525E-100, 3.5201699899499525E-100, 3.5201699899499525E-100, 101);
      Gamma.digamma(101);
      Gamma.logGamma(2276.157909);
      Gamma.trigamma(3032.64942865);
      Gamma.invGamma1pm1(3.2979904667207574E-4);
      try { 
        Gamma.logGamma1p(101);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 101 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Gamma.regularizedGammaP(296.8273443877835, 296.8273443877835);
      Gamma.digamma(296.8273443877835);
      int int0 = (-949);
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(1244.855292813865, 0.507718713566504, (-2882.1712253474), (-949));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (-949) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      double double0 = 1.0E-14;
      Gamma.digamma(1.0E-14);
      Gamma.lanczos((-2265.417399953303));
      Gamma.logGamma1p(0.0);
      Gamma.trigamma(3441.6045406703);
      int int0 = (-429);
      Gamma.regularizedGammaP(2294.5866613916, (-1.0000000000000058E14), (-2265.417399953303), (-429));
      Gamma.regularizedGammaQ(313.5876253946, 2.2810251664891242E-7);
      Gamma.gamma((-429));
      Gamma.logGamma(Double.NaN);
      Gamma.lanczos((-1773.06));
      Gamma.regularizedGammaQ((-3309.05699), 4.343529937408594E-15, (-1403.7), (-429));
      try { 
        Gamma.logGamma1p((-1773.06));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -1,773.06 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Gamma.gamma(2.131521224975586);
      Gamma.regularizedGammaQ(1.0629369107462798, 1.0629369107462798, 1.0629369107462798, 3280);
      try { 
        Gamma.logGamma1p(3280);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 3,280 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double double0 = 994.16;
      double double1 = 0.0;
      int int0 = 0;
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(994.16, 994.16, 0.0, 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (0) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Gamma.lanczos(2853.79);
      Gamma.gamma((-3565.6744613387673));
      Gamma.logGamma1p(1.0039281325361025);
      double double0 = 0.0;
      Gamma.trigamma(0.0);
      Gamma.digamma(Double.POSITIVE_INFINITY);
      Gamma.lanczos(1.0039281325361025);
      Gamma.digamma(1191.95);
      Gamma.trigamma(375.0478984792);
      Gamma.logGamma((-543.2275951109467));
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(7.082926361099199);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 7.083 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Gamma.gamma(2113.1565);
      Gamma.regularizedGammaP(2113.1565, 2.133604049682617);
      Gamma.regularizedGammaP(Double.NaN, Double.NaN);
      Gamma.regularizedGammaQ(2113.1565, (-1897.776), 0.0, 0);
      Gamma.gamma(0.0);
      Gamma.trigamma(0.008333333333333333);
      Gamma.logGamma((-1897.776));
      Gamma.invGamma1pm1(0);
      Gamma.regularizedGammaQ(Double.NaN, 0.0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Gamma.invGamma1pm1(0.0);
      Gamma.logGamma(612.2);
      Gamma.regularizedGammaP(1350.03657633821, 0.0, 1749.0938, (-1137));
      Gamma.regularizedGammaP(0.0, 0.0);
      Gamma.logGamma(0.0);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Gamma.logGamma((-2851.4));
      Gamma.regularizedGammaP((-2851.4), (-2851.4));
      Gamma.lanczos((-2851.4));
      Gamma.regularizedGammaP(0.9960738857933666, (-2851.4), (-383.745713746), 494);
      Gamma.regularizedGammaP(0.9960738857933666, 0.0);
      Gamma.regularizedGammaP((-2851.4), (-255.74973));
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      double double0 = 0.5;
      Gamma.regularizedGammaP(0.5, 0.5);
      Gamma.logGamma(6.820161668496171E-10);
      Gamma.invGamma1pm1(6.820161668496171E-10);
      int int0 = (-3199);
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(0.5, 0.6826894921370859, 0.5, (-3199));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (-3,199) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Gamma.regularizedGammaP((-2032.1336227621), 3109.1846, (-2032.1336227621), 0);
      Gamma.digamma(Double.NaN);
      Gamma.gamma((-2032.1336227621));
      Gamma.regularizedGammaQ((-2032.1336227621), 1774.186036);
      Gamma.regularizedGammaP(555.14621328, (-1154.68991224705), 6.116095104481416E-9, 0);
      Gamma.logGamma(816.539);
      Gamma.logGamma1p(0.0);
      Gamma.trigamma(6.116095104481416E-9);
      try { 
        Gamma.logGamma1p((-2222.0));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -2,222 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Gamma.regularizedGammaP(0.0, 0.0, 0.0, 0);
      Gamma.invGamma1pm1((-1.2504934821426706E-6));
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Gamma.logGamma((-19.89983));
      Gamma.trigamma((-19.89983));
      Gamma.trigamma(Double.NaN);
      Gamma.regularizedGammaQ(102.96792200446443, Double.NaN, (-19.89983), 12);
      Gamma.regularizedGammaP(Double.NaN, Double.NaN);
      Gamma.digamma(Double.NaN);
      int int0 = 0;
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(3.5638391501880846E-10, 4.438921624363781E-8, Double.NaN, 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (0) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Gamma.logGamma(0.0);
      Gamma.lanczos(0.0);
      Gamma.trigamma(0.0);
      Gamma.regularizedGammaP(0.03333333333333333, 0.0);
      Gamma.trigamma(Double.NaN);
      Gamma.logGamma(2.262354850769043);
      Gamma.lanczos(0.0);
      Gamma.invGamma1pm1(0.0);
      Gamma.logGamma1p(0.0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Gamma.gamma(677.875248846745);
      Gamma.regularizedGammaP(Double.POSITIVE_INFINITY, 0.03333333333333333);
      Gamma.digamma(0.0);
      int int0 = (-156);
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(0.03333333333333333, 0.03333333333333333, (-970.0), (-156));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (-156) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Gamma.regularizedGammaP((-0.42278433509846713), (-0.42278433509846713), (-0.42278433509846713), (-1800));
      Gamma.logGamma(66.0);
      Gamma.regularizedGammaP(209.34258675253682, (-334.6630744893654), 66.0, (-1800));
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Gamma.logGamma((-1952.2));
      Gamma.regularizedGammaQ(Double.NaN, 0.0, Double.NaN, (-720));
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Gamma.regularizedGammaQ(2781.5, (-2098.72071051897), 2781.5, 572);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Gamma.regularizedGammaP(0.0, 2336.0);
      Gamma.regularizedGammaP(0.0, Double.NaN, 0.0, (-1404));
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(1666.01132099135);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 1,666.011 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Gamma.trigamma((-541.19853));
      Gamma.regularizedGammaP(0.0, 0.0);
      Gamma.invGamma1pm1(Double.NaN);
      Gamma.logGamma(0.16666666666666666);
      Gamma.logGamma(1.78482304E8);
      Gamma.regularizedGammaQ(0.9999999999999971, 0.0, 0.0, 1070);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Gamma.regularizedGammaQ(0.0, 0.0);
      Gamma.gamma(0.53589928150177);
      Gamma.regularizedGammaP(0.026620534842894922, 57.15623566586292);
      Gamma.lanczos(0.0);
      Gamma.digamma(0.026620534842894922);
      Gamma.digamma(3325.981610118649);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Gamma.regularizedGammaP(0.0, 835.632763);
      Gamma.regularizedGammaQ(0.0, Double.NaN);
      Gamma.gamma(252.0);
      Gamma.regularizedGammaP(252.0, 1.733140900346534E-58, 0.2837681770324707, 449);
      Gamma.gamma(Double.NaN);
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(57.15623566586292);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 57.156 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Gamma.digamma(0.0);
      Gamma.digamma(2738.66002258);
      Gamma.gamma(7.915041454760029);
      Gamma.regularizedGammaQ(7.915041454760029, 0.0);
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(1.0, 2279.980695653, Double.NEGATIVE_INFINITY, 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction convergents failed to converge (in less than 0 iterations) for value 2,279.981
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Gamma.gamma((-6.4304548177935305E-6));
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Gamma.logGamma(0.0);
      Gamma.regularizedGammaQ((-2068.152), 0.0);
      Gamma.digamma(Double.NaN);
      Gamma.trigamma(0.0);
      Gamma.lanczos(Double.NaN);
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-2068.152));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -2,068.152 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Gamma.regularizedGammaP(2018.8, 3.399464998481189E-5);
      Gamma.logGamma1p(0.0);
      Gamma.digamma(-0.0);
      Gamma.digamma(1042.2754571871);
      Gamma.trigamma(Double.POSITIVE_INFINITY);
      Gamma.lanczos(0.0);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Gamma.gamma(0.0);
      Gamma.logGamma(Double.NaN);
      Gamma.regularizedGammaP(Double.NaN, 347.67714963);
      Gamma.trigamma(0.0);
      Gamma.logGamma(1.7619237899780273);
      Gamma.trigamma(0.0);
      Gamma.gamma(1.7619237899780273);
      Gamma.gamma(2801.28544955);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Gamma.regularizedGammaP(3186.31167, 3186.31167);
      Gamma.trigamma(0.0);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      double double0 = 573.2188582391;
      Gamma.regularizedGammaQ(573.2188582391, 573.2188582391);
      Gamma.gamma((-648.60578));
      Gamma.gamma(0.5772156649015329);
      Gamma.lanczos((-648.60578));
      Gamma.logGamma1p(0.5772156649015329);
      Gamma.lanczos(0.494445656060321);
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(42.0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 42 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Gamma.trigamma(4172.134);
      Gamma.gamma(2.3971422114486178E-4);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      double double0 = 8.0;
      try { 
        Gamma.logGamma1p(8.0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 8 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      try { 
        Gamma.logGamma1p(3233.70319744232);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 3,233.703 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Gamma.logGamma(46.2202);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Gamma.trigamma(0.0);
      int int0 = 2386;
      Gamma.regularizedGammaP(0.0, 0.0, Double.POSITIVE_INFINITY, 2386);
      double double0 = (-487.0023697779458);
      Gamma.digamma((-487.0023697779458));
      double double1 = 0.06977382302284241;
      Gamma.regularizedGammaQ(0.06977382302284241, (double) 2386);
      double double2 = 4266.4165;
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(4266.4165);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 4,266.417 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Gamma.regularizedGammaQ(1362.610969, 1362.610969, 1362.610969, 2);
      Gamma.regularizedGammaQ(6.861870970598542E106, 1362.610969);
      Gamma.digamma(7.552139802281006E-8);
      Gamma.gamma((-3003.1932624));
      try { 
        Gamma.logGamma1p((-3003.1932624));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -3,003.193 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(3635.8539);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 3,635.854 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Gamma.gamma(0.5849615335464478);
      Gamma.logGamma1p(0.0);
      Gamma.lanczos(0.5849615335464478);
      Gamma.regularizedGammaQ(0.0, 0.5849615335464478, 1532.53244, 1276);
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-3235.786760822504));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -3,235.787 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP((-2467.514702474141), 0.0, (-0.04219773455554433), (-1741));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((-1973.9), 1.4226751327514648);
      double double1 = Gamma.logGamma(1.4226751327514648);
      assertEquals((-0.12074311953194548), double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP(Double.NaN, (-1973.9), (-0.04219773455554433), 84);
      assertEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
  }
}
