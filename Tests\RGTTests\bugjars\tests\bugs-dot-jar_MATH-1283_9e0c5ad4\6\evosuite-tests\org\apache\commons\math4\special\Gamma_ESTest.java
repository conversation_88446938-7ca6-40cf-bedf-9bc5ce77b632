/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 10:53:29 GMT 2019
 */

package org.apache.commons.math4.special;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math4.special.Gamma;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class Gamma_ESTest extends Gamma_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double double0 = Gamma.gamma(1.0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double double0 = Gamma.gamma((-1173));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      double double0 = Gamma.logGamma1p(0.5);
      assertEquals((-0.1207822376352452), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      double double0 = Gamma.logGamma1p((-0.5));
      assertEquals(0.5723649429247001, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      double double0 = Gamma.trigamma(1.8251985168077E-117);
      assertEquals(3.0017863999271953E233, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      double double0 = Gamma.trigamma(0.0);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double double0 = Gamma.digamma((-2.1026444172410488E-4));
      assertEquals(4755.338330797272, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(0.9375, 0.9375, (-1294.9832763), 1203);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (1,203) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(1652.7001, 1215.0941, 0.0, 2559);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(0.0, (-3712.7713944), 3576.91116075, (-4636));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      double double0 = Gamma.logGamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(0.009730643063047805, 1623.61723);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(829.88782, 829.88782);
      assertEquals(0.49538382765071254, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      double double0 = Gamma.logGamma1p(1.1933419704437256);
      assertEquals(0.09333625967303966, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double double0 = Gamma.gamma((-563.1061643));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction diverged to NaN for value \u221E
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      // Undeclared exception!
      Gamma.regularizedGammaP(1.1044092571980793E155, 1.1044092571980793E155);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      double double0 = Gamma.logGamma1p(0.0);
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(0.16653861138229148);
      assertEquals(0.07786635802757381, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(0.6639155149459839);
      assertEquals(0.10828282770147504, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      double double0 = Gamma.lanczos((-945.145671));
      assertEquals(0.9881715770475805, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      double double0 = Gamma.trigamma(4745.58772338326);
      assertEquals(2.1074425903045313E-4, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double double0 = Gamma.digamma(209.2);
      assertEquals(5.34089877070031, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(1.7602040767669678, 55.6143771, 0.0, 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction convergents failed to converge (in less than 0 iterations) for value 55.614
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(0.0, (-2308.5347628418), (-2641.3528203), 403);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((-0.4919138160976202), Double.NaN, 0.0, 403);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(5.05239404378821E31, (-2.1524167411495098E-4), 1980.136008583, 0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(4745.58772338326, 0.49538382765071254, 0.49538382765071254, (-626));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (-626) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(1.0, 7.882557196966872, 2651.142098727, 3);
      assertEquals(0.9996227329240149, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(1215.70715, 0.0, 0.0, (-833));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(1.6711060014400145E-276, (-1228.3219007520267), 1.6711060014400145E-276, 85);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      double double0 = Gamma.logGamma(647.65);
      assertEquals(3542.4977960212054, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      double double0 = Gamma.logGamma(0.08333333333333333);
      assertEquals(2.4422973111828896, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      double double0 = Gamma.gamma(14.136097974741746);
      assertEquals(8.88028203604575E9, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      double double0 = Gamma.gamma((-1.2504934821426706E-6));
      assertEquals((-799684.8732809962), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      double double0 = Gamma.gamma(Double.POSITIVE_INFINITY);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      double double0 = Gamma.gamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      try { 
        Gamma.logGamma1p(43.66904688);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 43.669 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      try { 
        Gamma.logGamma1p((-3621.94270236489));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -3,621.943 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(1.3377854824066162);
      assertEquals((-0.1624271112969679), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1((-0.42278433509846713));
      assertEquals((-0.3522819961430704), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(3.141592653589793);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 3.142 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-2781.85361729));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -2,781.854 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      double double0 = Gamma.trigamma(Double.POSITIVE_INFINITY);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      double double0 = Gamma.trigamma((-1.735366061128156E-8));
      assertEquals(3.3206094595121605E15, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      double double0 = Gamma.trigamma(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      double double0 = Gamma.digamma(1.9575583661463974E-10);
      assertEquals((-5.108404517621456E9), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      double double0 = Gamma.digamma(0.0);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      double double0 = Gamma.digamma(Double.POSITIVE_INFINITY);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      double double0 = Gamma.digamma(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(1.701840523821621E-167, 0.0, (-1.2494415722763663E-13), (-833));
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(Double.NaN, 0.0, Double.NaN, 0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP((double) 403, 0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP((-0.5), 868.50259, Double.NaN, 0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP((-1316.439), Double.NaN, 0.0, 618);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(Double.NaN, 0.0, 0.0, 403);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      double double0 = Gamma.logGamma(2.5017542839050293);
      assertEquals(0.28591716117881244, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      double double0 = Gamma.logGamma(1.7602040767669678);
      assertEquals((-0.08183621188748666), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(0.08333333333333333, 0.08333333333333333, 0.08333333333333333, 403);
      assertEquals(0.8405561047515352, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      double double0 = Gamma.logGamma((-5590.442260856));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      double double0 = Gamma.logGamma(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(868.50259, 2609.442499497979);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((-0.42278433509846713), (-0.42278433509846713));
      assertEquals(Double.NaN, double0, 0.01);
  }
}
