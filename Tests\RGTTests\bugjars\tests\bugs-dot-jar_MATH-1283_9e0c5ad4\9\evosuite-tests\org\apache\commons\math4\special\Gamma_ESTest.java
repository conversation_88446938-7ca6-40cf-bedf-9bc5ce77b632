/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 11:05:31 GMT 2019
 */

package org.apache.commons.math4.special;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math4.special.Gamma;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class Gamma_ESTest extends Gamma_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double double0 = Gamma.gamma((-2.35264006));
      assertEquals((-1.2384890535016986), double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ(1.2076238768270153E-8, (-1.2384890535016986));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.trigamma(102.14);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(0.00983856684074484, double2, 0.01);
      
      double double3 = Gamma.gamma(758.9085782910547);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaP(1.2076238768270153E-8, (-152.70466), 0.0, 10);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(double4, double3, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaP(0.0, Double.NaN);
      assertEquals(double5, double3, 0.01);
      assertEquals(double5, double1, 0.01);
      assertEquals(double5, double4, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.invGamma1pm1(Double.NaN);
      assertNotEquals(double6, double2, 0.01);
      assertEquals(double6, double4, 0.01);
      assertEquals(double6, double1, 0.01);
      assertEquals(double6, double3, 0.01);
      assertEquals(double6, double5, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.trigamma((-1121.46486));
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertEquals(9.989979825665925, double7, 0.01);
      
      double double8 = Gamma.digamma(Double.NaN);
      assertEquals(double8, double6, 0.01);
      assertNotEquals(double8, double2, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertEquals(double8, double4, 0.01);
      assertNotEquals(double8, double7, 0.01);
      assertEquals(double8, double5, 0.01);
      assertEquals(double8, double3, 0.01);
      assertEquals(double8, double1, 0.01);
      assertEquals(Double.NaN, double8, 0.01);
      
      double double9 = Gamma.invGamma1pm1(0.0);
      assertNotEquals(double9, double3, 0.01);
      assertNotEquals(double9, double5, 0.01);
      assertNotEquals(double9, double1, 0.01);
      assertNotEquals(double9, double7, 0.01);
      assertEquals(double9, double2, 0.01);
      assertNotEquals(double9, double6, 0.01);
      assertNotEquals(double9, double0, 0.01);
      assertNotEquals(double9, double8, 0.01);
      assertNotEquals(double9, double4, 0.01);
      assertEquals(0.0, double9, 0.01);
      
      double double10 = Gamma.logGamma((-865.9450632));
      assertEquals(double10, double6, 0.01);
      assertNotEquals(double10, double9, 0.01);
      assertNotEquals(double10, double0, 0.01);
      assertEquals(double10, double3, 0.01);
      assertEquals(double10, double5, 0.01);
      assertNotEquals(double10, double2, 0.01);
      assertEquals(double10, double1, 0.01);
      assertEquals(double10, double8, 0.01);
      assertNotEquals(double10, double7, 0.01);
      assertEquals(double10, double4, 0.01);
      assertEquals(Double.NaN, double10, 0.01);
      
      double double11 = Gamma.regularizedGammaQ(340.11862, (-46.16400724379535));
      assertNotEquals(double11, double2, 0.01);
      assertEquals(double11, double3, 0.01);
      assertEquals(double11, double5, 0.01);
      assertEquals(double11, double8, 0.01);
      assertNotEquals(double11, double7, 0.01);
      assertEquals(double11, double4, 0.01);
      assertEquals(double11, double10, 0.01);
      assertEquals(double11, double1, 0.01);
      assertNotEquals(double11, double0, 0.01);
      assertEquals(double11, double6, 0.01);
      assertNotEquals(double11, double9, 0.01);
      assertEquals(Double.NaN, double11, 0.01);
      
      double double12 = Gamma.regularizedGammaQ(0.0, (-46.16400724379535));
      assertEquals(double12, double10, 0.01);
      assertEquals(double12, double4, 0.01);
      assertNotEquals(double12, double0, 0.01);
      assertEquals(double12, double1, 0.01);
      assertEquals(double12, double6, 0.01);
      assertNotEquals(double12, double9, 0.01);
      assertEquals(double12, double11, 0.01);
      assertEquals(double12, double3, 0.01);
      assertEquals(double12, double8, 0.01);
      assertNotEquals(double12, double2, 0.01);
      assertNotEquals(double12, double7, 0.01);
      assertEquals(double12, double5, 0.01);
      assertEquals(Double.NaN, double12, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(0.0, 0.0, 0.0, 1601);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = 2725.4270358556105;
      double double2 = 6.8716741130671986E-9;
      int int0 = 14;
      double double3 = Gamma.regularizedGammaP(369.7979, 2725.4270358556105, 6.8716741130671986E-9, 14);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(1.0, double3, 0.01);
      
      double double4 = Gamma.gamma((-2.862720607805682E216));
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(double4, double0, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.logGamma((-1033.0));
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(double5, double0, 0.01);
      assertEquals(double5, double4, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      try { 
        Gamma.logGamma1p((-1230.353));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -1,230.353 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((-102.436509), (-102.436509));
      assertEquals(Double.NaN, double0, 0.01);
      
      int int0 = 66;
      double double1 = Gamma.regularizedGammaP(0.0, 0.0, 0.0, 66);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.gamma(66);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(8.247650592082472E90, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(8.0, 5027.19);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(1.0, double3, 0.01);
      
      double double4 = Gamma.gamma(57.15623566586292);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(1.335659644652408E75, double4, 0.01);
      
      double double5 = Gamma.logGamma1p(0.0);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals(-0.0, double5, 0.01);
      
      double double6 = Gamma.invGamma1pm1(Double.NaN);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertEquals(double6, double0, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals(double6, double1, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(57.15623566586292);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 57.156 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      double double0 = Gamma.logGamma(6.283185307179586);
      assertEquals(5.277788665919055, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP(0.0, 6.283185307179586);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ(0.0, 3633.689566968784, 5.277788665919055, 224);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ((-82.3), 0.0);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-0.5772156649015329));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -0.577 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      double double0 = Gamma.digamma(967.3712408504);
      assertEquals(6.874065377953121, double0, 0.01);
      
      double double1 = Gamma.gamma(6.874065377953121);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(569.4257009809419, double1, 0.01);
      
      double double2 = 0.0;
      double double3 = Gamma.logGamma(0.0);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.logGamma(Double.NaN);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(double4, double3, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      try { 
        Gamma.logGamma1p(569.4257009809419);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 569.426 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      double double0 = Gamma.lanczos((-983.8691984151));
      assertEquals(0.9886362053470191, double0, 0.01);
      
      double double1 = 189.526778956;
      int int0 = 15;
      double double2 = Gamma.regularizedGammaP(189.526778956, 0.9886362053470191, 189.526778956, 15);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(189.526778956, (-983.8691984151));
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      int int1 = 0;
      double double4 = Gamma.regularizedGammaP(0.0, 0.0, 0.0, 0);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(double4, double3, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaP(0.0, 3.141592653589793);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(double5, double4, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.logGamma(15);
      assertNotEquals(double6, double2, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertEquals(25.191221182738687, double6, 0.01);
      
      double double7 = Gamma.regularizedGammaQ(720.127242805506, 0.0, 1777.94486152593, 13);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertEquals(1.0, double7, 0.01);
      
      double double8 = Gamma.logGamma(1.0);
      assertNotEquals(double8, double1, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertNotEquals(double8, double6, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertNotEquals(double8, double7, 0.01);
      assertNotEquals(double8, double4, 0.01);
      assertEquals(double8, double2, 0.01);
      assertNotEquals(double8, double5, 0.01);
      assertEquals(-0.0, double8, 0.01);
      
      try { 
        Gamma.logGamma1p(25.191221182738687);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 25.191 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double double0 = Gamma.trigamma(3477.063414422721);
      assertEquals(2.876403717983957E-4, double0, 0.01);
      
      double double1 = Gamma.lanczos(1.2555065155029297);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(11.262181727639977, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ(0.0, 666.93921590276, 2.876403717983957E-4, (-1));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.lanczos(3951.51414);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(1.002836372610093, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaQ(20.0, 0.0);
      assertEquals(double4, double3, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(1.0, double4, 0.01);
      
      double double5 = Gamma.lanczos((-417.6041419));
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(0.9732990486179512, double5, 0.01);
      
      double double6 = Gamma.gamma((-786.943018));
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals(double6, double2, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.regularizedGammaQ(1005.6905441460647, 3477.063414422721, 2.201331615447998, 3621);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertEquals(double7, double0, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertEquals(0.0, double7, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      double double0 = 0.0;
      double double1 = Gamma.regularizedGammaP(0.0, 0.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.lanczos(0.0);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(32.94631867978169, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ(32.94631867978169, Double.NaN, 0.0, 10);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.logGamma1p(Double.NaN);
      assertEquals(double4, double1, 0.01);
      assertEquals(double4, double3, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaQ((-331.01331), 1412.2, Double.NaN, 10);
      assertEquals(double5, double1, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertEquals(double5, double4, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      try { 
        Gamma.logGamma1p(563.72345147);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 563.723 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP((-0.6558780715202539), (-0.6558780715202539));
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ(Double.NaN, 8.136316299122392E-192);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.gamma(Double.NaN);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ(Double.NaN, 49.259);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaP(Double.NaN, Double.NaN);
      assertEquals(double4, double0, 0.01);
      assertEquals(double4, double3, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(double4, double2, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaP(0.5579402446746826, Double.NaN);
      assertEquals(double5, double0, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(double5, double1, 0.01);
      assertEquals(double5, double4, 0.01);
      assertEquals(double5, double2, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.invGamma1pm1(8.136316299122392E-192);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertEquals(4.696409222447111E-192, double6, 0.01);
      
      double double7 = Gamma.lanczos(8.136316299122392E-192);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertEquals(32.94631867978169, double7, 0.01);
      
      double double8 = Gamma.invGamma1pm1(0.5579402446746826);
      assertNotEquals(double8, double3, 0.01);
      assertNotEquals(double8, double7, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertNotEquals(double8, double4, 0.01);
      assertNotEquals(double8, double2, 0.01);
      assertNotEquals(double8, double6, 0.01);
      assertNotEquals(double8, double1, 0.01);
      assertNotEquals(double8, double5, 0.01);
      assertEquals(0.1242600942655174, double8, 0.01);
      
      double double9 = Gamma.regularizedGammaP(13.605844943531, Double.NaN, 2.3375763256988976E-7, (-3182));
      assertEquals(double9, double3, 0.01);
      assertEquals(double9, double5, 0.01);
      assertEquals(double9, double1, 0.01);
      assertNotEquals(double9, double7, 0.01);
      assertNotEquals(double9, double8, 0.01);
      assertEquals(double9, double2, 0.01);
      assertEquals(double9, double0, 0.01);
      assertNotEquals(double9, double6, 0.01);
      assertEquals(double9, double4, 0.01);
      assertEquals(Double.NaN, double9, 0.01);
      
      double double10 = Gamma.regularizedGammaQ((-3724.5956144340944), Double.NaN);
      assertNotEquals(double10, double8, 0.01);
      assertEquals(double10, double0, 0.01);
      assertNotEquals(double10, double6, 0.01);
      assertEquals(double10, double9, 0.01);
      assertEquals(double10, double4, 0.01);
      assertEquals(double10, double2, 0.01);
      assertEquals(double10, double1, 0.01);
      assertEquals(double10, double5, 0.01);
      assertNotEquals(double10, double7, 0.01);
      assertEquals(double10, double3, 0.01);
      assertEquals(Double.NaN, double10, 0.01);
      
      double double11 = Gamma.gamma((-1541.986));
      assertEquals(double11, double1, 0.01);
      assertNotEquals(double11, double7, 0.01);
      assertEquals(double11, double5, 0.01);
      assertEquals(double11, double3, 0.01);
      assertNotEquals(double11, double8, 0.01);
      assertNotEquals(double11, double6, 0.01);
      assertEquals(double11, double9, 0.01);
      assertEquals(double11, double10, 0.01);
      assertEquals(double11, double2, 0.01);
      assertEquals(double11, double4, 0.01);
      assertEquals(double11, double0, 0.01);
      assertEquals(Double.NaN, double11, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      double double0 = Gamma.trigamma((-707.6486771389315));
      assertEquals(12.378231620406579, double0, 0.01);
      
      double double1 = Gamma.gamma((-707.6486771389315));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      int int0 = 15;
      double double2 = Gamma.regularizedGammaQ(94.857557, (-1446.8248), (-1446.8248), 15);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.digamma((-707.6486771389315));
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(4.978343776803445, double3, 0.01);
      
      double double4 = 0.0;
      double double5 = Gamma.regularizedGammaQ(Double.NaN, 0.0);
      assertNotEquals(double5, double3, 0.01);
      assertEquals(double5, double2, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.logGamma((-575.871));
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertEquals(double6, double5, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals(double6, double2, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = (-21.239);
      try { 
        Gamma.logGamma1p((-21.239));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -21.239 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      double double0 = Gamma.logGamma((-4.5199654318611534E-10));
      assertEquals(Double.NaN, double0, 0.01);
      
      int int0 = (-2925);
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(0.5415972471237183, 0.5415972471237183, 0.5415972471237183, (-2925));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (-2,925) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double double0 = Gamma.gamma(1.0724295693252266E89);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP(Double.NaN, 0.0, (-2.193190669794277E-108), 362);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.invGamma1pm1(1.9575583661463974E-10);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(1.1299333536474163E-10, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(0.0, Double.NaN, 1.1299333536474163E-10, 362);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.lanczos((-2.193190669794277E-108));
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(32.94631867978169, double4, 0.01);
      
      double double5 = Gamma.logGamma(0.0);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(double5, double1, 0.01);
      assertEquals(double5, double0, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.logGamma(32.94631867978169);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertEquals(81.37112412939602, double6, 0.01);
      
      double double7 = Gamma.regularizedGammaQ(157.82484907, 0.0);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertEquals(1.0, double7, 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(1.4043786616805493E-54, 1.4043786616805493E-54, 1.4043786616805493E-54, 1763);
      assertEquals(1.0000000000000009, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      double double0 = Gamma.digamma(0.0);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      
      double double1 = Gamma.gamma(0.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.lanczos((-1658.862895169));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.9932547398892422, double2, 0.01);
      
      double double3 = Gamma.trigamma(764.57);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(0.001308780422288353, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaQ((-3.835454246739619E-8), 0.29255300760269165);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.lanczos((-3.835454246739619E-8));
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertEquals(32.946320359583396, double5, 0.01);
      
      double double6 = Gamma.invGamma1pm1(Double.NaN);
      assertEquals(double6, double4, 0.01);
      assertEquals(double6, double1, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertEquals(Double.NaN, double6, 0.01);
      
      double double7 = Gamma.logGamma1p(0.29255300760269165);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertEquals((-0.10688331190202198), double7, 0.01);
      
      double double8 = Gamma.regularizedGammaQ(1.2942728582966776E171, Double.NaN);
      assertNotEquals(double8, double5, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertEquals(double8, double1, 0.01);
      assertNotEquals(double8, double2, 0.01);
      assertEquals(double8, double4, 0.01);
      assertEquals(double8, double6, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertNotEquals(double8, double7, 0.01);
      assertEquals(Double.NaN, double8, 0.01);
      
      double double9 = Gamma.logGamma(0.001308780422288353);
      assertNotEquals(double9, double3, 0.01);
      assertNotEquals(double9, double4, 0.01);
      assertNotEquals(double9, double8, 0.01);
      assertNotEquals(double9, double1, 0.01);
      assertNotEquals(double9, double6, 0.01);
      assertNotEquals(double9, double7, 0.01);
      assertNotEquals(double9, double5, 0.01);
      assertNotEquals(double9, double0, 0.01);
      assertNotEquals(double9, double2, 0.01);
      assertEquals(6.6379055100979265, double9, 0.01);
      
      double double10 = Gamma.trigamma((-0.10688331190202198));
      assertNotEquals(double10, double3, 0.01);
      assertNotEquals(double10, double4, 0.01);
      assertNotEquals(double10, double8, 0.01);
      assertNotEquals(double10, double2, 0.01);
      assertNotEquals(double10, double0, 0.01);
      assertNotEquals(double10, double9, 0.01);
      assertNotEquals(double10, double1, 0.01);
      assertNotEquals(double10, double7, 0.01);
      assertNotEquals(double10, double5, 0.01);
      assertNotEquals(double10, double6, 0.01);
      assertEquals(89.47950134081286, double10, 0.01);
      
      double double11 = Gamma.regularizedGammaP(32.946320359583396, Double.NaN, 0.0, 210);
      assertNotEquals(double11, double9, 0.01);
      assertEquals(double11, double6, 0.01);
      assertNotEquals(double11, double3, 0.01);
      assertEquals(double11, double1, 0.01);
      assertEquals(double11, double8, 0.01);
      assertNotEquals(double11, double7, 0.01);
      assertNotEquals(double11, double5, 0.01);
      assertEquals(double11, double4, 0.01);
      assertNotEquals(double11, double2, 0.01);
      assertNotEquals(double11, double10, 0.01);
      assertNotEquals(double11, double0, 0.01);
      assertEquals(Double.NaN, double11, 0.01);
      
      double double12 = Gamma.gamma(6.820161668496171E-10);
      assertNotEquals(double12, double11, 0.01);
      assertNotEquals(double12, double0, 0.01);
      assertNotEquals(double12, double6, 0.01);
      assertNotEquals(double12, double7, 0.01);
      assertNotEquals(double12, double5, 0.01);
      assertNotEquals(double12, double9, 0.01);
      assertNotEquals(double12, double4, 0.01);
      assertNotEquals(double12, double1, 0.01);
      assertNotEquals(double12, double3, 0.01);
      assertNotEquals(double12, double2, 0.01);
      assertNotEquals(double12, double8, 0.01);
      assertNotEquals(double12, double10, 0.01);
      assertEquals(1.4662409019210641E9, double12, 0.01);
      
      double double13 = Gamma.invGamma1pm1(Double.NaN);
      assertNotEquals(double13, double2, 0.01);
      assertNotEquals(double13, double7, 0.01);
      assertNotEquals(double13, double5, 0.01);
      assertEquals(double13, double6, 0.01);
      assertNotEquals(double13, double10, 0.01);
      assertNotEquals(double13, double3, 0.01);
      assertNotEquals(double13, double12, 0.01);
      assertNotEquals(double13, double9, 0.01);
      assertEquals(double13, double1, 0.01);
      assertEquals(double13, double8, 0.01);
      assertEquals(double13, double4, 0.01);
      assertNotEquals(double13, double0, 0.01);
      assertEquals(double13, double11, 0.01);
      assertEquals(Double.NaN, double13, 0.01);
      
      double double14 = Gamma.regularizedGammaP(0.5, 0.001308780422288353, 3.141592653589793, 210);
      assertNotEquals(double14, double8, 0.01);
      assertNotEquals(double14, double2, 0.01);
      assertNotEquals(double14, double5, 0.01);
      assertNotEquals(double14, double1, 0.01);
      assertNotEquals(double14, double4, 0.01);
      assertNotEquals(double14, double3, 0.01);
      assertNotEquals(double14, double0, 0.01);
      assertNotEquals(double14, double6, 0.01);
      assertNotEquals(double14, double12, 0.01);
      assertNotEquals(double14, double9, 0.01);
      assertNotEquals(double14, double13, 0.01);
      assertNotEquals(double14, double11, 0.01);
      assertNotEquals(double14, double10, 0.01);
      assertNotEquals(double14, double7, 0.01);
      assertEquals(0.04076806118756724, double14, 0.01);
      
      double double15 = Gamma.trigamma(1.4662409019210641E9);
      assertNotEquals(double15, double13, 0.01);
      assertNotEquals(double15, double6, 0.01);
      assertNotEquals(double15, double7, 0.01);
      assertNotEquals(double15, double5, 0.01);
      assertNotEquals(double15, double14, 0.01);
      assertNotEquals(double15, double8, 0.01);
      assertNotEquals(double15, double0, 0.01);
      assertNotEquals(double15, double1, 0.01);
      assertNotEquals(double15, double4, 0.01);
      assertEquals(double15, double3, 0.01);
      assertNotEquals(double15, double11, 0.01);
      assertNotEquals(double15, double10, 0.01);
      assertNotEquals(double15, double2, 0.01);
      assertNotEquals(double15, double12, 0.01);
      assertNotEquals(double15, double9, 0.01);
      assertEquals(6.820161673506798E-10, double15, 0.01);
      
      double double16 = Gamma.logGamma(0.0);
      assertEquals(double16, double8, 0.01);
      assertNotEquals(double16, double15, 0.01);
      assertNotEquals(double16, double3, 0.01);
      assertNotEquals(double16, double12, 0.01);
      assertNotEquals(double16, double0, 0.01);
      assertEquals(double16, double11, 0.01);
      assertEquals(double16, double1, 0.01);
      assertEquals(double16, double4, 0.01);
      assertNotEquals(double16, double14, 0.01);
      assertNotEquals(double16, double10, 0.01);
      assertNotEquals(double16, double9, 0.01);
      assertNotEquals(double16, double5, 0.01);
      assertEquals(double16, double6, 0.01);
      assertNotEquals(double16, double7, 0.01);
      assertNotEquals(double16, double2, 0.01);
      assertEquals(double16, double13, 0.01);
      assertEquals(Double.NaN, double16, 0.01);
      
      double double17 = Gamma.trigamma(1742.2174290825);
      assertNotEquals(double17, double14, 0.01);
      assertNotEquals(double17, double16, 0.01);
      assertNotEquals(double17, double12, 0.01);
      assertNotEquals(double17, double0, 0.01);
      assertNotEquals(double17, double11, 0.01);
      assertEquals(double17, double15, 0.01);
      assertNotEquals(double17, double9, 0.01);
      assertNotEquals(double17, double13, 0.01);
      assertEquals(double17, double3, 0.01);
      assertNotEquals(double17, double5, 0.01);
      assertNotEquals(double17, double2, 0.01);
      assertNotEquals(double17, double7, 0.01);
      assertNotEquals(double17, double10, 0.01);
      assertNotEquals(double17, double4, 0.01);
      assertNotEquals(double17, double1, 0.01);
      assertNotEquals(double17, double8, 0.01);
      assertNotEquals(double17, double6, 0.01);
      assertEquals(5.74145929662359E-4, double17, 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double double0 = 2471.485215;
      int int0 = 10;
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(2471.485215, 2471.485215, 0.0, 10);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (10) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double double0 = Gamma.gamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ(0.0, Double.NaN);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.gamma(Double.NaN);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(0.0, 594.2121, 6.116095104481416E-9, 599);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.lanczos(0.0);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(32.94631867978169, double4, 0.01);
      
      double double5 = Gamma.invGamma1pm1(Double.NaN);
      assertEquals(double5, double0, 0.01);
      assertEquals(double5, double2, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(double5, double1, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.lanczos(0.0);
      assertNotEquals(double6, double2, 0.01);
      assertEquals(double6, double4, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertEquals(32.94631867978169, double6, 0.01);
      
      double double7 = Gamma.regularizedGammaQ(0.0, 0.0, 1.0800340064859439E241, 599);
      assertEquals(double7, double2, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertEquals(double7, double5, 0.01);
      assertEquals(double7, double3, 0.01);
      assertEquals(double7, double0, 0.01);
      assertEquals(double7, double1, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertEquals(Double.NaN, double7, 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      double double0 = Gamma.digamma(260.72);
      assertEquals(5.561528042399176, double0, 0.01);
      
      double double1 = 244.6964;
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(5.561528042399176, 260.72, 244.6964, 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction convergents failed to converge (in less than 0 iterations) for value 260.72
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      double double0 = Gamma.gamma(1246.995642033784);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.lanczos(3558.9);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0031494474562415, double1, 0.01);
      
      double double2 = Gamma.lanczos((-1107.2934382045));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.9899006463788713, double2, 0.01);
      
      double double3 = Gamma.lanczos(1.0031494474562415);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(13.120963706406666, double3, 0.01);
      
      double double4 = Gamma.digamma(0.0);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaQ((-1399.18675), 13.120963706406666);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertEquals(double5, double0, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(2054.0472416, 2453.828923, 0.054642130860422966, 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: Continued fraction convergents failed to converge (in less than 0 iterations) for value 2,453.829
         //
         verifyException("org.apache.commons.math4.util.ContinuedFraction", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      double double0 = Gamma.lanczos(605.1818);
      assertEquals(1.018569668882703, double0, 0.01);
      
      double double1 = Gamma.lanczos(605.1818);
      assertEquals(double1, double0, 0.01);
      assertEquals(1.018569668882703, double1, 0.01);
      
      double double2 = Gamma.logGamma(1.018569668882703);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals((-0.010437624361355244), double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ(954.0, (-2014.768916), 0.0, (-1773));
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      double double0 = Gamma.trigamma(1.0E-5);
      assertEquals(9.999999999999998E9, double0, 0.01);
      
      double double1 = Gamma.gamma((-1716.073));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.gamma(57.15623566586292);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(1.335659644652408E75, double2, 0.01);
      
      double double3 = 0.0;
      double double4 = Gamma.gamma(0.0);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(double4, double1, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.digamma(1.335659644652408E75);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertEquals(172.9833072602151, double5, 0.01);
      
      try { 
        Gamma.logGamma1p(172.9833072602151);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 172.983 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      double double0 = (-1.643181065367639E-4);
      double double1 = Gamma.logGamma((-1.643181065367639E-4));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Double.POSITIVE_INFINITY;
      double double3 = Gamma.digamma(Double.POSITIVE_INFINITY);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double3, 0.01);
      
      double double4 = Gamma.gamma(Double.NaN);
      assertEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      int int0 = 1710;
      double double5 = Gamma.regularizedGammaP(Double.NaN, Double.POSITIVE_INFINITY, 2230.349037, 1710);
      assertEquals(double5, double1, 0.01);
      assertEquals(double5, double4, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertEquals(Double.NaN, double5, 0.01);
      
      double double6 = Gamma.trigamma(1.0);
      assertNotEquals(double6, double4, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertEquals(1.6449340668481562, double6, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(Double.POSITIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // \u221E is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      double double0 = Gamma.gamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP(Double.NaN, 0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      double double0 = Gamma.logGamma1p(0.0);
      assertEquals(-0.0, double0, 0.01);
      
      double double1 = Gamma.logGamma1p(-0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ((-561.1), -0.0, 1.1645605564117432, 0);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.invGamma1pm1(-0.0);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(-0.0, double3, 0.01);
      
      double double4 = Gamma.digamma(Double.NaN);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.invGamma1pm1(-0.0);
      assertEquals(double5, double0, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertEquals(double5, double1, 0.01);
      assertEquals(double5, double3, 0.01);
      assertEquals(-0.0, double5, 0.01);
      
      double double6 = Gamma.gamma(6.7171126157142075E-9);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertEquals(1.4887348974667308E8, double6, 0.01);
      
      double double7 = Gamma.trigamma(1.1645605564117432);
      assertNotEquals(double7, double5, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double3, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertEquals(1.3218743443867846, double7, 0.01);
      
      double double8 = Gamma.gamma(6.079434951446575E-234);
      assertNotEquals(double8, double1, 0.01);
      assertNotEquals(double8, double7, 0.01);
      assertNotEquals(double8, double6, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertNotEquals(double8, double2, 0.01);
      assertNotEquals(double8, double4, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertNotEquals(double8, double5, 0.01);
      assertEquals(1.6448897109460054E233, double8, 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(3.399464998481189E-5, 0.0, 0.0, 0);
      assertEquals(0.0, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ(0.0, 1879.787045);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP((double) 0, (double) 0);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(0.0, 0.0);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.trigamma((-6.077618957228252E-8));
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(2.7072793079398194E14, double4, 0.01);
      
      double double5 = Gamma.trigamma((-6.077618957228252E-8));
      assertNotEquals(double5, double0, 0.01);
      assertEquals(double5, double4, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertEquals(2.7072793079398194E14, double5, 0.01);
      
      double double6 = Gamma.trigamma(5.055224959032211E-240);
      assertNotEquals(double6, double4, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double6, 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      double double0 = (-1608.899778088);
      double double1 = Gamma.lanczos((-1608.899778088));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.9930455233354198, double1, 0.01);
      
      double double2 = Gamma.digamma(0.9930455233354198);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals((-0.5887138268114872), double2, 0.01);
      
      double double3 = Gamma.logGamma(0.9930455233354198);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(0.004054146688421994, double3, 0.01);
      
      double double4 = Gamma.digamma(0.0);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double4, 0.01);
      
      double double5 = Gamma.regularizedGammaQ(251.9757704569, 251.9757704569);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(0.49162241369610893, double5, 0.01);
      
      double double6 = Gamma.regularizedGammaP(4.686843322948848E-11, 0.0, 3306.637, 1553);
      assertNotEquals(double6, double4, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertEquals(double6, double3, 0.01);
      assertNotEquals(double6, double2, 0.01);
      assertEquals(0.0, double6, 0.01);
      
      double double7 = Gamma.gamma((-1608.899778088));
      assertNotEquals(double7, double3, 0.01);
      assertNotEquals(double7, double0, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double1, 0.01);
      assertNotEquals(double7, double4, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertEquals(Double.NaN, double7, 0.01);
      
      double double8 = 0.0;
      double double9 = Gamma.regularizedGammaQ((-2090.634489438103), 0.0, 4.686843322948848E-11, 1553);
      assertNotEquals(double9, double3, 0.01);
      assertNotEquals(double9, double4, 0.01);
      assertNotEquals(double9, double0, 0.01);
      assertNotEquals(double9, double1, 0.01);
      assertNotEquals(double9, double5, 0.01);
      assertNotEquals(double9, double6, 0.01);
      assertNotEquals(double9, double2, 0.01);
      assertNotEquals(double9, double8, 0.01);
      assertEquals(double9, double7, 0.01);
      assertEquals(Double.NaN, double9, 0.01);
      
      double double10 = Gamma.logGamma(1553);
      assertNotEquals(double10, double0, 0.01);
      assertNotEquals(double10, double3, 0.01);
      assertNotEquals(double10, double9, 0.01);
      assertNotEquals(double10, double7, 0.01);
      assertNotEquals(double10, double2, 0.01);
      assertNotEquals(double10, double8, 0.01);
      assertNotEquals(double10, double5, 0.01);
      assertNotEquals(double10, double6, 0.01);
      assertNotEquals(double10, double4, 0.01);
      assertNotEquals(double10, double1, 0.01);
      assertEquals(9855.601777631126, double10, 0.01);
      
      double double11 = Gamma.regularizedGammaQ(0.0, 2.6971278190612793);
      assertNotEquals(double11, double2, 0.01);
      assertNotEquals(double11, double8, 0.01);
      assertNotEquals(double11, double3, 0.01);
      assertNotEquals(double11, double0, 0.01);
      assertNotEquals(double11, double1, 0.01);
      assertNotEquals(double11, double4, 0.01);
      assertEquals(double11, double9, 0.01);
      assertNotEquals(double11, double10, 0.01);
      assertNotEquals(double11, double5, 0.01);
      assertEquals(double11, double7, 0.01);
      assertNotEquals(double11, double6, 0.01);
      assertEquals(Double.NaN, double11, 0.01);
      
      try { 
        Gamma.logGamma1p((-1608.899778088));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -1,608.9 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      double double0 = 4137.31498142;
      double double1 = Gamma.trigamma(4137.31498142);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(2.417318636733462E-4, double1, 0.01);
      
      double double2 = Gamma.logGamma(2.417318636733462E-4);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(8.327541964080892, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ(4137.31498142, (-2521.319617));
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      int int0 = (-438);
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(4137.31498142, 2.417318636733462E-4, 8.327541964080892, (-438));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (-438) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP((-2.056338416977607E-7), (-2.056338416977607E-7));
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.logGamma(Double.NaN);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.lanczos((-0.42278433509846713));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(67.58129648038758, double2, 0.01);
      
      double double3 = Gamma.gamma(Double.NaN);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaQ(Double.NaN, (-2.056338416977607E-7), Double.NaN, 0);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(double4, double0, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(double4, double3, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
      
      double double5 = Gamma.invGamma1pm1(0);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertEquals(0.0, double5, 0.01);
      
      double double6 = Gamma.invGamma1pm1((-0.42278433509846713));
      assertNotEquals(double6, double2, 0.01);
      assertNotEquals(double6, double1, 0.01);
      assertNotEquals(double6, double5, 0.01);
      assertNotEquals(double6, double4, 0.01);
      assertNotEquals(double6, double3, 0.01);
      assertNotEquals(double6, double0, 0.01);
      assertEquals((-0.3522819961430704), double6, 0.01);
      
      double double7 = Gamma.logGamma(Double.NaN);
      assertEquals(double7, double4, 0.01);
      assertNotEquals(double7, double6, 0.01);
      assertEquals(double7, double0, 0.01);
      assertEquals(double7, double3, 0.01);
      assertEquals(double7, double1, 0.01);
      assertNotEquals(double7, double2, 0.01);
      assertNotEquals(double7, double5, 0.01);
      assertEquals(Double.NaN, double7, 0.01);
      
      double double8 = Gamma.logGamma1p((-2.056338416977607E-7));
      assertNotEquals(double8, double7, 0.01);
      assertNotEquals(double8, double1, 0.01);
      assertEquals(double8, double5, 0.01);
      assertNotEquals(double8, double3, 0.01);
      assertNotEquals(double8, double6, 0.01);
      assertNotEquals(double8, double0, 0.01);
      assertNotEquals(double8, double4, 0.01);
      assertNotEquals(double8, double2, 0.01);
      assertEquals(1.1869510944007921E-7, double8, 0.01);
      
      double double9 = Gamma.logGamma(Double.NaN);
      assertNotEquals(double9, double6, 0.01);
      assertNotEquals(double9, double2, 0.01);
      assertEquals(double9, double3, 0.01);
      assertEquals(double9, double1, 0.01);
      assertNotEquals(double9, double5, 0.01);
      assertEquals(double9, double7, 0.01);
      assertNotEquals(double9, double8, 0.01);
      assertEquals(double9, double0, 0.01);
      assertEquals(double9, double4, 0.01);
      assertEquals(Double.NaN, double9, 0.01);
      
      try { 
        Gamma.logGamma1p((-225.717863235259));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -225.718 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(0.0, 3156.6606725);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP(1021.9993313, 0.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ(Double.NaN, 0.0, 0.0, 575);
      assertEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      double double0 = Gamma.trigamma(1269.7114939470744);
      assertEquals(7.878907124920225E-4, double0, 0.01);
      
      double double1 = Gamma.digamma(1558.8872738);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(7.351406783815691, double1, 0.01);
      
      double double2 = Gamma.lanczos(1981.0);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(1.005660470321175, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaQ(7.878907124920225E-4, (-1243.9033));
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      double double0 = Gamma.invGamma1pm1(7.130134251490065E-128);
      assertEquals(4.1156251828110315E-128, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaQ(7.130134251490065E-128, 2.0624330192486066E-8, 2.0624330192486066E-8, 1446);
      assertEquals(double1, double0, 0.01);
      assertEquals((-7.105427357601002E-15), double1, 0.01);
      
      double double2 = Gamma.gamma(7.130134251490065E-128);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(1.4024981364004733E127, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(0.0, 0.0);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = Gamma.lanczos(3102.432467);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0036131197270917, double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP(4.686843322948848E-11, 1.0036131197270917);
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.9999999999897798, double2, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1(6.283185307179586);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 6.283 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ(3.399464998481189E-5, 1757.448367121829);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      double double0 = Gamma.logGamma(0.0);
      assertEquals(Double.NaN, double0, 0.01);
      
      double double1 = (-863.84);
      double double2 = Gamma.digamma((-863.84));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(1.0474304402447936, double2, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-195.8662248528));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -195.866 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      double double0 = Gamma.digamma(0.0);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      
      try { 
        Gamma.logGamma1p(Double.NEGATIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -\u221E is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      double double0 = Gamma.lanczos(496.0);
      assertEquals(1.022672982924594, double0, 0.01);
      
      double double1 = Gamma.invGamma1pm1(1.022672982924594);
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-0.009703386500907287), double1, 0.01);
      
      double double2 = Gamma.regularizedGammaQ(0.0, (-2005.15789657712), 2231.32037366702, 0);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(1566.817649, Double.NaN);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = Gamma.regularizedGammaP(496.0, (-2320.789546), (-1532.892879692645), 1591);
      assertEquals(double4, double3, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(Double.NaN, double4, 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      double double0 = (-2140.0);
      // Undeclared exception!
      try { 
        Gamma.invGamma1pm1((-2140.0));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -2,140 is smaller than the minimum (-0.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      double double0 = Gamma.trigamma(867.284);
      assertEquals(0.0011536898340109426, double0, 0.01);
      
      double double1 = Gamma.gamma(1788.458627);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      double double2 = Gamma.logGamma(452.9858349);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(2315.2736929117295, double2, 0.01);
      
      double double3 = Gamma.logGamma(867.284);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(4997.746461821348, double3, 0.01);
      
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaP(1788.458627, 1788.458627, 0.0011536898340109426, 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (0) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      double double0 = Gamma.logGamma((-37.291972334808));
      double double1 = Gamma.trigamma(Double.NaN);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(0.0, 0.0, 3.6899182659531625E-6, 0);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      double double0 = Gamma.regularizedGammaQ((-1257.58386120451), 0.0, (-1257.58386120451), (-2964));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      double double0 = Gamma.logGamma1p(0.0);
      assertEquals(-0.0, double0, 0.01);
      
      double double1 = Gamma.regularizedGammaP((-2149.93162690328), -0.0);
      assertEquals(Double.NaN, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      double double0 = (-2669.42656405922);
      Gamma.logGamma((-2669.42656405922));
      double double1 = 1.0E-14;
      Gamma.invGamma1pm1(1.0E-14);
      double double2 = 1.8958141803741455;
      try { 
        Gamma.logGamma1p(1.8958141803741455);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 1.896 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      double double0 = 4.7421875;
      try { 
        Gamma.logGamma1p(4.7421875);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 4.742 is larger than the maximum (1.5)
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      double double0 = 0.0;
      Gamma.trigamma(0.0);
      Gamma.digamma(2.5);
      Gamma.gamma(0.7031566378697297);
      Gamma.logGamma((-544.8989008848331));
      // Undeclared exception!
      try { 
        Gamma.regularizedGammaQ(1.2930841257168832, 1.2930841257168832, (-672.9847366), 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // illegal state: maximal count (0) exceeded
         //
         verifyException("org.apache.commons.math4.special.Gamma", e);
      }
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      double double0 = Gamma.digamma(2.053884626293416E-85);
      assertEquals((-4.868822655363413E84), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      double double0 = Gamma.regularizedGammaP(1478.84745468405, 1478.84745468405, 1.3088328582956644E-7, 904);
      assertEquals(0.5034575673039934, double0, 0.01);
      
      Gamma.regularizedGammaQ(0.9999999999999971, 1.1379799629071911E-50);
      Gamma.regularizedGammaP((double) 904, 1.1379799629071911E-50);
      Gamma.lanczos(1.3088328582956644E-7);
      double double1 = Gamma.logGamma1p(1.1379799629071911E-50);
      assertEquals((-6.568598609340961E-51), double1, 0.01);
      
      double double2 = Gamma.regularizedGammaP(0.0, 32.94631294752913);
      assertEquals(Double.NaN, double2, 0.01);
      
      double double3 = Gamma.regularizedGammaP(32.94631294752913, 1.3088328582956644E-7);
      assertEquals(2.3436029909514165E-264, double3, 0.01);
      
      Gamma.regularizedGammaQ(1.1379799629071911E-50, 0.0);
      double double4 = Gamma.logGamma(2.3436029909514165E-264);
      assertEquals(607.0307750655194, double4, 0.01);
      
      Gamma.regularizedGammaQ(0.0, 0.43053877353668213);
      double double5 = Gamma.regularizedGammaP(1.1379799629071911E-50, (double) 904);
      double double6 = Gamma.gamma((-6.568598609340961E-51));
      assertEquals((-1.5223947442578346E50), double6, 0.01);
      
      double double7 = Gamma.regularizedGammaQ(1.1379799629071911E-50, 0.0, 1147.01567301, 904);
      assertEquals(double7, double5, 0.01);
      assertEquals(1.0, double7, 0.01);
  }
}
