/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 20:07:48 GMT 2019
 */

package org.apache.commons.math.ode.nonstiff;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import org.apache.commons.math.ode.FirstOrderConverter;
import org.apache.commons.math.ode.FirstOrderDifferentialEquations;
import org.apache.commons.math.ode.SecondOrderDifferentialEquations;
import org.apache.commons.math.ode.events.EventHandler;
import org.apache.commons.math.ode.nonstiff.DormandPrince54Integrator;
import org.apache.commons.math.ode.nonstiff.DormandPrince853Integrator;
import org.apache.commons.math.ode.nonstiff.HighamHall54Integrator;
import org.apache.commons.math.ode.sampling.DummyStepHandler;
import org.apache.commons.math.ode.sampling.FixedStepHandler;
import org.apache.commons.math.ode.sampling.StepHandler;
import org.apache.commons.math.ode.sampling.StepNormalizer;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class EmbeddedRungeKuttaIntegrator_ESTest extends EmbeddedRungeKuttaIntegrator_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[2] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(eventHandler0).eventOccurred(anyDouble() , any(double[].class) , anyBoolean());
      doReturn(3.735473834576843E-21, (-2243.655716359544), 10.0, 3.735473834576843E-21, (-1.0)).when(eventHandler0).g(anyDouble() , any(double[].class));
      dormandPrince54Integrator0.addEventHandler(eventHandler0, 317.7105912977, 7.647903767660406E-22, (-2147462293));
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[1] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(8.360359937368687E-18, double0, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertNotEquals(double1, double0, 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      double double2 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, (-2599.2918), doubleArray0);
      assertEquals((-317.5105912977), double2, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, firstOrderConverter0.getDimension());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-100622.30923143645), (-100622.30923143645), (-100622.30923143645), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[2] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[1] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      DummyStepHandler dummyStepHandler0 = DummyStepHandler.getInstance();
      assertNotNull(dummyStepHandler0);
      assertFalse(dummyStepHandler0.requiresDenseOutput());
      
      dormandPrince54Integrator0.addStepHandler(dummyStepHandler0);
      assertFalse(dummyStepHandler0.requiresDenseOutput());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(8.360359937368687E-18, double0, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      double double2 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, (-2599.2918), doubleArray0);
      assertEquals((-2599.2918), double2, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(62, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, firstOrderConverter0.getDimension());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-825568.3662602248), (-825568.3662602248), (-825568.3662602248), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[2] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(eventHandler0).eventOccurred(anyDouble() , any(double[].class) , anyBoolean());
      doReturn(3.735473834576843E-21, (-2243.655716359544), 10.0, 3.735473834576843E-21, (-1.0)).when(eventHandler0).g(anyDouble() , any(double[].class));
      dormandPrince54Integrator0.addEventHandler(eventHandler0, 317.7105912977, 7.647903767660406E-22, (-2147462293));
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[1] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(8.360359937368687E-18, double0, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      double double2 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, (-2599.2918), doubleArray0);
      assertEquals((-317.5105912977), double2, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(8, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, firstOrderConverter0.getDimension());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-100622.30923143645), (-100622.30923143645), (-100622.30923143645), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(8, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-100622.30923143645), (-100622.30923143645), (-100622.30923143645), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[2] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(317.7105912977, 317.7105912977, 317.7105912977, (-1.0), 317.7105912977).when(eventHandler0).g(anyDouble() , any(double[].class));
      dormandPrince54Integrator0.addEventHandler(eventHandler0, 317.7105912977, 7.647903767660406E-22, (-2147462293));
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[1] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(8.360359937368687E-18, double0, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, (-2599.2918), doubleArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // function values at endpoints do not have different signs.  Endpoints: [-476.366, -317.511], Values: [317.711, 317.711]
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[2] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(0.0, 0.0, 0.0, 0.0, 0.0).when(eventHandler0).g(anyDouble() , any(double[].class));
      dormandPrince54Integrator0.addEventHandler(eventHandler0, 317.7105912977, 7.647903767660406E-22, (-2147462293));
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[1] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(8.360359937368687E-18, double0, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      double double2 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, (-2599.2918), doubleArray0);
      assertEquals((-2599.2918), double2, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(62, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, firstOrderConverter0.getDimension());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-825568.3662602248), (-825568.3662602248), (-825568.3662602248), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(62, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-825568.3662602248), (-825568.3662602248), (-825568.3662602248), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[2] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[1] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(8.360359937368687E-18, double0, 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      double double2 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, (-2599.2918), doubleArray0);
      assertEquals((-2599.2918), double2, 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(62, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(6, firstOrderConverter0.getDimension());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-825568.3662602248), (-825568.3662602248), (-825568.3662602248), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      double double3 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 317.7105912977, doubleArray0, 1.83377491834714E-21, doubleArray0);
      assertEquals(0.0, double3, 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(14, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(6, firstOrderConverter0.getDimension());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-926508.386082959), (-926508.386082959), (-926508.386082959), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[2] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[1] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(8.360359937368687E-18, double0, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      double double2 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, (-2599.2918), doubleArray0);
      assertEquals((-2599.2918), double2, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(62, dormandPrince54Integrator0.getEvaluations());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, firstOrderConverter0.getDimension());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-825568.3662602248), (-825568.3662602248), (-825568.3662602248), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-597.608618614), 317.7105912977, 2.7755575615628914E-17, 317.7105912977);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(435.7368329444455, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(597.608618614, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      
      int int0 = dormandPrince853Integrator0.getOrder();
      assertEquals(8, int0);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(435.7368329444455, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(597.608618614, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      
      double double3 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, (-4417.513844262782), doubleArray0);
      assertEquals((-4417.513844262782), double3, 0.01);
      assertEquals(92, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, firstOrderConverter0.getDimension());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-2229122.843904989), (-2229122.843904989), (-2229122.843904989), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[2] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(8.360359937368687E-18, double0, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, 101194.18829577231, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (-1.0);
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, (-1.0), 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = dormandPrince54Integrator0.filterStep(0.0, true, false);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, (-1.0), 0.0, 0.0}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.setSafety(0.0);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, (-1.0), 0.0, 0.0}, doubleArray0, 0.01);
      
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, (-871.12951), 0.0, 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(871.12951, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      
      int int0 = dormandPrince853Integrator0.getOrder();
      assertEquals(8, int0);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(871.12951, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.0, double1, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, (-1.0), 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(double1, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      double double0 = 0.0;
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = 731.7449358206;
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 731.7449358206}, doubleArray0, 0.01);
      
      Collection<StepHandler> collection0 = dormandPrince853Integrator0.getStepHandlers();
      assertNotNull(collection0);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 731.7449358206}, doubleArray0, 0.01);
      
      Collection<StepHandler> collection1 = dormandPrince853Integrator0.getStepHandlers();
      assertNotNull(collection1);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 731.7449358206}, doubleArray0, 0.01);
      assertFalse(collection1.equals((Object)collection0));
      assertNotSame(collection1, collection0);
      
      dormandPrince853Integrator0.setMaxGrowth((-2971.89));
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals((-2971.89), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 731.7449358206}, doubleArray0, 0.01);
      
      double double1 = dormandPrince853Integrator0.getMaxGrowth();
      assertEquals((-2971.89), double1, 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals((-2971.89), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 731.7449358206}, doubleArray0, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      dormandPrince853Integrator0.clearEventHandlers();
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals((-2971.89), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 731.7449358206}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        dormandPrince853Integrator0.computeDerivatives((-2971.89), doubleArray0, doubleArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (-453.6091276946);
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = 0.9;
      doubleArray0[7] = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.clearEventHandlers();
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.setMinReduction(0.0);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.setMinReduction(1.0);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.setMinReduction(0.0);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      
      Collection<EventHandler> collection0 = dormandPrince54Integrator0.getEventHandlers();
      assertNotNull(collection0);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.setMaxGrowth(0.9);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.resetInternalState();
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      
      double double0 = dormandPrince54Integrator0.getMinReduction();
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      
      double double1 = dormandPrince54Integrator0.getMaxGrowth();
      assertEquals(0.9, double1, 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      dormandPrince54Integrator0.setSafety((-453.6091276946));
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals((-453.6091276946), dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      
      double double2 = dormandPrince54Integrator0.getSafety();
      assertEquals((-453.6091276946), double2, 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals((-453.6091276946), dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      double double3 = dormandPrince54Integrator0.getSafety();
      assertEquals((-453.6091276946), double3, 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals((-453.6091276946), dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-453.6091276946), 0.0, 0.0, 0.0, 0.9, 0.0}, doubleArray0, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(double3, double2, 0.01);
      
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1134.06634232964), (-453.6091276946), 0.0, 0.0);
      assertNotNull(highamHall54Integrator0);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(717.2327685570101, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(453.6091276946, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(1134.06634232964, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      
      double[][] doubleArray1 = new double[4][9];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      // Undeclared exception!
      try { 
        highamHall54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 0.0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, 0.0, 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      
      dormandPrince853Integrator0.setMinReduction((-1041.442));
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals((-1041.442), dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      
      dormandPrince853Integrator0.setMaxGrowth((-1361.80279));
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals((-1041.442), dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals((-1361.80279), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      
      double double0 = dormandPrince853Integrator0.getMinReduction();
      assertEquals((-1041.442), double0, 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals((-1041.442), dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals((-1361.80279), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-1041.442);
      doubleArray0[1] = (-1361.80279);
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (-1041.442);
      doubleArray0[4] = (-1361.80279);
      doubleArray0[5] = (-1044.0);
      doubleArray0[6] = 0.0;
      try { 
        dormandPrince853Integrator0.integrate(firstOrderConverter0, 0.0, doubleArray0, 241.5794896584, doubleArray0);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 0, initial state vector has dimension 7
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-4439.26230811), (-4439.26230811), doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(4439.26230811, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals((-4439.26230811), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-4439.26230811), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.0, doubleArray0, 4611.6541139, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 1.0, 0.0, 0.0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      
      dormandPrince54Integrator0.setInitialStepSize(1.0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      
      double double0 = dormandPrince54Integrator0.filterStep((-1756.176), true, true);
      assertEquals((-1.0), double0, 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      
      dormandPrince54Integrator0.setMaxGrowth((-1756.176));
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1756.176), dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      
      dormandPrince54Integrator0.setSafety((-1.0));
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1756.176), dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals((-1.0), dormandPrince54Integrator0.getSafety(), 0.01);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals((-1.0), double1, 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1756.176), dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals((-1.0), dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(double1, double0, 0.01);
      
      dormandPrince54Integrator0.setSafety((-1756.176));
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1756.176), dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals((-1756.176), dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (-1756.176);
      doubleArray0[1] = 0.0;
      FixedStepHandler fixedStepHandler0 = mock(FixedStepHandler.class, new ViolatedAssumptionAnswer());
      StepNormalizer stepNormalizer0 = new StepNormalizer(0.0, fixedStepHandler0);
      assertNotNull(stepNormalizer0);
      assertTrue(stepNormalizer0.requiresDenseOutput());
      
      dormandPrince54Integrator0.addStepHandler(stepNormalizer0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1756.176), dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals((-1756.176), dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertTrue(stepNormalizer0.requiresDenseOutput());
      
      doubleArray0[2] = 1.0;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.integrate((FirstOrderDifferentialEquations) null, 0.0, doubleArray0, 0.0, doubleArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.0, 599.8753725977488, 599.8753725977488, (-1.0));
      assertNotNull(highamHall54Integrator0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(599.8753725977488, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      
      Collection<StepHandler> collection0 = highamHall54Integrator0.getStepHandlers();
      assertNotNull(collection0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(599.8753725977488, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      
      highamHall54Integrator0.clearStepHandlers();
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(599.8753725977488, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      
      highamHall54Integrator0.setMaxGrowth(0.0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(599.8753725977488, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      
      highamHall54Integrator0.setSafety(0.03709200011850479);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.03709200011850479, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(599.8753725977488, highamHall54Integrator0.getMaxStep(), 0.01);
      
      highamHall54Integrator0.setMinReduction(1137963.0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1137963.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.03709200011850479, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(599.8753725977488, highamHall54Integrator0.getMaxStep(), 0.01);
      
      double double0 = highamHall54Integrator0.getMaxGrowth();
      assertEquals(0.0, double0, 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1137963.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.03709200011850479, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(599.8753725977488, highamHall54Integrator0.getMaxStep(), 0.01);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 0.03709200011850479;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 1650.00362776365;
      doubleArray0[4] = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(1137963.0, 0.03709200011850479, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1137963.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(205.4490781942184, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.03709200011850479, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {0.03709200011850479, 0.0, 0.0, 1650.00362776365, 0.0}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[7][7];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 599.8753725977488;
      doubleArray2[1] = 0.0;
      doubleArray2[2] = 599.8753725977488;
      doubleArray2[3] = 1137963.0;
      doubleArray2[4] = 1137963.0;
      doubleArray2[5] = 0.03709200011850479;
      doubleArray2[6] = (-1.0);
      doubleArray2[7] = (-1.0);
      double double1 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray2, 0.0);
      assertEquals(Double.NaN, double1, 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1137963.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(205.4490781942184, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.03709200011850479, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(8, doubleArray2.length);
      assertEquals(5, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertArrayEquals(new double[] {599.8753725977488, 0.0, 599.8753725977488, 1137963.0, 1137963.0, 0.03709200011850479, (-1.0), (-1.0)}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {0.03709200011850479, 0.0, 0.0, 1650.00362776365, 0.0}, doubleArray0, 0.01);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, 0.0, 0.0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      double double0 = dormandPrince54Integrator0.getMaxGrowth();
      assertEquals(10.0, double0, 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(2008.75, 40.02137065, 40.02137065, 1.0);
      assertNotNull(highamHall54Integrator0);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2008.75, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(40.02137065, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(283.53646730744794, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      highamHall54Integrator0.setSafety((-355.5991));
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-355.5991), highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(2008.75, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(40.02137065, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(283.53646730744794, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(16.5185261, 16.5185261, 0.0, (-30.0));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(16.5185261, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(16.5185261, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(16.5185261, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      
      double double0 = dormandPrince54Integrator0.getMinReduction();
      assertEquals(0.2, double0, 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(16.5185261, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(16.5185261, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(16.5185261, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(2716.67621078435, (-316.729744), 797.6, 2716.67621078435);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(927.6056062641154, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(316.729744, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2716.67621078435, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      
      dormandPrince853Integrator0.setInitialStepSize(2716.67621078435);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(927.6056062641154, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(316.729744, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2716.67621078435, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      
      int int0 = dormandPrince853Integrator0.getOrder();
      assertEquals(8, int0);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(927.6056062641154, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(316.729744, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2716.67621078435, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      
      int int1 = dormandPrince853Integrator0.getOrder();
      assertEquals(8, int1);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(927.6056062641154, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(316.729744, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2716.67621078435, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertTrue(int1 == int0);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 797.6;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (double) 8;
      doubleArray0[3] = (double) 8;
      doubleArray0[4] = (double) 8;
      doubleArray0[5] = 2716.67621078435;
      double[] doubleArray1 = new double[4];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) 8;
      doubleArray1[1] = 0.0;
      doubleArray1[2] = (-316.729744);
      doubleArray1[3] = 797.6;
      try { 
        dormandPrince853Integrator0.integrate(firstOrderConverter0, 0.0, doubleArray0, 797.6, doubleArray1);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 0, initial state vector has dimension 6
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(4807.88390006173, 4807.88390006173, (-352.9953545), 4807.88390006173);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(4807.88390006173, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(4807.88390006173, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(4807.88390006173, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      
      double double0 = dormandPrince853Integrator0.getMinReduction();
      assertEquals(0.2, double0, 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(4807.88390006173, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(4807.88390006173, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(4807.88390006173, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(eventHandler0).eventOccurred(anyDouble() , any(double[].class) , anyBoolean());
      doReturn(3.735473834576843E-21, (-2243.655716359544), 10.0, 3.735473834576843E-21, (-1.0)).when(eventHandler0).g(anyDouble() , any(double[].class));
      dormandPrince54Integrator0.addEventHandler(eventHandler0, 317.7105912977, 7.647903767660406E-22, (-2147462293));
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[1] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(0.033651288179195625, double0, 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 0.0, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      double double2 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, (-2599.2918), doubleArray0);
      assertEquals((-317.5105912977), double2, 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(6, firstOrderConverter0.getDimension());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-100622.30923143645), (-100622.30923143645), (-100940.01982273415), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince54Integrator0.getEvaluations());
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[2] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[1] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(8.360359937368687E-18, double0, 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, 101194.18829577231, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 317.7105912977;
      doubleArray0[1] = 317.7105912977;
      doubleArray0[2] = 317.7105912977;
      doubleArray0[3] = 317.7105912977;
      doubleArray0[4] = 317.7105912977;
      doubleArray0[5] = 317.7105912977;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(317.7105912977, 317.7105912977, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 317.7105912977;
      doubleArray2[1] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[2] = 317.7105912977;
      doubleArray2[4] = 317.7105912977;
      doubleArray2[5] = 317.7105912977;
      doubleArray2[6] = 317.7105912977;
      doubleArray2[7] = 317.7105912977;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double double0 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 317.7105912977);
      assertEquals(8.360359937368687E-18, double0, 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.9, double1, 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(6, firstOrderConverter0.getDimension());
      
      double double2 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.2, doubleArray0, (-2599.2918), doubleArray0);
      assertEquals((-2599.2918), double2, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(62, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, firstOrderConverter0.getDimension());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-825568.3662602248), (-825568.3662602248), (-825568.3662602248), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(62, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-825568.3662602248), (-825568.3662602248), (-825568.3662602248), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      int int1 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, int1);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(62, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-825568.3662602248), (-825568.3662602248), (-825568.3662602248), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertTrue(int1 == int0);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      
      double double3 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, (-4765.458869465499));
      assertEquals(1.0023463698723055E-16, double3, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(317.7105912977, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(62, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(6, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {(-825568.3662602248), (-825568.3662602248), (-825568.3662602248), 317.7105912977, 317.7105912977, 317.7105912977}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double3, double1, 0.01);
      assertEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, (-8.1060272), doubleArray0, (-7790429.734028886), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double double0 = 1078.779;
      double double1 = 0.2;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(1078.779, 1078.779, 0.2, 0.2);
      assertNotNull(highamHall54Integrator0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(1078.779, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1078.779, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1078.779, highamHall54Integrator0.getMaxStep(), 0.01);
      
      double double2 = highamHall54Integrator0.getSafety();
      assertEquals(0.9, double2, 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(1078.779, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1078.779, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1078.779, highamHall54Integrator0.getMaxStep(), 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      double double3 = 0.0;
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = new double[5];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      double double4 = 0.0;
      // Undeclared exception!
      highamHall54Integrator0.integrate(firstOrderConverter0, 0.0, doubleArray0, (-201.68), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, 0.0, 0.0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      
      double double0 = dormandPrince54Integrator0.filterStep(500.7421114874954, false, false);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      
      double double1 = dormandPrince54Integrator0.getMaxGrowth();
      assertEquals(10.0, double1, 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertNotEquals(double1, double0, 0.01);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.0;
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      Collection<EventHandler> collection0 = dormandPrince54Integrator0.getEventHandlers();
      assertNotNull(collection0);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.clearEventHandlers();
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(5, 0.0, 5, 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(5.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      
      double[][] doubleArray1 = new double[1][2];
      doubleArray1[0] = doubleArray0;
      double double0 = dormandPrince853Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 0.0);
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(5.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      int int1 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, int1);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertTrue(int1 == int0);
      
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.9, (-1.0), doubleArray0, doubleArray0);
      assertNotNull(highamHall54Integrator0);
      assertEquals(0.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1.0), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      highamHall54Integrator0.setMinReduction((-1.0));
      assertEquals(0.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-1.0), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1.0), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      double double1 = highamHall54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 5);
      assertEquals(Double.NaN, double1, 0.01);
      assertEquals(0.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-1.0), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1.0), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(double1, double0, 0.01);
      
      int int2 = dormandPrince853Integrator0.getOrder();
      assertEquals(8, int2);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(5.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      
      double[][] doubleArray2 = new double[7][8];
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      doubleArray2[0] = doubleArray0;
      doubleArray2[1] = doubleArray0;
      doubleArray2[2] = doubleArray0;
      doubleArray2[3] = doubleArray0;
      doubleArray2[4] = doubleArray0;
      doubleArray2[5] = doubleArray0;
      doubleArray2[6] = doubleArray0;
      double double2 = highamHall54Integrator0.estimateError(doubleArray2, doubleArray0, doubleArray0, 5);
      assertEquals(Double.NaN, double2, 0.01);
      assertEquals(0.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-1.0), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1.0), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, doubleArray0.length);
      assertEquals(7, doubleArray2.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertNotSame(doubleArray2, doubleArray1);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, 10.0, doubleArray0, Double.NaN, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      dormandPrince54Integrator0.getEventHandlers();
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.clearEventHandlers();
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(5, 0.0, 5, 0.0);
      double[][] doubleArray1 = new double[1][2];
      doubleArray1[0] = doubleArray0;
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      double[] doubleArray2 = new double[0];
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.0, doubleArray0, 5, doubleArray2);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      double[] doubleArray0 = null;
      HighamHall54Integrator highamHall54Integrator0 = null;
      try {
        highamHall54Integrator0 = new HighamHall54Integrator(0.0, 0.0, (double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.nonstiff.AdaptiveStepsizeIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(1.0, 1.0, 827.861288, (-1376.42415648155));
      dormandPrince853Integrator0.setSafety((-1376.42415648155));
      dormandPrince853Integrator0.getMinReduction();
      double[][] doubleArray0 = new double[4][8];
      double[] doubleArray1 = new double[9];
      doubleArray1[0] = 0.2;
      doubleArray1[1] = 827.861288;
      doubleArray1[2] = 1.0;
      doubleArray1[3] = 1.0;
      doubleArray1[4] = 1.0;
      doubleArray1[5] = 0.2;
      doubleArray1[6] = 1.0;
      doubleArray1[7] = 1.0;
      doubleArray1[8] = (-1376.42415648155);
      doubleArray0[0] = doubleArray1;
      double[] doubleArray2 = new double[3];
      doubleArray2[0] = 1.0;
      doubleArray2[1] = 0.2;
      doubleArray2[2] = 753.4429410103;
      doubleArray0[1] = doubleArray2;
      double[] doubleArray3 = new double[3];
      doubleArray3[1] = 753.4429410103;
      doubleArray3[2] = 753.4429410103;
      doubleArray0[2] = doubleArray3;
      doubleArray0[3] = doubleArray1;
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(3).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertEquals(6, firstOrderConverter0.getDimension());
  }
}
