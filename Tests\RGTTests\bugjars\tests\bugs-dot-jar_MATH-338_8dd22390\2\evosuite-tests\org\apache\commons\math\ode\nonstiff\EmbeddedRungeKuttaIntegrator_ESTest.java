/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 20:15:35 GMT 2019
 */

package org.apache.commons.math.ode.nonstiff;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import org.apache.commons.math.ode.FirstOrderConverter;
import org.apache.commons.math.ode.FirstOrderDifferentialEquations;
import org.apache.commons.math.ode.SecondOrderDifferentialEquations;
import org.apache.commons.math.ode.events.EventHandler;
import org.apache.commons.math.ode.nonstiff.DormandPrince54Integrator;
import org.apache.commons.math.ode.nonstiff.DormandPrince853Integrator;
import org.apache.commons.math.ode.nonstiff.HighamHall54Integrator;
import org.apache.commons.math.ode.sampling.StepHandler;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class EmbeddedRungeKuttaIntegrator_ESTest extends EmbeddedRungeKuttaIntegrator_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-35.465418571126406), (-35.465418571126406), (-35.465418571126406), 23.37245291056269);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      dormandPrince54Integrator0.clearStepHandlers();
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-35.465418571126406);
      doubleArray0[1] = 23.37245291056269;
      double double0 = dormandPrince54Integrator0.integrate(firstOrderConverter0, (-3619.7), doubleArray0, 0.22552427941871755, doubleArray0);
      assertArrayEquals(new double[] {84571.07343739332, 23.37245291056269}, doubleArray0, 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(626, dormandPrince54Integrator0.getEvaluations());
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertEquals(2, doubleArray0.length);
      assertEquals(0.22552427941871755, double0, 0.01);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(626, dormandPrince54Integrator0.getEvaluations());
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, double1, 0.01);
      
      double[][] doubleArray1 = new double[7][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[2];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 23.37245291056269;
      doubleArray2[1] = 0.9;
      double double2 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray2, (-3828.910065848233));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertArrayEquals(new double[] {23.37245291056269, 0.9}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {84571.07343739332, 23.37245291056269}, doubleArray0, 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(626, dormandPrince54Integrator0.getEvaluations());
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(35.465418571126406, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray2.length);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertEquals(1.2457818438821513E-15, double2, 0.01);
      
      double[][] doubleArray3 = new double[4][5];
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      
      doubleArray3[0] = doubleArray0;
      doubleArray3[1] = doubleArray0;
      doubleArray3[2] = doubleArray0;
      doubleArray3[3] = doubleArray2;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray3, doubleArray0, doubleArray2, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double double0 = (-10.603513857775251);
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-10.603513857775251), (-10.603513857775251), (-10.603513857775251), (-0.8831545382286958));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double double1 = (-39.17726167561544);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-10.603513857775251);
      doubleArray0[1] = (-0.8831545382286958);
      double double2 = (-1.261386569922121);
      double double3 = dormandPrince54Integrator0.integrate(firstOrderConverter0, (-39.17726167561544), doubleArray0, (-1.261386569922121), doubleArray0);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertArrayEquals(new double[] {(-44.089091028280734), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertEquals(2, doubleArray0.length);
      assertEquals((-1.261386569922121), double3, 0.01);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, int0);
      
      int int1 = dormandPrince54Integrator0.getOrder();
      assertTrue(int1 == int0);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, int1);
      
      double double4 = dormandPrince54Integrator0.getSafety();
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, double4, 0.01);
      
      double[][] doubleArray1 = new double[7][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[2];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (-0.8831545382286958);
      doubleArray2[1] = 0.9;
      double double5 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray2, (-3828.910065848233));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertArrayEquals(new double[] {(-0.8831545382286958), 0.9}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {(-44.089091028280734), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray2.length);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertEquals(8.240969728616826E-16, double5, 0.01);
      
      double[][] doubleArray3 = new double[4][5];
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      
      doubleArray3[0] = doubleArray0;
      doubleArray3[1] = doubleArray0;
      doubleArray3[2] = doubleArray0;
      doubleArray3[3] = doubleArray2;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray3, doubleArray0, doubleArray2, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(10.0, 2315.221148730073, 10.0, 0.2);
      assertNotNull(highamHall54Integrator0);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(152.15850777166793, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2315.221148730073, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      highamHall54Integrator0.setMinReduction((-0.6328058553289824));
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(152.15850777166793, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2315.221148730073, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals((-0.6328058553289824), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 0.2;
      doubleArray0[1] = 10.0;
      highamHall54Integrator0.setSafety(2430.365830708803);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(152.15850777166793, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2315.221148730073, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals((-0.6328058553289824), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2430.365830708803, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double double0 = highamHall54Integrator0.getMinReduction();
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(152.15850777166793, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2315.221148730073, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals((-0.6328058553289824), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2430.365830708803, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals((-0.6328058553289824), double0, 0.01);
      
      highamHall54Integrator0.setSafety(2315.221148730073);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(152.15850777166793, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2315.221148730073, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals((-0.6328058553289824), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(2315.221148730073, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      highamHall54Integrator0.setMinReduction(2315.221148730073);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(152.15850777166793, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2315.221148730073, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2315.221148730073, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(2315.221148730073, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double double1 = highamHall54Integrator0.getMaxGrowth();
      assertNotEquals(double1, double0, 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(152.15850777166793, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2315.221148730073, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2315.221148730073, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(2315.221148730073, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(10.0, double1, 0.01);
      
      double[] doubleArray1 = new double[24];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 2315.221148730073;
      doubleArray1[1] = 2315.221148730073;
      doubleArray1[2] = 10.0;
      doubleArray1[3] = 0.0;
      doubleArray1[4] = 10.0;
      doubleArray1[5] = 10.0;
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0012326388888888888, 0.0, doubleArray0, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotNull(dormandPrince853Integrator0);
      assertArrayEquals(new double[] {0.2, 10.0}, doubleArray0, 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0012326388888888888, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2, doubleArray0.length);
      assertEquals(24, doubleArray1.length);
      
      int int0 = dormandPrince853Integrator0.getOrder();
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.2, 10.0}, doubleArray0, 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0012326388888888888, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(24, doubleArray1.length);
      assertEquals(8, int0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-10.603513857775251), (-10.603513857775251), (-10.603513857775251), (-0.8831545382286958));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-10.603513857775251);
      doubleArray0[1] = (-0.8831545382286958);
      double double0 = dormandPrince54Integrator0.integrate(firstOrderConverter0, (-39.17726167561544), doubleArray0, 0.0, doubleArray0);
      assertArrayEquals(new double[] {(-45.20309030196818), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(2, firstOrderConverter0.getDimension());
      assertEquals(2, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(5, int0);
      
      int int1 = dormandPrince54Integrator0.getOrder();
      assertTrue(int1 == int0);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(5, int1);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertNotEquals(double1, double0, 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.9, double1, 0.01);
      
      double[][] doubleArray1 = new double[7][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[2];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (-0.8831545382286958);
      doubleArray2[1] = 0.9;
      double double2 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray2, (-3828.910065848233));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertArrayEquals(new double[] {(-0.8831545382286958), 0.9}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {(-45.20309030196818), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray2.length);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertEquals(2.3811447599954886E-14, double2, 0.01);
      
      double[][] doubleArray3 = new double[4][5];
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      
      doubleArray3[0] = doubleArray0;
      doubleArray3[1] = doubleArray0;
      doubleArray3[2] = doubleArray0;
      doubleArray3[3] = doubleArray2;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray3, doubleArray0, doubleArray2, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-10.603513857775251), (-10.603513857775251), (-10.603513857775251), (-0.8831545382286958));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-10.603513857775251);
      doubleArray0[1] = (-0.8831545382286958);
      double double0 = dormandPrince54Integrator0.integrate(firstOrderConverter0, (-10.603513857775251), doubleArray0, 0.0, doubleArray0);
      assertArrayEquals(new double[] {(-19.96805524244033), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(14, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(2, firstOrderConverter0.getDimension());
      assertEquals(2, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(14, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, double1, 0.01);
      
      double[][] doubleArray1 = new double[7][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[2];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (-0.8831545382286958);
      doubleArray2[1] = 0.9;
      double double2 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray2, (-3828.910065848233));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertNotEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertArrayEquals(new double[] {(-0.8831545382286958), 0.9}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {(-19.96805524244033), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(14, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray2.length);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertEquals(1.5988192554986714E-14, double2, 0.01);
      
      double[][] doubleArray3 = new double[4][5];
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      
      doubleArray3[0] = doubleArray0;
      doubleArray3[1] = doubleArray0;
      doubleArray3[2] = doubleArray0;
      doubleArray3[3] = doubleArray2;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray3, doubleArray0, doubleArray2, 1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[1] = 0.0;
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince853Integrator0);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2, doubleArray0.length);
      
      dormandPrince853Integrator0.setInitialStepSize(0.0);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2, doubleArray0.length);
      
      dormandPrince853Integrator0.setSafety((-280.428865));
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals((-280.428865), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2, doubleArray0.length);
      
      double double0 = dormandPrince853Integrator0.getSafety();
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals((-280.428865), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2, doubleArray0.length);
      assertEquals((-280.428865), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double double0 = (-0.8831545382286958);
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.2, 0.2, 0.2, (-0.8831545382286958));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 0.2;
      doubleArray0[1] = (-0.8831545382286958);
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, (-3619.7), doubleArray0, 0.0, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-1252.937262484137);
      doubleArray0[1] = (-0.9371424300859873);
      doubleArray0[2] = (-1252.937262484137);
      doubleArray0[3] = (-1252.937262484137);
      doubleArray0[4] = (-1252.937262484137);
      doubleArray0[5] = 0.0;
      doubleArray0[6] = (-1252.937262484137);
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-1252.937262484137), (-1252.937262484137), doubleArray0, doubleArray0);
      assertNotNull(dormandPrince853Integrator0);
      assertArrayEquals(new double[] {(-1252.937262484137), (-0.9371424300859873), (-1252.937262484137), (-1252.937262484137), (-1252.937262484137), 0.0, (-1252.937262484137)}, doubleArray0, 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals((-1252.937262484137), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals((-1252.937262484137), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1252.937262484137, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7, doubleArray0.length);
      
      dormandPrince853Integrator0.setMinReduction(0.0);
      assertArrayEquals(new double[] {(-1252.937262484137), (-0.9371424300859873), (-1252.937262484137), (-1252.937262484137), (-1252.937262484137), 0.0, (-1252.937262484137)}, doubleArray0, 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals((-1252.937262484137), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals((-1252.937262484137), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1252.937262484137, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7, doubleArray0.length);
      
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertNotNull(highamHall54Integrator0);
      assertArrayEquals(new double[] {(-1252.937262484137), (-0.9371424300859873), (-1252.937262484137), (-1252.937262484137), (-1252.937262484137), 0.0, (-1252.937262484137)}, doubleArray0, 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(7, doubleArray0.length);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertArrayEquals(new double[] {(-1252.937262484137), (-0.9371424300859873), (-1252.937262484137), (-1252.937262484137), (-1252.937262484137), 0.0, (-1252.937262484137)}, doubleArray0, 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(7, doubleArray0.length);
      assertEquals(5, int0);
      
      double double0 = highamHall54Integrator0.getSafety();
      assertArrayEquals(new double[] {(-1252.937262484137), (-0.9371424300859873), (-1252.937262484137), (-1252.937262484137), (-1252.937262484137), 0.0, (-1252.937262484137)}, doubleArray0, 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(7, doubleArray0.length);
      assertEquals(0.9, double0, 0.01);
      
      double double1 = dormandPrince853Integrator0.getMinReduction();
      assertNotEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {(-1252.937262484137), (-0.9371424300859873), (-1252.937262484137), (-1252.937262484137), (-1252.937262484137), 0.0, (-1252.937262484137)}, doubleArray0, 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals((-1252.937262484137), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals((-1252.937262484137), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1252.937262484137, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7, doubleArray0.length);
      assertEquals(0.0, double1, 0.01);
      
      highamHall54Integrator0.setSafety(0.022651792198360825);
      assertArrayEquals(new double[] {(-1252.937262484137), (-0.9371424300859873), (-1252.937262484137), (-1252.937262484137), (-1252.937262484137), 0.0, (-1252.937262484137)}, doubleArray0, 0.01);
      assertEquals(0.022651792198360825, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(7, doubleArray0.length);
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-1566.7), 0.022651792198360825, 0.0, 0.0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1566.7, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5.957227781205945, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.022651792198360825, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      
      double[][] doubleArray1 = new double[5][8];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 0.0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, 0.0, 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      
      dormandPrince853Integrator0.setMaxGrowth(0.0);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      
      double double0 = dormandPrince853Integrator0.getSafety();
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, double0, 0.01);
      
      int int0 = dormandPrince853Integrator0.getOrder();
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(8, int0);
      
      double double1 = dormandPrince853Integrator0.getMaxGrowth();
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, double1, 0.01);
      
      double double2 = dormandPrince853Integrator0.getSafety();
      assertEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertNotNull(highamHall54Integrator0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0, doubleArray0.length);
      
      highamHall54Integrator0.setMinReduction(0.0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, doubleArray0.length);
      
      highamHall54Integrator0.setSafety(0.0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, doubleArray0.length);
      
      highamHall54Integrator0.setSafety(0.0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, doubleArray0.length);
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 10.0, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0, doubleArray0.length);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0, doubleArray0.length);
      assertEquals(5, int0);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      // Undeclared exception!
      highamHall54Integrator0.integrate(firstOrderConverter0, 5, doubleArray0, (-742.795376615), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-970.46315);
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-970.46315), (-970.46315), doubleArray0, doubleArray0);
      assertNotNull(dormandPrince853Integrator0);
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      dormandPrince853Integrator0.setSafety(0.9);
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      dormandPrince853Integrator0.setMaxGrowth((-280.785741381923));
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-280.785741381923), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      dormandPrince853Integrator0.resetInternalState();
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-280.785741381923), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      dormandPrince853Integrator0.setSafety((-970.46315));
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-280.785741381923), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals((-970.46315), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      dormandPrince853Integrator0.clearEventHandlers();
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-280.785741381923), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals((-970.46315), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      dormandPrince853Integrator0.setMaxGrowth((-970.46315));
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals((-970.46315), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      dormandPrince853Integrator0.setMinReduction(2194.3529);
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(2194.3529, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals((-970.46315), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      Collection<EventHandler> collection0 = dormandPrince853Integrator0.getEventHandlers();
      assertNotNull(collection0);
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(2194.3529, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals((-970.46315), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      double double0 = dormandPrince853Integrator0.getMinReduction();
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(2194.3529, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals((-970.46315), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(2194.3529, double0, 0.01);
      
      dormandPrince853Integrator0.setSafety((-970.46315));
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(2194.3529, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals((-970.46315), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      dormandPrince853Integrator0.setMinReduction((-325.0));
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals((-325.0), dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals((-970.46315), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      double double1 = dormandPrince853Integrator0.getMaxGrowth();
      assertNotEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals((-325.0), dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals((-970.46315), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals((-970.46315), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals((-970.46315), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals((-970.46315), double1, 0.01);
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-970.46315), (-970.46315), doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals((-970.46315), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-970.46315), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(1, doubleArray0.length);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertArrayEquals(new double[] {(-970.46315)}, doubleArray0, 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(970.46315, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals((-970.46315), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-970.46315), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(2186.838356867377, 2186.838356867377, 0.0, 0.0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      dormandPrince54Integrator0.setSafety(0.0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      dormandPrince54Integrator0.setMaxEvaluations((-1));
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      dormandPrince54Integrator0.clearEventHandlers();
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      double double0 = dormandPrince54Integrator0.getMinReduction();
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, double0, 0.01);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, double1, 0.01);
      
      dormandPrince54Integrator0.setMaxGrowth((-1));
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      double double2 = dormandPrince54Integrator0.getSafety();
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(2186.838356867377, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double double0 = 0.0;
      double double1 = 3156.5955971;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(1215.0043555758139, 0.0, 3156.5955971, 3156.5955971);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      dormandPrince54Integrator0.clearEventHandlers();
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      double double2 = dormandPrince54Integrator0.getSafety();
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, double2, 0.01);
      
      double double3 = dormandPrince54Integrator0.getMinReduction();
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, double3, 0.01);
      
      Collection<StepHandler> collection0 = dormandPrince54Integrator0.getStepHandlers();
      assertNotNull(collection0);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      dormandPrince54Integrator0.resetInternalState();
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, int0);
      
      double double4 = dormandPrince54Integrator0.getSafety();
      assertNotEquals(double4, double1, 0.01);
      assertEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, double4, 0.01);
      
      double double5 = dormandPrince54Integrator0.getSafety();
      assertEquals(double5, double2, 0.01);
      assertEquals(double5, double4, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, double5, 0.01);
      
      int int1 = dormandPrince54Integrator0.getOrder();
      assertTrue(int1 == int0);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, int1);
      
      double double6 = (-1768.0);
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 0.9;
      doubleArray0[1] = 0.9;
      doubleArray0[2] = 0.9;
      doubleArray0[3] = 0.9;
      doubleArray0[4] = (double) 5;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = (-1768.0);
      dormandPrince54Integrator0.clearEventHandlers();
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(1215.0043555758139, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      double double7 = (-5143.476117638918);
      double[] doubleArray1 = new double[0];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.integrate((FirstOrderDifferentialEquations) null, (-1768.0), doubleArray0, (-5143.476117638918), doubleArray1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 1.7976931348623157E308;
      doubleArray0[1] = 1.7976931348623157E308;
      doubleArray0[2] = 1.7976931348623157E308;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(1.7976931348623157E308, 1.7976931348623157E308, doubleArray0, doubleArray0);
      assertNotNull(highamHall54Integrator0);
      assertArrayEquals(new double[] {1.7976931348623157E308, 1.7976931348623157E308, 1.7976931348623157E308}, doubleArray0, 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.POSITIVE_INFINITY, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1.7976931348623157E308, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1.7976931348623157E308, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(3, doubleArray0.length);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertArrayEquals(new double[] {1.7976931348623157E308, 1.7976931348623157E308, 1.7976931348623157E308}, doubleArray0, 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.POSITIVE_INFINITY, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1.7976931348623157E308, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1.7976931348623157E308, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(3, doubleArray0.length);
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.0, 0.0, 0.78125, 0.78125);
      assertNotNull(highamHall54Integrator0);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, int0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (-0.025);
      doubleArray0[2] = (-0.025);
      doubleArray0[3] = 0.78125;
      doubleArray0[4] = 0.78125;
      doubleArray0[5] = (-0.025);
      doubleArray0[6] = (-0.025);
      doubleArray0[7] = (-0.025);
      doubleArray0[8] = 0.78125;
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-0.025), 5, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince853Integrator0);
      assertArrayEquals(new double[] {0.0, (-0.025), (-0.025), 0.78125, 0.78125, (-0.025), (-0.025), (-0.025), 0.78125}, doubleArray0, 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(5.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals((-0.025), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(9, doubleArray0.length);
      
      double[] doubleArray1 = new double[0];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double0 = (-1.0);
      double double1 = dormandPrince853Integrator0.estimateError((double[][]) null, doubleArray1, doubleArray1, (-1.0));
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {0.0, (-0.025), (-0.025), 0.78125, 0.78125, (-0.025), (-0.025), (-0.025), 0.78125}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(5.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals((-0.025), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertEquals(Double.NaN, double1, 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      try { 
        highamHall54Integrator0.integrate(firstOrderConverter0, 679.7315109, doubleArray0, (-662.3776), doubleArray0);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 0, initial state vector has dimension 9
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince853Integrator0);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2, doubleArray0.length);
      
      dormandPrince853Integrator0.setSafety((-280.428865));
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals((-280.428865), dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-748.0), (-4094.20216548603), (-4094.20216548603), 0.0);
      assertNotNull(highamHall54Integrator0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(4094.20216548603, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1749.9894913351768, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(748.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      
      double double0 = highamHall54Integrator0.getMinReduction();
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(4094.20216548603, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1749.9894913351768, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(748.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.2, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-0.494653873424);
      doubleArray0[1] = (-0.494653873424);
      doubleArray0[2] = (-0.494653873424);
      doubleArray0[3] = (-0.494653873424);
      doubleArray0[4] = (-0.494653873424);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-0.494653873424), (-0.494653873424), doubleArray0, doubleArray0);
      assertNotNull(highamHall54Integrator0);
      assertArrayEquals(new double[] {(-0.494653873424), (-0.494653873424), (-0.494653873424), (-0.494653873424), (-0.494653873424)}, doubleArray0, 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-0.494653873424), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals((-0.494653873424), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.494653873424, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, doubleArray0.length);
      
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      highamHall54Integrator0.addEventHandler(eventHandler0, 0.0, (-0.494653873424), (-214));
      assertArrayEquals(new double[] {(-0.494653873424), (-0.494653873424), (-0.494653873424), (-0.494653873424), (-0.494653873424)}, doubleArray0, 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-0.494653873424), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals((-0.494653873424), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.494653873424, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, doubleArray0.length);
      
      EventHandler eventHandler1 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      highamHall54Integrator0.addEventHandler(eventHandler1, (-0.494653873424), (-61.34781), (-214));
      assertArrayEquals(new double[] {(-0.494653873424), (-0.494653873424), (-0.494653873424), (-0.494653873424), (-0.494653873424)}, doubleArray0, 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-0.494653873424), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals((-0.494653873424), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.494653873424, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, doubleArray0.length);
      
      double double0 = 0.0;
      // Undeclared exception!
      try { 
        highamHall54Integrator0.integrate((FirstOrderDifferentialEquations) null, (-0.494653873424), doubleArray0, 0.0, doubleArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.9, 0.9, 0.9, 0.9);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.9, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      
      double double0 = dormandPrince853Integrator0.getMinReduction();
      assertEquals(0.9, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.2, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, (-794.036344496), 258.0, 258.0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(794.036344496, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      
      double double0 = dormandPrince54Integrator0.getSafety();
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(794.036344496, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-1607.137687), (-1607.137687), (-1607.137687), 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(1607.137687, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1607.137687, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(1607.137687, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      
      double double0 = dormandPrince853Integrator0.getMaxGrowth();
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(1607.137687, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1607.137687, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(1607.137687, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.0, double0, 0.01);
      
      dormandPrince853Integrator0.setMinReduction(10.0);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(1607.137687, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1607.137687, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(10.0, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(1607.137687, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-10.603513857775251), (-10.603513857775251), (-10.603513857775251), (-0.8831545382286958));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      dormandPrince54Integrator0.clearEventHandlers();
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[1] = (-0.8831545382286958);
      double double0 = dormandPrince54Integrator0.integrate(firstOrderConverter0, (-39.17726167561544), doubleArray0, 0.0, doubleArray0);
      assertArrayEquals(new double[] {(-34.599576444192934), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertEquals(2, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, int0);
      
      int int1 = dormandPrince54Integrator0.getOrder();
      assertTrue(int1 == int0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, int1);
      
      double double1 = dormandPrince54Integrator0.getSafety();
      assertNotEquals(double1, double0, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, double1, 0.01);
      
      double[][] doubleArray1 = new double[7][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[2];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (-0.8831545382286958);
      doubleArray2[1] = 0.9;
      double double2 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray2, (-3828.910065848233));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertArrayEquals(new double[] {(-0.8831545382286958), 0.9}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {(-34.599576444192934), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray2.length);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertEquals(7.349194637793684E-15, double2, 0.01);
      
      double double3 = dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray2, 1);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertArrayEquals(new double[] {(-0.8831545382286958), 0.9}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {(-34.599576444192934), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray2.length);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertEquals(1.919395992960098E-18, double3, 0.01);
      
      int int2 = dormandPrince54Integrator0.getOrder();
      assertTrue(int2 == int1);
      assertTrue(int2 == int0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(32, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, int2);
      
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(2049.6, (-240.556590878), doubleArray0, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotNull(dormandPrince853Integrator0);
      assertArrayEquals(new double[] {(-34.599576444192934), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2049.6, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals((-240.556590878), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(2, doubleArray0.length);
      
      dormandPrince853Integrator0.sanityChecks(firstOrderConverter0, 618.347375737955, doubleArray0, (-34.599576444192934), doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertArrayEquals(new double[] {(-34.599576444192934), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2049.6, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals((-240.556590878), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray0.length);
      
      int int3 = dormandPrince853Integrator0.getOrder();
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      assertArrayEquals(new double[] {(-34.599576444192934), (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2049.6, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals((-240.556590878), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertEquals(8, int3);
      
      double double4 = dormandPrince54Integrator0.integrate(firstOrderConverter0, 1993.729, doubleArray0, 6.729221861514409E-20, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertEquals(double4, double3, 0.01);
      assertEquals(double4, double0, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertEquals(double4, double2, 0.01);
      assertArrayEquals(new double[] {1726.1712379039702, (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1142, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.603513857775251, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertEquals((-5.551115123125783E-17), double4, 0.01);
      
      double double5 = dormandPrince853Integrator0.integrate(firstOrderConverter0, 5, doubleArray0, (-1117.2707295650252), doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertArrayEquals(new double[] {2717.309725840551, (-0.8831545382286958)}, doubleArray0, 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertEquals(98, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2049.6, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals((-240.556590878), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertEquals((-1117.2707295650252), double5, 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.9, 0.0, (-1021.8391048450488), 1.0E-12);
      assertNotNull(highamHall54Integrator0);
      assertEquals(0.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[0];
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      highamHall54Integrator0.addEventHandler(eventHandler0, 0.9, 1.3892095610261066E-20, (-1));
      assertEquals(0.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      
      highamHall54Integrator0.sanityChecks(firstOrderConverter0, 1.0E-12, doubleArray0, (-1.0), doubleArray0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0, firstOrderConverter0.getDimension());
      assertEquals(0, doubleArray0.length);
      
      highamHall54Integrator0.clearStepHandlers();
      assertEquals(0.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      
      double double0 = highamHall54Integrator0.filterStep(Double.NEGATIVE_INFINITY, true, false);
      assertEquals(0.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(-0.0, double0, 0.01);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals(0.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, int0);
      
      // Undeclared exception!
      highamHall54Integrator0.integrate(firstOrderConverter0, (-1.0), doubleArray0, 1.0E-12, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(541.350229, 0.0, 541.350229, 541.350229);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(541.350229, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      
      dormandPrince853Integrator0.setMinReduction(541.350229);
      assertEquals(541.350229, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(541.350229, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      double double0 = 1.479578590104411E-20;
      double[] doubleArray0 = new double[0];
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(1.479578590104411E-20, 3192.6114616667646, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince853Integrator0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1.479578590104411E-20, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(3192.6114616667646, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(6.87293210006042E-9, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, doubleArray0.length);
      
      double[][] doubleArray1 = new double[5][8];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      double double1 = dormandPrince853Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 3192.6114616667646);
      assertNotEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1.479578590104411E-20, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(3192.6114616667646, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(6.87293210006042E-9, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      assertEquals(Double.NaN, double1, 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, Double.NaN, doubleArray0, 2.4, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = 1099.59299007915;
      doubleArray0[1] = (-5.685526961588504);
      doubleArray0[2] = (-5.685526961588504);
      doubleArray0[3] = (-5.685526961588504);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-5.685526961588504), (-5.685526961588504), doubleArray0, doubleArray0);
      assertNotNull(highamHall54Integrator0);
      assertArrayEquals(new double[] {1099.59299007915, (-5.685526961588504), (-5.685526961588504), (-5.685526961588504)}, doubleArray0, 0.01);
      assertEquals(5.685526961588504, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals((-5.685526961588504), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-5.685526961588504), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(4, doubleArray0.length);
      
      double double0 = highamHall54Integrator0.getMaxGrowth();
      assertArrayEquals(new double[] {1099.59299007915, (-5.685526961588504), (-5.685526961588504), (-5.685526961588504)}, doubleArray0, 0.01);
      assertEquals(5.685526961588504, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals((-5.685526961588504), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-5.685526961588504), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(4, doubleArray0.length);
      assertEquals(10.0, double0, 0.01);
      
      double double1 = highamHall54Integrator0.getMinReduction();
      assertNotEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {1099.59299007915, (-5.685526961588504), (-5.685526961588504), (-5.685526961588504)}, doubleArray0, 0.01);
      assertEquals(5.685526961588504, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals((-5.685526961588504), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-5.685526961588504), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(4, doubleArray0.length);
      assertEquals(0.2, double1, 0.01);
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-3.75), (-114.600243424622), doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertArrayEquals(new double[] {1099.59299007915, (-5.685526961588504), (-5.685526961588504), (-5.685526961588504)}, doubleArray0, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals((-114.600243424622), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-3.75), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(20.730434458600534, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(4, doubleArray0.length);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertArrayEquals(new double[] {1099.59299007915, (-5.685526961588504), (-5.685526961588504), (-5.685526961588504)}, doubleArray0, 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals((-114.600243424622), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-3.75), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(20.730434458600534, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(4, doubleArray0.length);
      assertEquals(5, int0);
      
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-114.600243424622), (-114.600243424622), 1099.59299007915, 2.2250738585072014E-308);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(114.600243424622, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(114.600243424622, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(114.600243424622, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      
      double[][] doubleArray1 = new double[4][9];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      // Undeclared exception!
      try { 
        dormandPrince853Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, (-114.600243424622));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.1, 0.1, doubleArray0, doubleArray0);
      assertNotNull(dormandPrince853Integrator0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.1, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.1, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.1, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, doubleArray0.length);
      
      double double0 = (-1.0);
      dormandPrince853Integrator0.setMaxGrowth((-1.0));
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.1, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.1, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.1, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals((-1.0), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, doubleArray0.length);
      
      double double1 = 2769.470848074051;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(2769.470848074051, 0.1, 0.1, (-1.0));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.1, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(2769.470848074051, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(16.6417272182729, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.1, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(2769.470848074051, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(16.6417272182729, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(5, int0);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, (-897.2177319083817), doubleArray0, 5, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      double double0 = (-0.8831545382286958);
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-10.603513857775251), (-10.603513857775251), (-10.603513857775251), (-0.8831545382286958));
      int int0 = 1;
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-10.603513857775251);
      doubleArray0[1] = (-0.8831545382286958);
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, (-3619.7), doubleArray0, 0.0, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-10.603513857775251), (-10.603513857775251), (-10.603513857775251), (-0.8831545382286958));
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-10.603513857775251);
      doubleArray0[1] = (-0.8831545382286958);
      dormandPrince54Integrator0.integrate(firstOrderConverter0, (-39.17726167561544), doubleArray0, 0.0, doubleArray0);
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.getSafety();
      double[][] doubleArray1 = new double[7][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[2];
      doubleArray2[0] = (double) 5;
      doubleArray2[1] = 0.9;
      dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray2, (-3828.910065848233));
      double[][] doubleArray3 = new double[14][5];
      doubleArray3[0] = doubleArray0;
      doubleArray3[1] = doubleArray0;
      doubleArray3[2] = doubleArray0;
      doubleArray3[3] = doubleArray2;
      dormandPrince54Integrator0.estimateError(doubleArray3, doubleArray0, doubleArray2, 1);
      dormandPrince54Integrator0.integrate(firstOrderConverter0, (-39.17726167561544), doubleArray2, (-1180.8918127116763), doubleArray0);
      double[] doubleArray4 = new double[3];
      doubleArray4[0] = (-39.17726167561544);
      doubleArray4[1] = 1814.76298;
      doubleArray4[2] = (-0.8831545382286958);
      try { 
        dormandPrince54Integrator0.integrate(firstOrderConverter0, 5.0, doubleArray2, 1814.76298, doubleArray4);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 2, final state vector has dimension 3
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 1069.451475319047;
      doubleArray0[1] = (-1062.3793063824846);
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (-1062.3793063824846);
      doubleArray0[4] = 1047.90212;
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(1069.451475319047, (-1062.3793063824846), doubleArray0, doubleArray0);
      Double double0 = new Double(1069.451475319047);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1062.3793063824846), (-1062.3793063824846), 8.881784197001252E-16, (-1062.3793063824846));
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      
      highamHall54Integrator0.setMinReduction(8.881784197001252E-16);
      highamHall54Integrator0.getOrder();
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      double[][] doubleArray0 = new double[3][5];
  }
}
