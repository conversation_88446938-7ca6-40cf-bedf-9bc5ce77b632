/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 20:19:59 GMT 2019
 */

package org.apache.commons.math.ode.nonstiff;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math.ode.FirstOrderConverter;
import org.apache.commons.math.ode.FirstOrderDifferentialEquations;
import org.apache.commons.math.ode.SecondOrderDifferentialEquations;
import org.apache.commons.math.ode.events.EventHandler;
import org.apache.commons.math.ode.nonstiff.DormandPrince54Integrator;
import org.apache.commons.math.ode.nonstiff.DormandPrince853Integrator;
import org.apache.commons.math.ode.nonstiff.HighamHall54Integrator;
import org.apache.commons.math.ode.sampling.DummyStepHandler;
import org.apache.commons.math.ode.sampling.FixedStepHandler;
import org.apache.commons.math.ode.sampling.StepHandler;
import org.apache.commons.math.ode.sampling.StepNormalizer;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class EmbeddedRungeKuttaIntegrator_ESTest extends EmbeddedRungeKuttaIntegrator_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double double0 = 0.0;
      double double1 = 1.0001;
      double double2 = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, 1.0001, 0.0);
      double[][] doubleArrayArray0 = null;
      double[] doubleArray0 = new double[4];
      double double3 = (-2225.7002451);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      dormandPrince54Integrator0.resetInternalState();
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(eventHandler0).eventOccurred(anyDouble() , any(double[].class) , anyBoolean());
      doReturn(0.0, (-652.4593731), 0.2, 0.0, 0.0).when(eventHandler0).g(anyDouble() , any(double[].class));
      dormandPrince54Integrator0.addEventHandler(eventHandler0, 1.0, (-2225.7002451), 2);
      dormandPrince54Integrator0.setSafety(0.0);
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 1.0001, doubleArray0, 0.2, doubleArray0);
      EventHandler eventHandler1 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      double double4 = (-0.0042527702905061394);
      double double5 = 3361.54965281221;
      int int0 = 0;
      dormandPrince54Integrator0.addEventHandler(eventHandler1, double4, double5, int0);
      dormandPrince54Integrator0.setMinReduction(double3);
      doubleArray0[0] = double0;
      doubleArray0[1] = double1;
      doubleArray0[2] = double2;
      doubleArray0[3] = double1;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArrayArray0, doubleArray0, doubleArray0, doubleArray0[2]);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-664.235393519417);
      doubleArray0[1] = (-664.235393519417);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-664.235393519417), (-664.235393519417), (-664.235393519417), (-664.235393519417));
      highamHall54Integrator0.setSafety((-664.235393519417));
      highamHall54Integrator0.setInitialStepSize(383.39704);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      highamHall54Integrator0.integrate(firstOrderConverter0, (-664.235393519417), doubleArray0, 0.6666666666666666, doubleArray0);
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-1564.28778348), (-664.235393519417), doubleArray0, doubleArray0);
      double[][] doubleArray1 = new double[7][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[1];
      doubleArray2[0] = (-1564.28778348);
      // Undeclared exception!
      try { 
        dormandPrince853Integrator0.estimateError(doubleArray1, doubleArray2, doubleArray0, (-1564.28778348));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      double[] doubleArray0 = new double[0];
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-383.92748), 10.0, doubleArray0, doubleArray0);
      double[][] doubleArray1 = new double[0][7];
      FixedStepHandler fixedStepHandler0 = mock(FixedStepHandler.class, new ViolatedAssumptionAnswer());
      StepNormalizer stepNormalizer0 = new StepNormalizer(252.327324716, fixedStepHandler0);
      dormandPrince853Integrator0.setMinReduction((-383.92748));
      dormandPrince853Integrator0.addStepHandler(stepNormalizer0);
      dormandPrince853Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 1.0);
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, Double.NaN, doubleArray0, 10.0, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      double double0 = (-664.235393519417);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-664.235393519417);
      doubleArray0[0] = (-664.235393519417);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-664.235393519417), (-664.235393519417), doubleArray0, doubleArray0);
      highamHall54Integrator0.setSafety((-664.235393519417));
      highamHall54Integrator0.setInitialStepSize(383.39704);
      int int0 = 1;
      DummyStepHandler dummyStepHandler0 = DummyStepHandler.getInstance();
      highamHall54Integrator0.addStepHandler(dummyStepHandler0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      highamHall54Integrator0.setSafety((-664.235393519417));
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      // Undeclared exception!
      highamHall54Integrator0.integrate(firstOrderConverter0, (-664.235393519417), doubleArray0, (-1612.88), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      double double0 = 0.337385197562337;
      double double1 = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.337385197562337, 0.337385197562337, 1.0001, 0.0);
      double[][] doubleArrayArray0 = null;
      double[] doubleArray0 = new double[4];
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 1.0001, doubleArray0, 0.2, doubleArray0);
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      dormandPrince54Integrator0.addEventHandler(eventHandler0, (-0.0042527702905061394), 3361.54965281221, 0);
      dormandPrince54Integrator0.setMinReduction((-2222.8952251152314));
      doubleArray0[0] = 0.337385197562337;
      doubleArray0[1] = 1.0001;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 1.0001;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError((double[][]) null, doubleArray0, doubleArray0, 0.0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.337385197562337, 0.337385197562337, 1.0001, 0.0);
      double[] doubleArray0 = new double[4];
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 1.0001, doubleArray0, 0.2, doubleArray0);
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      dormandPrince54Integrator0.addEventHandler(eventHandler0, (-0.0042527702905061394), 3361.54965281221, 0);
      dormandPrince54Integrator0.setMinReduction((-2222.8952251152314));
      doubleArray0[0] = 0.337385197562337;
      doubleArray0[1] = 1.0001;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 1.0001;
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.getMinReduction();
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(1.0001, 0.337385197562337, (-3238.7173809), 0.0);
      dormandPrince853Integrator0.getOrder();
      dormandPrince853Integrator0.getOrder();
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double double0 = (-664.235393519417);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-664.235393519417);
      doubleArray0[1] = (-664.235393519417);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-664.235393519417), (-664.235393519417), doubleArray0, doubleArray0);
      highamHall54Integrator0.setSafety((-664.235393519417));
      highamHall54Integrator0.setInitialStepSize(383.39704);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      highamHall54Integrator0.setSafety((-664.235393519417));
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      // Undeclared exception!
      highamHall54Integrator0.integrate(firstOrderConverter0, 629459.0881021831, doubleArray0, (-1612.88), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      double double0 = 0.0;
      double double1 = 1.0001;
      double double2 = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, 1.0001, 0.0);
      double[][] doubleArrayArray0 = null;
      double[] doubleArray0 = new double[4];
      double double3 = (-2225.7002451);
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(0.0, 0.0, 0.0, 0.0, 0.0).when(eventHandler0).g(anyDouble() , any(double[].class));
      dormandPrince54Integrator0.addEventHandler(eventHandler0, (-1.0), (-91.125), 1182);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 1.0001, doubleArray0, 0.2, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double double0 = 1.0;
      double double1 = (-2347.72336);
      double[] doubleArray0 = new double[10];
      doubleArray0[0] = (-2347.72336);
      doubleArray0[1] = 1.0;
      doubleArray0[2] = (-2347.72336);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(1.0, (-2347.72336), doubleArray0, doubleArray0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(5).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      double double2 = 0.0;
      // Undeclared exception!
      highamHall54Integrator0.integrate(firstOrderConverter0, (-15.815603874378382), doubleArray0, 1.0, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      double double0 = 0.0;
      double double1 = (-15.815603874378382);
      double double2 = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, (-15.815603874378382), 0.0);
      double[][] doubleArrayArray0 = null;
      double[] doubleArray0 = new double[4];
      double double3 = (-2225.7002451);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, (-15.815603874378382), doubleArray0, 14.448810562010081, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-2359.1996690609853), (-1185.69), (-1185.69), (-1185.69));
      highamHall54Integrator0.setMinReduction((-2359.1996690609853));
      highamHall54Integrator0.getSafety();
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (-1185.69);
      doubleArray0[1] = (-2359.1996690609853);
      doubleArray0[2] = 0.9;
      doubleArray0[3] = 0.9;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 1501.9194503191993;
      doubleArray0[6] = (-2359.1996690609853);
      doubleArray0[7] = (-2359.1996690609853);
      double[] doubleArray1 = new double[2];
      doubleArray1[0] = 0.0;
      doubleArray1[1] = 0.9;
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.9, 1501.9194503191993, doubleArray0, doubleArray1);
      dormandPrince853Integrator0.getOrder();
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, (-769.5341000067), 0.9, 8);
      dormandPrince54Integrator0.addStepHandler((StepHandler) null);
      double[][] doubleArray2 = new double[3][8];
      doubleArray2[0] = doubleArray1;
      doubleArray2[1] = doubleArray0;
      doubleArray2[2] = doubleArray1;
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      dormandPrince54Integrator0.addEventHandler(eventHandler0, 0.9, 0.9, (-589));
      highamHall54Integrator0.getMinReduction();
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray2, doubleArray0, doubleArray1, 0.0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double double0 = 0.0;
      double double1 = 1.0001;
      double double2 = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, 1.0001, 0.0);
      double[][] doubleArrayArray0 = null;
      double[] doubleArray0 = new double[4];
      double double3 = (-2225.7002451);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 1.0001, doubleArray0, 0.2, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(1.0, 0.0, doubleArray0, doubleArray0);
      dormandPrince54Integrator0.getEventHandlers();
      dormandPrince54Integrator0.setMinReduction((-781.905356197255));
      dormandPrince54Integrator0.setInitialStepSize((-1569.2988692));
      dormandPrince54Integrator0.setMaxEvaluations(0);
      dormandPrince54Integrator0.setInitialStepSize(1.0);
      dormandPrince54Integrator0.setMinReduction(0);
      dormandPrince54Integrator0.estimateError((double[][]) null, doubleArray0, doubleArray0, 1.0);
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.getSafety();
      dormandPrince54Integrator0.getMinReduction();
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(891.5746, 84.32040550667716, doubleArray0, doubleArray0);
      highamHall54Integrator0.getOrder();
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, 0.0, (-0.42268232132379197));
      dormandPrince54Integrator0.clearStepHandlers();
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.clearEventHandlers();
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.setSafety(0.0);
      dormandPrince54Integrator0.setMaxGrowth(0.0);
      dormandPrince54Integrator0.getSafety();
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (-1534.840495);
      doubleArray0[1] = (-1534.840495);
      doubleArray0[2] = (-1534.840495);
      doubleArray0[3] = (-1534.840495);
      doubleArray0[4] = (-1534.840495);
      doubleArray0[5] = (-1534.840495);
      doubleArray0[6] = 759.8;
      doubleArray0[7] = (-1534.840495);
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-1534.840495), (-1534.840495), doubleArray0, doubleArray0);
      dormandPrince54Integrator0.setMaxGrowth((-1534.840495));
      dormandPrince54Integrator0.setMaxGrowth((-1534.840495));
      dormandPrince54Integrator0.setMaxEvaluations(758);
      dormandPrince54Integrator0.getStepHandlers();
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.setMaxGrowth((-1534.840495));
      dormandPrince54Integrator0.setSafety((-1534.840495));
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.setSafety((-2865.867));
      dormandPrince54Integrator0.getOrder();
      dormandPrince54Integrator0.getMaxGrowth();
      double[][] doubleArray1 = new double[9][5];
      double[] doubleArray2 = new double[7];
      doubleArray2[0] = (double) 5;
      doubleArray2[1] = (-1534.840495);
      doubleArray2[2] = (-2865.867);
      doubleArray2[3] = (-2865.867);
      doubleArray2[4] = (double) 5;
      doubleArray2[5] = (-1534.840495);
      doubleArray2[6] = 759.8;
      doubleArray1[0] = doubleArray2;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      doubleArray1[8] = doubleArray0;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 758);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double double0 = (-2366.0989);
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-2366.0989);
      doubleArray0[1] = (-2366.0989);
      doubleArray0[2] = (-2366.0989);
      doubleArray0[3] = (-2366.0989);
      doubleArray0[4] = (-2366.0989);
      doubleArray0[5] = (-2366.0989);
      doubleArray0[6] = (-2366.0989);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-2366.0989), (-2366.0989), doubleArray0, doubleArray0);
      double double1 = 843.16442864;
      double double2 = (-1502.1196);
      highamHall54Integrator0.setSafety((-1502.1196));
      highamHall54Integrator0.filterStep(843.16442864, true, true);
      highamHall54Integrator0.getSafety();
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      try { 
        highamHall54Integrator0.integrate(firstOrderConverter0, (-1502.1196), doubleArray0, (-2366.0989), doubleArray0);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 0, initial state vector has dimension 7
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-887.988006);
      doubleArray0[1] = 118.9413061030008;
      doubleArray0[2] = (-887.988006);
      doubleArray0[3] = (-887.988006);
      doubleArray0[4] = (-887.988006);
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(118.9413061030008, (-887.988006), doubleArray0, doubleArray0);
      dormandPrince54Integrator0.clearStepHandlers();
      dormandPrince54Integrator0.setSafety((-887.988006));
      dormandPrince54Integrator0.setInitialStepSize(3525.02);
      dormandPrince54Integrator0.setMaxGrowth(0.0);
      dormandPrince54Integrator0.setMinReduction((-887.988006));
      dormandPrince54Integrator0.getMaxGrowth();
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = new double[0];
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(2532.594, 852.57, doubleArray0, doubleArray1);
      dormandPrince853Integrator0.getOrder();
      dormandPrince853Integrator0.getMaxGrowth();
      dormandPrince853Integrator0.getEventHandlers();
      dormandPrince853Integrator0.setMinReduction(0.0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      double double0 = (-1612.88);
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, (-1144.242089685), doubleArray1, (-1612.88), doubleArray1);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-1564.28778348), (-2119.907747), doubleArray0, doubleArray0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, (-136.0934405), doubleArray0, (-1564.28778348), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-2228.0), 0.01, 0.0, 0.01);
      dormandPrince54Integrator0.getSafety();
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(125.0, 2005.09268071, doubleArray0, doubleArray0);
      dormandPrince853Integrator0.getOrder();
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-4161.4148195), 1.0, (-1817.6344688600504), (-1817.6344688600504));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-1817.6344688600504);
      doubleArray0[1] = (-1817.6344688600504);
      doubleArray0[2] = (-4161.4148195);
      doubleArray0[3] = (-1817.6344688600504);
      doubleArray0[4] = (-1817.6344688600504);
      doubleArray0[5] = (-1817.6344688600504);
      doubleArray0[6] = 1.0;
      double[] doubleArray1 = new double[7];
      doubleArray1[0] = 0.0;
      doubleArray1[1] = 0.0;
      doubleArray1[2] = (-1817.6344688600504);
      doubleArray1[3] = 1.0;
      doubleArray1[4] = 0.0;
      doubleArray1[5] = (-1817.6344688600504);
      doubleArray1[6] = (-4161.4148195);
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.integrate((FirstOrderDifferentialEquations) null, (-1817.6344688600504), doubleArray0, 0.0, doubleArray1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      double double0 = (-1108.755824948);
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(1581.08, (-1108.755824948), 1581.08, 0.0);
      dormandPrince54Integrator0.setSafety(0.0);
      dormandPrince54Integrator0.getMaxGrowth();
      double double1 = 0.0;
      dormandPrince54Integrator0.setMaxGrowth(0.0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 10.0;
      try { 
        dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.0, doubleArray0, 0.0, doubleArray0);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 0, initial state vector has dimension 1
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double double0 = 1.0;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(1.0, 1.0, (-3507.028757788), 15.0);
      highamHall54Integrator0.setSafety(1.0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      // Undeclared exception!
      try { 
        highamHall54Integrator0.integrate(firstOrderConverter0, 0.0, (double[]) null, 1.0, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(1548.017035164, (-2674.966869015078), 15.697238121770845, (-2674.966869015078));
      dormandPrince54Integrator0.getMaxGrowth();
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-1063.1), (-1063.1), doubleArray0, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-664.235393519417);
      doubleArray0[1] = (-664.235393519417);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-664.235393519417), (-664.235393519417), (-664.235393519417), (-664.235393519417));
      highamHall54Integrator0.setSafety((-664.235393519417));
      highamHall54Integrator0.setInitialStepSize(383.39704);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      highamHall54Integrator0.integrate(firstOrderConverter0, (-664.235393519417), doubleArray0, 0.6666666666666666, doubleArray0);
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-1564.28778348), (-664.235393519417), doubleArray0, doubleArray0);
      double[][] doubleArray1 = new double[7][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[1];
      highamHall54Integrator0.getEventHandlers();
      doubleArray2[0] = (-1564.28778348);
      // Undeclared exception!
      try { 
        dormandPrince853Integrator0.estimateError(doubleArray1, doubleArray2, doubleArray0, (-1564.28778348));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      double double0 = 33.7131925199239;
      double double1 = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(33.7131925199239, 33.7131925199239, (-22.516054661226224), 0.0);
      double[][] doubleArrayArray0 = null;
      double[] doubleArray0 = new double[4];
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      dormandPrince54Integrator0.integrate(firstOrderConverter0, (-22.516054661226224), doubleArray0, 0.2, doubleArray0);
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      dormandPrince54Integrator0.addEventHandler(eventHandler0, (-0.0042527702905061394), 3361.54965281221, 0);
      dormandPrince54Integrator0.setMinReduction((-2225.7002451));
      doubleArray0[0] = 33.7131925199239;
      doubleArray0[1] = (-22.516054661226224);
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (-22.516054661226224);
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError((double[][]) null, doubleArray0, doubleArray0, 0.0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      double double0 = 0.0;
      double double1 = (-15.815603874378382);
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, (-15.815603874378382), 0.0);
      double[][] doubleArrayArray0 = null;
      double[] doubleArray0 = new double[4];
      double double2 = (-2225.7002451);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, (-15.815603874378382), doubleArray0, 14.448810562010081, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = new double[0];
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(852.57, 852.57, 852.57, 2532.594);
      dormandPrince853Integrator0.getOrder();
      dormandPrince853Integrator0.getMaxGrowth();
      dormandPrince853Integrator0.getEventHandlers();
      dormandPrince853Integrator0.setMinReduction(0.0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(212).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      double double0 = (-1612.88);
      try { 
        dormandPrince853Integrator0.integrate(firstOrderConverter0, (-1144.242089685), doubleArray1, (-1612.88), doubleArray1);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 424, initial state vector has dimension 0
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.9, Double.NEGATIVE_INFINITY, 0.0, 0.0);
      dormandPrince853Integrator0.clearEventHandlers();
      dormandPrince853Integrator0.getSafety();
      dormandPrince853Integrator0.clearEventHandlers();
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (-1192.537654978271);
      doubleArray0[4] = 0.0;
      doubleArray0[5] = Double.NEGATIVE_INFINITY;
      doubleArray0[6] = 0.0;
      doubleArray0[7] = 0.9;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.9, (-1192.537654978271), 0.0, 0.0);
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      highamHall54Integrator0.addEventHandler(eventHandler0, (-1192.537654978271), (-175.03), 68);
      double[][] doubleArrayArray0 = null;
      highamHall54Integrator0.setInitialStepSize(0.0);
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(3782.828, (-1101.9188039318108), doubleArray0, doubleArray0);
      dormandPrince54Integrator0.getOrder();
      // Undeclared exception!
      try { 
        highamHall54Integrator0.estimateError((double[][]) null, doubleArray0, doubleArray0, 0.0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      double[] doubleArray0 = new double[1];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-1612.88), 4784.0, 0.0, (-10.757575757575758));
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError((double[][]) null, doubleArray0, doubleArray0, (-1612.88));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      double double0 = (-4077.246395769243);
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-4077.246395769243), (-4077.246395769243), (-4077.246395769243), (-4077.246395769243));
      dormandPrince853Integrator0.setMaxGrowth((-4077.246395769243));
      dormandPrince853Integrator0.setMaxGrowth((-4077.246395769243));
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn((-664)).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = null;
      try {
        firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.FirstOrderConverter", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      double double0 = 0.0;
      double double1 = 1.0001;
      double double2 = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, 1.0001, 0.0);
      double[][] doubleArrayArray0 = null;
      double[] doubleArray0 = new double[4];
      double double3 = (-2225.7002451);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(2).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      dormandPrince54Integrator0.resetInternalState();
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(0.0, 0.0, 0.0).when(eventHandler0).g(anyDouble() , any(double[].class));
      dormandPrince54Integrator0.addEventHandler(eventHandler0, 1.0, (-2225.7002451), 2);
      dormandPrince54Integrator0.setSafety(0.0);
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 1.0001, doubleArray0, 0.2, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      double double0 = (-664.235393519417);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-664.235393519417);
      doubleArray0[1] = (-664.235393519417);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-664.235393519417), (-664.235393519417), doubleArray0, doubleArray0);
      highamHall54Integrator0.setSafety((-664.235393519417));
      highamHall54Integrator0.setInitialStepSize(383.39704);
      int int0 = 1;
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      // Undeclared exception!
      highamHall54Integrator0.integrate(firstOrderConverter0, 629459.0881021831, doubleArray0, (-1612.88), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(656.6774798201471, 656.6774798201471, 656.6774798201471, 656.6774798201471);
      dormandPrince853Integrator0.getEventHandlers();
      dormandPrince853Integrator0.getOrder();
      dormandPrince853Integrator0.setMaxEvaluations(8);
      dormandPrince853Integrator0.getMinReduction();
      dormandPrince853Integrator0.setMinReduction(0.2);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      double[] doubleArray0 = null;
      double double0 = 1705.1171071722588;
      // Undeclared exception!
      try { 
        dormandPrince853Integrator0.integrate(firstOrderConverter0, 0.2, (double[]) null, 1705.1171071722588, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }
}
