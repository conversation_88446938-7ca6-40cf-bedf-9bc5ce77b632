/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 20:24:10 GMT 2019
 */

package org.apache.commons.math.ode.nonstiff;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.Collection;
import org.apache.commons.math.ode.FirstOrderConverter;
import org.apache.commons.math.ode.FirstOrderDifferentialEquations;
import org.apache.commons.math.ode.SecondOrderDifferentialEquations;
import org.apache.commons.math.ode.events.EventHandler;
import org.apache.commons.math.ode.nonstiff.DormandPrince54Integrator;
import org.apache.commons.math.ode.nonstiff.DormandPrince853Integrator;
import org.apache.commons.math.ode.nonstiff.HighamHall54Integrator;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class EmbeddedRungeKuttaIntegrator_ESTest extends EmbeddedRungeKuttaIntegrator_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, (-37.53041286152357), (-37.53041286152357), (-37.53041286152357));
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(37.53041286152357, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      dormandPrince853Integrator0.resetInternalState();
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(37.53041286152357, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-37.53041286152357);
      doubleArray0[1] = (-37.53041286152357);
      double double0 = dormandPrince853Integrator0.integrate(firstOrderConverter0, 1, doubleArray0, 0.0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals((-1.3877787807814457E-17), double0, 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(37.53041286152357, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(38, dormandPrince853Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertArrayEquals(new double[] {7.993605777301127E-15, (-37.53041286152357)}, doubleArray0, 0.01);
      
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-37.53041286152357), 1, 2397.57, (-37.53041286152357));
      assertNotNull(highamHall54Integrator0);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(37.53041286152357, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(1.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(6.126207053432292, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(37.53041286152357, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(1.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(6.126207053432292, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(7.993605777301127E-15, (-37.53041286152357), 7.993605777301127E-15, (-37.53041286152357));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(7.993605777301127E-15, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5.477255928604864E-7, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(37.53041286152357, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      
      double[][] doubleArray1 = new double[8][8];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      double[] doubleArray2 = new double[7];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 906.36696;
      doubleArray2[1] = (-1.3877787807814457E-17);
      doubleArray2[2] = (double) 1;
      doubleArray2[3] = (double) 1;
      doubleArray2[4] = 0.0;
      doubleArray2[5] = (-37.53041286152357);
      doubleArray2[6] = 0.0;
      doubleArray1[2] = doubleArray2;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      double double1 = dormandPrince853Integrator0.initializeStep(firstOrderConverter0, true, 1, doubleArray0, 1, doubleArray0, doubleArray0, doubleArray0, doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray2.length);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertEquals(double1, double0, 0.01);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertEquals(1.0E-12, double1, 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(37.53041286152357, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(39, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertArrayEquals(new double[] {8.073541835074138E-15, (-37.90571699013881)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-37.90571699013881), 0.0, 1.0, 1.0, 0.0, (-37.53041286152357), 0.0}, doubleArray2, 0.01);
      
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray2, doubleArray0, (-2232.4034));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double double0 = 8.829840530781136E-20;
      double double1 = (-468.91045861);
      double double2 = 197.0;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(8.829840530781136E-20, 8.829840530781136E-20, (-468.91045861), 197.0);
      assertNotNull(highamHall54Integrator0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(8.829840530781136E-20, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(8.829840530781136E-20, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(8.829840530781136E-20, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      highamHall54Integrator0.clearEventHandlers();
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(8.829840530781136E-20, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(8.829840530781136E-20, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(8.829840530781136E-20, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      
      double[] doubleArray0 = new double[0];
      // Undeclared exception!
      highamHall54Integrator0.integrate(firstOrderConverter0, 8.829840530781136E-20, doubleArray0, (-468.91045861), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, (-0.037812895149910514), (-0.037812895149910514), (-0.037812895149910514));
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      dormandPrince853Integrator0.resetInternalState();
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-0.037812895149910514);
      doubleArray0[1] = 0.0;
      double double0 = dormandPrince853Integrator0.integrate(firstOrderConverter0, 1, doubleArray0, 0.0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(398, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertArrayEquals(new double[] {(-0.037812895149910514), 0.0}, doubleArray0, 0.01);
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-0.037812895149910514), (-0.037812895149910514), (-0.037812895149910514), (-0.037812895149910514));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.037812895149910514, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.037812895149910514, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.037812895149910514, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.037812895149910514, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      
      double[][] doubleArray1 = new double[8][8];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      double[] doubleArray2 = new double[7];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 906.36696;
      doubleArray2[1] = 0.0;
      doubleArray2[2] = (double) 1;
      doubleArray2[3] = (double) 1;
      doubleArray2[4] = 0.0;
      doubleArray2[5] = (-0.037812895149910514);
      doubleArray2[6] = 0.0;
      doubleArray1[2] = doubleArray2;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray2, doubleArray0, (-2232.4034));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, (-37.53041286152357), (-37.53041286152357), (-37.53041286152357));
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(37.53041286152357, dormandPrince853Integrator0.getMaxStep(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      dormandPrince853Integrator0.resetInternalState();
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(37.53041286152357, dormandPrince853Integrator0.getMaxStep(), 0.01);
      
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-37.53041286152357);
      doubleArray0[1] = (-2232.4034);
      double double0 = dormandPrince853Integrator0.integrate(firstOrderConverter0, (-2232.4034), doubleArray0, 0.0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(770, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(37.53041286152357, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertArrayEquals(new double[] {(-4983662.470744436), (-2232.4034)}, doubleArray0, 0.01);
      
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-2232.4034), 1, 2397.57, (-2232.4034));
      assertNotNull(highamHall54Integrator0);
      assertEquals(47.248316372120605, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(1.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2232.4034, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals(47.248316372120605, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(1.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2232.4034, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-4983662.470744436), (-2232.4034), (-4983662.470744436), (-37.53041286152357));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(4983662.470744436, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(105477.69927402797, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(2232.4034, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      
      double[][] doubleArray1 = new double[8][8];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      double[] doubleArray2 = new double[7];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 906.36696;
      doubleArray2[1] = 0.0;
      doubleArray2[2] = (double) 1;
      doubleArray2[3] = (double) 1;
      doubleArray2[4] = 0.0;
      doubleArray2[5] = (-37.53041286152357);
      doubleArray2[6] = 0.0;
      doubleArray1[2] = doubleArray2;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      double double1 = dormandPrince853Integrator0.initializeStep(firstOrderConverter0, true, 1, doubleArray0, 1, doubleArray0, doubleArray0, doubleArray0, doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray2.length);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertEquals(double1, double0, 0.01);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertEquals(7.072651711585304E-5, double1, 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(771, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(37.53041286152357, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertArrayEquals(new double[] {(-5033499.09545188), (-2254.7274340000004)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-2254.7274340000004), 0.0, 1.0, 1.0, 0.0, (-37.53041286152357), 0.0}, doubleArray2, 0.01);
      
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray2, doubleArray0, (-2232.4034));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, (-0.037812895149910514), (-0.037812895149910514), (-0.037812895149910514));
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      dormandPrince853Integrator0.resetInternalState();
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-0.037812895149910514);
      doubleArray0[1] = (double) 1;
      double double0 = dormandPrince853Integrator0.integrate(firstOrderConverter0, (-0.037812895149910514), doubleArray0, 0.0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(6.938893903907228E-18, double0, 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(26, dormandPrince853Integrator0.getEvaluations());
      assertEquals(2, firstOrderConverter0.getDimension());
      assertArrayEquals(new double[] {2.0816681711721685E-17, 1.0}, doubleArray0, 0.01);
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(2.0816681711721685E-17, 1, 2.0816681711721685E-17, (-0.037812895149910514));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(2.0816681711721685E-17, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(4.562530187486071E-9, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(1.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      
      double[][] doubleArray1 = new double[8][8];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      double[] doubleArray2 = new double[7];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 906.36696;
      doubleArray2[1] = 6.938893903907228E-18;
      doubleArray2[2] = (double) 1;
      doubleArray2[3] = (double) 1;
      doubleArray2[4] = 0.0;
      doubleArray2[5] = (-0.037812895149910514);
      doubleArray2[6] = 0.0;
      doubleArray1[2] = doubleArray2;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray2, doubleArray0, (-2232.4034));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(7.010264425053526E-17, (-1.0), doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertNotNull(highamHall54Integrator0);
      assertEquals((-1.0), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7.010264425053526E-17, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals(0, doubleArray0.length);
      assertEquals(5, int0);
      assertEquals((-1.0), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7.010264425053526E-17, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.3, 0.3, doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.3, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.3, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.3, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      double double0 = (-1168.653693);
      int int1 = 1;
      dormandPrince54Integrator0.addEventHandler(eventHandler0, (-1.0), (-1168.653693), 1);
      assertEquals(0, doubleArray0.length);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.3, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.3, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.3, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      int int2 = dormandPrince54Integrator0.getOrder();
      assertEquals(0, doubleArray0.length);
      assertTrue(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(5, int2);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.3, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.3, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.3, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[5][7];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      EventHandler eventHandler1 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      dormandPrince54Integrator0.addEventHandler(eventHandler1, (-2230.3185759166754), 5, 1883);
      assertEquals(0, doubleArray0.length);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.3, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.3, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.3, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      double double1 = highamHall54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 5);
      assertEquals(0, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      assertEquals((-1.0), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7.010264425053526E-17, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      int int3 = highamHall54Integrator0.getOrder();
      assertEquals(0, doubleArray0.length);
      assertFalse(int3 == int1);
      assertTrue(int3 == int0);
      assertTrue(int3 == int2);
      assertEquals(5, int3);
      assertEquals((-1.0), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7.010264425053526E-17, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      double double2 = 1863.90588889187;
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 1863.90588889187, doubleArray0, (-201.62327598735), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double double0 = 0.0;
      double double1 = (-0.037812895149910514);
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, (-0.037812895149910514), (-0.037812895149910514), (-0.037812895149910514));
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      
      int int0 = 1;
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      dormandPrince853Integrator0.resetInternalState();
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double double2 = (-2232.4034);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-0.037812895149910514);
      doubleArray0[1] = (-2232.4034);
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, (-2232.4034), doubleArray0, 0.0, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, 0.0, 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (-2232.4034);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(5656.91564, 0.0, doubleArray0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertNotNull(highamHall54Integrator0);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(5656.91564, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertArrayEquals(new double[] {0.0, (-2232.4034)}, doubleArray0, 0.01);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals(2, doubleArray0.length);
      assertEquals(5, int0);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(5656.91564, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertArrayEquals(new double[] {0.0, (-2232.4034)}, doubleArray0, 0.01);
      
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double[] doubleArray1 = new double[2];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (-2232.4034);
      doubleArray1[1] = (double) 1;
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, 0.0, doubleArray0, 5656.91564, doubleArray1);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double double0 = 0.0;
      double double1 = 0.0;
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, 0.0, 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      dormandPrince853Integrator0.resetInternalState();
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (-2232.4034);
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, (-2232.4034), doubleArray0, 0.0, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(154.104418, (-1839.37), (-1839.37), 8.906422717743473);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(532.4049617881674, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1839.37, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(154.104418, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      
      dormandPrince54Integrator0.setMinReduction((-1.0));
      assertEquals(532.4049617881674, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1839.37, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(154.104418, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals(532.4049617881674, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1839.37, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(154.104418, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      
      dormandPrince54Integrator0.setInitialStepSize((-711.286));
      assertEquals(532.4049617881674, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1839.37, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(154.104418, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      
      dormandPrince54Integrator0.setMinReduction((-1.0));
      assertEquals(532.4049617881674, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1839.37, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(154.104418, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      dormandPrince54Integrator0.setMaxGrowth(154.104418);
      assertEquals(532.4049617881674, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1839.37, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(154.104418, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(154.104418, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      
      dormandPrince54Integrator0.addEventHandler(eventHandler0, (-1.0), (-3152.9674711903085), 12);
      assertEquals(532.4049617881674, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1839.37, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(154.104418, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(154.104418, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      
      dormandPrince54Integrator0.setInitialStepSize((-711.286));
      assertEquals(532.4049617881674, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1839.37, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(154.104418, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(154.104418, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      
      double double0 = dormandPrince54Integrator0.getMaxGrowth();
      assertEquals(154.104418, double0, 0.01);
      assertEquals(532.4049617881674, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1839.37, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(154.104418, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(154.104418, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      
      double double1 = dormandPrince54Integrator0.getMinReduction();
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-1.0), double1, 0.01);
      assertEquals(532.4049617881674, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(1839.37, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(154.104418, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(154.104418, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      
      double[][] doubleArray0 = new double[1][9];
      double[] doubleArray1 = new double[8];
      doubleArray1[0] = 154.104418;
      doubleArray1[1] = (-1.0);
      doubleArray1[2] = (-711.286);
      doubleArray1[3] = (-1.0);
      doubleArray1[4] = 0.0;
      doubleArray1[5] = 154.104418;
      doubleArray1[6] = 154.104418;
      doubleArray1[7] = 154.104418;
      doubleArray0[0] = doubleArray1;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray0, doubleArray1, doubleArray1, 154.104418);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      double double0 = 3003.98364492;
      double double1 = 0.0419047619047619;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(3003.98364492, 3003.98364492, 0.0419047619047619, 0.0419047619047619);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(3003.98364492, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(3003.98364492, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(3003.98364492, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[0];
      double double2 = (-833.90483);
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.0, doubleArray0, (-833.90483), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.clearEventHandlers();
      assertEquals(0, doubleArray0.length);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.setMaxGrowth(0.0);
      assertEquals(0, doubleArray0.length);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(0, doubleArray0.length);
      assertEquals(5, int0);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      double double0 = dormandPrince54Integrator0.getMaxGrowth();
      assertEquals(0, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.0, (-1193.820516), doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertNotNull(highamHall54Integrator0);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(-0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals((-1193.820516), highamHall54Integrator0.getMaxStep(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      double[][] doubleArray1 = new double[25][2];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (-1408.0);
      doubleArray2[1] = (-1193.820516);
      doubleArray2[2] = (-1193.820516);
      doubleArray2[3] = (-1193.820516);
      doubleArray2[4] = 0.0;
      doubleArray2[5] = (-1193.820516);
      doubleArray2[6] = (-1193.820516);
      // Undeclared exception!
      try { 
        highamHall54Integrator0.estimateError(doubleArray1, doubleArray2, doubleArray0, 0.0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.0, 0.0, 0.0, 0.0);
      assertNotNull(highamHall54Integrator0);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      highamHall54Integrator0.setMaxGrowth((-1548.14));
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals((-1548.14), highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      double double0 = highamHall54Integrator0.getMaxGrowth();
      assertEquals((-1548.14), double0, 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals((-1548.14), highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals((-1548.14), highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-0.19316434850839564);
      doubleArray0[1] = (double) 5;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, (-30.375), doubleArray0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(-0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals((-30.375), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertArrayEquals(new double[] {(-0.19316434850839564), 5.0}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError((double[][]) null, doubleArray0, doubleArray0, 2383.61);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(1659.23503624486, 0.0, 0.2, (-0.322376179245283));
      assertNotNull(highamHall54Integrator0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1659.23503624486, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      
      highamHall54Integrator0.setMinReduction(0.0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1659.23503624486, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      
      Collection<EventHandler> collection0 = highamHall54Integrator0.getEventHandlers();
      assertNotNull(collection0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1659.23503624486, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      
      highamHall54Integrator0.setSafety(1659.23503624486);
      assertEquals(1659.23503624486, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1659.23503624486, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals(1659.23503624486, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1659.23503624486, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      
      int int1 = highamHall54Integrator0.getOrder();
      assertTrue(int1 == int0);
      assertEquals(5, int1);
      assertEquals(1659.23503624486, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1659.23503624486, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      
      double double0 = highamHall54Integrator0.getSafety();
      assertEquals(1659.23503624486, double0, 0.01);
      assertEquals(1659.23503624486, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1659.23503624486, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      
      double double1 = highamHall54Integrator0.getMinReduction();
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertEquals(1659.23503624486, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1659.23503624486, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, 4576.372195243263, 1032.45415156);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      
      double[][] doubleArray0 = new double[8][9];
      double[] doubleArray1 = new double[2];
      doubleArray1[0] = 0.0;
      doubleArray1[1] = 1659.23503624486;
      doubleArray0[0] = doubleArray1;
      double[] doubleArray2 = new double[1];
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      doubleArray2[0] = 1659.23503624486;
      doubleArray0[1] = doubleArray2;
      double[] doubleArray3 = new double[0];
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      
      doubleArray0[2] = doubleArray3;
      double[] doubleArray4 = new double[7];
      assertFalse(doubleArray4.equals((Object)doubleArray2));
      assertFalse(doubleArray4.equals((Object)doubleArray3));
      assertFalse(doubleArray4.equals((Object)doubleArray1));
      
      doubleArray4[0] = 0.0;
      doubleArray4[1] = 0.0;
      doubleArray4[2] = 1032.45415156;
      doubleArray4[3] = 0.0;
      doubleArray4[4] = 0.0;
      doubleArray4[5] = 0.2;
      doubleArray4[6] = 0.0;
      doubleArray0[3] = doubleArray4;
      double[] doubleArray5 = new double[9];
      assertFalse(doubleArray5.equals((Object)doubleArray3));
      assertFalse(doubleArray5.equals((Object)doubleArray4));
      assertFalse(doubleArray5.equals((Object)doubleArray1));
      assertFalse(doubleArray5.equals((Object)doubleArray2));
      
      doubleArray5[0] = (-0.322376179245283);
      doubleArray5[1] = 1032.45415156;
      doubleArray5[2] = 4576.372195243263;
      doubleArray5[3] = 0.0;
      doubleArray5[4] = 0.0;
      doubleArray5[5] = (-0.322376179245283);
      doubleArray5[6] = 0.2;
      doubleArray5[7] = 0.2;
      doubleArray5[8] = 1659.23503624486;
      doubleArray0[4] = doubleArray5;
      double[] doubleArray6 = new double[5];
      assertFalse(doubleArray6.equals((Object)doubleArray3));
      assertFalse(doubleArray6.equals((Object)doubleArray4));
      assertFalse(doubleArray6.equals((Object)doubleArray1));
      assertFalse(doubleArray6.equals((Object)doubleArray5));
      assertFalse(doubleArray6.equals((Object)doubleArray2));
      
      doubleArray6[0] = 1659.23503624486;
      doubleArray6[1] = 1659.23503624486;
      doubleArray6[2] = (-0.322376179245283);
      doubleArray6[3] = 1659.23503624486;
      doubleArray6[4] = 0.0;
      doubleArray0[5] = doubleArray6;
      double[] doubleArray7 = new double[0];
      assertFalse(doubleArray7.equals((Object)doubleArray1));
      assertFalse(doubleArray7.equals((Object)doubleArray4));
      assertFalse(doubleArray7.equals((Object)doubleArray3));
      assertFalse(doubleArray7.equals((Object)doubleArray6));
      assertFalse(doubleArray7.equals((Object)doubleArray2));
      assertFalse(doubleArray7.equals((Object)doubleArray5));
      
      doubleArray0[6] = doubleArray7;
      double[] doubleArray8 = new double[3];
      assertFalse(doubleArray8.equals((Object)doubleArray1));
      assertFalse(doubleArray8.equals((Object)doubleArray4));
      assertFalse(doubleArray8.equals((Object)doubleArray6));
      assertFalse(doubleArray8.equals((Object)doubleArray7));
      assertFalse(doubleArray8.equals((Object)doubleArray3));
      assertFalse(doubleArray8.equals((Object)doubleArray5));
      assertFalse(doubleArray8.equals((Object)doubleArray2));
      
      doubleArray8[0] = 4576.372195243263;
      doubleArray8[1] = 1659.23503624486;
      doubleArray8[2] = 1659.23503624486;
      doubleArray0[7] = doubleArray8;
      // Undeclared exception!
      try { 
        dormandPrince853Integrator0.estimateError(doubleArray0, doubleArray6, doubleArray5, 1659.23503624486);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.ode.nonstiff.DormandPrince853Integrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-3267.98785);
      doubleArray0[1] = (-3267.98785);
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-3267.98785), (-3267.98785), doubleArray0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-3267.98785), dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(3267.98785, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals((-3267.98785), dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertArrayEquals(new double[] {(-3267.98785), (-3267.98785)}, doubleArray0, 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      double double0 = 1648.935601558186;
      double[] doubleArray1 = new double[4];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (-3267.98785);
      doubleArray1[1] = (-3267.98785);
      doubleArray1[2] = 1648.935601558186;
      doubleArray1[3] = 1648.935601558186;
      try { 
        dormandPrince853Integrator0.integrate(firstOrderConverter0, 1648.935601558186, doubleArray0, (-3267.98785), doubleArray1);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 0, initial state vector has dimension 2
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double double0 = 0.0;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-2071.385889268151), 0.0, 0.0, (-2071.385889268151));
      assertNotNull(highamHall54Integrator0);
      assertEquals(2071.385889268151, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double double1 = 1082.02015417;
      double double2 = 0.0;
      highamHall54Integrator0.setMaxGrowth(0.0);
      assertEquals(2071.385889268151, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      highamHall54Integrator0.setMinReduction(1082.02015417);
      assertEquals(2071.385889268151, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(1082.02015417, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      highamHall54Integrator0.clearStepHandlers();
      assertEquals(2071.385889268151, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(1082.02015417, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double[] doubleArray0 = null;
      double[] doubleArray1 = null;
      // Undeclared exception!
      try { 
        highamHall54Integrator0.integrate(firstOrderConverter0, 165.20045171727028, (double[]) null, 1.0, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.0, 4013.807, 0.0, 0.0);
      assertNotNull(highamHall54Integrator0);
      assertEquals(4013.807, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double double0 = highamHall54Integrator0.filterStep(0.0, true, true);
      assertEquals(0.0, double0, 0.01);
      assertEquals(4013.807, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      highamHall54Integrator0.setMinReduction(3556.9398448989814);
      assertEquals(4013.807, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(3556.9398448989814, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      highamHall54Integrator0.setSafety((-3520.49297598387));
      assertEquals(4013.807, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(3556.9398448989814, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-3520.49297598387), highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals(4013.807, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(3556.9398448989814, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-3520.49297598387), highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double double1 = highamHall54Integrator0.getSafety();
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-3520.49297598387), double1, 0.01);
      assertEquals(4013.807, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(3556.9398448989814, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-3520.49297598387), highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-3520.49297598387), 3402.124, 0.0, 806.93);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(3402.124, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(3460.802456862591, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(3520.49297598387, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      
      int int1 = dormandPrince853Integrator0.getOrder();
      assertFalse(int1 == int0);
      assertEquals(8, int1);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(3402.124, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(3460.802456862591, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(3520.49297598387, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      
      double double2 = dormandPrince853Integrator0.getMaxGrowth();
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(10.0, double2, 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(3402.124, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(3460.802456862591, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(3520.49297598387, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      
      highamHall54Integrator0.setMaxGrowth(10.0);
      assertEquals(4013.807, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(3556.9398448989814, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-3520.49297598387), highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double double3 = dormandPrince853Integrator0.getSafety();
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(0.9, double3, 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(3402.124, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(3460.802456862591, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(3520.49297598387, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.0;
      double[] doubleArray1 = new double[7];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[1] = 0.0;
      doubleArray1[2] = 0.0;
      doubleArray1[3] = 0.0;
      doubleArray1[4] = 1649.7864;
      doubleArray1[5] = 0.0;
      doubleArray1[6] = 0.0;
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(1649.7864, 0.0, doubleArray0, doubleArray1);
      assertEquals(1, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(1649.7864, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 1649.7864, 0.0, 0.0}, doubleArray1, 0.01);
      
      double double0 = dormandPrince853Integrator0.getMaxGrowth();
      assertEquals(1, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(10.0, double0, 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(1649.7864, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 1649.7864, 0.0, 0.0}, doubleArray1, 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      double double0 = 675.25;
      double double1 = 3151.16160642;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(2445.0, 675.25, 3151.16160642, 2445.0);
      assertNotNull(highamHall54Integrator0);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(675.25, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(2445.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1284.9070978090206, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      highamHall54Integrator0.resetInternalState();
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(675.25, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(2445.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1284.9070978090206, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double double2 = (-5908.7912785501);
      highamHall54Integrator0.setMinReduction((-5908.7912785501));
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals((-5908.7912785501), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(675.25, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(2445.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1284.9070978090206, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals(5, int0);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals((-5908.7912785501), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(675.25, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(2445.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1284.9070978090206, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double double3 = highamHall54Integrator0.getSafety();
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(0.9, double3, 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals((-5908.7912785501), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(675.25, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(2445.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1284.9070978090206, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      highamHall54Integrator0.setInitialStepSize(0.9);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals((-5908.7912785501), highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(675.25, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(2445.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1284.9070978090206, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-134.694);
      try { 
        highamHall54Integrator0.integrate(firstOrderConverter0, 2445.0, doubleArray0, 0.9, doubleArray0);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 0, initial state vector has dimension 1
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(1181.411, 1181.411, 0.9, 0.0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(1181.411, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(1181.411, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1181.411, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = 0.9;
      doubleArray0[1] = (-2449.896505164);
      doubleArray0[2] = 0.9;
      doubleArray0[3] = 1181.411;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.integrate((FirstOrderDifferentialEquations) null, (-3455.4829727), doubleArray0, (-1459.0), doubleArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 0.0, 0.0, 938.95325215479);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      
      double double0 = dormandPrince54Integrator0.getMaxGrowth();
      assertEquals(10.0, double0, 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      
      double[] doubleArray0 = new double[0];
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(10.0, 10.0, doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      int int0 = dormandPrince853Integrator0.getOrder();
      assertEquals(0, doubleArray0.length);
      assertEquals(8, int0);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 1769.6773950833;
      doubleArray0[1] = 1769.6773950833;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 0.0;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(1769.6773950833, 0.0, doubleArray0, doubleArray0);
      assertEquals(6, doubleArray0.length);
      assertNotNull(highamHall54Integrator0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1769.6773950833, highamHall54Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {1769.6773950833, 1769.6773950833, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      highamHall54Integrator0.setSafety(0.0);
      assertEquals(6, doubleArray0.length);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1769.6773950833, highamHall54Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {1769.6773950833, 1769.6773950833, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = highamHall54Integrator0.getSafety();
      assertEquals(6, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1769.6773950833, highamHall54Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {1769.6773950833, 1769.6773950833, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      highamHall54Integrator0.setMaxGrowth(224.00768);
      assertEquals(6, doubleArray0.length);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(224.00768, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1769.6773950833, highamHall54Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {1769.6773950833, 1769.6773950833, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.0;
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.9, 0.0, doubleArray0, doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.9, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.setMinReduction((-1638.0));
      assertEquals(1, doubleArray0.length);
      assertEquals(0.9, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals((-1638.0), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-0.08899033645133331);
      doubleArray0[1] = (-397.39958903);
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-0.08899033645133331), (-0.08899033645133331), doubleArray0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals((-0.08899033645133331), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals((-0.08899033645133331), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.08899033645133331, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertArrayEquals(new double[] {(-0.08899033645133331), (-397.39958903)}, doubleArray0, 0.01);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(2, doubleArray0.length);
      assertEquals(5, int0);
      assertEquals((-0.08899033645133331), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals((-0.08899033645133331), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.08899033645133331, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertArrayEquals(new double[] {(-0.08899033645133331), (-397.39958903)}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1360.13787901), (-1360.13787901), (-1831.1081), (-1360.13787901));
      assertNotNull(highamHall54Integrator0);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(1360.13787901, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(1360.13787901, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1360.13787901, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      highamHall54Integrator0.setSafety(1251.1528089);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(1251.1528089, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1360.13787901, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(1360.13787901, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1360.13787901, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      
      double double0 = highamHall54Integrator0.getMinReduction();
      assertEquals(0.2, double0, 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(1251.1528089, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1360.13787901, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(1360.13787901, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1360.13787901, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.2, 386.42257241977, 0.2, 2.9475147891527724);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(386.42257241977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(8.791161156750228, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      
      double double0 = dormandPrince54Integrator0.getMinReduction();
      assertEquals(0.2, double0, 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(386.42257241977, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(8.791161156750228, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.0;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.2, 0.0, doubleArray0, doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertNotNull(highamHall54Integrator0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.2, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-0.3111643669578199), (-0.3111643669578199), (-0.3111643669578199), (-0.3111643669578199));
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.3111643669578199, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.3111643669578199, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.3111643669578199, dormandPrince853Integrator0.getMaxStep(), 0.01);
      
      int int0 = dormandPrince853Integrator0.getOrder();
      assertEquals(8, int0);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.3111643669578199, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.3111643669578199, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.3111643669578199, dormandPrince853Integrator0.getMaxStep(), 0.01);
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, (-0.037812895149910514), (-0.037812895149910514), (-0.037812895149910514));
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      dormandPrince853Integrator0.resetInternalState();
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-0.037812895149910514);
      doubleArray0[1] = (double) 1;
      double double0 = dormandPrince853Integrator0.integrate(firstOrderConverter0, (-0.037812895149910514), doubleArray0, 0.0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(6.938893903907228E-18, double0, 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.037812895149910514, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(26, dormandPrince853Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertArrayEquals(new double[] {2.0816681711721685E-17, 1.0}, doubleArray0, 0.01);
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(2.0816681711721685E-17, 1, 2.0816681711721685E-17, (-0.037812895149910514));
      assertNotNull(dormandPrince54Integrator0);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(4.562530187486071E-9, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(2.0816681711721685E-17, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      
      double[][] doubleArray1 = new double[8][8];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      double[] doubleArray2 = new double[7];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 906.36696;
      doubleArray2[1] = 6.938893903907228E-18;
      doubleArray2[2] = (double) 1;
      doubleArray2[3] = (double) 1;
      doubleArray2[4] = 0.0;
      doubleArray2[5] = (-0.037812895149910514);
      doubleArray2[6] = 0.0;
      doubleArray1[2] = doubleArray2;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[0] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.estimateError(doubleArray1, doubleArray2, doubleArray0, (-2232.4034));
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-4007.41), (-4007.41), doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-4007.41), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(4007.41, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals((-4007.41), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.resetInternalState();
      assertEquals(0, doubleArray0.length);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-4007.41), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(4007.41, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals((-4007.41), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(477.9, (-833.90483), (-4007.41), (-833.90483));
      assertNotNull(highamHall54Integrator0);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(477.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(833.90483, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(631.2868747701, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      
      double[][] doubleArray1 = new double[8][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      dormandPrince54Integrator0.setMinReduction((-4007.41));
      assertEquals(0, doubleArray0.length);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-4007.41), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(4007.41, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals((-4007.41), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals((-4007.41), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      highamHall54Integrator0.addEventHandler(eventHandler0, (-460.6810447673949), 2725.0028, (-1));
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(477.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(833.90483, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(631.2868747701, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      
      dormandPrince54Integrator0.setMinReduction((-460.6810447673949));
      assertEquals(0, doubleArray0.length);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-460.6810447673949), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-4007.41), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(4007.41, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals((-4007.41), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      doubleArray1[7] = doubleArray0;
      double double0 = highamHall54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, (-4007.41));
      assertEquals(0, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(477.9, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(833.90483, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(631.2868747701, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      int int0 = dormandPrince54Integrator0.getOrder();
      assertEquals(0, doubleArray0.length);
      assertEquals(5, int0);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-460.6810447673949), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals((-4007.41), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(4007.41, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals((-4007.41), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-708.1333072957094), 477.9, 477.9, 477.9);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(581.7361150527097, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(708.1333072957094, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(477.9, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, 5, doubleArray0, Double.NaN, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      double double0 = 0.0;
      double double1 = 0.0;
      double double2 = 0.0;
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, 0.0, 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      
      double double3 = dormandPrince853Integrator0.getSafety();
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(0.9, double3, 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn((-2951)).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = null;
      try {
        firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.FirstOrderConverter", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(1223.0, 1223.0, 1223.0, 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(1223.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1223.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(1223.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      
      double double0 = dormandPrince853Integrator0.getMaxGrowth();
      assertEquals(10.0, double0, 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(1223.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1223.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(1223.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      
      int int0 = dormandPrince853Integrator0.getOrder();
      assertEquals(8, int0);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(1223.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1223.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(1223.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      
      dormandPrince853Integrator0.setSafety(0.0);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(1223.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1223.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(1223.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(8).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(16, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 1223.0;
      doubleArray0[1] = 10.0;
      doubleArray0[2] = 10.0;
      try { 
        dormandPrince853Integrator0.integrate(firstOrderConverter0, 8, doubleArray0, 10.0, doubleArray0);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 16, initial state vector has dimension 3
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      double[][] doubleArray0 = new double[6][4];
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, 0.0, 0.0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (-2232.4034);
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(5656.91564, 0.0, doubleArray0, doubleArray0);
      highamHall54Integrator0.getOrder();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      dormandPrince853Integrator0.addEventHandler(eventHandler0, (-2232.4034), 5656.91564, 1);
      double[] doubleArray1 = new double[2];
      doubleArray1[0] = (-2232.4034);
      doubleArray1[1] = (double) 1;
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, 0.0, doubleArray0, 5656.91564, doubleArray1);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-797.2763223205743);
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-797.2763223205743), (-797.2763223205743), doubleArray0, doubleArray0);
      dormandPrince853Integrator0.resetInternalState();
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      dormandPrince853Integrator0.addEventHandler(eventHandler0, (-797.2763223205743), (-797.2763223205743), 0);
      dormandPrince853Integrator0.setInitialStepSize((-797.2763223205743));
      dormandPrince853Integrator0.getEventHandlers();
      dormandPrince853Integrator0.getMinReduction();
      dormandPrince853Integrator0.getMaxGrowth();
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-797.2763223205743), 0.2, 10.0, (-2009.81703359262));
      highamHall54Integrator0.getOrder();
      highamHall54Integrator0.getOrder();
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(10.0, (-388.00876101), 10.0, (-3599.59491095768));
      dormandPrince54Integrator0.getOrder();
      double[][] doubleArray1 = new double[1][5];
      doubleArray1[0] = doubleArray0;
      // Undeclared exception!
      try { 
        highamHall54Integrator0.estimateError(doubleArray1, doubleArray0, doubleArray0, 5);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }
}
