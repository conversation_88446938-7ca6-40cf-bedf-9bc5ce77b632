/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 20:27:36 GMT 2019
 */

package org.apache.commons.math.ode.nonstiff;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math.ode.FirstOrderConverter;
import org.apache.commons.math.ode.SecondOrderDifferentialEquations;
import org.apache.commons.math.ode.events.EventHandler;
import org.apache.commons.math.ode.nonstiff.DormandPrince54Integrator;
import org.apache.commons.math.ode.nonstiff.DormandPrince853Integrator;
import org.apache.commons.math.ode.nonstiff.HighamHall54Integrator;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class EmbeddedRungeKuttaIntegrator_ESTest extends EmbeddedRungeKuttaIntegrator_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-1184.767010092211);
      doubleArray0[1] = 1892.320595;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1184.767010092211), 1892.320595, doubleArray0, doubleArray0);
      assertNotNull(highamHall54Integrator0);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1184.767010092211), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertArrayEquals(new double[] {(-1184.767010092211), 1892.320595}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double double0 = highamHall54Integrator0.integrate(firstOrderConverter0, 23.4375, doubleArray0, (-1184.767010092211), doubleArray0);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(43, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1184.767010092211), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(2, firstOrderConverter0.getDimension());
      assertEquals((-1184.767010092211), double0, 0.01);
      assertArrayEquals(new double[] {(-2287495.0444294685), 1892.320595}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      double double1 = highamHall54Integrator0.integrate(firstOrderConverter0, 1.0, doubleArray0, 2.2794362650344272E-21, doubleArray0);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(43, highamHall54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1184.767010092211), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(2, firstOrderConverter0.getDimension());
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {(-2289387.3650244684), 1892.320595}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.0, 0.0, 0.0, 0.0);
      assertNotNull(highamHall54Integrator0);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      
      highamHall54Integrator0.setSafety(0.0);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      
      double double0 = highamHall54Integrator0.getSafety();
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(943.4695, 943.4695, 943.4695, 943.4695);
      assertNotNull(highamHall54Integrator0);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(943.4695, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(943.4695, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(943.4695, highamHall54Integrator0.getMaxStep(), 0.01);
      
      highamHall54Integrator0.setSafety((-2201.8102645020176));
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(943.4695, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals((-2201.8102645020176), highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(943.4695, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(943.4695, highamHall54Integrator0.getMaxStep(), 0.01);
      
      double double0 = highamHall54Integrator0.getSafety();
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(943.4695, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals((-2201.8102645020176), highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(943.4695, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(943.4695, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals((-2201.8102645020176), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-2069.33808), (-2069.33808), (-2069.33808), 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getMaxStep(), 0.01);
      
      int int0 = dormandPrince853Integrator0.getOrder();
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(8, int0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-2069.33808), (-2069.33808), (-2069.33808), 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(2069.33808, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      
      dormandPrince853Integrator0.setMinReduction(0.0);
      assertEquals(2069.33808, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(2069.33808, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      
      double double0 = dormandPrince853Integrator0.getMinReduction();
      assertEquals(2069.33808, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(2069.33808, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-1767.14197), (-1767.14197), (-1767.14197), 19.985053242002433);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(1767.14197, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1767.14197, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(1767.14197, dormandPrince853Integrator0.getMinStep(), 0.01);
      
      dormandPrince853Integrator0.setMinReduction((-1767.14197));
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals((-1767.14197), dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(1767.14197, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1767.14197, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(1767.14197, dormandPrince853Integrator0.getMinStep(), 0.01);
      
      double double0 = dormandPrince853Integrator0.getMinReduction();
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals((-1767.14197), dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(1767.14197, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1767.14197, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(1767.14197, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals((-1767.14197), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-2069.33808), (-2069.33808), (-2069.33808), 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(2069.33808, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(2069.33808, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(2069.33808, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      
      dormandPrince853Integrator0.setMaxGrowth((-1781.1305));
      assertEquals((-1781.1305), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(2069.33808, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(2069.33808, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(2069.33808, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      
      double double0 = dormandPrince853Integrator0.getMaxGrowth();
      assertEquals((-1781.1305), dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(2069.33808, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(2069.33808, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(2069.33808, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals((-1781.1305), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-1184.767010092211);
      doubleArray0[1] = 1892.320595;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1184.767010092211), 1892.320595, doubleArray0, doubleArray0);
      assertNotNull(highamHall54Integrator0);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1184.767010092211), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertArrayEquals(new double[] {(-1184.767010092211), 1892.320595}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(1144.6, 1.0, (-1184.767010092211), 1892.320595, 2568.6234).when(eventHandler0).g(anyDouble() , any(double[].class));
      highamHall54Integrator0.addEventHandler(eventHandler0, 1295.088007, 1.0, 1);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1184.767010092211), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertArrayEquals(new double[] {(-1184.767010092211), 1892.320595}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      // Undeclared exception!
      try { 
        highamHall54Integrator0.integrate(firstOrderConverter0, 23.4375, doubleArray0, (-1184.767010092211), doubleArray0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // function values at endpoints do not have different signs.  Endpoints: [14.311, 22.699], Values: [2,568.623, 2,568.623]
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-2069.33808), (-2069.33808), (-2069.33808), 0.0);
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(2069.33808, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(2069.33808, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(2069.33808, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      double[] doubleArray0 = new double[1];
      try { 
        dormandPrince853Integrator0.integrate(firstOrderConverter0, 4340.3, doubleArray0, 0.0, doubleArray0);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 0, initial state vector has dimension 1
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.9, 0.9, 2078.3035263664, 0.9);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.9, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(0, firstOrderConverter0.getDimension());
      
      // Undeclared exception!
      try { 
        dormandPrince54Integrator0.integrate(firstOrderConverter0, 576.2723698796, (double[]) null, 0.0, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-1184.767010092211);
      doubleArray0[1] = 1892.320595;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1184.767010092211), 1892.320595, doubleArray0, doubleArray0);
      assertNotNull(highamHall54Integrator0);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1184.767010092211), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertArrayEquals(new double[] {(-1184.767010092211), 1892.320595}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      double double0 = highamHall54Integrator0.integrate(firstOrderConverter0, 0.9, doubleArray0, 5, doubleArray0);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1184.767010092211), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(22, highamHall54Integrator0.getEvaluations());
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(2, firstOrderConverter0.getDimension());
      assertEquals(5.0, double0, 0.01);
      assertArrayEquals(new double[] {6573.747429407789, 1892.320595}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1184.767010092211), 1892.320595, (-1184.767010092211), (-1184.767010092211));
      assertNotNull(highamHall54Integrator0);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1184.767010092211, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(1497.317272148446, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      highamHall54Integrator0.setInitialStepSize(1370.6181707635);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1184.767010092211, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(1497.317272148446, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(eventHandler0).eventOccurred(anyDouble() , any(double[].class) , anyBoolean());
      doReturn((-452.0), (double)870, 2894.95, 1.5131582113853385E-20, (-452.0)).when(eventHandler0).g(anyDouble() , any(double[].class));
      highamHall54Integrator0.addEventHandler(eventHandler0, 1295.088007, 1.0, 1);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1184.767010092211, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(1497.317272148446, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      
      double double0 = highamHall54Integrator0.integrate(firstOrderConverter0, 23.4375, doubleArray0, (-1184.767010092211), doubleArray0);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(1184.767010092211, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(1892.320595, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(1497.317272148446, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(7, highamHall54Integrator0.getEvaluations());
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(2, firstOrderConverter0.getDimension());
      assertEquals((-1347.1806707635), double0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-1184.767010092211);
      doubleArray0[1] = 1892.320595;
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertNotNull(firstOrderConverter0);
      assertEquals(2, firstOrderConverter0.getDimension());
      
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.9, (-426), doubleArray0, doubleArray0);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals((-426.0), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0.9, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertArrayEquals(new double[] {(-1184.767010092211), 1892.320595}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.9, doubleArray0, (-1184.767010092211), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.9, (-426), doubleArray0, doubleArray0);
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.9, doubleArray0, (-1184.767010092211), doubleArray0);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-1184.767010092211);
      doubleArray0[1] = 1892.320595;
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1184.767010092211), 1892.320595, doubleArray0, doubleArray0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      highamHall54Integrator0.integrate(firstOrderConverter0, 23.4375, doubleArray0, (-1184.767010092211), doubleArray0);
      double double0 = highamHall54Integrator0.integrate(firstOrderConverter0, 0.9, doubleArray0, 5, doubleArray0);
      assertArrayEquals(new double[] {(-2279736.5299899685), 1892.320595}, doubleArray0, 0.01);
      assertEquals(5.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1184.767010092211), 1892.320595, (-1184.767010092211), (-1184.767010092211));
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      EventHandler eventHandler0 = mock(EventHandler.class, new ViolatedAssumptionAnswer());
      doReturn(1).when(eventHandler0).eventOccurred(anyDouble() , any(double[].class) , anyBoolean());
      doReturn((-452.0), (double)870, 2894.95, 1.5131582113853385E-20, (-452.0)).when(eventHandler0).g(anyDouble() , any(double[].class));
      highamHall54Integrator0.addEventHandler(eventHandler0, 1295.088007, 1.0, 1);
      // Undeclared exception!
      try { 
        highamHall54Integrator0.integrate(firstOrderConverter0, 23.4375, doubleArray0, (-1184.767010092211), doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // internal error, please fill a bug report at https://issues.apache.org/jira/browse/MATH
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1184.767010092211), 1892.320595, doubleArray0, doubleArray0);
      double double0 = highamHall54Integrator0.getMaxGrowth();
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(10.0, double0, 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.2, 0.2, 0.0, 0.0);
      double double0 = dormandPrince853Integrator0.getMinReduction();
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.2, double0, 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-1184.767010092211), 1892.320595, (-1184.767010092211), (-1184.767010092211));
      double double0 = highamHall54Integrator0.getSafety();
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, double0, 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-3642.571), 0.0, doubleArray0, doubleArray0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      try { 
        dormandPrince853Integrator0.integrate(firstOrderConverter0, (-937.406), doubleArray0, 0.0, (double[]) null);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 0, initial state vector has dimension 9
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }
}
