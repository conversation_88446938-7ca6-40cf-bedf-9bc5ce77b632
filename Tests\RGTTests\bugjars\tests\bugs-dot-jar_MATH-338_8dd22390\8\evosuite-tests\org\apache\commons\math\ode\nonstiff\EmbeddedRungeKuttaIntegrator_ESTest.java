/*
 * This file was automatically generated by EvoSuite
 * Fri Dec 27 20:38:28 GMT 2019
 */

package org.apache.commons.math.ode.nonstiff;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math.ode.FirstOrderConverter;
import org.apache.commons.math.ode.FirstOrderDifferentialEquations;
import org.apache.commons.math.ode.SecondOrderDifferentialEquations;
import org.apache.commons.math.ode.nonstiff.DormandPrince54Integrator;
import org.apache.commons.math.ode.nonstiff.DormandPrince853Integrator;
import org.apache.commons.math.ode.nonstiff.HighamHall54Integrator;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class EmbeddedRungeKuttaIntegrator_ESTest extends EmbeddedRungeKuttaIntegrator_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(2534.598674588284, (-2568.0), 2534.598674588284, 2534.598674588284);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(2534.598674588284, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(2551.244675906785, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(2568.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertNotNull(highamHall54Integrator0);
      
      double[] doubleArray0 = new double[9];
      double[] doubleArray1 = new double[0];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        highamHall54Integrator0.integrate((FirstOrderDifferentialEquations) null, (-3.0467644718982196), doubleArray0, (-2373.40454845682), doubleArray1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      dormandPrince853Integrator0.setSafety(0.0);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = dormandPrince853Integrator0.getSafety();
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, double0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator((-18.52006565999696), (-18.52006565999696), doubleArray0, doubleArray0);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(18.52006565999696, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals((-18.52006565999696), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals((-18.52006565999696), highamHall54Integrator0.getMaxStep(), 0.01);
      assertNotNull(highamHall54Integrator0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      highamHall54Integrator0.setSafety((-18.52006565999696));
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(18.52006565999696, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals((-18.52006565999696), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals((-18.52006565999696), highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals((-18.52006565999696), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      double double0 = highamHall54Integrator0.getSafety();
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(18.52006565999696, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals((-18.52006565999696), highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals((-18.52006565999696), highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals((-18.52006565999696), highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals((-18.52006565999696), double0, 0.01);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.0, 0.0, 1480.0, 1480.0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertNotNull(highamHall54Integrator0);
      
      int int0 = highamHall54Integrator0.getOrder();
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(5, int0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-1.0), 391.912154, doubleArray0, doubleArray0);
      assertEquals(391.912154, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.setMinReduction((-1008.9167));
      assertEquals(391.912154, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-1008.9167), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = dormandPrince54Integrator0.getMinReduction();
      assertEquals(391.912154, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-1008.9167), dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals((-1.0), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals((-1008.9167), double0, 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(0.0, 0.0, 0.0, 0.0);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertNotNull(dormandPrince853Integrator0);
      
      dormandPrince853Integrator0.setMaxGrowth(0.0);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      
      double double0 = dormandPrince853Integrator0.getMaxGrowth();
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.0, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(0.0, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-3.7333333333333334), (-3.7333333333333334), doubleArray0, doubleArray0);
      assertEquals((-3.7333333333333334), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(3.7333333333333334, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-3.7333333333333334), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertNotNull(dormandPrince54Integrator0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      
      dormandPrince54Integrator0.setMaxGrowth((-3.7333333333333334));
      assertEquals((-3.7333333333333334), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(3.7333333333333334, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals((-3.7333333333333334), dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-3.7333333333333334), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = dormandPrince54Integrator0.getMaxGrowth();
      assertEquals((-3.7333333333333334), dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(3.7333333333333334, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals((-3.7333333333333334), dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals((-3.7333333333333334), dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals((-3.7333333333333334), double0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator((-1775.3636383), (-1775.3636383), (-1775.3636383), (-1775.3636383));
      assertEquals(1775.3636383, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(1775.3636383, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertEquals(1775.3636383, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertNotNull(dormandPrince853Integrator0);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertEquals(0, firstOrderConverter0.getDimension());
      assertNotNull(firstOrderConverter0);
      
      double[] doubleArray0 = new double[6];
      try { 
        dormandPrince853Integrator0.integrate(firstOrderConverter0, (-1775.3636383), doubleArray0, 0.0, doubleArray0);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // dimensions mismatch: ODE problem has dimension 0, initial state vector has dimension 6
         //
         verifyException("org.apache.commons.math.ode.AbstractIntegrator", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(0.0, 0.0, doubleArray0, doubleArray0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertNotNull(highamHall54Integrator0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      
      highamHall54Integrator0.setMinReduction(0.0);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      
      double double0 = highamHall54Integrator0.getMinReduction();
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.0, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator(0.0, 1849.334, 0.0, 0.0);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals(0.0, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(1849.334, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.0, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertNotNull(dormandPrince54Integrator0);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertEquals(0, firstOrderConverter0.getDimension());
      assertNotNull(firstOrderConverter0);
      
      double[] doubleArray0 = new double[0];
      // Undeclared exception!
      dormandPrince54Integrator0.integrate(firstOrderConverter0, 0.0, doubleArray0, 2.2404374302607883, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      DormandPrince853Integrator dormandPrince853Integrator0 = new DormandPrince853Integrator(2471.668, 0.9, doubleArray0, doubleArray0);
      assertEquals(0, dormandPrince853Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince853Integrator0.getMaxEvaluations());
      assertEquals(0.9, dormandPrince853Integrator0.getMaxStep(), 0.01);
      assertEquals(Double.NaN, dormandPrince853Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(2471.668, dormandPrince853Integrator0.getMinStep(), 0.01);
      assertEquals("Dormand-Prince 8 (5, 3)", dormandPrince853Integrator0.getName());
      assertEquals(0.9, dormandPrince853Integrator0.getSafety(), 0.01);
      assertEquals(47.16461809449961, dormandPrince853Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(10.0, dormandPrince853Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.2, dormandPrince853Integrator0.getMinReduction(), 0.01);
      assertEquals(8, dormandPrince853Integrator0.getOrder());
      assertNotNull(dormandPrince853Integrator0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      assertEquals(0, firstOrderConverter0.getDimension());
      assertNotNull(firstOrderConverter0);
      
      // Undeclared exception!
      dormandPrince853Integrator0.integrate(firstOrderConverter0, 3.0, doubleArray0, 0.9, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(1.0, 1.0, doubleArray0, doubleArray0);
      assertEquals(1.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(1.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertNotNull(highamHall54Integrator0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = highamHall54Integrator0.getMaxGrowth();
      assertEquals(1.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(1.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(10.0, double0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(1.0, 1.0, doubleArray0, doubleArray0);
      assertEquals(1.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertNotNull(highamHall54Integrator0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = highamHall54Integrator0.getMinReduction();
      assertEquals(1.0, highamHall54Integrator0.getMinStep(), 0.01);
      assertEquals("Higham-Hall 5(4)", highamHall54Integrator0.getName());
      assertEquals(10.0, highamHall54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(0.9, highamHall54Integrator0.getSafety(), 0.01);
      assertEquals(0.2, highamHall54Integrator0.getMinReduction(), 0.01);
      assertEquals(1.0, highamHall54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(1.0, highamHall54Integrator0.getMaxStep(), 0.01);
      assertEquals(5, highamHall54Integrator0.getOrder());
      assertEquals(0, highamHall54Integrator0.getEvaluations());
      assertEquals(Double.NaN, highamHall54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(Integer.MAX_VALUE, highamHall54Integrator0.getMaxEvaluations());
      assertEquals(0.2, double0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      DormandPrince54Integrator dormandPrince54Integrator0 = new DormandPrince54Integrator((-1754.41620614), (-1754.41620614), 117.99689926870735, 117.99689926870735);
      assertEquals(1754.41620614, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(1754.41620614, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1754.41620614, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertNotNull(dormandPrince54Integrator0);
      
      double double0 = dormandPrince54Integrator0.getSafety();
      assertEquals(1754.41620614, dormandPrince54Integrator0.getMaxStep(), 0.01);
      assertEquals(1754.41620614, dormandPrince54Integrator0.getCurrentSignedStepsize(), 0.01);
      assertEquals(0, dormandPrince54Integrator0.getEvaluations());
      assertEquals(Integer.MAX_VALUE, dormandPrince54Integrator0.getMaxEvaluations());
      assertEquals(Double.NaN, dormandPrince54Integrator0.getCurrentStepStart(), 0.01);
      assertEquals(5, dormandPrince54Integrator0.getOrder());
      assertEquals("Dormand-Prince 5(4)", dormandPrince54Integrator0.getName());
      assertEquals(0.2, dormandPrince54Integrator0.getMinReduction(), 0.01);
      assertEquals(0.9, dormandPrince54Integrator0.getSafety(), 0.01);
      assertEquals(10.0, dormandPrince54Integrator0.getMaxGrowth(), 0.01);
      assertEquals(1754.41620614, dormandPrince54Integrator0.getMinStep(), 0.01);
      assertEquals(0.9, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      HighamHall54Integrator highamHall54Integrator0 = new HighamHall54Integrator(1180.3879437, 0.0, doubleArray0, doubleArray0);
      SecondOrderDifferentialEquations secondOrderDifferentialEquations0 = mock(SecondOrderDifferentialEquations.class, new ViolatedAssumptionAnswer());
      doReturn(0).when(secondOrderDifferentialEquations0).getDimension();
      FirstOrderConverter firstOrderConverter0 = new FirstOrderConverter(secondOrderDifferentialEquations0);
      // Undeclared exception!
      highamHall54Integrator0.integrate(firstOrderConverter0, (-500.91), doubleArray0, 0.0, doubleArray0);
  }
}
