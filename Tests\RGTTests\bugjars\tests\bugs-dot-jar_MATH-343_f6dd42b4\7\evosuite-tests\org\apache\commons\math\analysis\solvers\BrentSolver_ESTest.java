/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 09:53:24 GMT 2019
 */

package org.apache.commons.math.analysis.solvers;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math.analysis.UnivariateRealFunction;
import org.apache.commons.math.analysis.polynomials.PolynomialFunction;
import org.apache.commons.math.analysis.polynomials.PolynomialFunctionLagrangeForm;
import org.apache.commons.math.analysis.polynomials.PolynomialSplineFunction;
import org.apache.commons.math.analysis.solvers.BrentSolver;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class BrentSolver_ESTest extends BrentSolver_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[2] = Double.POSITIVE_INFINITY;
      doubleArray0[4] = (-1576.6089536540462);
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(4, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0, 0.0, Double.POSITIVE_INFINITY, 0.0, (-1576.6089536540462), 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      assertEquals(9, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(4, polynomialFunction0.degree());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, Double.POSITIVE_INFINITY, 0.0, (-1576.6089536540462), 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, 529.1935602821771, Double.POSITIVE_INFINITY, 788.0194);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      assertEquals(9, doubleArray0.length);
      assertEquals(4, polynomialFunction0.degree());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, Double.POSITIVE_INFINITY, 0.0, (-1576.6089536540462), 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-902.737690251);
      doubleArray0[1] = 1.0E-15;
      doubleArray0[2] = 503.2066132414;
      doubleArray0[4] = 612.97316336578;
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertNotNull(polynomialFunctionLagrangeForm0);
      assertEquals(4, polynomialFunctionLagrangeForm0.degree());
      assertArrayEquals(new double[] {(-902.737690251), 1.0E-15, 503.2066132414, 0.0, 612.97316336578}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunctionLagrangeForm0);
      assertEquals(5, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(4, polynomialFunctionLagrangeForm0.degree());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertArrayEquals(new double[] {(-902.737690251), 1.0E-15, 503.2066132414, 0.0, 612.97316336578}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunctionLagrangeForm0, (-783.6079735180898), 1.0E-15);
      assertEquals(1.0E-15, double0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(4, polynomialFunctionLagrangeForm0.degree());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertArrayEquals(new double[] {(-902.737690251), 1.0E-15, 503.2066132414, 0.0, 612.97316336578}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      assertNotNull(brentSolver0);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      
      double[] doubleArray0 = new double[24];
      doubleArray0[2] = 2.6584176161553588E16;
      brentSolver0.setMaximalIterationCount((-3022));
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals((-3022), brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      
      doubleArray0[6] = (-0.4186597168659655);
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(24, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(6, polynomialFunction0.degree());
      
      try { 
        brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, 347.32, 2.6584176161553588E16, 980.0549488);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // Maximal number of iterations (-3,022) exceeded
         //
         verifyException("org.apache.commons.math.analysis.solvers.BrentSolver", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      assertNotNull(brentSolver0);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-893.908193);
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertNotNull(polynomialFunctionLagrangeForm0);
      assertEquals(1, polynomialFunctionLagrangeForm0.degree());
      assertArrayEquals(new double[] {(-893.908193), 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunctionLagrangeForm0, (-893.908193), 0.5);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1, brentSolver0.getIterationCount());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1, polynomialFunctionLagrangeForm0.degree());
      assertArrayEquals(new double[] {(-893.908193), 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      assertNotNull(brentSolver0);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 1.0E-15;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(0, polynomialFunction0.degree());
      assertArrayEquals(new double[] {1.0E-15, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, 0.0, 1.0E-15);
      assertEquals(0.0, double0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, polynomialFunction0.degree());
      assertArrayEquals(new double[] {1.0E-15, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (-320.6909);
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(0, polynomialFunction0.degree());
      assertArrayEquals(new double[] {(-320.6909), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      assertEquals(9, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(0, polynomialFunction0.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertArrayEquals(new double[] {(-320.6909), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      brentSolver0.functionValueAccuracy = (-1679.11756079924);
      assertEquals(0, polynomialFunction0.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals((-1679.11756079924), brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      
      // Undeclared exception!
      try { 
        brentSolver0.solve((-2154.44587794581), 3911.735645);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // function values at endpoints do not have different signs.  Endpoints: [-2,154.446, 3,911.736], Values: [-320.691, -320.691]
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[2] = 66.8461297243576;
      doubleArray0[0] = 1.0E-15;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(8, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(2, polynomialFunction0.degree());
      assertArrayEquals(new double[] {1.0E-15, 0.0, 66.8461297243576, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      assertEquals(8, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(2, polynomialFunction0.degree());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertArrayEquals(new double[] {1.0E-15, 0.0, 66.8461297243576, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((-1571.44115011), 0.0, (-1376.891888));
      assertEquals(1.0E-15, double0, 0.01);
      assertEquals(8, doubleArray0.length);
      assertEquals(2, polynomialFunction0.degree());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertArrayEquals(new double[] {1.0E-15, 0.0, 66.8461297243576, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(0, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      assertEquals(9, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(0, polynomialFunction0.degree());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      brentSolver0.setFunctionValueAccuracy((-474.5));
      assertEquals(9, doubleArray0.length);
      assertEquals(0, polynomialFunction0.degree());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals((-474.5), brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve(0.0, 8.58714, 0.24219860515371172);
      assertEquals(5.8054930315809266, double0, 0.01);
      assertEquals(9, doubleArray0.length);
      assertEquals(0, polynomialFunction0.degree());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals((-474.5), brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(23, brentSolver0.getIterationCount());
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = 0.0014227989722712664;
      doubleArray0[2] = (-3511.0);
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(2, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0014227989722712664, 0.0, (-3511.0), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      assertEquals(9, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(2, polynomialFunction0.degree());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0014227989722712664, 0.0, (-3511.0), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      brentSolver0.setFunctionValueAccuracy(49.02169325188774);
      assertEquals(9, doubleArray0.length);
      assertEquals(2, polynomialFunction0.degree());
      assertEquals(49.02169325188774, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0014227989722712664, 0.0, (-3511.0), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve(0.0, 8.58714, 0.24219860515371172);
      assertEquals(0.0014227989722712664, double0, 0.01);
      assertEquals(9, doubleArray0.length);
      assertEquals(2, polynomialFunction0.degree());
      assertEquals(49.02169325188774, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0014227989722712664, 0.0, (-3511.0), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-902.737690251);
      doubleArray0[1] = 1.0E-15;
      doubleArray0[2] = 503.2066132414;
      doubleArray0[4] = 612.97316336578;
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertNotNull(polynomialFunctionLagrangeForm0);
      assertEquals(4, polynomialFunctionLagrangeForm0.degree());
      assertArrayEquals(new double[] {(-902.737690251), 1.0E-15, 503.2066132414, 0.0, 612.97316336578}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunctionLagrangeForm0);
      assertEquals(5, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(4, polynomialFunctionLagrangeForm0.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertArrayEquals(new double[] {(-902.737690251), 1.0E-15, 503.2066132414, 0.0, 612.97316336578}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve(1.0E-15, 801.1777631067674, 612.97316336578);
      assertEquals(1.0E-15, double0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(4, polynomialFunctionLagrangeForm0.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertArrayEquals(new double[] {(-902.737690251), 1.0E-15, 503.2066132414, 0.0, 612.97316336578}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      assertNotNull(brentSolver0);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      
      double[] doubleArray0 = new double[4];
      doubleArray0[1] = (-2603.504144585);
      brentSolver0.functionValueAccuracy = 1.0E-14;
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getFunctionValueAccuracy(), 0.01);
      
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(1, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0, (-2603.504144585), 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, (-1279.859), 2.0, 1.0E-6);
      assertEquals((-2.1175823681357508E-22), double0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0, (-2603.504144585), 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      assertNotNull(brentSolver0);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      
      double[] doubleArray0 = new double[4];
      brentSolver0.functionValueAccuracy = 1.0E-14;
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getFunctionValueAccuracy(), 0.01);
      
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(0, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, (-1279.859), 2.0, 1.0E-6);
      assertEquals(1.0E-6, double0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(0, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[2] = 66.8461297243576;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(8, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(2, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0, 0.0, 66.8461297243576, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      assertEquals(8, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(2, polynomialFunction0.degree());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 66.8461297243576, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((-1571.44115011), 0.0, (-1376.891888));
      assertEquals(0.0, double0, 0.01);
      assertEquals(8, doubleArray0.length);
      assertEquals(2, polynomialFunction0.degree());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 66.8461297243576, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-902.737690251);
      doubleArray0[1] = 1.0E-15;
      doubleArray0[2] = 503.2066132414;
      doubleArray0[4] = 612.97316336578;
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertNotNull(polynomialFunctionLagrangeForm0);
      assertEquals(4, polynomialFunctionLagrangeForm0.degree());
      assertArrayEquals(new double[] {(-902.737690251), 1.0E-15, 503.2066132414, 0.0, 612.97316336578}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunctionLagrangeForm0);
      assertEquals(5, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(4, polynomialFunctionLagrangeForm0.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertArrayEquals(new double[] {(-902.737690251), 1.0E-15, 503.2066132414, 0.0, 612.97316336578}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((-713.48737469149), 801.1777631067674, 612.97316336578);
      assertEquals((-1.1368683772161603E-13), double0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(4, polynomialFunctionLagrangeForm0.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(2, brentSolver0.getIterationCount());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertArrayEquals(new double[] {(-902.737690251), 1.0E-15, 503.2066132414, 0.0, 612.97316336578}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-1.0);
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertNotNull(polynomialFunctionLagrangeForm0);
      assertEquals(1, polynomialFunctionLagrangeForm0.degree());
      assertArrayEquals(new double[] {(-1.0), 0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunctionLagrangeForm0);
      assertEquals(2, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(1, polynomialFunctionLagrangeForm0.degree());
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertArrayEquals(new double[] {(-1.0), 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve(0.0, 98.45664026196563);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertEquals(1, polynomialFunctionLagrangeForm0.degree());
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertArrayEquals(new double[] {(-1.0), 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[2] = 1.0E-14;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(2, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0, 0.0, 1.0E-14, 0.0}, doubleArray0, 0.01);
      
      PolynomialFunction polynomialFunction1 = (PolynomialFunction)polynomialFunction0.derivative();
      assertEquals(4, doubleArray0.length);
      assertNotNull(polynomialFunction1);
      assertNotSame(polynomialFunction0, polynomialFunction1);
      assertNotSame(polynomialFunction1, polynomialFunction0);
      assertFalse(polynomialFunction1.equals((Object)polynomialFunction0));
      assertEquals(2, polynomialFunction0.degree());
      assertEquals(1, polynomialFunction1.degree());
      assertArrayEquals(new double[] {0.0, 0.0, 1.0E-14, 0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction1);
      assertEquals(4, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertFalse(polynomialFunction0.equals((Object)polynomialFunction1));
      assertFalse(polynomialFunction1.equals((Object)polynomialFunction0));
      assertEquals(2, polynomialFunction0.degree());
      assertEquals(1, polynomialFunction1.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(0, brentSolver0.getIterationCount());
      assertArrayEquals(new double[] {0.0, 0.0, 1.0E-14, 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((-1002.212975305), 1445.13646);
      assertEquals(1.1368683772161603E-13, double0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertNotSame(polynomialFunction0, polynomialFunction1);
      assertNotSame(polynomialFunction1, polynomialFunction0);
      assertFalse(polynomialFunction0.equals((Object)polynomialFunction1));
      assertFalse(polynomialFunction1.equals((Object)polynomialFunction0));
      assertEquals(2, polynomialFunction0.degree());
      assertEquals(1, polynomialFunction1.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertArrayEquals(new double[] {0.0, 0.0, 1.0E-14, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(0, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      assertEquals(4, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(0, polynomialFunction0.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = brentSolver0.solve((-982.683341), 0.0);
      assertEquals((-982.683341), double0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(0, polynomialFunction0.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      assertNotNull(brentSolver0);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(0, brentSolver0.getIterationCount());
      
      double[] doubleArray0 = new double[4];
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertNotNull(polynomialFunctionLagrangeForm0);
      assertEquals(3, polynomialFunctionLagrangeForm0.degree());
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      try { 
        brentSolver0.solve((UnivariateRealFunction) polynomialFunctionLagrangeForm0, (-359.1468077528), 438.03256545407, 2.0);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // Abscissa 0 is duplicated at both indices 1 and 1
         //
         verifyException("org.apache.commons.math.analysis.polynomials.PolynomialFunctionLagrangeForm", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      assertNotNull(brentSolver0);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-2601.3178);
      doubleArray0[2] = 1743.30955401;
      doubleArray0[3] = 15875.007910212267;
      PolynomialFunction[] polynomialFunctionArray0 = new PolynomialFunction[3];
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(3, polynomialFunction0.degree());
      assertArrayEquals(new double[] {(-2601.3178), 0.0, 1743.30955401, 15875.007910212267}, doubleArray0, 0.01);
      
      polynomialFunctionArray0[0] = polynomialFunction0;
      PolynomialSplineFunction polynomialSplineFunction0 = new PolynomialSplineFunction(doubleArray0, polynomialFunctionArray0);
      assertEquals(4, doubleArray0.length);
      assertEquals(3, polynomialFunctionArray0.length);
      assertNotNull(polynomialSplineFunction0);
      assertEquals(3, polynomialSplineFunction0.getN());
      assertArrayEquals(new double[] {(-2601.3178), 0.0, 1743.30955401, 15875.007910212267}, doubleArray0, 0.01);
      
      try { 
        brentSolver0.solve((UnivariateRealFunction) polynomialSplineFunction0, (-2971.0), 1743.30955401, (-2601.3178));
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // Argument -2,971 outside domain [-2,601.318 ; 15,875.008]
         //
         verifyException("org.apache.commons.math.analysis.polynomials.PolynomialSplineFunction", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      assertNotNull(brentSolver0);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      
      // Undeclared exception!
      try { 
        brentSolver0.solve((UnivariateRealFunction) null, 346.81784174775004, 2.6584176161553604E16, 980.0549488);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.analysis.solvers.BrentSolver", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertNotNull(polynomialFunctionLagrangeForm0);
      assertEquals(8, polynomialFunctionLagrangeForm0.degree());
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunctionLagrangeForm0);
      assertEquals(9, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(8, polynomialFunctionLagrangeForm0.degree());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      try { 
        brentSolver0.solve((UnivariateRealFunction) polynomialFunctionLagrangeForm0, (-2541.48), (-364.2649359));
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // Abscissa 0 is duplicated at both indices 1 and 1
         //
         verifyException("org.apache.commons.math.analysis.polynomials.PolynomialFunctionLagrangeForm", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (-581.421);
      doubleArray0[1] = 1334.54;
      doubleArray0[2] = 1005.0;
      doubleArray0[3] = (-953.003458);
      doubleArray0[4] = 1.0E-14;
      doubleArray0[6] = 1340.6488;
      doubleArray0[7] = 1326.46053805727;
      doubleArray0[8] = (-556.18541);
      double[] doubleArray1 = new double[9];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[1] = (-953.003458);
      doubleArray1[8] = (-581.421);
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray1);
      assertEquals(9, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      assertNotNull(polynomialFunctionLagrangeForm0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(8, polynomialFunctionLagrangeForm0.degree());
      assertArrayEquals(new double[] {(-581.421), 1334.54, 1005.0, (-953.003458), 1.0E-14, 0.0, 1340.6488, 1326.46053805727, (-556.18541)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-953.003458), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, (-581.421)}, doubleArray1, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunctionLagrangeForm0);
      assertEquals(9, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      assertNotNull(brentSolver0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(8, polynomialFunctionLagrangeForm0.degree());
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertArrayEquals(new double[] {(-581.421), 1334.54, 1005.0, (-953.003458), 1.0E-14, 0.0, 1340.6488, 1326.46053805727, (-556.18541)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-953.003458), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, (-581.421)}, doubleArray1, 0.01);
      
      try { 
        brentSolver0.solve((UnivariateRealFunction) polynomialFunctionLagrangeForm0, (-556.18541), 1.472772941370999E25);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // Maximal number of iterations (100) exceeded
         //
         verifyException("org.apache.commons.math.analysis.solvers.BrentSolver", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertNotNull(polynomialFunction0);
      assertEquals(0, polynomialFunction0.degree());
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      assertEquals(1, doubleArray0.length);
      assertNotNull(brentSolver0);
      assertEquals(0, polynomialFunction0.degree());
      assertEquals(1.0E-6, brentSolver0.getAbsoluteAccuracy(), 0.01);
      assertEquals(1.0E-14, brentSolver0.getRelativeAccuracy(), 0.01);
      assertEquals(100, brentSolver0.getMaximalIterationCount());
      assertEquals(1.0E-15, brentSolver0.getFunctionValueAccuracy(), 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        brentSolver0.solve((UnivariateRealFunction) null, (-271.6), Double.NaN);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.analysis.solvers.BrentSolver", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[2] = Double.POSITIVE_INFINITY;
      doubleArray0[7] = Double.POSITIVE_INFINITY;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      brentSolver0.setMaximalIterationCount(990986631);
      // Undeclared exception!
      brentSolver0.solve((-3714.16281125), 0.5, (-727.5));
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray0);
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunctionLagrangeForm0);
      try { 
        brentSolver0.solve((-713.48737469149), 801.1777631067674, 612.97316336578);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // Abscissa 0 is duplicated at both indices 1 and 1
         //
         verifyException("org.apache.commons.math.analysis.polynomials.PolynomialFunctionLagrangeForm", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray0);
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunctionLagrangeForm0);
      try { 
        brentSolver0.solve((-1.0), 2230.82694746);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // Abscissa 0 is duplicated at both indices 1 and 1
         //
         verifyException("org.apache.commons.math.analysis.polynomials.PolynomialFunctionLagrangeForm", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-902.737690251);
      doubleArray0[1] = 1.0E-15;
      doubleArray0[2] = 503.2066132414;
      doubleArray0[4] = 612.97316336578;
      PolynomialFunctionLagrangeForm polynomialFunctionLagrangeForm0 = new PolynomialFunctionLagrangeForm(doubleArray0, doubleArray0);
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunctionLagrangeForm0);
      brentSolver0.setMaximalIterationCount(1);
      try { 
        brentSolver0.solve((-902.737690251), 3725.602289178);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // Maximal number of iterations (1) exceeded
         //
         verifyException("org.apache.commons.math.analysis.solvers.BrentSolver", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      // Undeclared exception!
      try { 
        brentSolver0.solve((-1380.463357262), 1.0E-14);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.analysis.solvers.BrentSolver", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[2] = 1.0E-14;
      doubleArray0[3] = 1.0E-14;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, (-1618.2715918100982), (-1.0));
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 251.7790364970402;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      // Undeclared exception!
      try { 
        brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, 1.5, 2068.42683701672, 1718.5448626);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // function values at endpoints do not have different signs.  Endpoints: [1.5, 2,068.427], Values: [251.779, 251.779]
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      double[] doubleArray0 = new double[7];
      doubleArray0[2] = 2.6584176161553604E16;
      doubleArray0[6] = (-0.4186597168659655);
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      brentSolver0.setRelativeAccuracy(Double.NaN);
      try { 
        brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, 347.32, 2.6584176161553604E16, 953.4454587611579);
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // Maximal number of iterations (100) exceeded
         //
         verifyException("org.apache.commons.math.analysis.solvers.BrentSolver", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      double[] doubleArray0 = new double[7];
      doubleArray0[2] = 2.6584176161553604E16;
      doubleArray0[3] = 1849.2841742;
      doubleArray0[6] = (-0.4186597168659655);
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, 347.32, 2.6584176161553604E16, 953.4454587611579);
      assertEquals(91, brentSolver0.getIterationCount());
      assertEquals(15874.151024545763, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[1] = Double.NaN;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      double double0 = brentSolver0.solve(946.0559154695, Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      double[] doubleArray0 = new double[4];
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, 0.0, 1.0E-6);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      double[] doubleArray0 = new double[3];
      doubleArray0[2] = 1.0E-6;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, (-2052.124536), 1.0E-6);
      assertEquals(0, brentSolver0.getIterationCount());
      assertEquals(1.0E-6, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      double[] doubleArray0 = new double[4];
      doubleArray0[2] = 1.0E-6;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, 1.0E-6, 3.1821047389393694E14);
      assertEquals(1.0E-6, double0, 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      double[] doubleArray0 = new double[7];
      doubleArray0[2] = 2.6584176161553604E16;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      // Undeclared exception!
      try { 
        brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, (-3047.6242263883546), 1849.2841742);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // function values at endpoints do not have different signs.  Endpoints: [-3,047.624, 1,849.284], Values: [246,914,185,088,231,740,000,000, 90,913,946,869,901,610,000,000]
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[2] = (-3511.0);
      doubleArray0[4] = 1083.2195;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, (-1031.4354), 0.0014227989722712664);
      assertEquals(37, brentSolver0.getIterationCount());
      assertEquals((-1.800350982244199), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[2] = Double.POSITIVE_INFINITY;
      doubleArray0[7] = Double.POSITIVE_INFINITY;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      try { 
        brentSolver0.solve((-3714.16281125), 0.5, (-727.5));
        fail("Expecting exception: Exception");
      
      } catch(Exception e) {
         //
         // Maximal number of iterations (100) exceeded
         //
         verifyException("org.apache.commons.math.analysis.solvers.BrentSolver", e);
      }
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[2] = Double.POSITIVE_INFINITY;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      BrentSolver brentSolver0 = new BrentSolver(polynomialFunction0);
      // Undeclared exception!
      try { 
        brentSolver0.solve((-3714.16281125), 0.5, (-727.5));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // function values at endpoints do not have different signs.  Endpoints: [-3,714.163, 0.5], Values: [\u221E, \u221E]
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      double[] doubleArray0 = new double[4];
      doubleArray0[2] = 1.0E-6;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, 0.0, 6.059507930301896E20, 1.1276432207928142E8);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      double[] doubleArray0 = new double[3];
      doubleArray0[2] = 1.0E-6;
      PolynomialFunction polynomialFunction0 = new PolynomialFunction(doubleArray0);
      double double0 = brentSolver0.solve((UnivariateRealFunction) polynomialFunction0, (-2052.124536), 0.0, (-363.49996125000007));
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, brentSolver0.getIterationCount());
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      BrentSolver brentSolver0 = new BrentSolver();
      // Undeclared exception!
      try { 
        brentSolver0.solve(1.0E-6, 816.463684901322, 0.5);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.analysis.solvers.BrentSolver", e);
      }
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      BrentSolver brentSolver0 = null;
      try {
        brentSolver0 = new BrentSolver((UnivariateRealFunction) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // function to solve cannot be null
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }
}
