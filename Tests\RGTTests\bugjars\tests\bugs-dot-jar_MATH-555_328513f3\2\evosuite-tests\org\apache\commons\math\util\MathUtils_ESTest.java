/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 11:34:57 GMT 2019
 */

package org.apache.commons.math.util;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.math.BigInteger;
import org.apache.commons.math.exception.util.Localizable;
import org.apache.commons.math.exception.util.LocalizedFormats;
import org.apache.commons.math.util.MathUtils;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MathUtils_ESTest extends MathUtils_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[2] = 3.834E-20;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 3.834E-20}, doubleArray0, 0.01);
      assertEquals(3.834E-20, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[1] = (-477.5155342);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly decreasing (-477.516 <= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[2] = 2845.346989084339;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 2845.346989084339, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 2631.0031;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (2,631.003 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      long long0 = MathUtils.pow(3250L, (long) 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      long long0 = MathUtils.pow(0L, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      int int0 = MathUtils.pow(0, 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[1] = (-477.5155342);
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 2708.97311);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, (-477.5155342), 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {-0.0, 2708.97311, -0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      long long0 = MathUtils.lcm(1L, 9154082963658192752L);
      assertEquals(9154082963658192752L, long0);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      long long0 = MathUtils.lcm((long) (byte) (-1), 9154082963658192752L);
      assertEquals(9154082963658192752L, long0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      int int0 = MathUtils.lcm((-1745), (-1745));
      assertEquals(1745, int0);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      short short0 = MathUtils.indicator((short)0);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      long long0 = MathUtils.indicator(0L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      int int0 = MathUtils.indicator(0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      double double0 = MathUtils.indicator((double) 0L);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)0);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double[] doubleArray1 = new double[7];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN((double) 0.0F, 0.0, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.9999997615814209, 4157.28365, 4157.28365);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, 1015.55, 1015.55);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN((float) 0, (float) 0, 4194304);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(3.4028235E38F, 3.4028235E38F, (-149));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(3578.526F, 0.0F, 3578.526F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(63, (-2328));
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      long long0 = MathUtils.addAndCheck((long) 0, (-1304L));
      assertEquals((-1304L), long0);
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[1] = (double) 6390784L;
      MathUtils.checkOrder(doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 6390784.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(4503599627370496L, 4503599627370496L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(221L, 1997L);
      assertEquals((-1776L), long0);
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(11, (-1537));
      assertEquals(1548, int0);
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(0, 195);
      assertEquals((-195), int0);
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      double double0 = MathUtils.sinh(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      double double0 = MathUtils.sinh(2460.94948629);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      float float0 = MathUtils.round((float) (byte) (-1), (int) (byte) (-1));
      assertEquals(-0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      double double0 = MathUtils.round(3584.0, 0, 0);
      assertEquals(3584.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      double double0 = MathUtils.round((-1317.678338203), 0, 0);
      assertEquals((-1318.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      double double0 = MathUtils.round((double) 0L, (int) (byte) (-1));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      double double0 = MathUtils.round((-1982.847), 303);
      assertEquals((-1982.847), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte)89;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(1, byteArray0.length);
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte)89}, byteArray0);
      assertEquals((short)89, bigInteger0.shortValue());
      assertEquals((byte)89, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertEquals(1, byteArray0.length);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertArrayEquals(new byte[] {(byte)89}, byteArray0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)89, bigInteger0.shortValue());
      assertEquals((byte)89, bigInteger0.byteValue());
      assertEquals((short) (-24167), bigInteger1.shortValue());
      assertEquals((byte) (-103), bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (long) 1303);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte)125;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(1, byteArray0.length);
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte)125}, byteArray0);
      assertEquals((byte)125, bigInteger0.byteValue());
      assertEquals((short)125, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 2472);
      assertEquals(1, byteArray0.length);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertArrayEquals(new byte[] {(byte)125}, byteArray0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)125, bigInteger0.byteValue());
      assertEquals((short)125, bigInteger0.shortValue());
      assertEquals((short) (-26079), bigInteger1.shortValue());
      assertEquals((byte)33, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byteArray0[2] = (byte)13;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(3, byteArray0.length);
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)13}, byteArray0);
      assertEquals((short)13, bigInteger0.shortValue());
      assertEquals((byte)13, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 177241);
      assertEquals(3, byteArray0.length);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)13}, byteArray0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)13, bigInteger0.shortValue());
      assertEquals((byte)13, bigInteger0.byteValue());
      assertEquals((byte) (-19), bigInteger1.byteValue());
      assertEquals((short)15853, bigInteger1.shortValue());
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      long long0 = MathUtils.pow(0L, 7910884519577875640L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      long long0 = MathUtils.pow((-1186L), 1L);
      assertEquals((-1186L), long0);
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      long long0 = MathUtils.pow(1966L, 2147031546);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      long long0 = MathUtils.pow((-2987L), 5244);
      assertEquals((-5701371375993058735L), long0);
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      int int0 = MathUtils.pow(20, (long) 1025);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      int int0 = MathUtils.pow(0, (long) 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      int int0 = MathUtils.pow(205, 6);
      assertEquals((-951076551), int0);
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle(0.0, 0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle(0.030589580535888672, 3168.47432154084);
      assertEquals(3166.7559843990475, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck((int) (short)1, 756);
      assertEquals(756, int0);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck((-1020), 1303);
      assertEquals((-1329060), int0);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      double double0 = MathUtils.log(891.489909682508, 918.68883723255);
      assertEquals(1.0044242346760868, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      double double0 = MathUtils.log(13.92954799809, 1.1102230246251565E-16);
      assertEquals((-13.947087499672113), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[1] = (double) 947;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 947.0, 0.0}, doubleArray0, 0.01);
      assertEquals((-786047905), int0);
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      int int0 = MathUtils.hash((double) 10L);
      assertEquals(1076101120, int0);
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      int int0 = MathUtils.hash((-768.3540922));
      assertEquals((-289237773), int0);
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      long long0 = MathUtils.gcd((long) 0, (long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      int int0 = MathUtils.gcd(0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-132.5F), (-132.5F));
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, 0.0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (-3011.793872568313);
      double[] doubleArray1 = new double[3];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray1);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {(-3011.793872568313), 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(3011.793872568313, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      int[] intArray0 = new int[4];
      intArray0[0] = 2472;
      int[] intArray1 = new int[7];
      assertFalse(intArray1.equals((Object)intArray0));
      
      int int0 = MathUtils.distance1(intArray0, intArray1);
      assertEquals(4, intArray0.length);
      assertEquals(7, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertArrayEquals(new int[] {2472, 0, 0, 0}, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0}, intArray1);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(2472, int0);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 13.92954799809;
      double[] doubleArray1 = new double[8];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double0 = MathUtils.distance1(doubleArray0, doubleArray1);
      assertEquals(7, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {13.92954799809, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(13.92954799809, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      int[] intArray0 = new int[4];
      int[] intArray1 = MathUtils.copyOf(intArray0, 1826);
      assertEquals(4, intArray0.length);
      assertEquals(1826, intArray1.length);
      assertNotNull(intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0}, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertEquals(2, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(0L, 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      int int0 = MathUtils.addAndCheck((int) (short)0, (int) (short)0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      int int0 = MathUtils.addAndCheck(0, 3541);
      assertEquals(3541, int0);
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      double[] doubleArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.safeNorm(doubleArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.round((-2066.506472), 482, 482);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, (BigInteger) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 4661292996414230915L);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byteArray0[1] = (byte)111;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(3, byteArray0.length);
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte)0, (byte)111, (byte)0}, byteArray0);
      assertEquals((short)28416, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, 1076101120);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // BigInteger would overflow supported range
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray((double[]) null, 517.088);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      // Undeclared exception!
      MathUtils.factorialDouble(2147481862);
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN((-2499.1694F), 2109.384F, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      int[] intArray0 = new int[8];
      int[] intArray1 = new int[5];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 5
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((double[]) null, doubleArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      double[] doubleArray1 = new double[0];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance1((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      int[] intArray0 = new int[9];
      int[] intArray1 = new int[1];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance1(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance1((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      double[] doubleArray1 = new double[3];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance1(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 3
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      int[] intArray0 = new int[5];
      int[] intArray1 = new int[2];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      // Undeclared exception!
      try { 
        MathUtils.distance(doubleArray0, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      double[] doubleArray0 = new double[21];
      double[] doubleArray1 = new double[4];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null, (int) (short)67);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      int[] intArray0 = new int[9];
      // Undeclared exception!
      try { 
        MathUtils.copyOf(intArray0, (-3282));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      // Undeclared exception!
      try { 
        MathUtils.copyOf(doubleArray0, (-951076551));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkFinite((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((short) (-1), (short) (-1));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-715827883), 1103);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1,103, n = -715,827,883
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-1227), (-1227));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -1,227
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble(506, 980426426);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 980,426,426, n = 506
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[5][5];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 5 != 6
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray0 = new double[7][1];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, mathUtils_OrderDirection0, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-641L), 419L);
      assertEquals((-268579L), long0);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-641L), (-641L));
      assertEquals(410881L, long0);
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(3628800L, 3628800L);
      assertEquals(13168189440000L, long0);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(1071L, 1L);
      assertEquals(1071L, long0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(4194304, (-697));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(7, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      float float0 = MathUtils.indicator((float) 8926498302486690232L);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      float float0 = MathUtils.indicator((-2396.8875F));
      assertEquals((-1.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      long long0 = MathUtils.gcd((-2644281811660520814L), 103L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      int int0 = MathUtils.gcd(124, 193);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      int int0 = MathUtils.gcd(4194304, 4194304);
      assertEquals(4194304, int0);
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      double double0 = MathUtils.factorialLog(1810);
      assertEquals(11771.62817054621, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      // Undeclared exception!
      MathUtils.factorialLog(2124011521);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      long long0 = MathUtils.factorial(2);
      assertEquals(2L, long0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(2196.850302, 2196.850302, 817);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(2086.2044, (-0.12502530217170715), 1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-105.02774), 3294198.0, 59);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(10.0, 10.0, 4194304);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(2631.0031, (double) 60, 2631.0031);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-252.07813707), (-252.07813707), 0.0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(40320.0, (double) 3250L, 0.0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, 9.0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F, 2868);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(2181.545F, (-1.0F), 1897);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-2379.6F), 1.0F, 101);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(1604.6F, 1604.6F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(1250, 13);
      assertEquals(2.743841940410532E30, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(1296, 6);
      assertEquals(6505247592703944L, long0);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly decreasing (0 <= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      int[] intArray0 = new int[1];
      int[] intArray1 = MathUtils.copyOf(intArray0, 0);
      assertEquals(1, intArray0.length);
      assertEquals(0, intArray1.length);
      assertNotNull(intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertArrayEquals(new int[] {0}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(1705.2067680893508, 0.0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      MathUtils.checkNotNull((Object) mathUtils_OrderDirection0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      double[][] doubleArray0 = new double[7][1];
      LocalizedFormats localizedFormats0 = LocalizedFormats.UNSUPPORTED_OPERATION;
      assertEquals(LocalizedFormats.UNSUPPORTED_OPERATION, localizedFormats0);
      assertEquals("unsupported operation", localizedFormats0.getSourceString());
      
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null, (Localizable) localizedFormats0, (Object[]) doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // unsupported operation
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      LocalizedFormats localizedFormats0 = LocalizedFormats.DENOMINATOR_FORMAT;
      assertEquals(LocalizedFormats.DENOMINATOR_FORMAT, localizedFormats0);
      assertEquals("denominator format", localizedFormats0.getSourceString());
      
      Object[] objectArray0 = new Object[1];
      MathUtils.checkNotNull((Object) ")lh", (Localizable) localizedFormats0, objectArray0);
      assertEquals(1, objectArray0.length);
      assertEquals("denominator format", localizedFormats0.getSourceString());
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[2][0];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(2, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[7][0];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray1[3];
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(8, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      double[][] doubleArray0 = new double[2][0];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[7] = Double.NaN;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, Double.NaN, 0.0}, doubleArray0, 0.01);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[1] = 2.2250738585072014E-308;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 2.2250738585072014E-308, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(2.2250738585072014E-308, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[5] = 1.304E19;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 1.304E19, 0.0}, doubleArray0, 0.01);
      assertEquals(1.304E19, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      MathUtils.checkFinite(doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      MathUtils.checkFinite(0.0);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(Double.POSITIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // \u221E is not a finite number
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[1] = (-536.6753);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not decreasing (-536.675 < 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = 2.2250738585072014E-308;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly decreasing (0 <= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (-2994.0774516);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      int[] intArray0 = new int[4];
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(4, intArray0.length);
      assertArrayEquals(new int[] {0, 0, 0, 0}, intArray0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertEquals(0, intArray0.length);
      assertEquals(0, intArray1.length);
      assertNotNull(intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertArrayEquals(new int[] {}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      
      int int0 = MathUtils.distanceInf(intArray0, intArray1);
      assertEquals(0, intArray0.length);
      assertEquals(0, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertArrayEquals(new int[] {}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      int[] intArray0 = new int[9];
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(9, intArray0.length);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0, 0, 0}, intArray0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      int[] intArray0 = new int[8];
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(8, intArray0.length);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0, 0}, intArray0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byteArray0[0] = (byte) (-96);
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(3, byteArray0.length);
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte) (-96), (byte)0, (byte)0}, byteArray0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, bigInteger0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-6,291,456)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short) (-7168), bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger1, bigInteger0);
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short) (-7168), bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 8926498302486690232L);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      byte[] byteArray0 = new byte[2];
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(2, byteArray0.length);
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0}, byteArray0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (long) (byte) (-36));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-36)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0L);
      assertNotNull(bigInteger1);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-362));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-362)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(6, byteArray0.length);
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 383);
      assertEquals(6, byteArray0.length);
      assertNotNull(bigInteger1);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow(4577762542105553359L, (-2644281811660520851L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-2,644,281,811,660,520,851)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((-1L), (-260));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-260)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow(1225, (-2915L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-2,915)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      int int0 = MathUtils.pow((-2147475393), 40348L);
      assertEquals((-429868799), int0);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((-153), (-153));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-153)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      int int0 = MathUtils.pow(0, 7);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.subAndCheck(2144169630, (-2147030112));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in subtraction: 2,144,169,630 - -2,147,030,112
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(2144744510, 2144744510);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      short short0 = MathUtils.sign((short)13);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      short short0 = MathUtils.sign((short) (-1231));
      assertEquals((short) (-1), short0);
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      long long0 = MathUtils.sign((-299L));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      long long0 = MathUtils.sign(0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      long long0 = MathUtils.sign((long) 1296);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      int int0 = MathUtils.sign((-1));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      int int0 = MathUtils.sign(0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      int int0 = MathUtils.sign(2147481862);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      float float0 = MathUtils.sign((-2033.5F));
      assertEquals((-1.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      float float0 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      float float0 = MathUtils.sign(Float.NaN);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      float float0 = MathUtils.sign(1097.1226F);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      double double0 = MathUtils.sign((double) 3.4028235E38F);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      double double0 = MathUtils.sign(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      double double0 = MathUtils.sign((-530.1139152905674));
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)86);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte) (-72));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.round((float) 1179, 0, 1394);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 1,394, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      float float0 = MathUtils.round((float) 1179, 0, 1);
      assertEquals(1178.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, 0, 0);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 10;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 707.5467F);
      assertEquals(2, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {10.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {707.5466918945312, 0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = Double.POSITIVE_INFINITY;
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, (-3030.25722));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // Array contains an infinite element, \u221E at index 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, 707.5467F);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((-5034868814120038111L), 836L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-1523L), (long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      long long0 = MathUtils.lcm((long) (-1), (long) (-3633));
      assertEquals(3633L, long0);
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(4194304, 756);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.lcm((-2147374213), 2716);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      long long0 = MathUtils.lcm((long) 0, (long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      int int0 = MathUtils.lcm(2147481862, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      int int0 = MathUtils.lcm(0, (int) (byte)12);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      short short0 = MathUtils.indicator((short)1216);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      short short0 = MathUtils.indicator((short) (-1719));
      assertEquals((short) (-1), short0);
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      long long0 = MathUtils.indicator(4661292996414230915L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      long long0 = MathUtils.indicator((-260174L));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      int int0 = MathUtils.indicator(1628);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      int int0 = MathUtils.indicator((-1023));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      float float0 = MathUtils.round((-2567.669F), 6);
      assertEquals((-2567.669F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      float float0 = MathUtils.indicator(Float.NaN);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      double double0 = MathUtils.indicator((double) (short)13);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
      double double0 = MathUtils.indicator((-2116.6720604652464));
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)56);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte) (-20));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      long long0 = MathUtils.gcd(1073741824L, 120L);
      assertEquals(8L, long0);
  }

  @Test(timeout = 4000)
  public void test226()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.lcm(836L, 9154082963658192752L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test227()  throws Throwable  {
      long long0 = MathUtils.gcd((long) 7, (-406L));
      assertEquals(7L, long0);
  }

  @Test(timeout = 4000)
  public void test228()  throws Throwable  {
      long long0 = MathUtils.gcd((long) 10, 0L);
      assertEquals(10L, long0);
  }

  @Test(timeout = 4000)
  public void test229()  throws Throwable  {
      int int0 = MathUtils.gcd((int) (byte) (-1), Integer.MIN_VALUE);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test230()  throws Throwable  {
      int int0 = MathUtils.gcd(1433, 0);
      assertEquals(1433, int0);
  }

  @Test(timeout = 4000)
  public void test231()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorialLog((-371));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -371
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test232()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(1025);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test233()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorialDouble((-1106));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1,106
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test234()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test235()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorial(3597);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test236()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorial((-3099));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -3,099
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test237()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = 2853.54562232;
      double[] doubleArray1 = new double[4];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {2853.54562232, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test238()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 109);
      assertEquals(5, doubleArray0.length);
      assertEquals(109, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray1);
      assertEquals(5, doubleArray0.length);
      assertEquals(109, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test239()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      boolean boolean0 = MathUtils.equalsIncludingNaN((double[]) null, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test240()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double[]) null, (double[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test241()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-523.6449);
      double[] doubleArray1 = new double[1];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertEquals(1, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {(-523.6449)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test242()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      double[] doubleArray1 = new double[4];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertEquals(5, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test243()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      boolean boolean0 = MathUtils.equals(doubleArray0, (double[]) null);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test244()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1875.103), (-1875.103), 661);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test245()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1597.977816024), 527.0, 188);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test246()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(826.0, 0.0, 67094735);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test247()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(947.0, (double) (-2642), (-2642));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test248()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) (-2139), (double) (-3346), (double) 1207);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test249()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) (-2642), (double) (-2642), (double) (-260174L));
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test250()  throws Throwable  {
      int int0 = MathUtils.compareTo((-0.12502530217170715), (-0.12502530217170715), (-0.12502530217170715));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test251()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 113236205062349959L, (double) (-2760.1777F), (double) 527L);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test252()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[4] = Double.NaN;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, Double.NaN, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test253()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.09090909090909091, 0.09090909090909091);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test254()  throws Throwable  {
      float[] floatArray0 = new float[9];
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(9, floatArray0.length);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test255()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float[]) null, (float[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test256()  throws Throwable  {
      float[] floatArray0 = new float[1];
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(1, floatArray0.length);
      assertArrayEquals(new float[] {0.0F}, floatArray0, 0.01F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test257()  throws Throwable  {
      float[] floatArray0 = new float[8];
      float[] floatArray1 = new float[9];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray1);
      assertEquals(8, floatArray0.length);
      assertEquals(9, floatArray1.length);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray1, 0.01F);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test258()  throws Throwable  {
      float[] floatArray0 = new float[6];
      boolean boolean0 = MathUtils.equals(floatArray0, (float[]) null);
      assertEquals(6, floatArray0.length);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test259()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float[]) null, (float[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test260()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, (float) 0, 2);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test261()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, Float.NaN, 1295);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test262()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, 707.5467F, 10);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test263()  throws Throwable  {
      float[] floatArray0 = new float[7];
      floatArray0[2] = Float.NaN;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(7, floatArray0.length);
      assertArrayEquals(new float[] {0.0F, 0.0F, Float.NaN, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test264()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(3931.0F, (float) 1018500752, 1018500752);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test265()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(0.0F, (float) 0, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test266()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, (-543.002F), 3408.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test267()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-1398.7946F), (-125.924F), (float) 9252);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test268()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float) 3648485774074266L, (-69.08552F), Float.NaN);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test269()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(1.0F, (float) (short)1, 1.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test270()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-781.599F), (-781.599F), (-781.599F));
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test271()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, Float.NaN, Float.NaN);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test272()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, (float) (-2642));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test273()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 60, (float) (-2328), (float) 119);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test274()  throws Throwable  {
      int int0 = MathUtils.compareTo(0, (-2934.20552452), 0.0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test275()  throws Throwable  {
      int int0 = MathUtils.compareTo(19.26098563742326, 1.1585366648121113E8, 13.92954799809);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test276()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-3340), (-3340));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -3,340
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test277()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(1, 1628);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1,628, n = 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test278()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(1381, (-938));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test279()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(0, (-2313));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test280()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog((short)10, 1);
      assertEquals(2.302585092994046, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test281()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(275, 0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test282()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(188, 35);
      assertEquals(87.760812056479, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test283()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(1052, 1052);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test284()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(67, 35);
      assertEquals(1.3413576695470557E19, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test285()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(14, 13);
      assertEquals(14.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test286()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(199, 0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test287()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(60, 60);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test288()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(66, 15);
      assertEquals(2.68367258592576E14, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test289()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(251, 129);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test290()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(64, 1);
      assertEquals(64L, long0);
  }

  @Test(timeout = 4000)
  public void test291()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(0, (-1844));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test292()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(1076101120, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test293()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(2396, 2396);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test294()  throws Throwable  {
      long long0 = MathUtils.addAndCheck((-2644281811660520851L), (long) (byte) (-1));
      assertEquals((-2644281811660520852L), long0);
  }

  @Test(timeout = 4000)
  public void test295()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(1966L, (-3648485774072300L));
      assertEquals(3648485774074266L, long0);
  }

  @Test(timeout = 4000)
  public void test296()  throws Throwable  {
      int int0 = MathUtils.addAndCheck((-1057), (-1011));
      assertEquals((-2068), int0);
  }

  @Test(timeout = 4000)
  public void test297()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double[][] doubleArray1 = new double[3][3];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertEquals(1, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test298()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double[][] doubleArray1 = new double[3][3];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 3 != 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test299()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle((-952.443223), (-952.443223));
      assertEquals((-952.443223), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test300()  throws Throwable  {
      double double0 = MathUtils.sinh((-1653.079768117483));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test301()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(1073741824L, (long) (byte) (-1));
      assertEquals(1073741823L, long0);
  }

  @Test(timeout = 4000)
  public void test302()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test303()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(29791, int0);
  }

  @Test(timeout = 4000)
  public void test304()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-1.0F), 3.4028235E38F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test305()  throws Throwable  {
      int[] intArray0 = new int[3];
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertEquals(3, intArray1.length);
      assertNotSame(intArray1, intArray0);
  }

  @Test(timeout = 4000)
  public void test306()  throws Throwable  {
      int int0 = MathUtils.hash(0.0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test307()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(2, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test308()  throws Throwable  {
      double double0 = MathUtils.round(2631.0031, 119);
      assertEquals(2631.0031, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test309()  throws Throwable  {
      double double0 = MathUtils.cosh((-1125.6785317245));
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test310()  throws Throwable  {
      MathUtils.round(749.31F, (int) (byte) (-1));
  }
}
