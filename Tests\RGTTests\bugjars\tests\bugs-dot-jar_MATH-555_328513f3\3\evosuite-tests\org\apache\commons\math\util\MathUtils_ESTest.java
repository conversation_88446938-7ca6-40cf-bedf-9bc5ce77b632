/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 11:39:32 GMT 2019
 */

package org.apache.commons.math.util;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.math.BigInteger;
import java.util.Locale;
import org.apache.commons.math.exception.util.Localizable;
import org.apache.commons.math.exception.util.LocalizedFormats;
import org.apache.commons.math.util.MathUtils;
import org.apache.commons.math.util.Pair;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MathUtils_ESTest extends MathUtils_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-622.6), (-622.6), 107);
      assertTrue(boolean0);
      
      byte[] byteArray0 = new byte[8];
      byteArray0[0] = (byte) (-92);
      byteArray0[1] = (byte)21;
      byteArray0[2] = (byte)104;
      byteArray0[3] = (byte) (-69);
      byteArray0[4] = (byte)90;
      byteArray0[5] = (byte)22;
      byteArray0[6] = (byte)11;
      byteArray0[7] = (byte)121;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((short)2937, bigInteger0.shortValue());
      assertEquals((byte)121, bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte) (-92), (byte)21, (byte)104, (byte) (-69), (byte)90, (byte)22, (byte)11, (byte)121}, byteArray0);
      assertEquals(8, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 298L);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)2937, bigInteger0.shortValue());
      assertEquals((byte)121, bigInteger0.byteValue());
      assertEquals((short) (-5903), bigInteger1.shortValue());
      assertEquals((byte) (-15), bigInteger1.byteValue());
      assertArrayEquals(new byte[] {(byte) (-92), (byte)21, (byte)104, (byte) (-69), (byte)90, (byte)22, (byte)11, (byte)121}, byteArray0);
      assertEquals(8, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
      
      double double1 = MathUtils.sinh(0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      int int0 = MathUtils.hash((double) 0);
      assertEquals(0, int0);
      
      double double2 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0}, doubleArray1, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      
      short short0 = MathUtils.sign((short) (-3170));
      assertEquals((short) (-1), short0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 0;
      floatArray0[1] = (float) (short) (-1);
      floatArray0[2] = 1927.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, (float[]) null);
      assertFalse(boolean0);
      assertArrayEquals(new float[] {0.0F, (-1.0F), 1927.0F}, floatArray0, 0.01F);
      assertEquals(3, floatArray0.length);
      
      long long1 = MathUtils.sign((long) 0);
      assertFalse(long1 == long0);
      assertEquals(0L, long1);
      
      int int1 = MathUtils.mulAndCheck((-2028), (int) (short) (-1));
      assertFalse(int1 == int0);
      assertEquals(2028, int1);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) (short) (-3170), 0.0, 0.0);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      int int2 = MathUtils.subAndCheck(0, 2578);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals((-2578), int2);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float) 1L, (-1.0F), (-5200.0F));
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
      
      double double3 = MathUtils.distance1(doubleArray1, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertEquals(double3, double1, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(0.0, double3, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0}, doubleArray1, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      long long2 = MathUtils.mulAndCheck((-906L), (long) 0);
      assertTrue(long2 == long1);
      assertFalse(long2 == long0);
      assertEquals(0L, long2);
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      double double0 = MathUtils.round(Double.NaN, 4194304, 4194304);
      assertEquals(Double.NaN, double0, 0.01);
      
      Integer integer0 = new Integer(4194304);
      assertEquals(4194304, (int)integer0);
      assertNotNull(integer0);
      
      int int0 = MathUtils.gcd(1332, 1332);
      assertEquals(1332, int0);
      
      long long0 = MathUtils.subAndCheck(85L, 9L);
      assertEquals(76L, long0);
      
      int int1 = MathUtils.compareTo((double) integer0, 0.0, (-939.14));
      assertFalse(integer0.equals((Object)int0));
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0L);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = bigInteger1.or(bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotNull(bigInteger2);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((byte)11, bigInteger2.byteValue());
      assertEquals((short)11, bigInteger2.shortValue());
      
      BigInteger bigInteger3 = bigInteger1.and(bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertNotNull(bigInteger3);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((byte)0, bigInteger3.byteValue());
      assertEquals((short)0, bigInteger3.shortValue());
      
      BigInteger bigInteger4 = bigInteger1.shiftRight(388);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger3));
      assertFalse(bigInteger4.equals((Object)bigInteger0));
      assertFalse(bigInteger4.equals((Object)bigInteger2));
      assertTrue(bigInteger4.equals((Object)bigInteger3));
      assertFalse(bigInteger4.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger1, bigInteger4);
      assertNotSame(bigInteger4, bigInteger0);
      assertNotSame(bigInteger4, bigInteger2);
      assertNotSame(bigInteger4, bigInteger3);
      assertNotSame(bigInteger4, bigInteger1);
      assertNotNull(bigInteger4);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger4.shortValue());
      assertEquals((byte)0, bigInteger4.byteValue());
      
      BigInteger bigInteger5 = bigInteger0.mod(bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger0.equals((Object)bigInteger4));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger3));
      assertFalse(bigInteger1.equals((Object)bigInteger4));
      assertFalse(bigInteger5.equals((Object)bigInteger1));
      assertFalse(bigInteger5.equals((Object)bigInteger2));
      assertFalse(bigInteger5.equals((Object)bigInteger0));
      assertTrue(bigInteger5.equals((Object)bigInteger3));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger0, bigInteger5);
      assertNotSame(bigInteger1, bigInteger5);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger1, bigInteger4);
      assertNotSame(bigInteger5, bigInteger1);
      assertSame(bigInteger5, bigInteger4);
      assertNotSame(bigInteger5, bigInteger2);
      assertNotSame(bigInteger5, bigInteger3);
      assertNotSame(bigInteger5, bigInteger0);
      assertNotNull(bigInteger5);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((byte)0, bigInteger5.byteValue());
      assertEquals((short)0, bigInteger5.shortValue());
      
      BigInteger bigInteger6 = MathUtils.pow(bigInteger0, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger0.equals((Object)bigInteger4));
      assertFalse(bigInteger0.equals((Object)bigInteger5));
      assertFalse(bigInteger6.equals((Object)bigInteger0));
      assertFalse(bigInteger6.equals((Object)bigInteger4));
      assertFalse(bigInteger6.equals((Object)bigInteger5));
      assertFalse(bigInteger6.equals((Object)bigInteger1));
      assertFalse(bigInteger6.equals((Object)bigInteger3));
      assertFalse(bigInteger6.equals((Object)bigInteger2));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger6);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger0, bigInteger5);
      assertNotSame(bigInteger6, bigInteger0);
      assertNotSame(bigInteger6, bigInteger4);
      assertNotSame(bigInteger6, bigInteger5);
      assertNotSame(bigInteger6, bigInteger1);
      assertNotSame(bigInteger6, bigInteger3);
      assertNotSame(bigInteger6, bigInteger2);
      assertNotNull(bigInteger6);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short) (-7168), bigInteger6.shortValue());
      assertEquals((byte)0, bigInteger6.byteValue());
      
      int[] intArray0 = new int[0];
      int int2 = MathUtils.distance1(intArray0, intArray0);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(0, int2);
      assertArrayEquals(new int[] {}, intArray0);
      assertEquals(0, intArray0.length);
      
      boolean boolean0 = MathUtils.equals((double) 388, 0.0, (double) 1332);
      assertTrue(boolean0);
      
      double double1 = MathUtils.sign(Double.NaN);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.factorialDouble((-1599));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1,599
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte) (-71));
      assertEquals((byte) (-1), byte0);
      
      double double0 = MathUtils.EPSILON;
      assertEquals(1.1102230246251565E-16, double0, 0.01);
      
      int int0 = MathUtils.pow(158, 886L);
      assertEquals(0, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1.0F), 1092.4F, (float) (byte) (-71));
      assertFalse(boolean0);
      
      int[] intArray0 = new int[19];
      intArray0[0] = (int) (byte) (-1);
      intArray0[1] = 158;
      intArray0[2] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0, 158);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertEquals(19, intArray0.length);
      assertEquals(158, intArray1.length);
      
      long long0 = MathUtils.subAndCheck((long) (-1), 36L);
      assertEquals((-37L), long0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 1.1102230246251565E-16;
      doubleArray0[1] = (double) (byte) (-1);
      doubleArray0[2] = (double) (byte) (-1);
      doubleArray0[3] = (double) (byte) (-71);
      doubleArray0[4] = (double) (-37L);
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {1.1102230246251565E-16, (-1.0), (-1.0), (-71.0), (-37.0)}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {1.1102230246251565E-16, (-1.0), (-1.0), (-71.0), (-37.0)}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      long long1 = MathUtils.binomialCoefficient(692, 3);
      assertFalse(long1 == long0);
      assertEquals(54989780L, long1);
      
      int int1 = MathUtils.pow((-1), (long) 692);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[1][3];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {1.1102230246251565E-16, (-1.0), (-1.0), (-37.0), (-71.0)}, doubleArray0, 0.01);
      assertEquals(1, doubleArray1.length);
      assertEquals(5, doubleArray0.length);
      
      long long2 = MathUtils.gcd(0L, 36L);
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      assertEquals(36L, long2);
      
      int[] intArray2 = new int[3];
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray2.equals((Object)intArray0));
      
      intArray2[0] = 3;
      intArray2[1] = (int) (byte) (-71);
      intArray2[2] = (int) (byte) (-1);
      double double2 = MathUtils.distance(intArray2, intArray1);
      assertFalse(intArray0.equals((Object)intArray2));
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray2));
      assertFalse(intArray1.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray2.equals((Object)intArray0));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(229.0371148962543, double2, 0.01);
      assertNotSame(intArray0, intArray2);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray2);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray2, intArray1);
      assertNotSame(intArray2, intArray0);
      assertArrayEquals(new int[] {3, (-71), (-1)}, intArray2);
      assertEquals(19, intArray0.length);
      assertEquals(158, intArray1.length);
      assertEquals(3, intArray2.length);
      
      long long3 = MathUtils.binomialCoefficient(31, (-2754));
      assertFalse(long3 == long1);
      assertFalse(long3 == long2);
      assertFalse(long3 == long0);
      assertEquals(1L, long3);
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      int[] intArray0 = new int[8];
      intArray0[0] = 281;
      intArray0[1] = (-3770);
      intArray0[2] = 1315;
      intArray0[3] = (-215);
      intArray0[4] = (-977);
      intArray0[5] = 2418;
      intArray0[6] = 61;
      intArray0[7] = 10;
      int[] intArray1 = new int[4];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = 1315;
      intArray1[1] = 281;
      intArray1[2] = 2418;
      intArray1[3] = 2418;
      // Undeclared exception!
      try { 
        MathUtils.distance1(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      double[] doubleArray0 = null;
      boolean boolean0 = MathUtils.equals((double[]) null, (double[]) null);
      assertTrue(boolean0);
      
      int int0 = 226;
      int int1 = 1030;
      int int2 = MathUtils.gcd(226, 1030);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(2, int2);
      
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null, 226);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte) (-64));
      assertEquals((byte) (-1), byte0);
      
      double double0 = MathUtils.EPSILON;
      assertEquals(1.1102230246251565E-16, double0, 0.01);
      
      int int0 = MathUtils.pow(158, 886L);
      assertEquals(0, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1.0F), 1092.4F, (float) (byte) (-64));
      assertFalse(boolean0);
      
      int[] intArray0 = new int[28];
      intArray0[0] = (int) (byte) (-1);
      intArray0[2] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0, 0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {}, intArray1);
      assertEquals(28, intArray0.length);
      assertEquals(0, intArray1.length);
      
      long long0 = MathUtils.subAndCheck((long) (-1), 36L);
      assertEquals((-37L), long0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 1.1102230246251565E-16;
      doubleArray0[1] = (double) (byte) (-1);
      doubleArray0[2] = (double) (byte) (-1);
      doubleArray0[3] = (double) (byte) (-64);
      doubleArray0[4] = (double) (-37L);
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {1.1102230246251565E-16, (-1.0), (-1.0), (-64.0), (-37.0)}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      long long1 = MathUtils.binomialCoefficient(692, 3);
      assertFalse(long1 == long0);
      assertEquals(54989780L, long1);
      
      int int1 = MathUtils.pow((-1), (long) 692);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[1][3];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {(-64.0), (-37.0), (-1.0), (-1.0), 1.1102230246251565E-16}, doubleArray0, 0.01);
      assertEquals(1, doubleArray1.length);
      assertEquals(5, doubleArray0.length);
      
      long long2 = MathUtils.gcd(0L, 36L);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(36L, long2);
      
      int[] intArray2 = new int[3];
      assertFalse(intArray2.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      
      intArray2[0] = 3;
      intArray2[1] = (int) (byte) (-64);
      intArray2[2] = (int) (byte) (-1);
      // Undeclared exception!
      try { 
        MathUtils.distance(intArray2, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      MathUtils.checkNotNull((Object) "");
      double[][] doubleArray0 = new double[6][6];
      long long0 = MathUtils.subAndCheck(4398046511103L, 4398046511103L);
      assertEquals(0L, long0);
      
      int int0 = MathUtils.pow(1, 3291);
      assertEquals(1, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-533.75342941714), (-143.30217), 1);
      assertFalse(boolean0);
      
      float float0 = MathUtils.round((float) 1, 1);
      assertEquals(1.0F, float0, 0.01F);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(117, 117);
      assertEquals(0, int0);
      
      double double0 = MathUtils.binomialCoefficientLog(511, 117);
      assertEquals(271.7574751232736, double0, 0.01);
      
      int int1 = MathUtils.pow(117, 117);
      assertFalse(int1 == int0);
      assertEquals(1460596133, int1);
      
      double double1 = MathUtils.factorialDouble(0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      double double2 = MathUtils.binomialCoefficientDouble(1460596133, 889);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 0.2857142686843872;
      doubleArray0[1] = (-846.73);
      doubleArray0[2] = (-2674.6849653157337);
      doubleArray0[3] = 2709.7172793142;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 8.0E298;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[4][0];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {(-2674.6849653157337), (-846.73), 0.0, 0.2857142686843872, 2709.7172793142, 8.0E298}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {(-2674.6849653157337), (-846.73), 0.0, 0.2857142686843872, 2709.7172793142, 8.0E298}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      double[] doubleArray2 = MathUtils.normalizeArray(doubleArray0, (-3489.36525));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotNull(doubleArray2);
      assertArrayEquals(new double[] {(-2674.6849653157337), (-846.73), 0.0, 0.2857142686843872, 2709.7172793142, 8.0E298}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.166619096583772E-292, 3.693187797665625E-293, -0.0, (-1.24620180072058E-296), (-1.181899163970439E-292), (-3489.36525)}, doubleArray2, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray2.length);
      
      boolean boolean1 = MathUtils.equals((-846.73), (-2674.6849653157337), 0.0);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      int int0 = MathUtils.mulAndCheck(53, 53);
      assertEquals(2809, int0);
      
      boolean boolean2 = MathUtils.equals(0.0, (-2674.6849653157337), 148);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
      
      boolean boolean3 = MathUtils.equals((float) 2809, (float) 148, 148);
      assertFalse(boolean3 == boolean0);
      assertTrue(boolean3 == boolean1);
      assertTrue(boolean3 == boolean2);
      assertFalse(boolean3);
      
      double double0 = MathUtils.round(Double.NEGATIVE_INFINITY, (-2846), 2147436712);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 1193.9282;
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {1193.9282}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      double double0 = MathUtils.factorialDouble(89);
      assertEquals(1.6507955160909186E136, double0, 0.01);
      
      short short0 = MathUtils.indicator((short)180);
      assertEquals((short)1, short0);
      
      int[] intArray0 = new int[6];
      intArray0[0] = (int) (short)1;
      intArray0[1] = 27;
      intArray0[2] = (int) (short)1;
      intArray0[3] = 89;
      intArray0[4] = (int) (short)1;
      intArray0[5] = 89;
      int[] intArray1 = new int[7];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = 89;
      intArray1[1] = 27;
      intArray1[2] = (-2147483647);
      intArray1[3] = 27;
      intArray1[4] = 27;
      intArray1[5] = 27;
      intArray1[6] = 89;
      int int0 = MathUtils.distanceInf(intArray0, intArray1);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(88, int0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertArrayEquals(new int[] {1, 27, 1, 89, 1, 89}, intArray0);
      assertArrayEquals(new int[] {89, 27, (-2147483647), 27, 27, 27, 89}, intArray1);
      assertEquals(6, intArray0.length);
      assertEquals(7, intArray1.length);
      
      int[] intArray2 = MathUtils.copyOf(intArray1);
      assertFalse(intArray2.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray2, intArray0);
      assertNotSame(intArray2, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray1, intArray2);
      assertNotNull(intArray2);
      assertArrayEquals(new int[] {89, 27, (-2147483647), 27, 27, 27, 89}, intArray2);
      assertArrayEquals(new int[] {89, 27, (-2147483647), 27, 27, 27, 89}, intArray1);
      assertEquals(7, intArray2.length);
      assertEquals(7, intArray1.length);
      
      byte byte0 = MathUtils.sign((byte) (-42));
      assertEquals((byte) (-1), byte0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float[]) null, (float[]) null);
      assertTrue(boolean0);
      
      float float0 = MathUtils.round((float) 27, 89, (int) (short)1);
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int1 = MathUtils.distanceInf(intArray0, intArray1);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray0.equals((Object)intArray2));
      assertTrue(int1 == int0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertFalse(intArray1.equals((Object)intArray2));
      assertEquals(88, int1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray0, intArray2);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray1, intArray2);
      assertArrayEquals(new int[] {1, 27, 1, 89, 1, 89}, intArray0);
      assertArrayEquals(new int[] {89, 27, (-2147483647), 27, 27, 27, 89}, intArray1);
      assertEquals(6, intArray0.length);
      assertEquals(7, intArray1.length);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(566.0F, (-2995.3374F));
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, (BigInteger) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(117, 117);
      assertEquals(0, int0);
      
      int int1 = MathUtils.addAndCheck(Integer.MIN_VALUE, 261632);
      assertFalse(int1 == int0);
      assertEquals((-2147222016), int1);
      
      double double0 = MathUtils.binomialCoefficientLog(511, 117);
      assertEquals(271.7574751232736, double0, 0.01);
      
      int int2 = MathUtils.gcd(261632, 0);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(261632, int2);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      int int0 = 117;
      int int1 = MathUtils.subAndCheck(117, 117);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      // Undeclared exception!
      try { 
        MathUtils.addAndCheck(Integer.MIN_VALUE, Integer.MIN_VALUE);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in addition: -2,147,483,648 + -2,147,483,648
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(0, (-5095));
      assertEquals(1.0, double0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(602.932908, 2251.5, 0.0);
      assertFalse(boolean0);
      
      float[] floatArray0 = new float[0];
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray0);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertEquals(0, floatArray0.length);
      
      long long0 = MathUtils.mulAndCheck(0L, (long) (-5095));
      assertEquals(0L, long0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      double double1 = MathUtils.cosh(0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      long long1 = MathUtils.pow((-2644281811660520851L), 0);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      boolean boolean2 = MathUtils.equals((-622.7F), (float) 0L);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2);
      
      byte byte0 = MathUtils.sign((byte) (-1));
      assertEquals((byte) (-1), byte0);
      
      double[] doubleArray0 = new double[0];
      double double2 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(0.0, double2, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      
      double double3 = MathUtils.sign(0.0);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(0.0, double3, 0.01);
      
      int int0 = MathUtils.subAndCheck((int) (byte) (-1), (-2424));
      assertEquals(2423, int0);
      
      float float0 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float0, 0.01F);
      
      boolean boolean3 = MathUtils.equals(0.0F, 667.6289F);
      assertTrue(boolean3 == boolean0);
      assertTrue(boolean3 == boolean2);
      assertFalse(boolean3 == boolean1);
      assertFalse(boolean3);
      
      int int1 = MathUtils.hash(1178.26);
      assertFalse(int1 == int0);
      assertEquals(2112015069, int1);
      
      boolean boolean4 = MathUtils.equals((float) 0L, (float) (-2424), (-5218.6274F));
      assertFalse(boolean4 == boolean1);
      assertTrue(boolean4 == boolean0);
      assertTrue(boolean4 == boolean2);
      assertTrue(boolean4 == boolean3);
      assertFalse(boolean4);
      
      int int2 = MathUtils.compareTo(0.6666666269302368, 0.0, 2151.5);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(0, int2);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3.940510424527919E-20);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 2587.042528;
      doubleArray0[3] = (-718.602714);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(2684.991415675066, double0, 0.01);
      assertArrayEquals(new double[] {(-3.940510424527919E-20), 0.0, 2587.042528, (-718.602714)}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      
      double[][] doubleArray1 = new double[6][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((-9223372036854775808L), (-9223372036854775808L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      double double0 = MathUtils.sign(1472.1638270177);
      assertEquals(1.0, double0, 0.01);
      
      double[] doubleArray0 = new double[0];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[9][8];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      double[] doubleArray2 = new double[0];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray1[2] = doubleArray2;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      double[] doubleArray3 = new double[6];
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      
      doubleArray3[0] = 1472.1638270177;
      doubleArray3[1] = 1.0;
      doubleArray3[2] = 1.0;
      doubleArray3[3] = 1.0;
      doubleArray3[4] = 1.0;
      doubleArray3[5] = 1.0;
      doubleArray1[8] = doubleArray3;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray3));
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray3);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.5F, 0.5F, 12);
      assertTrue(boolean0);
      
      long long0 = MathUtils.subAndCheck((long) 12, 4503599627370496L);
      assertEquals((-4503599627370484L), long0);
      
      int int0 = MathUtils.sign((-631));
      assertEquals((-1), int0);
      
      MathUtils.OrderDirection mathUtils_OrderDirection1 = MathUtils.OrderDirection.INCREASING;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection1, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray3));
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray3);
      assertSame(mathUtils_OrderDirection1, mathUtils_OrderDirection0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      
      byte[] byteArray0 = new byte[8];
      byteArray0[0] = (byte) (-43);
      byteArray0[1] = (byte)59;
      byteArray0[2] = (byte)7;
      byteArray0[3] = (byte) (-1);
      byteArray0[4] = (byte) (-93);
      byteArray0[5] = (byte)17;
      byteArray0[6] = (byte) (-57);
      byteArray0[7] = (byte) (-83);
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((short) (-14419), bigInteger0.shortValue());
      assertEquals((byte) (-83), bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte) (-43), (byte)59, (byte)7, (byte) (-1), (byte) (-93), (byte)17, (byte) (-57), (byte) (-83)}, byteArray0);
      assertEquals(8, byteArray0.length);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, bigInteger0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-3,081,860,725,471,066,195)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      MathUtils.checkNotNull((Object) "");
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3.940510424527919E-20);
      doubleArray0[1] = (-3.940510424527919E-20);
      doubleArray0[2] = 2587.042528;
      doubleArray0[3] = (-718.602714);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(2684.991415675066, double0, 0.01);
      assertArrayEquals(new double[] {(-3.940510424527919E-20), (-3.940510424527919E-20), 2587.042528, (-718.602714)}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      
      double[][] doubleArray1 = new double[6][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-718.602714), (-3.940510424527919E-20), (-3.940510424527919E-20), 2587.042528}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      long long0 = MathUtils.subAndCheck(4398046511103L, 4398046511103L);
      assertEquals(0L, long0);
      
      double double1 = MathUtils.sign((-718.602714));
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-1.0), double1, 0.01);
      
      long long1 = MathUtils.mulAndCheck(0L, 0L);
      assertTrue(long1 == long0);
      assertEquals(0L, long1);
      
      float float0 = MathUtils.sign(2424.0F);
      assertEquals(1.0F, float0, 0.01F);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {(-718.602714), (-3.940510424527919E-20), (-3.940510424527919E-20), 2587.042528}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      
      long long2 = MathUtils.gcd(1880L, 0L);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(1880L, long2);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      MathUtils.checkNotNull((Object) "");
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3.940510424527919E-20);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 2587.042528;
      doubleArray0[3] = (-718.602714);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(2684.991415675066, double0, 0.01);
      assertArrayEquals(new double[] {(-3.940510424527919E-20), 0.0, 2587.042528, (-718.602714)}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      
      double[][] doubleArray1 = new double[6][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-718.602714), (-3.940510424527919E-20), 0.0, 2587.042528}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      long long0 = MathUtils.subAndCheck(4398046511103L, 4398046511103L);
      assertEquals(0L, long0);
      
      double double1 = MathUtils.sign((-718.602714));
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-1.0), double1, 0.01);
      
      double[] doubleArray2 = new double[0];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      double double2 = MathUtils.distance(doubleArray2, doubleArray0);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertArrayEquals(new double[] {}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {(-718.602714), (-3.940510424527919E-20), 0.0, 2587.042528}, doubleArray0, 0.01);
      assertEquals(0, doubleArray2.length);
      assertEquals(4, doubleArray0.length);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(boolean0);
      assertNotSame(doubleArray0, doubleArray2);
      assertArrayEquals(new double[] {(-718.602714), (-3.940510424527919E-20), 0.0, 2587.042528}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(158, 158);
      assertEquals(0.0, double0, 0.01);
      
      int int0 = MathUtils.pow(158, 886L);
      assertEquals(0, int0);
      
      int[] intArray0 = new int[3];
      intArray0[0] = 158;
      intArray0[1] = 158;
      intArray0[2] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0, 158);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {158, 158, 0}, intArray0);
      assertEquals(3, intArray0.length);
      assertEquals(158, intArray1.length);
      
      long long0 = MathUtils.subAndCheck((long) 158, 36L);
      assertEquals(122L, long0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 0.0;
      doubleArray0[4] = (double) 122L;
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 122.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 122.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      long long1 = MathUtils.binomialCoefficient(692, 3);
      assertFalse(long1 == long0);
      assertEquals(54989780L, long1);
      
      int int1 = MathUtils.pow(158, (long) 692);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[1][3];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 122.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray1.length);
      assertEquals(5, doubleArray0.length);
      
      long long2 = MathUtils.gcd(0L, 36L);
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      assertEquals(36L, long2);
      
      int[] intArray2 = new int[3];
      assertFalse(intArray2.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      
      intArray2[0] = 3;
      intArray2[1] = 692;
      double double2 = MathUtils.distance(intArray2, intArray1);
      assertFalse(intArray0.equals((Object)intArray2));
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray2.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertFalse(intArray1.equals((Object)intArray2));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(556.0404661533188, double2, 0.01);
      assertNotSame(intArray0, intArray2);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray2, intArray0);
      assertNotSame(intArray2, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray1, intArray2);
      assertArrayEquals(new int[] {158, 158, 0}, intArray0);
      assertArrayEquals(new int[] {3, 692, 0}, intArray2);
      assertEquals(3, intArray0.length);
      assertEquals(3, intArray2.length);
      assertEquals(158, intArray1.length);
      
      int int2 = MathUtils.pow((-2132973622), 692);
      assertTrue(int2 == int0);
      assertTrue(int2 == int1);
      assertEquals(0, int2);
      
      int int3 = MathUtils.compareTo(0.0, 2653.0588674571, (-200.5057738));
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      assertEquals((-1), int3);
      
      // Undeclared exception!
      MathUtils.factorialDouble(2146240891);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      MathUtils.checkNotNull((Object) "");
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3.940510424527919E-20);
      doubleArray0[1] = 12.639049936266973;
      doubleArray0[2] = 2587.042528;
      doubleArray0[3] = (-3.940510424527919E-20);
      // Undeclared exception!
      try { 
        MathUtils.safeNorm((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F);
      assertTrue(boolean0);
      
      int[] intArray0 = new int[4];
      intArray0[0] = 0;
      intArray0[1] = 0;
      intArray0[2] = 79;
      intArray0[3] = (-2662);
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
      assertArrayEquals(new int[] {0, 0, 79, (-2662)}, intArray0);
      assertEquals(4, intArray0.length);
      
      float[] floatArray0 = new float[6];
      floatArray0[0] = (float) 79;
      floatArray0[1] = (float) 0;
      floatArray0[2] = (float) 79;
      floatArray0[3] = (float) 0;
      floatArray0[4] = (float) 0;
      floatArray0[5] = (float) (-2662);
      float[] floatArray1 = new float[5];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = 0.0F;
      floatArray1[1] = 0.0F;
      floatArray1[2] = (float) 0;
      floatArray1[3] = 1.2392163F;
      floatArray1[4] = (float) 0;
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray1);
      assertFalse(boolean1 == boolean0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean1);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertArrayEquals(new float[] {79.0F, 0.0F, 79.0F, 0.0F, 0.0F, (-2662.0F)}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 1.2392163F, 0.0F}, floatArray1, 0.01F);
      assertEquals(6, floatArray0.length);
      assertEquals(5, floatArray1.length);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((double) (-2662), (double) 0, (double) 0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      MathUtils.checkNotNull((Object) "");
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3.940510424527919E-20);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (-718.602714);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(718.602714, double0, 0.01);
      assertArrayEquals(new double[] {(-3.940510424527919E-20), 0.0, 0.0, (-718.602714)}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      
      long long0 = MathUtils.subAndCheck(4398046511103L, 4398046511103L);
      assertEquals(0L, long0);
      
      double double1 = MathUtils.sign((-3.940510424527919E-20));
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-1.0), double1, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(4398046511103L, 4398046511103L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 1791.7247549;
      doubleArray0[1] = 3.834E-20;
      doubleArray0[2] = (-293.687460317);
      doubleArray0[3] = (-3465.7079);
      doubleArray0[4] = (-521.479833979201);
      doubleArray0[5] = 1220.856349555964;
      doubleArray0[6] = 1118.6450276868;
      doubleArray0[7] = 4031.0;
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {1791.7247549, 3.834E-20, (-293.687460317), (-3465.7079), (-521.479833979201), 1220.856349555964, 1118.6450276868, 4031.0}, doubleArray0, 0.01);
      assertEquals(8, doubleArray0.length);
      
      byte byte0 = MathUtils.sign((byte)19);
      assertEquals((byte)1, byte0);
      
      double double1 = MathUtils.factorialDouble((byte)19);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.21645100408832E17, double1, 0.01);
      
      double double2 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(0.0, double2, 0.01);
      assertArrayEquals(new double[] {1791.7247549, 3.834E-20, (-293.687460317), (-3465.7079), (-521.479833979201), 1220.856349555964, 1118.6450276868, 4031.0}, doubleArray0, 0.01);
      assertEquals(8, doubleArray0.length);
      
      long long0 = MathUtils.sign(2423L);
      assertEquals(1L, long0);
      
      float float0 = MathUtils.round((float) 2423L, (-771));
      assertEquals(Float.NaN, float0, 0.01F);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.041666666666666664, 0.041666666666666664);
      assertTrue(boolean0);
      
      int int0 = (-2388);
      // Undeclared exception!
      try { 
        MathUtils.round(Float.NaN, (-2388), (-1972));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method -1,972, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
      
      double double1 = MathUtils.sinh(0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      int int0 = MathUtils.pow(0, 0);
      assertEquals(1, int0);
      
      double double2 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(0.0, double2, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      
      short short0 = MathUtils.sign((short) (-3170));
      assertEquals((short) (-1), short0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 1;
      floatArray0[1] = (float) (short) (-1);
      floatArray0[2] = 1927.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertArrayEquals(new float[] {1.0F, (-1.0F), 1927.0F}, floatArray0, 0.01F);
      assertEquals(3, floatArray0.length);
      
      long long1 = MathUtils.sign((long) 1);
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
      
      int int1 = MathUtils.mulAndCheck((-2028), (int) (short) (-1));
      assertFalse(int1 == int0);
      assertEquals(2028, int1);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) (short) (-3170), 0.0, 0.0);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      int int2 = MathUtils.subAndCheck(0, 2578);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals((-2578), int2);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float) 1L, (-1.0F), (-5200.0F));
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
      
      double double3 = MathUtils.distance1(doubleArray1, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(0.0, double3, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(0.0F, 1224.3395F, 4194304);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(8, 6);
      assertEquals(3.332204510175204, double0, 0.01);
      
      byte byte0 = MathUtils.indicator((byte) (-34));
      assertEquals((byte) (-1), byte0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) (byte) (-34);
      doubleArray0[1] = 3.332204510175204;
      doubleArray0[2] = (double) (byte) (-34);
      doubleArray0[3] = 2.0;
      doubleArray0[4] = 10.0;
      doubleArray0[5] = (double) (byte) (-1);
      doubleArray0[6] = (double) 6;
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {(-34.0), 3.332204510175204, (-34.0), 2.0, 10.0, (-1.0), 6.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      double double2 = MathUtils.binomialCoefficientLog(8, (-1));
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals((-1861620218), int0);
      assertArrayEquals(new double[] {(-34.0), 3.332204510175204, (-34.0), 2.0, 10.0, (-1.0), 6.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      long long0 = MathUtils.gcd((long) (-1), (long) 6);
      assertEquals(1L, long0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = (int) (byte) (-34);
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      assertArrayEquals(new int[] {(-34)}, intArray0);
      assertEquals(1, intArray0.length);
      
      int[] intArray1 = new int[8];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = (-800);
      intArray1[1] = 6;
      intArray1[2] = (-1861620218);
      intArray1[3] = 0;
      intArray1[4] = 8;
      intArray1[5] = (int) (byte) (-34);
      intArray1[6] = 0;
      intArray1[7] = (int) (byte) (-34);
      double double3 = MathUtils.distance(intArray0, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertFalse(intArray0.equals((Object)intArray1));
      assertEquals(766.0, double3, 0.01);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray0, intArray1);
      assertArrayEquals(new int[] {(-800), 6, (-1861620218), 0, 8, (-34), 0, (-34)}, intArray1);
      assertArrayEquals(new int[] {(-34)}, intArray0);
      assertEquals(8, intArray1.length);
      assertEquals(1, intArray0.length);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 0, (double) 6);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      assertArrayEquals(new double[] {(-34.0), 3.332204510175204, (-34.0), 2.0, 10.0, (-1.0), 6.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      double double4 = MathUtils.indicator(3.834E-20);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(1.0, double4, 0.01);
      
      int[] intArray2 = MathUtils.copyOf(intArray0, 0);
      assertFalse(intArray2.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray0.equals((Object)intArray1));
      assertNotSame(intArray2, intArray0);
      assertNotSame(intArray2, intArray1);
      assertNotSame(intArray0, intArray2);
      assertNotSame(intArray0, intArray1);
      assertNotNull(intArray2);
      assertArrayEquals(new int[] {}, intArray2);
      assertArrayEquals(new int[] {(-34)}, intArray0);
      assertEquals(0, intArray2.length);
      assertEquals(1, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 3.834E-20;
      doubleArray0[1] = 0.5;
      doubleArray0[2] = 2.2250738585072014E-308;
      doubleArray0[4] = (-1401.7227360294341);
      doubleArray0[5] = 1.0;
      float float0 = MathUtils.round((-3146.2534F), (-950));
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.hash(0.5);
      assertEquals(1071644672, int0);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      int[] intArray0 = new int[2];
      intArray0[0] = 1071644672;
      intArray0[1] = 1071644672;
      int int1 = new Integer(1071644672);
      assertTrue(int1 == int0);
      assertEquals(1071644672, int1);
      
      int int2 = MathUtils.pow(1071644672, 1071644672);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(0, int2);
      
      short short0 = MathUtils.sign((short)775);
      assertEquals((short)1, short0);
      
      double[] doubleArray2 = new double[4];
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 1071644672;
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = 0.5;
      doubleArray2[3] = (double) (-950);
      double[] doubleArray3 = MathUtils.copyOf(doubleArray2, 0);
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertNotSame(doubleArray3, doubleArray2);
      assertNotSame(doubleArray3, doubleArray1);
      assertNotSame(doubleArray3, doubleArray0);
      assertNotSame(doubleArray2, doubleArray3);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotNull(doubleArray3);
      assertArrayEquals(new double[] {}, doubleArray3, 0.01);
      assertArrayEquals(new double[] {1.071644672E9, Double.NaN, 0.5, (-950.0)}, doubleArray2, 0.01);
      assertEquals(0, doubleArray3.length);
      assertEquals(4, doubleArray2.length);
      
      double double0 = MathUtils.normalizeAngle(0.0, 0.0);
      assertEquals(0.0, double0, 0.01);
      
      short short1 = MathUtils.sign((short)1);
      assertTrue(short1 == short0);
      assertEquals((short)1, short1);
      
      int int3 = MathUtils.lcm((int) (short)1, (-2022));
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      assertEquals(2022, int3);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray3, mathUtils_OrderDirection0, false, false);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte) (-64));
      assertEquals((byte) (-1), byte0);
      
      double double0 = MathUtils.binomialCoefficientLog(158, 158);
      assertEquals(0.0, double0, 0.01);
      
      int int0 = MathUtils.pow(158, 886L);
      assertEquals(0, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1.0F), 1092.4F, (float) (byte) (-64));
      assertFalse(boolean0);
      
      int[] intArray0 = new int[3];
      intArray0[0] = 158;
      intArray0[1] = 158;
      intArray0[2] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0, 158);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {158, 158, 0}, intArray0);
      assertEquals(3, intArray0.length);
      assertEquals(158, intArray1.length);
      
      long long0 = MathUtils.subAndCheck((long) 158, 36L);
      assertEquals(122L, long0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (double) (byte) (-1);
      doubleArray0[2] = (double) (byte) (-1);
      doubleArray0[3] = (double) (byte) (-64);
      doubleArray0[4] = (double) 122L;
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {0.0, (-1.0), (-1.0), (-64.0), 122.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {0.0, (-1.0), (-1.0), (-64.0), 122.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      long long1 = MathUtils.binomialCoefficient(692, 3);
      assertFalse(long1 == long0);
      assertEquals(54989780L, long1);
      
      int int1 = MathUtils.pow(158, (long) 692);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[1][3];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {(-64.0), (-1.0), (-1.0), 0.0, 122.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray1.length);
      assertEquals(5, doubleArray0.length);
      
      long long2 = MathUtils.gcd(0L, 36L);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(36L, long2);
      
      int[] intArray2 = new int[3];
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray2.equals((Object)intArray0));
      
      intArray2[0] = 3;
      intArray2[1] = 692;
      intArray2[2] = (int) (byte) (-1);
      double double2 = MathUtils.distance(intArray2, intArray1);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray0.equals((Object)intArray2));
      assertFalse(intArray1.equals((Object)intArray2));
      assertFalse(intArray1.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray2.equals((Object)intArray0));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(556.0413653677216, double2, 0.01);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray0, intArray2);
      assertNotSame(intArray1, intArray2);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray2, intArray1);
      assertNotSame(intArray2, intArray0);
      assertArrayEquals(new int[] {158, 158, 0}, intArray0);
      assertArrayEquals(new int[] {3, 692, (-1)}, intArray2);
      assertEquals(3, intArray0.length);
      assertEquals(158, intArray1.length);
      assertEquals(3, intArray2.length);
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Double.POSITIVE_INFINITY, 750.65718, Double.POSITIVE_INFINITY);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
      
      double double1 = MathUtils.sinh(0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      int int0 = MathUtils.pow(0, 0);
      assertEquals(1, int0);
      
      double double2 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(0.0, double2, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      
      short short0 = MathUtils.sign((short) (-3170));
      assertEquals((short) (-1), short0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 1;
      floatArray0[1] = (float) (short) (-1);
      floatArray0[2] = 1927.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertArrayEquals(new float[] {1.0F, (-1.0F), 1927.0F}, floatArray0, 0.01F);
      assertEquals(3, floatArray0.length);
      
      long long1 = MathUtils.sign((long) 1);
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
      
      int int1 = MathUtils.mulAndCheck((int) (short) (-1), (int) (short) (-1));
      assertTrue(int1 == int0);
      assertEquals(1, int1);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) (short) (-3170), 0.0, 0.0);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      int int2 = MathUtils.subAndCheck(0, 2578);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals((-2578), int2);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float) 1L, (-1.0F), (-5200.0F));
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
      
      double double3 = MathUtils.distance1(doubleArray1, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(0.0, double3, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger0.add(bigInteger1);
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotNull(bigInteger2);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((short)2, bigInteger2.shortValue());
      assertEquals((byte)2, bigInteger2.byteValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertTrue(bigInteger3.equals((Object)bigInteger0));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertTrue(bigInteger3.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotNull(bigInteger3);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      assertEquals((short)1, bigInteger3.shortValue());
      
      // Undeclared exception!
      try { 
        MathUtils.lcm((-4398046511103L), (-2147483648L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(107, 107);
      assertEquals(0, int0);
      
      int int1 = MathUtils.addAndCheck(Integer.MIN_VALUE, 261632);
      assertFalse(int1 == int0);
      assertEquals((-2147222016), int1);
      
      double double0 = MathUtils.binomialCoefficientLog(511, 107);
      assertEquals(259.0818670104425, double0, 0.01);
      
      int int2 = MathUtils.pow(107, 107);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals((-620144141), int2);
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      MathUtils.checkNotNull((Object) "");
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3.940510424527919E-20);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 2587.042528;
      doubleArray0[3] = (-718.602714);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(2684.991415675066, double0, 0.01);
      assertArrayEquals(new double[] {(-3.940510424527919E-20), 0.0, 2587.042528, (-718.602714)}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      
      double[][] doubleArray1 = new double[6][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-718.602714), (-3.940510424527919E-20), 0.0, 2587.042528}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      // Undeclared exception!
      MathUtils.factorialLog(2118066063);
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 3.834E-20;
      doubleArray0[1] = 0.5;
      doubleArray0[2] = 2.2250738585072014E-308;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = (-1401.7227360294341);
      doubleArray0[5] = 1.0;
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      float float0 = MathUtils.round((-3146.2534F), (-950));
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.hash(0.5);
      assertEquals(1071644672, int0);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      int[] intArray0 = new int[2];
      intArray0[0] = 1071644672;
      intArray0[1] = 1071644672;
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      assertArrayEquals(new int[] {1071644672, 1071644672}, intArray0);
      assertEquals(2, intArray0.length);
      
      int int2 = MathUtils.pow(1071644672, 1071644672);
      assertFalse(int2 == int0);
      assertTrue(int2 == int1);
      assertEquals(0, int2);
      
      short short0 = MathUtils.sign((short)775);
      assertEquals((short)1, short0);
      
      double[] doubleArray2 = new double[4];
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 1071644672;
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = 0.5;
      doubleArray2[3] = (double) (-950);
      double[] doubleArray3 = MathUtils.copyOf(doubleArray2, 0);
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray3);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray3, doubleArray2);
      assertNotSame(doubleArray3, doubleArray0);
      assertNotSame(doubleArray3, doubleArray1);
      assertNotNull(doubleArray3);
      assertArrayEquals(new double[] {1.071644672E9, Double.NaN, 0.5, (-950.0)}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {}, doubleArray3, 0.01);
      assertEquals(4, doubleArray2.length);
      assertEquals(0, doubleArray3.length);
      
      double double1 = MathUtils.normalizeAngle(0.0, 0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      short short1 = MathUtils.sign((short)1);
      assertTrue(short1 == short0);
      assertEquals((short)1, short1);
      
      int int3 = MathUtils.lcm((int) (short)1, (-2022));
      assertFalse(int3 == int2);
      assertFalse(int3 == int0);
      assertFalse(int3 == int1);
      assertEquals(2022, int3);
      
      MathUtils.checkFinite(doubleArray3);
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray3));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray3);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray3, doubleArray2);
      assertNotSame(doubleArray3, doubleArray0);
      assertNotSame(doubleArray3, doubleArray1);
      assertArrayEquals(new double[] {1.071644672E9, Double.NaN, 0.5, (-950.0)}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {}, doubleArray3, 0.01);
      assertEquals(4, doubleArray2.length);
      assertEquals(0, doubleArray3.length);
      
      float[] floatArray0 = new float[7];
      floatArray0[0] = (float) 0;
      floatArray0[1] = Float.NaN;
      floatArray0[2] = (-3146.2534F);
      floatArray0[3] = (float) (-2022);
      floatArray0[4] = 0.5F;
      floatArray0[5] = (float) 1071644672;
      floatArray0[6] = (float) 0;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertFalse(boolean0);
      assertArrayEquals(new float[] {0.0F, Float.NaN, (-3146.2534F), (-2022.0F), 0.5F, 1.07164467E9F, 0.0F}, floatArray0, 0.01F);
      assertEquals(7, floatArray0.length);
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      int int0 = (-1561);
      float float0 = MathUtils.round(0.0F, (-1561));
      assertEquals(Float.NaN, float0, 0.01F);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) Float.NaN;
      doubleArray0[1] = (double) (-1561);
      doubleArray0[2] = (double) 0.0F;
      doubleArray0[3] = (double) 0.0F;
      doubleArray0[4] = 3730.108110139;
      doubleArray0[5] = (double) (-1561);
      doubleArray0[6] = (double) (-1561);
      doubleArray0[7] = (double) (-1561);
      doubleArray0[2] = (double) Float.NaN;
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, Double.NaN);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // Cannot normalize to NaN
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(8, 6);
      assertEquals(3.332204510175204, double0, 0.01);
      
      byte byte0 = MathUtils.indicator((byte) (-34));
      assertEquals((byte) (-1), byte0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) (byte) (-1);
      doubleArray0[5] = 3.332204510175204;
      doubleArray0[2] = (double) (byte) (-34);
      doubleArray0[3] = 2.0;
      doubleArray0[4] = 10.0;
      doubleArray0[5] = (double) (byte) (-1);
      doubleArray0[6] = (double) 6;
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {(-1.0), 0.0, (-34.0), 2.0, 10.0, (-1.0), 6.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      double double2 = MathUtils.binomialCoefficientLog(8, (-1));
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(819866847, int0);
      assertArrayEquals(new double[] {(-1.0), 0.0, (-34.0), 2.0, 10.0, (-1.0), 6.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      long long0 = MathUtils.gcd((long) (-1), (long) 6);
      assertEquals(1L, long0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = (int) (byte) (-34);
      int int1 = new Integer((byte) (-34));
      assertFalse(int1 == int0);
      assertEquals((-34), int1);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-1.0));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {(-0.05555555555555555), 0.0, (-1.8888888888888888), 0.1111111111111111, 0.5555555555555556, (-0.05555555555555555), 0.3333333333333333}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {(-1.0), 0.0, (-34.0), 2.0, 10.0, (-1.0), 6.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray1.length);
      assertEquals(7, doubleArray0.length);
      
      int int2 = MathUtils.gcd(8, 4053);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(1, int2);
      
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(4570447457359481746L, 1005L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-1560L), 1013L);
      assertEquals((-1580280L), long0);
      
      long long1 = MathUtils.pow(263L, 263L);
      assertFalse(long1 == long0);
      assertEquals((-4878295365420519433L), long1);
      
      // Undeclared exception!
      try { 
        MathUtils.lcm(263L, (-4878295365420519433L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      MathUtils.checkNotNull((Object) "");
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3.940510424527919E-20);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 2587.042528;
      doubleArray0[3] = (-718.602714);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(2684.991415675066, double0, 0.01);
      assertArrayEquals(new double[] {(-3.940510424527919E-20), 0.0, 2587.042528, (-718.602714)}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      
      double[][] doubleArray1 = new double[6][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-718.602714), (-3.940510424527919E-20), 0.0, 2587.042528}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      // Undeclared exception!
      try { 
        MathUtils.subAndCheck((-1068), 2147483646);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in subtraction: -1,068 - 2,147,483,646
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      int int0 = MathUtils.lcm((-1), (-1));
      assertEquals(1, int0);
      
      double[] doubleArray0 = null;
      int int1 = MathUtils.hash((double[]) null);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      boolean boolean0 = MathUtils.equals((float) 1, (float) 1, 156);
      assertTrue(boolean0);
      
      long long0 = MathUtils.indicator((long) 0);
      assertEquals(1L, long0);
      
      float float0 = MathUtils.indicator((float) (-1));
      assertEquals((-1.0F), float0, 0.01F);
      
      int int2 = 118;
      long long1 = MathUtils.pow(1084L, 118);
      assertFalse(long1 == long0);
      assertEquals(0L, long1);
      
      boolean boolean1 = MathUtils.equals((double[]) null, (double[]) null);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      boolean boolean2 = MathUtils.equals((-1.0F), (float) 156, 3900);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2);
      
      boolean boolean3 = MathUtils.equals((double) 156, (double) 118, 5);
      assertTrue(boolean3 == boolean2);
      assertFalse(boolean3 == boolean1);
      assertFalse(boolean3 == boolean0);
      assertFalse(boolean3);
      
      int int3 = MathUtils.subAndCheck(1, 3049);
      assertFalse(int3 == int0);
      assertFalse(int3 == int1);
      assertFalse(int3 == int2);
      assertEquals((-3048), int3);
      
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 8);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(1.0, 0.1666666505023083);
      assertFalse(boolean0);
      
      byte byte0 = (byte)40;
      byte byte1 = MathUtils.indicator((byte)40);
      assertFalse(byte1 == byte0);
      assertEquals((byte)1, byte1);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) (byte)40, 1060.7413F, (int) (byte)40);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
      
      double double1 = MathUtils.sinh(0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      int int0 = MathUtils.pow(0, 0);
      assertEquals(1, int0);
      
      double double2 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(0.0, double2, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      
      short short0 = MathUtils.sign((short) (-3170));
      assertEquals((short) (-1), short0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 1;
      floatArray0[1] = (float) (short) (-1);
      floatArray0[2] = 1927.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertArrayEquals(new float[] {1.0F, (-1.0F), 1927.0F}, floatArray0, 0.01F);
      assertEquals(3, floatArray0.length);
      
      long long1 = MathUtils.sign((long) 1);
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
      
      int int1 = MathUtils.mulAndCheck((-2028), (int) (short) (-1));
      assertFalse(int1 == int0);
      assertEquals(2028, int1);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) (short) (-3170), 0.0, 0.0);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      int int2 = MathUtils.hash((double) 1927.0F);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(1084103680, int2);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float) 1L, (-1.0F), (-5200.0F));
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      int[] intArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.distance1((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      int int0 = 4451;
      float float0 = MathUtils.round(0.0F, 4451);
      assertEquals(Float.NaN, float0, 0.01F);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) Float.NaN;
      doubleArray0[1] = 2.404307984052299E-9;
      doubleArray0[2] = (double) Float.NaN;
      doubleArray0[3] = (double) 0.0F;
      doubleArray0[4] = (double) 4451;
      doubleArray0[5] = (double) 4451;
      double[] doubleArray1 = new double[3];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) 0.0F;
      doubleArray1[1] = 2.404307984052299E-9;
      doubleArray1[2] = (double) 0.0F;
      // Undeclared exception!
      try { 
        MathUtils.distance(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 3
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      long long0 = 36L;
      long long1 = MathUtils.gcd(36L, 8L);
      assertFalse(long1 == long0);
      assertEquals(4L, long1);
      
      int int0 = (-1650);
      int int1 = MathUtils.sign((-1650));
      assertFalse(int1 == int0);
      assertEquals((-1), int1);
      
      double double0 = (-992.57042737534);
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1891.54729255982), (-992.57042737534), (-1744.228760535));
      assertFalse(boolean0);
      
      int[] intArray0 = new int[5];
      intArray0[0] = (-1650);
      intArray0[1] = (-1650);
      intArray0[2] = (-1650);
      int int2 = 6;
      intArray0[3] = 6;
      intArray0[4] = (-1650);
      int int3 = MathUtils.distance1(intArray0, intArray0);
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      assertEquals(0, int3);
      assertArrayEquals(new int[] {(-1650), (-1650), (-1650), 6, (-1650)}, intArray0);
      assertEquals(5, intArray0.length);
      
      long long2 = MathUtils.indicator(6402373705728000L);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(1L, long2);
      
      // Undeclared exception!
      try { 
        MathUtils.factorial((-1650));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1,650
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      int int0 = MathUtils.sign((-7));
      assertEquals((-1), int0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) (-7);
      doubleArray0[1] = (double) (-1);
      doubleArray0[2] = (double) (-7);
      doubleArray0[3] = (double) (-7);
      doubleArray0[4] = (double) (-7);
      doubleArray0[5] = (double) (-1);
      doubleArray0[6] = (double) (-1);
      doubleArray0[7] = (double) (-7);
      doubleArray0[8] = (double) (-7);
      int int1 = MathUtils.hash(doubleArray0);
      assertFalse(int1 == int0);
      assertEquals(1251045663, int1);
      assertArrayEquals(new double[] {(-7.0), (-1.0), (-7.0), (-7.0), (-7.0), (-1.0), (-1.0), (-7.0), (-7.0)}, doubleArray0, 0.01);
      assertEquals(9, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(0, (-2550));
      assertEquals(1.0, double0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(602.932908, 2251.5, 0.0);
      assertFalse(boolean0);
      
      float[] floatArray0 = new float[0];
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray0);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertEquals(0, floatArray0.length);
      
      long long0 = MathUtils.mulAndCheck((long) (-2550), 1880L);
      assertEquals((-4794000L), long0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      double double1 = MathUtils.round((double) 0, (-2550));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      double double2 = MathUtils.cosh(0.0);
      assertEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(1.0, double2, 0.01);
      
      long long1 = MathUtils.pow((-2644281811660520851L), 0);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      boolean boolean2 = MathUtils.equals((-622.7F), (float) 0);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2);
      
      byte byte0 = MathUtils.sign((byte) (-1));
      assertEquals((byte) (-1), byte0);
      
      double[] doubleArray0 = new double[0];
      double double3 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(0.0, double3, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      
      double double4 = MathUtils.sign(0.0);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(double4, double3, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(0.0, double4, 0.01);
      
      int int0 = MathUtils.subAndCheck((int) (byte) (-1), (-2424));
      assertEquals(2423, int0);
      
      float float0 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float0, 0.01F);
      
      boolean boolean3 = MathUtils.equals(0.0F, 667.6289F);
      assertFalse(boolean3 == boolean1);
      assertTrue(boolean3 == boolean2);
      assertTrue(boolean3 == boolean0);
      assertFalse(boolean3);
      
      int int1 = MathUtils.hash(1178.26);
      assertFalse(int1 == int0);
      assertEquals(2112015069, int1);
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      int[] intArray0 = new int[4];
      intArray0[0] = 348;
      intArray0[1] = 0;
      intArray0[2] = 100;
      intArray0[3] = (-2620);
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {348, 0, 100, (-2620)}, intArray0);
      assertArrayEquals(new int[] {348, 0, 100, (-2620)}, intArray1);
      assertEquals(4, intArray0.length);
      assertEquals(4, intArray1.length);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 348;
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = 0.0036;
      doubleArray0[4] = (double) (-2620);
      doubleArray0[5] = (double) 100;
      doubleArray0[6] = (double) (-2620);
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 348.0, 0.0, 0.0036, (-2620.0), 100.0, (-2620.0)}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      double double1 = MathUtils.binomialCoefficientLog(5057, 348);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1263.293473074046, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 3.834E-20;
      doubleArray0[1] = 0.5;
      doubleArray0[2] = 2.2250738585072014E-308;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = (-1401.7227360294341);
      doubleArray0[5] = 1.0;
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      float float0 = MathUtils.round((-3146.2534F), (-950));
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.hash(0.5);
      assertEquals(1071644672, int0);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      int[] intArray0 = new int[2];
      intArray0[0] = 1071644672;
      intArray0[1] = 1071644672;
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      assertArrayEquals(new int[] {1071644672, 1071644672}, intArray0);
      assertEquals(2, intArray0.length);
      
      int int2 = MathUtils.pow(1071644672, 1071644672);
      assertFalse(int2 == int0);
      assertTrue(int2 == int1);
      assertEquals(0, int2);
      
      short short0 = MathUtils.sign((short)775);
      assertEquals((short)1, short0);
      
      double[] doubleArray2 = new double[4];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      doubleArray2[0] = (double) 1071644672;
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = 0.5;
      doubleArray2[3] = (double) (-950);
      double[] doubleArray3 = MathUtils.copyOf(doubleArray2, 0);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray3);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray3, doubleArray1);
      assertNotSame(doubleArray3, doubleArray2);
      assertNotSame(doubleArray3, doubleArray0);
      assertNotNull(doubleArray3);
      assertArrayEquals(new double[] {1.071644672E9, Double.NaN, 0.5, (-950.0)}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {}, doubleArray3, 0.01);
      assertEquals(4, doubleArray2.length);
      assertEquals(0, doubleArray3.length);
      
      double double1 = MathUtils.normalizeAngle(0.0, 0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      short short1 = MathUtils.sign((short)1);
      assertTrue(short1 == short0);
      assertEquals((short)1, short1);
      
      int int3 = MathUtils.lcm((int) (short)1, (-2022));
      assertFalse(int3 == int0);
      assertFalse(int3 == int1);
      assertFalse(int3 == int2);
      assertEquals(2022, int3);
      
      // Undeclared exception!
      try { 
        MathUtils.equals((-2365.34F), (float) 0, 1071644672);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = Double.POSITIVE_INFINITY;
      doubleArray0[1] = 2.2250738585072014E-308;
      doubleArray0[2] = 153.88656668271;
      doubleArray0[3] = 197.1480607;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // value \u221E at index 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      long long0 = MathUtils.indicator(6L);
      assertEquals(1L, long0);
      
      long long1 = MathUtils.subAndCheck(1L, 6L);
      assertFalse(long1 == long0);
      assertEquals((-5L), long1);
      
      int[] intArray0 = new int[7];
      intArray0[0] = 437;
      intArray0[1] = 263;
      intArray0[2] = (-2621);
      intArray0[3] = 543;
      intArray0[4] = 324;
      intArray0[5] = 1720;
      intArray0[6] = 194;
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
      assertArrayEquals(new int[] {437, 263, (-2621), 543, 324, 1720, 194}, intArray0);
      assertEquals(7, intArray0.length);
      
      int int1 = MathUtils.pow(61, 322);
      assertFalse(int1 == int0);
      assertEquals(145020809, int1);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 543, (double) 194);
      assertFalse(boolean0);
      
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {437, 263, (-2621), 543, 324, 1720, 194}, intArray0);
      assertArrayEquals(new int[] {437, 263, (-2621), 543, 324, 1720, 194}, intArray1);
      assertEquals(7, intArray0.length);
      assertEquals(7, intArray1.length);
      
      // Undeclared exception!
      try { 
        MathUtils.lcm(2040463360, 6);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray0 = new double[3][5];
      doubleArray0[0] = null;
      doubleArray0[1] = null;
      doubleArray0[2] = null;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, mathUtils_OrderDirection0, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(117, 117);
      assertEquals(0, int0);
      
      int int1 = MathUtils.addAndCheck(Integer.MIN_VALUE, 261632);
      assertEquals((-2147222016), int1);
      assertFalse(int1 == int0);
      
      double double0 = MathUtils.binomialCoefficientLog(511, 117);
      assertEquals(271.7574751232736, double0, 0.01);
      
      int int2 = MathUtils.pow(117, 117);
      assertEquals(1460596133, int2);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      
      int int3 = MathUtils.subAndCheck(903, 1512);
      assertEquals((-609), int3);
      
      double double1 = MathUtils.factorialDouble(3777);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte) (-34));
      assertEquals((byte) (-1), byte0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[2] = (double) (byte) (-34);
      doubleArray0[3] = 2.0;
      doubleArray0[4] = 10.0;
      doubleArray0[5] = (double) (byte) (-1);
      doubleArray0[6] = (double) 6;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, (-34.0), 2.0, 10.0, (-1.0), 6.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      double double1 = MathUtils.binomialCoefficientLog(8, (-1));
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(619588831, int0);
      assertArrayEquals(new double[] {0.0, 0.0, (-34.0), 2.0, 10.0, (-1.0), 6.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      long long0 = MathUtils.gcd((long) (-1), (long) 6);
      assertEquals(1L, long0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = (int) (byte) (-34);
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      assertArrayEquals(new int[] {(-34)}, intArray0);
      assertEquals(1, intArray0.length);
      
      int[] intArray1 = new int[9];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = 0;
      intArray1[1] = (-449);
      intArray1[2] = 0;
      intArray1[3] = (-1);
      intArray1[4] = 619588831;
      intArray1[5] = 0;
      intArray1[6] = (int) (byte) (-1);
      intArray1[7] = (int) (byte) (-1);
      intArray1[8] = 0;
      int int2 = MathUtils.distance1(intArray0, intArray1);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(34, int2);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertArrayEquals(new int[] {(-34)}, intArray0);
      assertArrayEquals(new int[] {0, (-449), 0, (-1), 619588831, 0, (-1), (-1), 0}, intArray1);
      assertEquals(1, intArray0.length);
      assertEquals(9, intArray1.length);
      
      int int3 = MathUtils.distance1(intArray0, intArray0);
      assertTrue(int3 == int1);
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      assertFalse(intArray0.equals((Object)intArray1));
      assertEquals(0, int3);
      assertNotSame(intArray0, intArray1);
      assertArrayEquals(new int[] {(-34)}, intArray0);
      assertEquals(1, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      int[] intArray0 = new int[4];
      intArray0[0] = 348;
      intArray0[1] = 0;
      intArray0[2] = 121;
      intArray0[3] = (-2620);
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {348, 0, 121, (-2620)}, intArray0);
      assertArrayEquals(new int[] {348, 0, 121, (-2620)}, intArray1);
      assertEquals(4, intArray0.length);
      assertEquals(4, intArray1.length);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 348;
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = 0.0036;
      doubleArray0[4] = (double) (-2620);
      doubleArray0[5] = (double) 121;
      doubleArray0[6] = (double) (-2620);
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 348.0, 0.0, 0.0036, (-2620.0), 121.0, (-2620.0)}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      double[] doubleArray1 = new double[3];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) 348;
      doubleArray1[1] = (double) 0;
      doubleArray1[2] = (double) (-2620);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray1, mathUtils_OrderDirection0, false, false);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertTrue(boolean0);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {348.0, 0.0, (-2620.0)}, doubleArray1, 0.01);
      assertEquals(3, doubleArray1.length);
      
      double double1 = MathUtils.indicator((-2620.0));
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-1.0), double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 3.834E-20;
      doubleArray0[1] = 0.5;
      doubleArray0[2] = 2.2250738585072014E-308;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = (-1401.7227360294341);
      doubleArray0[5] = 1.0;
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      float float0 = MathUtils.round((-3146.2534F), (-950));
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.hash(0.5);
      assertEquals(1071644672, int0);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      int[] intArray0 = new int[2];
      intArray0[0] = 1071644672;
      intArray0[1] = 1071644672;
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      assertArrayEquals(new int[] {1071644672, 1071644672}, intArray0);
      assertEquals(2, intArray0.length);
      
      int int2 = MathUtils.pow(1071644672, 1071644672);
      assertTrue(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(0, int2);
      
      short short0 = MathUtils.sign((short)775);
      assertEquals((short)1, short0);
      
      double[] doubleArray2 = new double[4];
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 1071644672;
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = 0.5;
      doubleArray2[3] = (double) (-950);
      double[] doubleArray3 = MathUtils.copyOf(doubleArray2, 0);
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray3);
      assertNotSame(doubleArray3, doubleArray1);
      assertNotSame(doubleArray3, doubleArray0);
      assertNotSame(doubleArray3, doubleArray2);
      assertNotNull(doubleArray3);
      assertArrayEquals(new double[] {1.071644672E9, Double.NaN, 0.5, (-950.0)}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {}, doubleArray3, 0.01);
      assertEquals(4, doubleArray2.length);
      assertEquals(0, doubleArray3.length);
      
      double double1 = MathUtils.normalizeAngle(0.0, 0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      short short1 = MathUtils.sign((short)1);
      assertTrue(short1 == short0);
      assertEquals((short)1, short1);
      
      int int3 = MathUtils.lcm((int) (short)1, (-2022));
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      assertEquals(2022, int3);
      
      double[] doubleArray4 = new double[1];
      assertFalse(doubleArray4.equals((Object)doubleArray1));
      assertFalse(doubleArray4.equals((Object)doubleArray0));
      assertFalse(doubleArray4.equals((Object)doubleArray3));
      assertFalse(doubleArray4.equals((Object)doubleArray2));
      
      doubleArray4[0] = 2.2250738585072014E-308;
      double double2 = MathUtils.distanceInf(doubleArray4, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray0.equals((Object)doubleArray4));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray3));
      assertFalse(doubleArray4.equals((Object)doubleArray1));
      assertFalse(doubleArray4.equals((Object)doubleArray0));
      assertFalse(doubleArray4.equals((Object)doubleArray3));
      assertFalse(doubleArray4.equals((Object)doubleArray2));
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(3.834E-20, double2, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray4);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray3);
      assertNotSame(doubleArray4, doubleArray1);
      assertNotSame(doubleArray4, doubleArray0);
      assertNotSame(doubleArray4, doubleArray3);
      assertNotSame(doubleArray4, doubleArray2);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {2.2250738585072014E-308}, doubleArray4, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(1, doubleArray4.length);
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 3.834E-20;
      doubleArray0[1] = 0.5;
      doubleArray0[2] = 2.2250738585072014E-308;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = (-1401.7227360294341);
      doubleArray0[5] = 1.0;
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      float float0 = MathUtils.round((-3146.2534F), (-950));
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.hash(0.5);
      assertEquals(1071644672, int0);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.5, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      int[] intArray0 = new int[2];
      intArray0[0] = 1071644672;
      intArray0[1] = 1071644672;
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      assertArrayEquals(new int[] {1071644672, 1071644672}, intArray0);
      assertEquals(2, intArray0.length);
      
      int int2 = MathUtils.pow(1071644672, 1071644672);
      assertTrue(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(0, int2);
      
      short short0 = MathUtils.sign((short)775);
      assertEquals((short)1, short0);
      
      double[] doubleArray2 = new double[4];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      doubleArray2[0] = (double) 1071644672;
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = 0.5;
      doubleArray2[3] = (double) (-950);
      double[] doubleArray3 = MathUtils.copyOf(doubleArray2, 0);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray3);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray3, doubleArray2);
      assertNotSame(doubleArray3, doubleArray1);
      assertNotSame(doubleArray3, doubleArray0);
      assertNotNull(doubleArray3);
      assertArrayEquals(new double[] {1.071644672E9, Double.NaN, 0.5, (-950.0)}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {}, doubleArray3, 0.01);
      assertEquals(4, doubleArray2.length);
      assertEquals(0, doubleArray3.length);
      
      double double1 = MathUtils.normalizeAngle(0.0, 0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      short short1 = MathUtils.sign((short)1);
      assertTrue(short1 == short0);
      assertEquals((short)1, short1);
      
      int int3 = MathUtils.lcm((int) (short)1, (-2022));
      assertFalse(int3 == int1);
      assertFalse(int3 == int2);
      assertFalse(int3 == int0);
      assertEquals(2022, int3);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray2, mathUtils_OrderDirection0, false, false);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray3));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(boolean0);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray3);
      assertNotSame(doubleArray2, doubleArray1);
      assertArrayEquals(new double[] {1.071644672E9, Double.NaN, 0.5, (-950.0)}, doubleArray2, 0.01);
      assertEquals(4, doubleArray2.length);
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      MathUtils.checkNotNull((Object) "");
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3.940510424527919E-20);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 2587.042528;
      doubleArray0[3] = (-718.602714);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(2684.991415675066, double0, 0.01);
      assertArrayEquals(new double[] {(-3.940510424527919E-20), 0.0, 2587.042528, (-718.602714)}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      
      double[][] doubleArray1 = new double[6][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-718.602714), (-3.940510424527919E-20), 0.0, 2587.042528}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      short short0 = MathUtils.indicator((short)439);
      assertEquals((short)1, short0);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {(-718.602714), (-3.940510424527919E-20), 0.0, 2587.042528}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 0.058823529411764705;
      doubleArray0[1] = 884.6;
      doubleArray0[2] = (-301.6398);
      doubleArray0[3] = 1.304E19;
      doubleArray0[4] = 3.834E-20;
      doubleArray0[5] = 0.875;
      doubleArray0[6] = (-74.0076031);
      double[] doubleArray1 = new double[9];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 1.304E19;
      doubleArray1[1] = 3.834E-20;
      doubleArray1[2] = (-74.0076031);
      doubleArray1[3] = 2503.55143105494;
      doubleArray1[4] = 884.6;
      doubleArray1[5] = 1.304E19;
      doubleArray1[6] = (-301.6398);
      doubleArray1[7] = (-74.0076031);
      doubleArray1[8] = 1.304E19;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(3.912E19, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.058823529411764705, 884.6, (-301.6398), 1.304E19, 3.834E-20, 0.875, (-74.0076031)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.304E19, 3.834E-20, (-74.0076031), 2503.55143105494, 884.6, 1.304E19, (-301.6398), (-74.0076031), 1.304E19}, doubleArray1, 0.01);
      assertEquals(7, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 3.834E-20;
      doubleArray0[2] = 2.2250738585072014E-308;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = (-1401.7227360294341);
      doubleArray0[5] = 1.0;
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.0, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      int int0 = (-950);
      float float0 = MathUtils.round((-3146.2534F), (-950));
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int1 = MathUtils.hash(0.5);
      assertFalse(int1 == int0);
      assertEquals(1071644672, int1);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {3.834E-20, 0.0, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {3.834E-20, 0.0, 2.2250738585072014E-308, 0.0, (-1401.7227360294341), 1.0}, doubleArray1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      int[] intArray0 = new int[2];
      intArray0[0] = (-950);
      intArray0[1] = 1071644672;
      int int2 = MathUtils.distanceInf(intArray0, intArray0);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(0, int2);
      assertArrayEquals(new int[] {(-950), 1071644672}, intArray0);
      assertEquals(2, intArray0.length);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(1071644672, (-950));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-950)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 1.304E19;
      doubleArray0[1] = 16.0;
      doubleArray0[2] = 3.26E18;
      doubleArray0[3] = 0.097;
      doubleArray0[4] = (-833.718);
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {1.304E19, 16.0, 3.26E18, 0.097, (-833.718)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.304E19, 16.0, 3.26E18, 0.097, (-833.718)}, doubleArray1, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null, 1303);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      long long0 = MathUtils.pow((-1003L), 4194304);
      assertEquals((-1836273450054844415L), long0);
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      MathUtils.checkFinite(1100.9762759);
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      int int0 = bigInteger0.getLowestSetBit();
      assertEquals(1, int0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      int int1 = bigInteger0.bitCount();
      assertFalse(int1 == int0);
      assertEquals(2, int1);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-5034868814120038111L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-5,034,868,814,120,038,111)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      long long0 = MathUtils.indicator(0L);
      assertEquals(1L, long0);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = bigInteger0.nextProbablePrime();
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)2, bigInteger1.shortValue());
      assertEquals((byte)2, bigInteger1.byteValue());
      
      String string0 = bigInteger0.toString((-1211));
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertEquals("1", string0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotNull(string0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      String string1 = bigInteger0.toString(248);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertTrue(string1.equals((Object)string0));
      assertEquals("1", string1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotNull(string1);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      BigInteger bigInteger2 = bigInteger0.abs();
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger0);
      assertNotNull(bigInteger2);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      
      String string2 = bigInteger0.toString(65);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertTrue(string2.equals((Object)string1));
      assertTrue(string2.equals((Object)string0));
      assertEquals("1", string2);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertNotNull(string2);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      BigInteger bigInteger3 = bigInteger0.nextProbablePrime();
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertSame(bigInteger3, bigInteger1);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertNotNull(bigInteger3);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((byte)2, bigInteger3.byteValue());
      assertEquals((short)2, bigInteger3.shortValue());
      
      BigInteger bigInteger4 = MathUtils.pow(bigInteger0, 3746L);
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertTrue(bigInteger4.equals((Object)bigInteger0));
      assertFalse(bigInteger4.equals((Object)bigInteger3));
      assertFalse(bigInteger4.equals((Object)bigInteger1));
      assertTrue(bigInteger4.equals((Object)bigInteger2));
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger4);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger4, bigInteger3);
      assertNotSame(bigInteger4, bigInteger1);
      assertNotSame(bigInteger4, bigInteger2);
      assertNotSame(bigInteger4, bigInteger0);
      assertNotNull(bigInteger4);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger4.shortValue());
      assertEquals((byte)1, bigInteger4.byteValue());
      
      float float0 = MathUtils.sign((-2424.1548F));
      assertEquals((-1.0F), float0, 0.01F);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 3746L;
      doubleArray0[1] = (double) 65;
      doubleArray0[2] = (double) 65;
      doubleArray0[3] = 1648.567092;
      doubleArray0[4] = (double) 3746L;
      doubleArray0[5] = (double) (-2424.1548F);
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {3746.0, 65.0, 65.0, 1648.567092, 3746.0, (-2424.15478515625)}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      long long1 = MathUtils.pow(4669953537629749248L, 19);
      assertFalse(long1 == long0);
      assertEquals(0L, long1);
      
      double double1 = MathUtils.sign(3746.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      double double2 = MathUtils.binomialCoefficientLog(65, 19);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(37.05012752972103, double2, 0.01);
      
      int[] intArray0 = new int[4];
      intArray0[0] = 2;
      intArray0[1] = 248;
      intArray0[2] = 19;
      intArray0[3] = (-1211);
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertArrayEquals(new int[] {2, 248, 19, (-1211)}, intArray0);
      assertEquals(4, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      int int0 = MathUtils.hash(0.0);
      assertEquals(0, int0);
      
      double double0 = MathUtils.log(16.0, 0.0);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      
      double double1 = MathUtils.cosh(0.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      byte byte0 = MathUtils.indicator((byte)0);
      assertEquals((byte)1, byte0);
      
      double double2 = MathUtils.round(0.0, 0, 0);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      boolean boolean0 = MathUtils.equals((-2225.661959866106), (double) 0, (-2225.661959866106));
      assertFalse(boolean0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = bigInteger0.setBit(0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = bigInteger0.pow(63);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger0);
      assertNotNull(bigInteger2);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger1, bigInteger0);
      assertSame(bigInteger3, bigInteger2);
      assertSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotNull(bigInteger3);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger3.shortValue());
      assertEquals((byte)0, bigInteger3.byteValue());
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.0;
      int int1 = MathUtils.hash(doubleArray0);
      assertFalse(int1 == int0);
      assertEquals(31, int1);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      double[][] doubleArray1 = new double[5][2];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      double[] doubleArray2 = new double[0];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray1[3] = doubleArray2;
      double[] doubleArray3 = new double[4];
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      
      doubleArray3[0] = (double) 176;
      doubleArray3[1] = 0.0;
      doubleArray3[2] = (double) 176;
      doubleArray3[3] = (double) 0;
      doubleArray1[4] = doubleArray3;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 0 != 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-3399L), (-1381L));
      assertEquals(4694019L, long0);
      
      short short0 = (short)229;
      short short1 = MathUtils.indicator((short)229);
      assertFalse(short1 == short0);
      assertEquals((short)1, short1);
      
      float float0 = MathUtils.indicator((float) (short)1);
      assertEquals(1.0F, float0, 0.01F);
      
      int int0 = MathUtils.mulAndCheck((-285), (-975));
      assertEquals(277875, int0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = bigInteger0.shiftLeft((short)229);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      short short2 = bigInteger0.shortValueExact();
      assertFalse(short2 == short1);
      assertFalse(short2 == short0);
      assertEquals((short)0, short2);
      assertSame(bigInteger0, bigInteger1);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, bigInteger0);
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotNull(bigInteger2);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      
      boolean boolean0 = bigInteger0.testBit((short)229);
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(boolean0);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger3 = bigInteger2.add(bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger3);
      assertNotSame(bigInteger3, bigInteger1);
      assertSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertNotNull(bigInteger3);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((short)1, bigInteger3.shortValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      
      BigInteger bigInteger4 = bigInteger0.abs();
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger4.equals((Object)bigInteger3));
      assertFalse(bigInteger4.equals((Object)bigInteger2));
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertSame(bigInteger0, bigInteger4);
      assertSame(bigInteger4, bigInteger1);
      assertSame(bigInteger4, bigInteger0);
      assertNotSame(bigInteger4, bigInteger3);
      assertNotSame(bigInteger4, bigInteger2);
      assertNotNull(bigInteger4);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger4.shortValue());
      assertEquals((byte)0, bigInteger4.byteValue());
      
      int int1 = bigInteger2.getLowestSetBit();
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger4));
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger3);
      assertNotSame(bigInteger2, bigInteger4);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      
      BigInteger bigInteger5 = bigInteger0.modInverse(bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger4));
      assertFalse(bigInteger5.equals((Object)bigInteger2));
      assertFalse(bigInteger5.equals((Object)bigInteger3));
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger5);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger2, bigInteger5);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger3);
      assertNotSame(bigInteger2, bigInteger4);
      assertNotSame(bigInteger5, bigInteger2);
      assertSame(bigInteger5, bigInteger4);
      assertSame(bigInteger5, bigInteger1);
      assertSame(bigInteger5, bigInteger0);
      assertNotSame(bigInteger5, bigInteger3);
      assertNotNull(bigInteger5);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((short)0, bigInteger5.shortValue());
      assertEquals((byte)0, bigInteger5.byteValue());
      
      double double0 = MathUtils.log(864.0, 1.0);
      assertEquals(0.0, double0, 0.01);
      
      long long1 = MathUtils.pow((long) (short)229, (long) (short)0);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (double) 277875;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      float float0 = bigInteger0.floatValue();
      assertEquals(1.0F, float0, 0.01F);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      int int0 = 1526;
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 1526;
      doubleArray0[1] = (double) 1526;
      doubleArray0[2] = (double) 1526;
      doubleArray0[3] = (double) 1.0F;
      doubleArray0[4] = (double) 1526;
      doubleArray0[5] = (double) 1.0F;
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {1526.0, 1526.0, 1526.0, 1.0, 1526.0, 1.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      BigInteger bigInteger1 = bigInteger0.nextProbablePrime();
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)2, bigInteger1.byteValue());
      assertEquals((short)2, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger0.clearBit(1526);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertTrue(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotNull(bigInteger2);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      
      float float1 = bigInteger0.floatValue();
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertTrue(bigInteger0.equals((Object)bigInteger2));
      assertEquals(float1, float0, 0.01F);
      assertEquals(1.0F, float1, 0.01F);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      int int1 = 1056;
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, 1056);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertTrue(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertTrue(bigInteger3.equals((Object)bigInteger2));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger3, bigInteger2);
      assertSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotNull(bigInteger3);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      assertEquals((short)1, bigInteger3.shortValue());
      
      double[] doubleArray1 = new double[2];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) 1056;
      doubleArray1[1] = (double) 1056;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray1, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (1,056 >= 1,056)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      int int0 = MathUtils.sign(514);
      assertEquals(1, int0);
      
      double double0 = MathUtils.binomialCoefficientLog(1, 1);
      assertEquals(0.0, double0, 0.01);
      
      float float0 = MathUtils.round((float) 1, 514);
      assertEquals(Float.NaN, float0, 0.01F);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, (float) 1);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (double) 1;
      doubleArray0[3] = (double) 514;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (double) 1;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 514.0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 1.0, 514.0, 0.0, 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.9961240310077519, 512.0077519379845, 0.0, 0.9961240310077519}, doubleArray1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      float[] floatArray0 = new float[8];
      floatArray0[0] = (-2632.1F);
      floatArray0[1] = (float) 1;
      floatArray0[2] = Float.NaN;
      floatArray0[3] = Float.NaN;
      floatArray0[4] = (float) 1;
      floatArray0[5] = (float) 514;
      floatArray0[6] = (float) 514;
      floatArray0[7] = (float) 1;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      assertArrayEquals(new float[] {(-2632.1F), 1.0F, Float.NaN, Float.NaN, 1.0F, 514.0F, 514.0F, 1.0F}, floatArray0, 0.01F);
      assertEquals(8, floatArray0.length);
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[1] = 0.08371849358081818;
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.08371849358081818}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {0.0, 0.08371849358081818}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      int int0 = 16352;
      float float0 = MathUtils.round((-3.61655845E18F), 16352);
      assertEquals(Float.NaN, float0, 0.01F);
      
      float[] floatArray0 = new float[6];
      floatArray0[0] = (float) 16352;
      floatArray0[1] = Float.NaN;
      floatArray0[3] = 0.0F;
      floatArray0[4] = (float) 16352;
      floatArray0[5] = (-3.61655845E18F);
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      assertArrayEquals(new float[] {16352.0F, Float.NaN, 0.0F, 0.0F, 16352.0F, (-3.61655845E18F)}, floatArray0, 0.01F);
      assertEquals(6, floatArray0.length);
      
      int int1 = 9;
      long long0 = MathUtils.pow((long) 16352, 9);
      assertEquals(162094402213249024L, long0);
      
      long long1 = MathUtils.pow(162094402213249024L, 9);
      assertFalse(long1 == long0);
      assertEquals(0L, long1);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog(9, 16352);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 16,352, n = 9
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, (-2570.3486F));
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) (-2570.3486F);
      doubleArray0[1] = (double) (-2570.3486F);
      double[][] doubleArray1 = new double[1][3];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-2570.3486328125), (-2570.3486328125)}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
      
      int int0 = MathUtils.indicator(1283);
      assertEquals(1, int0);
      
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {(-2570.3486328125), (-2570.3486328125)}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      double[] doubleArray2 = new double[5];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 0.0;
      doubleArray2[1] = (-1735.6844497884535);
      doubleArray2[2] = (double) 1;
      doubleArray2[3] = (double) 1L;
      doubleArray2[4] = (double) (-2570.3486F);
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray2);
      assertTrue(boolean1 == boolean0);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(boolean1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertArrayEquals(new double[] {(-2570.3486328125), (-2570.3486328125)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-1735.6844497884535), 1.0, 1.0, (-2570.3486328125)}, doubleArray2, 0.01);
      assertEquals(2, doubleArray0.length);
      assertEquals(5, doubleArray2.length);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance1((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      long long0 = MathUtils.sign((-925L));
      assertEquals((-1L), long0);
      
      int int0 = (-3765);
      int int1 = (-1624);
      int int2 = MathUtils.gcd((-3765), (-1624));
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(1, int2);
      
      int int3 = 67;
      float float0 = new Integer((-3765));
      assertEquals((-3765), float0, 0.01F);
      
      // Undeclared exception!
      try { 
        MathUtils.distance((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      long long0 = 25L;
      long long1 = MathUtils.mulAndCheck(25L, 25L);
      assertFalse(long1 == long0);
      assertEquals(625L, long1);
      
      int int0 = 13;
      double double0 = MathUtils.binomialCoefficientLog(13, 13);
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 13;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (double) 25L;
      doubleArray0[3] = (double) 625L;
      doubleArray0[4] = 3.834E-20;
      doubleArray0[5] = (double) 13;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {13.0, 0.0, 25.0, 625.0, 3.834E-20, 13.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      double[] doubleArray1 = null;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      float float0 = MathUtils.round((-1.4E-45F), 0);
      assertEquals(-0.0F, float0, 0.01F);
      
      int int0 = MathUtils.indicator(0);
      assertEquals(1, int0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) -0.0F;
      doubleArray0[1] = 10.0;
      doubleArray0[2] = (double) -0.0F;
      doubleArray0[3] = (double) (-1.4E-45F);
      doubleArray0[4] = 534.73932705244;
      doubleArray0[5] = (double) 0;
      doubleArray0[6] = (double) 0;
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {-0.0, 10.0, -0.0, (-1.401298464324817E-45), 534.73932705244, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      MathUtils.checkFinite(0.0);
      boolean boolean0 = MathUtils.equalsIncludingNaN(3815.972F, 3815.972F);
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 3815.972F;
      doubleArray0[1] = (double) 3815.972F;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (double) 3815.972F;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 0.0;
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      assertArrayEquals(new double[] {3815.971923828125, 3815.971923828125, 0.0, 3815.971923828125, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      float float0 = (-842.24F);
      boolean boolean2 = MathUtils.equalsIncludingNaN((-842.24F), 3815.972F);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
      
      BigInteger bigInteger0 = null;
      int int0 = 0;
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-2914.34F), (-2914.34F), (-2914.34F));
      assertTrue(boolean0);
      
      short short0 = MathUtils.indicator((short) (-4394));
      assertEquals((short) (-1), short0);
      
      int int0 = MathUtils.sign(621);
      assertEquals(1, int0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (double) (short) (-1);
      doubleArray0[2] = (double) 1;
      doubleArray0[3] = (double) (-2914.34F);
      doubleArray0[4] = (double) (short) (-1);
      doubleArray0[5] = (double) (short) (-1);
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {0.0, (-1.0), 1.0, (-2914.340087890625), (-1.0), (-1.0)}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      double[] doubleArray1 = new double[6];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) (-2914.34F);
      doubleArray1[1] = 0.0;
      doubleArray1[2] = (double) 1;
      doubleArray1[3] = (double) 1;
      doubleArray1[4] = (double) 621;
      doubleArray1[5] = 1970.5660688066325;
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray1);
      assertFalse(boolean1 == boolean0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, (-1.0), 1.0, (-2914.340087890625), (-1.0), (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-2914.340087890625), 0.0, 1.0, 1.0, 621.0, 1970.5660688066325}, doubleArray1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      long long0 = MathUtils.factorial(1);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      float[] floatArray0 = new float[1];
      floatArray0[0] = 1.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertArrayEquals(new float[] {1.0F}, floatArray0, 0.01F);
      assertEquals(1, floatArray0.length);
      
      long long0 = MathUtils.pow((-1L), 1163L);
      assertEquals((-1L), long0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = 0;
      int[] intArray1 = new int[1];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = 0;
      int int0 = MathUtils.distanceInf(intArray0, intArray1);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(0, int0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertArrayEquals(new int[] {0}, intArray0);
      assertArrayEquals(new int[] {0}, intArray1);
      assertEquals(1, intArray0.length);
      assertEquals(1, intArray1.length);
      
      MathUtils.checkFinite(1.0);
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
      
      long long1 = MathUtils.subAndCheck((-1L), 5040L);
      assertFalse(long1 == long0);
      assertEquals((-5041L), long1);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = (double) 1.0F;
      doubleArray0[4] = (double) 5040L;
      doubleArray0[5] = (double) 0;
      doubleArray0[6] = (double) (-1L);
      doubleArray0[7] = 0.0;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 1.0, 5040.0, 0.0, (-1.0), 0.0}, doubleArray0, 0.01);
      assertEquals(0, doubleArray1.length);
      assertEquals(8, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      int int0 = MathUtils.pow(0, 0L);
      assertEquals(1, int0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 0;
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, 1.304E19);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      int int0 = (-1172);
      // Undeclared exception!
      try { 
        MathUtils.factorialLog((-1172));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1,172
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (-0.2499999701976776);
      doubleArray0[2] = 423.1;
      doubleArray0[3] = (-1.0);
      doubleArray0[4] = 1261.111517;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = 0.0;
      doubleArray0[7] = 1.0;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, (-0.2499999701976776), 423.1, (-1.0), 1261.111517, 0.0, 0.0, 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-0.2499999701976776), 423.1, (-1.0), 1261.111517, 0.0, 0.0, 1.0}, doubleArray1, 0.01);
      assertEquals(8, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      
      int int0 = MathUtils.hash(doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals((-1942785212), int0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, (-0.2499999701976776), 423.1, (-1.0), 1261.111517, 0.0, 0.0, 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-0.2499999701976776), 423.1, (-1.0), 1261.111517, 0.0, 0.0, 1.0}, doubleArray1, 0.01);
      assertEquals(8, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      
      MathUtils.checkFinite(1261.111517);
      boolean boolean0 = MathUtils.equals((float) (-1942785212), (float) (-1942785212));
      assertTrue(boolean0);
      
      long long0 = MathUtils.binomialCoefficient(230, (-1942785212));
      assertEquals(1L, long0);
      
      long long1 = MathUtils.mulAndCheck((long) 230, 1L);
      assertFalse(long1 == long0);
      assertEquals(230L, long1);
      
      double[][] doubleArray2 = new double[0][1];
      MathUtils.sortInPlace(doubleArray1, doubleArray2);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, (-0.2499999701976776), 423.1, (-1.0), 1261.111517, 0.0, 0.0, 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-1.0), (-0.2499999701976776), 0.0, 0.0, 0.0, 1.0, 423.1, 1261.111517}, doubleArray1, 0.01);
      assertEquals(8, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertEquals(0, doubleArray2.length);
      
      MathUtils.checkFinite((-1531.0));
      double[] doubleArray3 = MathUtils.copyOf(doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray3);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray3, doubleArray1);
      assertNotSame(doubleArray3, doubleArray0);
      assertNotSame(doubleArray1, doubleArray3);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray3);
      assertArrayEquals(new double[] {0.0, (-0.2499999701976776), 423.1, (-1.0), 1261.111517, 0.0, 0.0, 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-1.0), (-0.2499999701976776), 0.0, 0.0, 0.0, 1.0, 423.1, 1261.111517}, doubleArray3, 0.01);
      assertArrayEquals(new double[] {(-1.0), (-0.2499999701976776), 0.0, 0.0, 0.0, 1.0, 423.1, 1261.111517}, doubleArray1, 0.01);
      assertEquals(8, doubleArray0.length);
      assertEquals(8, doubleArray3.length);
      assertEquals(8, doubleArray1.length);
      
      long long2 = MathUtils.sign(1L);
      assertTrue(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(1L, long2);
      
      // Undeclared exception!
      try { 
        MathUtils.round(0.0F, (-1942785212), 81);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 81, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)0);
      assertEquals((byte)0, byte0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow((long) (byte)0, (-1595L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,595)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(694.03F, (-517.8912F), (-517.8912F));
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 694.03F;
      doubleArray0[1] = (double) 694.03F;
      doubleArray0[2] = (double) 694.03F;
      doubleArray0[3] = (double) 694.03F;
      doubleArray0[4] = (double) (-517.8912F);
      double[][] doubleArray1 = new double[6][5];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-517.8911743164062), 694.030029296875, 694.030029296875, 694.030029296875, 694.030029296875}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(1481.5269132547376, double0, 0.01);
      assertArrayEquals(new double[] {(-517.8911743164062), 694.030029296875, 694.030029296875, 694.030029296875, 694.030029296875}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(149.6266F, (-517.8912F), 149.6266F);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      int int0 = MathUtils.subAndCheck((-685), (-2219));
      assertEquals(1534, int0);
      
      double double1 = MathUtils.round(694.030029296875, (-2799), 0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      
      int int1 = MathUtils.pow(3295, 4261L);
      assertFalse(int1 == int0);
      assertEquals((-818387873), int1);
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      float float0 = MathUtils.round(968.9156F, 3, 3);
      assertEquals(968.915F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-1441.29);
      doubleArray0[1] = 2.0;
      doubleArray0[2] = (-58.4918);
      doubleArray0[3] = 248.9175939415102;
      doubleArray0[4] = 0.167;
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {(-1441.29), 2.0, (-58.4918), 248.9175939415102, 0.167}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      double[] doubleArray1 = new double[1];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 2.0;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {(-1441.29), 2.0, (-58.4918), 248.9175939415102, 0.167}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {2.0}, doubleArray1, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      
      long long0 = MathUtils.subAndCheck(0L, 5352L);
      assertEquals((-5352L), long0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray1, doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {2.0}, doubleArray1, 0.01);
      assertEquals(1, doubleArray1.length);
      
      long long1 = MathUtils.indicator((-5352L));
      assertFalse(long1 == long0);
      assertEquals((-1L), long1);
      
      int int0 = (-3326);
      int int1 = 9;
      // Undeclared exception!
      try { 
        MathUtils.round((-546.36650990826), (-3326), 9);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, (-2974.2668F));
      assertFalse(boolean0);
      
      double double0 = MathUtils.sinh(1951.747);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      int int0 = (-1);
      int int1 = MathUtils.lcm((-1), (-1));
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      int int2 = 492;
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (-1), (float) 1, 492);
      assertFalse(boolean0);
      
      MathUtils.checkFinite((double) 1);
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) (-1);
      doubleArray0[1] = (double) (-1);
      doubleArray0[2] = 3191.8;
      doubleArray0[3] = (double) 492;
      doubleArray0[4] = 0.0;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      MathUtils.checkFinite((-1874.9459241052));
      short short0 = MathUtils.indicator((short)0);
      assertEquals((short)1, short0);
      
      double[] doubleArray0 = new double[0];
      double[][] doubleArray1 = new double[9][1];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      double[] doubleArray2 = new double[9];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) (short)1;
      doubleArray2[1] = (-1874.9459241052);
      doubleArray2[2] = (double) (short)1;
      doubleArray2[3] = (-1874.9459241052);
      doubleArray2[4] = (double) (short)1;
      doubleArray2[5] = (double) (short)1;
      doubleArray2[6] = (double) (short)1;
      doubleArray2[7] = (-1874.9459241052);
      doubleArray2[8] = (double) (short)1;
      doubleArray1[2] = doubleArray2;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      doubleArray1[8] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertNotSame(doubleArray0, doubleArray2);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      
      // Undeclared exception!
      try { 
        MathUtils.factorial((-943));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -943
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-1.0), (-1.0));
      assertTrue(boolean0);
      
      int int0 = (-520);
      int int1 = 2636;
      int int2 = MathUtils.pow((-520), 2636);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(0, int2);
      
      int int3 = 63;
      int int4 = MathUtils.subAndCheck(186, 63);
      assertFalse(int4 == int3);
      assertFalse(int4 == int2);
      assertFalse(int4 == int0);
      assertFalse(int4 == int1);
      assertEquals(123, int4);
      
      float[] floatArray0 = new float[2];
      floatArray0[0] = 175.76149F;
      floatArray0[1] = (float) 123;
      float[] floatArray1 = new float[2];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = (float) 0;
      floatArray1[1] = (float) 0;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertArrayEquals(new float[] {175.76149F, 123.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {0.0F, 0.0F}, floatArray1, 0.01F);
      assertEquals(2, floatArray0.length);
      assertEquals(2, floatArray1.length);
      
      boolean boolean2 = MathUtils.equals((-1.0), (double) 0.0F, 2087.35);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-520), (-1816));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -520
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 211.3393551040776;
      doubleArray0[1] = (-57.3315586147);
      doubleArray0[2] = 0.5;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {211.3393551040776, (-57.3315586147), 0.5}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {211.3393551040776, (-57.3315586147), 0.5}, doubleArray1, 0.01);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      
      int int0 = MathUtils.subAndCheck(0, 0);
      assertEquals(0, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 0, 1.0F);
      assertFalse(boolean0);
      
      MathUtils.checkFinite(doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertNotSame(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {211.3393551040776, (-57.3315586147), 0.5}, doubleArray0, 0.01);
      assertEquals(3, doubleArray0.length);
      
      byte[] byteArray0 = new byte[6];
      byteArray0[0] = (byte) (-63);
      byteArray0[1] = (byte)0;
      byteArray0[2] = (byte) (-83);
      byteArray0[3] = (byte)0;
      byteArray0[4] = (byte)116;
      byteArray0[5] = (byte)87;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((short)29783, bigInteger0.shortValue());
      assertEquals((byte)87, bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte) (-63), (byte)0, (byte) (-83), (byte)0, (byte)116, (byte)87}, byteArray0);
      assertEquals(6, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (long) (byte)116);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotNull(bigInteger1);
      assertEquals((short)18337, bigInteger1.shortValue());
      assertEquals((byte) (-95), bigInteger1.byteValue());
      assertEquals((short)29783, bigInteger0.shortValue());
      assertEquals((byte)87, bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte) (-63), (byte)0, (byte) (-83), (byte)0, (byte)116, (byte)87}, byteArray0);
      assertEquals(6, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      short short0 = MathUtils.indicator((short) (-762));
      assertEquals((short) (-1), short0);
      
      boolean boolean0 = MathUtils.equals((double) (short) (-762), 4.0);
      assertFalse(boolean0);
      
      int int0 = MathUtils.lcm((int) (short) (-1), (int) (short) (-762));
      assertEquals(762, int0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = BigInteger.TEN;
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      
      long long0 = bigInteger1.longValue();
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals(10L, long0);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger0.min(bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotNull(bigInteger2);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      
      int int1 = bigInteger0.intValueExact();
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger3 = bigInteger0.not();
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger3);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertNotNull(bigInteger3);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte) (-1), bigInteger3.byteValue());
      assertEquals((short) (-1), bigInteger3.shortValue());
      
      int int2 = bigInteger1.signum();
      assertFalse(bigInteger1.equals((Object)bigInteger3));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(1, int2);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      
      BigInteger bigInteger4 = MathUtils.pow(bigInteger0, 762);
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger4.equals((Object)bigInteger1));
      assertFalse(bigInteger4.equals((Object)bigInteger3));
      assertSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger0, bigInteger3);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger4, bigInteger0);
      assertNotSame(bigInteger4, bigInteger1);
      assertNotSame(bigInteger4, bigInteger3);
      assertSame(bigInteger4, bigInteger2);
      assertNotNull(bigInteger4);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger4.shortValue());
      assertEquals((byte)0, bigInteger4.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN((double) 10L, (double) 0, (int) (short) (-762));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      double double0 = MathUtils.round(Double.NaN, 4194304, 4194304);
      assertEquals(Double.NaN, double0, 0.01);
      
      int int0 = 1332;
      // Undeclared exception!
      try { 
        MathUtils.factorial(1332);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      long long0 = 1570L;
      long long1 = MathUtils.pow((-953L), 1570L);
      assertFalse(long1 == long0);
      assertEquals(1508636153931689649L, long1);
      
      double double0 = MathUtils.sign(0.0);
      assertEquals(0.0, double0, 0.01);
      
      float float0 = MathUtils.round(1.0F, (-821));
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.indicator((-2204));
      assertEquals((-1), int0);
      
      int int1 = 1539;
      int int2 = MathUtils.pow(1539, 4577762542105553359L);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals((-1895934613), int2);
      
      double double1 = 0.0;
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, 0.0, Double.NEGATIVE_INFINITY);
      assertTrue(boolean0);
      
      float float1 = MathUtils.round((float) (-821), (-122));
      assertEquals(float1, float0, 0.01F);
      assertEquals(Float.NaN, float1, 0.01F);
      
      long long2 = MathUtils.gcd((long) (-2204), (-953L));
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      assertEquals(1L, long2);
      
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("yJMyB");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.yJMyB
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(0L, 0L);
      assertEquals(0L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 0L, (double) 0L, (-1698.3));
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(0.3999999761581421, (-1263.324488251));
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double double0 = MathUtils.normalizeAngle(0.0, 0.0);
      assertEquals(0.0, double0, 0.01);
      
      double double1 = MathUtils.normalizeAngle((-8.0E298), 0.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      boolean boolean2 = MathUtils.equals((-1.0F), (-1.0F), 58);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2);
      
      boolean boolean3 = MathUtils.equals(3.4028235E38F, 519.7F, 1.0F);
      assertFalse(boolean3 == boolean2);
      assertFalse(boolean3 == boolean0);
      assertTrue(boolean3 == boolean1);
      assertFalse(boolean3);
      
      int int0 = MathUtils.compareTo(0L, (-2245.833), 0.0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-2731.4532414857613), (-2731.4532414857613), (-2731.4532414857613));
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-2731.4532414857613);
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals((-807285735), int0);
      assertArrayEquals(new double[] {(-2731.4532414857613)}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {(-2731.4532414857613)}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      int int1 = MathUtils.subAndCheck((-807285735), 93);
      assertFalse(int1 == int0);
      assertEquals((-807285828), int1);
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (-2831.01929);
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 2594.6104861;
      doubleArray0[6] = 682.69375252;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, (-2831.01929), 0.0, 0.0, 0.0, 2594.6104861, 682.69375252}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-2831.01929), 0.0, 0.0, 0.0, 2594.6104861, 682.69375252}, doubleArray1, 0.01);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      
      MathUtils.checkFinite(0.0);
      double double0 = MathUtils.distanceInf(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(0.0, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, (-2831.01929), 0.0, 0.0, 0.0, 2594.6104861, 682.69375252}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-2831.01929), 0.0, 0.0, 0.0, 2594.6104861, 682.69375252}, doubleArray1, 0.01);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      
      double double1 = MathUtils.safeNorm(doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(3900.351850110708, double1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {0.0, (-2831.01929), 0.0, 0.0, 0.0, 2594.6104861, 682.69375252}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      boolean boolean0 = MathUtils.equals((double[]) null, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(boolean0);
      assertNotSame(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {0.0, (-2831.01929), 0.0, 0.0, 0.0, 2594.6104861, 682.69375252}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      int[] intArray0 = new int[9];
      intArray0[0] = 96;
      intArray0[1] = (-2636);
      intArray0[2] = 230;
      intArray0[3] = 70;
      intArray0[4] = 137;
      intArray0[5] = 198;
      intArray0[6] = 3462;
      intArray0[7] = 0;
      intArray0[8] = (-2424);
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
      assertArrayEquals(new int[] {96, (-2636), 230, 70, 137, 198, 3462, 0, (-2424)}, intArray0);
      assertEquals(9, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      int int0 = MathUtils.compareTo(233.3556838, 233.3556838, 561.35);
      assertEquals(0, int0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = 0;
      int int1 = MathUtils.distance1(intArray0, intArray0);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      assertArrayEquals(new int[] {0}, intArray0);
      assertEquals(1, intArray0.length);
      
      short short0 = MathUtils.indicator((short)759);
      assertEquals((short)1, short0);
      
      float[] floatArray0 = new float[7];
      floatArray0[0] = (float) 0;
      floatArray0[1] = (float) 0;
      floatArray0[2] = (float) (short)1;
      floatArray0[3] = (float) (short)759;
      floatArray0[4] = (float) 0;
      floatArray0[5] = (float) (short)1;
      floatArray0[6] = (-365.88446F);
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 1.0F, 759.0F, 0.0F, 1.0F, (-365.88446F)}, floatArray0, 0.01F);
      assertEquals(7, floatArray0.length);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) 0.0F;
      doubleArray0[1] = (double) 0.0F;
      doubleArray0[2] = 233.3556838;
      doubleArray0[3] = (double) (short)759;
      doubleArray0[4] = (double) 1.0F;
      doubleArray0[5] = (double) 1.0F;
      doubleArray0[6] = (double) 0.0F;
      doubleArray0[7] = (double) 0;
      doubleArray0[8] = (double) 0.0F;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 2.0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.4693605871657814, 1.526616707412841, 0.002011352710688855, 0.002011352710688855, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 233.3556838, 759.0, 1.0, 1.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(9, doubleArray1.length);
      assertEquals(9, doubleArray0.length);
      
      boolean boolean1 = MathUtils.equals(0.0F, 1.0F, 1.0F);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertEquals(794.0641505330445, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 233.3556838, 759.0, 1.0, 1.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(9, doubleArray0.length);
      
      boolean boolean2 = MathUtils.equals((double[]) null, doubleArray0);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(boolean2);
      assertNotSame(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 233.3556838, 759.0, 1.0, 1.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(9, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      long long0 = 1150L;
      long long1 = 9193070505571053912L;
      long long2 = MathUtils.gcd(1150L, 9193070505571053912L);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(2L, long2);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 9193070505571053912L;
      int int0 = (-1418);
      // Undeclared exception!
      try { 
        MathUtils.copyOf(doubleArray0, (-1418));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      long long0 = MathUtils.gcd((-861L), (-861L));
      assertEquals(861L, long0);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (-1052.4835515696693);
      doubleArray0[3] = 1024.0;
      doubleArray0[4] = (-2297.158169129213);
      doubleArray0[5] = (-465.41204);
      doubleArray0[6] = 0.0;
      doubleArray0[7] = 0.0;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      double[] doubleArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      float[] floatArray0 = new float[7];
      floatArray0[0] = 0.0F;
      floatArray0[1] = 80.9578F;
      floatArray0[2] = (-1.0F);
      floatArray0[3] = (-992.19F);
      floatArray0[4] = 5920.34F;
      floatArray0[5] = 2559.4185F;
      floatArray0[6] = 0.0F;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertArrayEquals(new float[] {0.0F, 80.9578F, (-1.0F), (-992.19F), 5920.34F, 2559.4185F, 0.0F}, floatArray0, 0.01F);
      assertEquals(7, floatArray0.length);
      
      boolean boolean1 = MathUtils.equals((double) 80.9578F, 0.0);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) (-992.19F);
      doubleArray0[1] = (double) 0.0F;
      doubleArray0[2] = (double) (-1.0F);
      doubleArray0[3] = (double) 80.9578F;
      doubleArray0[4] = (double) (-992.19F);
      doubleArray0[5] = (double) 0.0F;
      doubleArray0[6] = (double) (-1.0F);
      doubleArray0[7] = (double) 0.0F;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[8][5];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {80.95780181884766, 0.0, 0.0, 0.0, (-1.0), (-1.0), (-992.1900024414062), (-992.1900024414062)}, doubleArray0, 0.01);
      assertEquals(8, doubleArray1.length);
      assertEquals(8, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 1125.7375F, 1852);
      assertFalse(boolean0);
      
      double double0 = MathUtils.binomialCoefficientLog(1852, (-3963));
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = Double.NaN;
      doubleArray0[1] = 0.07692307692307693;
      doubleArray0[2] = 0.0;
      double[] doubleArray1 = new double[9];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 0.07692307692307693;
      doubleArray1[1] = 0.0;
      doubleArray1[2] = (double) 1125.7375F;
      doubleArray1[3] = (double) 0.0F;
      doubleArray1[4] = (double) (-3963);
      doubleArray1[5] = (double) 1125.7375F;
      doubleArray1[6] = (double) 1125.7375F;
      doubleArray1[7] = 4.455505956692757;
      doubleArray1[8] = (double) 0.0F;
      double double1 = MathUtils.distance(doubleArray0, doubleArray1);
      assertNotEquals(double1, double0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(Double.NaN, double1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {Double.NaN, 0.07692307692307693, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.07692307692307693, 0.0, 1125.737548828125, 0.0, (-3963.0), 1125.737548828125, 1125.737548828125, 4.455505956692757, 0.0}, doubleArray1, 0.01);
      assertEquals(3, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      int int0 = 8388607;
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(0.0, 0.0, 8388607);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-2313.3130415680316), (-2313.3130415680316), (-2313.3130415680316));
      assertTrue(boolean0);
      
      double double0 = MathUtils.sinh(0.0);
      assertEquals(0.0, double0, 0.01);
      
      float float0 = 0.0F;
      float float1 = 0.0F;
      boolean boolean1 = MathUtils.equalsIncludingNaN(0.0F, 0.0F, 0.0F);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      int int0 = (-1455);
      int int1 = MathUtils.gcd(0, (-1455));
      assertFalse(int1 == int0);
      assertEquals(1455, int1);
      
      int int2 = 815;
      boolean boolean2 = MathUtils.equals((-1895.205F), 0.0F, 815);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
      
      double double1 = MathUtils.normalizeAngle((-957.86675), 0.0F);
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-2.822583308702974), double1, 0.01);
      
      boolean boolean3 = MathUtils.equalsIncludingNaN(0.0F, (-1895.205F), 1455);
      assertFalse(boolean3 == boolean1);
      assertFalse(boolean3 == boolean0);
      assertTrue(boolean3 == boolean2);
      assertFalse(boolean3);
      
      String string0 = "";
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, 377.39F);
      assertFalse(boolean0);
      
      int int0 = (-3643);
      int int1 = MathUtils.pow((-3643), 20);
      assertFalse(int1 == int0);
      assertEquals(1991036209, int1);
      
      double double0 = MathUtils.sign((-1398.9774983945));
      assertEquals((-1.0), double0, 0.01);
      
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
      
      float[] floatArray0 = new float[2];
      floatArray0[0] = (float) (-3643);
      floatArray0[1] = 0.0F;
      float[] floatArray1 = new float[8];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = (float) 20;
      floatArray1[1] = (float) (-3643);
      floatArray1[2] = 0.0F;
      floatArray1[3] = (float) 20;
      floatArray1[4] = (float) 20;
      floatArray1[5] = (-1197.6165F);
      floatArray1[6] = (float) 20;
      floatArray1[7] = (float) 0;
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray1);
      assertTrue(boolean1 == boolean0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean1);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertArrayEquals(new float[] {(-3643.0F), 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {20.0F, (-3643.0F), 0.0F, 20.0F, 20.0F, (-1197.6165F), 20.0F, 0.0F}, floatArray1, 0.01F);
      assertEquals(2, floatArray0.length);
      assertEquals(8, floatArray1.length);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(0, 1336);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1,336, n = 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(0, (-2550));
      assertEquals(1.0, double0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(602.932908, 2251.5, 0.0);
      assertFalse(boolean0);
      
      float[] floatArray0 = new float[0];
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray0);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertEquals(0, floatArray0.length);
      
      long long0 = MathUtils.mulAndCheck(0L, 1880L);
      assertEquals(0L, long0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      double double1 = MathUtils.round((double) 0L, (-2550));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      double double2 = MathUtils.cosh(0.0);
      assertEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(1.0, double2, 0.01);
      
      long long1 = MathUtils.pow((-2644281811660520851L), 0);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      boolean boolean2 = MathUtils.equals((-622.7F), (float) 0L);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2);
      
      byte byte0 = MathUtils.sign((byte) (-1));
      assertEquals((byte) (-1), byte0);
      
      double[] doubleArray0 = new double[0];
      double double3 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(0.0, double3, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      
      double double4 = MathUtils.sign(0.0);
      assertEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(double4, double3, 0.01);
      assertEquals(0.0, double4, 0.01);
      
      int int0 = MathUtils.subAndCheck((int) (byte) (-1), (-2424));
      assertEquals(2423, int0);
      
      float float0 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float0, 0.01F);
      
      boolean boolean3 = MathUtils.equals(0.0F, 667.6289F);
      assertTrue(boolean3 == boolean2);
      assertFalse(boolean3 == boolean1);
      assertTrue(boolean3 == boolean0);
      assertFalse(boolean3);
      
      int int1 = MathUtils.hash(1178.26);
      assertFalse(int1 == int0);
      assertEquals(2112015069, int1);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      int int0 = 1512;
      int int1 = 267;
      double double0 = MathUtils.binomialCoefficientDouble(1512, 267);
      assertEquals(3.529099467534413E304, double0, 0.01);
      
      long long0 = 427L;
      long long1 = MathUtils.addAndCheck((long) 1512, 427L);
      assertFalse(long1 == long0);
      assertEquals(1939L, long1);
      
      int int2 = 559;
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(1512, 559);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      int int0 = (-1561);
      float float0 = MathUtils.round(0.0F, (-1561));
      assertEquals(Float.NaN, float0, 0.01F);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) 0.0F;
      doubleArray0[1] = (double) (-1561);
      doubleArray0[2] = (double) 0.0F;
      doubleArray0[3] = (double) 0.0F;
      doubleArray0[4] = 3730.108110139;
      doubleArray0[5] = (double) (-1561);
      doubleArray0[6] = (double) (-1561);
      doubleArray0[7] = (double) (-1561);
      doubleArray0[8] = (double) Float.NaN;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 0.0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, (-1561.0), 0.0, 0.0, 3730.108110139, (-1561.0), (-1561.0), (-1561.0), Double.NaN}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {-0.0, 0.0, -0.0, -0.0, -0.0, 0.0, 0.0, 0.0, Double.NaN}, doubleArray1, 0.01);
      assertEquals(9, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      
      // Undeclared exception!
      try { 
        MathUtils.pow((long) (-1561), (long) (-1561));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,561)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      float float0 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float0, 0.01F);
      
      double[] doubleArray0 = new double[0];
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)91);
      assertEquals((byte)1, byte0);
      
      int int0 = MathUtils.pow(950, 0);
      assertEquals(1, int0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) (byte)91;
      doubleArray0[1] = (double) (byte)91;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {91.0, 91.0}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      long long0 = (-843L);
      long long1 = MathUtils.indicator((-843L));
      assertFalse(long1 == long0);
      assertEquals((-1L), long1);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = bigInteger0.shiftRight(14);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = null;
      // Undeclared exception!
      try { 
        bigInteger0.gcd((BigInteger) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      short short0 = MathUtils.sign((short)0);
      assertEquals((short)0, short0);
      
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray((double[]) null, (short)0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      long long0 = 113236205062349959L;
      long long1 = MathUtils.subAndCheck(113236205062349959L, 113236205062349959L);
      assertFalse(long1 == long0);
      assertEquals(0L, long1);
      
      double[] doubleArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      int int0 = MathUtils.hash((-7.098037282025));
      assertEquals(582761551, int0);
      
      long long0 = MathUtils.mulAndCheck((long) 582761551, (long) 582761551);
      assertEquals(339611025323925601L, long0);
      
      long long1 = MathUtils.lcm(0L, 339611025323925601L);
      assertFalse(long1 == long0);
      assertEquals(0L, long1);
      
      int int1 = MathUtils.mulAndCheck(582761551, 0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      long long2 = MathUtils.addAndCheck(339611025323925601L, (long) 582761551);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(339611025906687152L, long2);
      
      long long3 = MathUtils.binomialCoefficient(0, 0);
      assertFalse(long3 == long1);
      assertFalse(long3 == long0);
      assertFalse(long3 == long2);
      assertEquals(1L, long3);
      
      byte[] byteArray0 = new byte[4];
      byteArray0[0] = (byte) (-72);
      byteArray0[1] = (byte) (-1);
      byteArray0[2] = (byte)10;
      byteArray0[3] = (byte)3;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((short)2563, bigInteger0.shortValue());
      assertEquals((byte)3, bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte) (-72), (byte) (-1), (byte)10, (byte)3}, byteArray0);
      assertEquals(4, byteArray0.length);
      
      byte[] byteArray1 = bigInteger0.toByteArray();
      assertFalse(byteArray1.equals((Object)byteArray0));
      assertNotSame(byteArray0, byteArray1);
      assertNotSame(byteArray1, byteArray0);
      assertNotNull(byteArray1);
      assertEquals((short)2563, bigInteger0.shortValue());
      assertEquals((byte)3, bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte) (-72), (byte) (-1), (byte)10, (byte)3}, byteArray0);
      assertArrayEquals(new byte[] {(byte) (-72), (byte) (-1), (byte)10, (byte)3}, byteArray1);
      assertEquals(4, byteArray0.length);
      assertEquals(4, byteArray1.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (int) (byte)10);
      assertFalse(byteArray0.equals((Object)byteArray1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(byteArray0, byteArray1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)2563, bigInteger0.shortValue());
      assertEquals((byte)3, bigInteger0.byteValue());
      assertEquals((short) (-27991), bigInteger1.shortValue());
      assertEquals((byte) (-87), bigInteger1.byteValue());
      assertArrayEquals(new byte[] {(byte) (-72), (byte) (-1), (byte)10, (byte)3}, byteArray0);
      assertEquals(4, byteArray0.length);
      
      int int2 = MathUtils.pow((int) (byte)10, 316);
      assertFalse(int2 == int0);
      assertTrue(int2 == int1);
      assertEquals(0, int2);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(3156.6812F, (float) 0, (float) 1L);
      assertFalse(boolean0);
      
      int[] intArray0 = new int[5];
      intArray0[0] = (int) (byte) (-72);
      intArray0[1] = (int) (byte) (-1);
      intArray0[2] = 316;
      intArray0[3] = (int) (byte) (-72);
      intArray0[4] = 316;
      int[] intArray1 = new int[0];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-411.045151983998), (-1.0E-6));
      assertFalse(boolean0);
      
      int[] intArray0 = new int[0];
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
      assertArrayEquals(new int[] {}, intArray0);
      assertEquals(0, intArray0.length);
      
      double double0 = MathUtils.round((double) 0, 0);
      assertEquals(0.0, double0, 0.01);
      
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertEquals(0, intArray0.length);
      assertEquals(0, intArray1.length);
      
      double double1 = MathUtils.cosh(0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      long long0 = 0L;
      long long1 = MathUtils.addAndCheck((-2681L), 0L);
      assertFalse(long1 == long0);
      assertEquals((-2681L), long1);
      
      double double2 = MathUtils.sinh((-1.0E-6));
      assertEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals((-9.999999999732445E-7), double2, 0.01);
      
      int int1 = MathUtils.pow((-626), 0L);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      int int2 = 0;
      int int3 = MathUtils.sign(0);
      assertTrue(int3 == int0);
      assertTrue(int3 == int2);
      assertFalse(int3 == int1);
      assertEquals(0, int3);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) (-2681L);
      doubleArray0[2] = (-9.999999999732445E-7);
      doubleArray0[3] = (double) 0;
      doubleArray0[4] = (-1.0E-6);
      doubleArray0[5] = (double) (-2681L);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not decreasing (-2,681 < -0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(0, 0);
      assertEquals(1.0, double0, 0.01);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      double double1 = MathUtils.log(1.01, 1.01);
      assertEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      double double2 = MathUtils.normalizeAngle(3943.139, (-577.272867847999));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals((-574.4712358621227), double2, 0.01);
      
      double double3 = MathUtils.log((-1236.09076542278), (-1236.09076542278));
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      double double4 = MathUtils.sinh(0.0);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(0.0, double4, 0.01);
      
      long long0 = MathUtils.mulAndCheck((long) 0, (long) 0);
      assertEquals(0L, long0);
      
      float float0 = MathUtils.round((float) 0L, 241, 0);
      assertEquals(Float.NaN, float0, 0.01F);
      
      float float1 = MathUtils.round(1576.1664F, 0, 0);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(1577.0F, float1, 0.01F);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-574.4712358621227);
      doubleArray0[1] = (double) 1577.0F;
      doubleArray0[2] = (double) 0L;
      doubleArray0[3] = (-577.272867847999);
      doubleArray0[4] = (double) 0;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[8][8];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[6];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 0L;
      doubleArray2[1] = 1.0;
      doubleArray2[2] = (double) 0L;
      doubleArray2[3] = 0.0;
      doubleArray2[4] = (-574.4712358621227);
      doubleArray2[5] = (-0.7853981633974483);
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 6 != 5
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (-2263.991993585081);
      doubleArray0[1] = (-605.64566);
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 1793.02;
      doubleArray0[4] = 1034.342915042;
      doubleArray0[5] = 0.0;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 0.0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {(-2263.991993585081), (-605.64566), 0.0, 1793.02, 1034.342915042, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, -0.0, -0.0, -0.0, -0.0}, doubleArray1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      
      long long0 = MathUtils.lcm(0L, 0L);
      assertEquals(0L, long0);
      
      int int0 = MathUtils.sign(0);
      assertEquals(0, int0);
      
      long long1 = MathUtils.addAndCheck(4499201580859392L, 0L);
      assertFalse(long1 == long0);
      assertEquals(4499201580859392L, long1);
      
      double double0 = MathUtils.sign(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      long long0 = 5040L;
      long long1 = MathUtils.mulAndCheck(5040L, 0L);
      assertFalse(long1 == long0);
      assertEquals(0L, long1);
      
      int int0 = (-3010);
      double double0 = MathUtils.round(3904.924, (-3010));
      assertEquals(0.0, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-3010), (-3010));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -3,010
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      float float0 = Float.NaN;
      int int0 = 989;
      float float1 = MathUtils.round(Float.NaN, 989);
      assertEquals(float1, float0, 0.01F);
      assertEquals(Float.NaN, float1, 0.01F);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-2061.05185378), (double) Float.NaN, (double) 989);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-2061.05185378);
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {(-2061.05185378)}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      MathUtils.checkOrder(doubleArray0);
      assertArrayEquals(new double[] {(-2061.05185378)}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {(-2061.05185378)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      
      long long0 = MathUtils.addAndCheck((long) 989, 202L);
      assertEquals(1191L, long0);
      
      boolean boolean1 = MathUtils.equals(3257.948F, Float.NaN, 1042);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      boolean boolean2 = MathUtils.equals((double) Float.NaN, (double) 202L);
      assertTrue(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2);
      
      double double1 = 784.04014401;
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(0.0, 784.04014401, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      int int0 = 0;
      long long0 = MathUtils.pow(747L, 0);
      assertEquals(1L, long0);
      
      int[] intArray0 = new int[4];
      intArray0[0] = (-1439);
      intArray0[1] = 0;
      intArray0[2] = 0;
      intArray0[3] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {(-1439), 0, 0, 0}, intArray0);
      assertArrayEquals(new int[] {(-1439), 0, 0, 0}, intArray1);
      assertEquals(4, intArray0.length);
      assertEquals(4, intArray1.length);
      
      byte byte0 = MathUtils.sign((byte)2);
      assertEquals((byte)1, byte0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 1L;
      doubleArray0[2] = (double) (byte)2;
      doubleArray0[3] = (double) (-1439);
      doubleArray0[4] = (double) (-1439);
      doubleArray0[5] = Double.POSITIVE_INFINITY;
      doubleArray0[6] = (double) (-1439);
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, (byte)2);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // Array contains an infinite element, \u221E at index 5
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      float float0 = MathUtils.indicator((-839.61F));
      assertEquals((-1.0F), float0, 0.01F);
      
      double double0 = MathUtils.factorialLog(2682);
      assertEquals(18495.427175859717, double0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(1966.6F, 1966.6F, (-3088.626F));
      assertTrue(boolean0);
      
      double double1 = MathUtils.round(3.834E-20, 118);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(3.834E-20, double1, 0.01);
      
      int[] intArray0 = new int[7];
      intArray0[0] = 0;
      intArray0[1] = 2682;
      intArray0[2] = 118;
      intArray0[3] = 2682;
      intArray0[4] = 2682;
      intArray0[5] = 2682;
      intArray0[6] = 118;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertArrayEquals(new int[] {0, 2682, 118, 2682, 2682, 2682, 118}, intArray0);
      assertEquals(7, intArray0.length);
      
      byte byte0 = MathUtils.indicator((byte)64);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      double double0 = 0.0;
      int int0 = MathUtils.compareTo(0.0, 0.0, 0.0);
      assertEquals(0, int0);
      
      long long0 = MathUtils.binomialCoefficient(0, 0);
      assertEquals(1L, long0);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-2463), (-2463));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -2,463
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (-12.25277);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (-793.6);
      doubleArray0[3] = Double.NaN;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = 3.9736429850260626E-8;
      doubleArray0[7] = 14.926358;
      // Undeclared exception!
      try { 
        MathUtils.distance((double[]) null, doubleArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(42, 42);
      assertEquals(1L, long0);
      
      long long1 = MathUtils.pow((-3L), 2);
      assertFalse(long1 == long0);
      assertEquals(9L, long1);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(1.0F, 1.0F, 202);
      assertTrue(boolean0);
      
      int int0 = MathUtils.pow(793, 0);
      assertEquals(1, int0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) 1.0F;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = (double) 202;
      doubleArray0[3] = (double) 0;
      doubleArray0[4] = (double) 0;
      doubleArray0[5] = (double) 0;
      doubleArray0[6] = (double) 1;
      doubleArray0[7] = (double) 202;
      doubleArray0[8] = (double) 0;
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {1.0, 0.0, 202.0, 0.0, 0.0, 0.0, 1.0, 202.0, 0.0}, doubleArray0, 0.01);
      assertEquals(9, doubleArray0.length);
      
      boolean boolean1 = MathUtils.equals(0.0F, (float) 202, 0.0F);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2);
      assertArrayEquals(new double[] {1.0, 0.0, 202.0, 0.0, 0.0, 0.0, 1.0, 202.0, 0.0}, doubleArray0, 0.01);
      assertEquals(9, doubleArray0.length);
      
      float float0 = MathUtils.round((-592.9F), 0);
      assertEquals((-593.0F), float0, 0.01F);
      
      double[][] doubleArray1 = new double[6][4];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      double[] doubleArray2 = new double[6];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 793;
      doubleArray2[1] = 0.0;
      doubleArray2[2] = (double) 0.0F;
      doubleArray2[3] = (double) 0.0F;
      doubleArray2[4] = 0.0;
      doubleArray2[5] = 0.0;
      doubleArray1[5] = doubleArray2;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 6 != 9
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      int int0 = MathUtils.indicator(1623);
      assertEquals(1, int0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = 1623;
      int int1 = MathUtils.distance1(intArray0, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      assertArrayEquals(new int[] {1623}, intArray0);
      assertEquals(1, intArray0.length);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 1623;
      doubleArray0[1] = (double) 1623;
      double double0 = 5.669184079525E-24;
      doubleArray0[2] = 5.669184079525E-24;
      doubleArray0[3] = (double) 1623;
      doubleArray0[4] = (double) 0;
      doubleArray0[5] = (double) 1623;
      doubleArray0[6] = (double) 1623;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {1623.0, 1623.0, 5.669184079525E-24, 1623.0, 0.0, 1623.0, 1623.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      float float0 = (-508.51215F);
      boolean boolean1 = MathUtils.equals((float) 0, (-508.51215F));
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double double1 = 6.283185307179586;
      MathUtils.checkFinite(6.283185307179586);
      double double2 = MathUtils.normalizeAngle(1623.0, 3481.53912888291);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(3482.8228509251576, double2, 0.01);
      
      double double3 = MathUtils.safeNorm(doubleArray0);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(3629.1383274821587, double3, 0.01);
      assertArrayEquals(new double[] {1623.0, 1623.0, 5.669184079525E-24, 1623.0, 0.0, 1623.0, 1623.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      byte byte0 = MathUtils.sign((byte)20);
      assertEquals((byte)1, byte0);
      
      float float1 = MathUtils.sign(0.5F);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(1.0F, float1, 0.01F);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      double double4 = 0.10526403784751892;
      double double5 = MathUtils.log(5.669184079525E-24, 0.10526403784751892);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double3, 0.01);
      assertEquals(0.04205884066325045, double5, 0.01);
      
      double double6 = 0.041666666666621166;
      boolean boolean2 = MathUtils.equals(1623.0, 0.041666666666621166);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
      
      // Undeclared exception!
      try { 
        MathUtils.pow((long) 0, (-1184));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,184)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (-28.394);
      double double0 = 99.5945177;
      doubleArray0[1] = 99.5945177;
      double double1 = 0.1111111111111111;
      doubleArray0[2] = 0.1111111111111111;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-28.394));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {(-28.394), 99.5945177, 0.1111111111111111}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {11.30557875960873, (-39.655337884151436), (-0.04424087545729039)}, doubleArray1, 0.01);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      
      MathUtils.checkFinite(doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertNotSame(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-28.394), 99.5945177, 0.1111111111111111}, doubleArray0, 0.01);
      assertEquals(3, doubleArray0.length);
      
      boolean boolean0 = MathUtils.equals(1.0F, 1.0F, 1.0F);
      assertTrue(boolean0);
      
      long long0 = 0L;
      // Undeclared exception!
      try { 
        MathUtils.pow(0L, (-1158));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,158)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      MathUtils.checkFinite((-3294.613183));
      long long0 = MathUtils.factorial(10);
      assertEquals(3628800L, long0);
      
      Pair<Integer, Object> pair0 = new Pair<Integer, Object>((Integer) null, (Object) null);
      assertNotNull(pair0);
      
      MathUtils.checkNotNull((Object) pair0);
      double double0 = MathUtils.sign((double) 10);
      assertEquals(1.0, double0, 0.01);
      
      double double1 = MathUtils.normalizeAngle(0.0, 2.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      byte[] byteArray0 = new byte[3];
      byteArray0[0] = (byte)0;
      byteArray0[1] = (byte) (-52);
      byteArray0[2] = (byte)82;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((short) (-13230), bigInteger0.shortValue());
      assertEquals((byte)82, bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte)0, (byte) (-52), (byte)82}, byteArray0);
      assertEquals(3, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (int) (byte)0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short) (-13230), bigInteger0.shortValue());
      assertEquals((byte)82, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertArrayEquals(new byte[] {(byte)0, (byte) (-52), (byte)82}, byteArray0);
      assertEquals(3, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      int[] intArray0 = new int[1];
      intArray0[0] = 1363;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertArrayEquals(new int[] {1363}, intArray0);
      assertEquals(1, intArray0.length);
      
      int int1 = 659;
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, (float) 0, 659);
      assertFalse(boolean0);
      
      long long0 = MathUtils.indicator((long) 659);
      assertEquals(1L, long0);
      
      float float0 = 0.0F;
      boolean boolean1 = MathUtils.equalsIncludingNaN(0.0F, 0.0F, (float) 1L);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      int int2 = 205;
      // Undeclared exception!
      try { 
        MathUtils.factorial(205);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 1773L);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-2076.44734802);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (-1212.068858839932);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(2404.31788823126, double0, 0.01);
      assertArrayEquals(new double[] {(-2076.44734802), 0.0, 0.0, (-1212.068858839932)}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble(12, 1508);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1,508, n = 12
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      int int0 = (-1788);
      float float0 = MathUtils.round((-1528.3727F), (-1788));
      assertEquals(Float.NaN, float0, 0.01F);
      
      double double0 = MathUtils.indicator((double) (-1528.3727F));
      assertEquals((-1.0), double0, 0.01);
      
      float float1 = MathUtils.indicator(-0.0F);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(1.0F, float1, 0.01F);
      
      double[] doubleArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      int int0 = 4084;
      float float0 = MathUtils.round(468.63995F, 4084);
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int1 = 2144501993;
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(Float.NaN, 468.63995F, 2144501993);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((-4267615245585081135L), 1308L);
      assertEquals((-4267615245585082443L), long0);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.SAMPLE_SIZE_EXCEEDS_COLLECTION_SIZE;
      assertEquals(LocalizedFormats.SAMPLE_SIZE_EXCEEDS_COLLECTION_SIZE, localizedFormats0);
      assertEquals("sample size ({0}) exceeds collection size ({1})", localizedFormats0.getSourceString());
      
      Object[] objectArray0 = new Object[0];
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null, (Localizable) localizedFormats0, objectArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // sample size ({0}) exceeds collection size ({1})
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      double double0 = Double.NEGATIVE_INFINITY;
      double double1 = MathUtils.round(Double.NEGATIVE_INFINITY, (-1532));
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double1, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.equals(0.0F, 0.0F, (-1532));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-0.33333333333333287);
      doubleArray0[1] = (-186.22);
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {(-0.33333333333333287), (-186.22)}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      byte byte0 = MathUtils.sign((byte)11);
      assertEquals((byte)1, byte0);
      
      long long0 = (-148L);
      long long1 = MathUtils.indicator((-148L));
      assertFalse(long1 == long0);
      assertEquals((-1L), long1);
      
      float float0 = 0.5F;
      boolean boolean1 = MathUtils.equals((-1961.206F), 0.5F, 6);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      long long2 = MathUtils.pow((long) (byte)11, 0L);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(1L, long2);
      
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double double0 = (-754.902117944);
      doubleArray0[0] = (-754.902117944);
      doubleArray0[1] = (-1528.33);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
      assertArrayEquals(new double[] {(-754.902117944), (-1528.33)}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.UNPARSEABLE_3D_VECTOR;
      assertEquals(LocalizedFormats.UNPARSEABLE_3D_VECTOR, localizedFormats0);
      assertEquals("unparseable 3D vector: \"{0}\"", localizedFormats0.getSourceString());
      
      Locale locale0 = Locale.KOREA;
      assertNotNull(locale0);
      assertEquals("KOR", locale0.getISO3Country());
      assertEquals("", locale0.getVariant());
      assertEquals("ko_KR", locale0.toString());
      assertEquals("kor", locale0.getISO3Language());
      assertEquals("KR", locale0.getCountry());
      assertEquals("ko", locale0.getLanguage());
      
      Locale locale1 = Locale.UK;
      assertFalse(locale1.equals((Object)locale0));
      assertNotSame(locale1, locale0);
      assertNotNull(locale1);
      assertEquals("GB", locale1.getCountry());
      assertEquals("eng", locale1.getISO3Language());
      assertEquals("en", locale1.getLanguage());
      assertEquals("", locale1.getVariant());
      assertEquals("GBR", locale1.getISO3Country());
      assertEquals("en_GB", locale1.toString());
      
      String string0 = locale0.getDisplayScript(locale1);
      assertFalse(locale0.equals((Object)locale1));
      assertFalse(locale1.equals((Object)locale0));
      assertEquals("", string0);
      assertNotSame(locale0, locale1);
      assertNotSame(locale1, locale0);
      assertNotNull(string0);
      assertEquals("KOR", locale0.getISO3Country());
      assertEquals("", locale0.getVariant());
      assertEquals("ko_KR", locale0.toString());
      assertEquals("kor", locale0.getISO3Language());
      assertEquals("KR", locale0.getCountry());
      assertEquals("ko", locale0.getLanguage());
      assertEquals("GB", locale1.getCountry());
      assertEquals("eng", locale1.getISO3Language());
      assertEquals("en", locale1.getLanguage());
      assertEquals("", locale1.getVariant());
      assertEquals("GBR", locale1.getISO3Country());
      assertEquals("en_GB", locale1.toString());
      
      // Undeclared exception!
      try { 
        LocalizedFormats.valueOf("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.exception.util.LocalizedFormats.
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      double[] doubleArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      int int0 = MathUtils.indicator((-3538));
      assertEquals((-1), int0);
      
      boolean boolean0 = MathUtils.equals((float) (-1), (float) (-1), (float) (-3538));
      assertTrue(boolean0);
      
      int int1 = 0;
      int int2 = MathUtils.sign(0);
      assertTrue(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(0, int2);
      
      double double0 = 0.0;
      MathUtils.checkFinite(0.0);
      int int3 = 154;
      // Undeclared exception!
      try { 
        MathUtils.pow(154, (long) (-1));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      float float0 = 1.0F;
      int int0 = 205;
      // Undeclared exception!
      try { 
        MathUtils.round(1.0F, 205, 205);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 205, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null, (-3477));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(117, 117);
      assertEquals(0, int0);
      
      int int1 = MathUtils.addAndCheck((-2147483625), 261632);
      assertFalse(int1 == int0);
      assertEquals((-2147221993), int1);
      
      double double0 = MathUtils.binomialCoefficientLog(511, 117);
      assertEquals(271.7574751232736, double0, 0.01);
      
      Integer integer0 = new Integer((-2147221993));
      assertTrue(integer0.equals((Object)int1));
      assertFalse(integer0.equals((Object)int0));
      assertEquals((-2147221993), (int)integer0);
      assertNotNull(integer0);
      
      int int2 = MathUtils.pow((-819), 1507L);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(1676658261, int2);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 1507L;
      doubleArray0[1] = (double) 511;
      doubleArray0[2] = 271.7574751232736;
      doubleArray0[3] = (double) 0;
      doubleArray0[4] = (double) (-819);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {1507.0, 511.0, 271.7574751232736, 0.0, (-819.0)}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 903.4186578426755;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {903.4186578426755}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(903.4186578426755, 64.0);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      assertArrayEquals(new double[] {903.4186578426755}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      boolean boolean2 = MathUtils.equals(1555.0728F, 1555.0728F, 1555.0728F);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2);
      
      long long0 = MathUtils.pow(0L, 0L);
      assertEquals(1L, long0);
      
      boolean boolean3 = MathUtils.equals((-526.6F), (float) 1L, 1294);
      assertFalse(boolean3 == boolean2);
      assertTrue(boolean3 == boolean0);
      assertFalse(boolean3 == boolean1);
      assertFalse(boolean3);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      short short0 = MathUtils.indicator((short) (-1707));
      assertEquals((short) (-1), short0);
      
      MathUtils.checkOrder(doubleArray0);
      assertArrayEquals(new double[] {903.4186578426755}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      long long1 = MathUtils.addAndCheck((long) 1294, (-2338L));
      assertFalse(long1 == long0);
      assertEquals((-1044L), long1);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      int int0 = 17;
      int int1 = MathUtils.addAndCheck(14, 17);
      assertFalse(int1 == int0);
      assertEquals(31, int1);
      
      byte byte0 = MathUtils.sign((byte) (-108));
      assertEquals((byte) (-1), byte0);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) (byte) (-108);
      doubleArray0[1] = (double) 17;
      doubleArray0[2] = (double) 31;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not decreasing (-108 < 17)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      int int0 = 0;
      int int1 = (-3751);
      // Undeclared exception!
      try { 
        MathUtils.round(1437.9158F, 0, (-3751));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method -3,751, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-2200.4F), (-2200.4F), (-299.267F));
      assertTrue(boolean0);
      
      double double0 = MathUtils.sinh(0.0);
      assertEquals(0.0, double0, 0.01);
      
      MathUtils.checkFinite((-3771.4082363099847));
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) (-299.267F), 0.0, 0.0);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-3771.4082363099847);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (double) (-299.267F);
      doubleArray0[3] = 0.0;
      doubleArray0[4] = (-3771.4082363099847);
      doubleArray0[5] = (-2271.49756620856);
      doubleArray0[6] = 0.0;
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {(-3771.4082363099847), 0.0, (-299.2669982910156), 0.0, (-3771.4082363099847), (-2271.49756620856), 0.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(2.356194490192345, (-2.2250738585072014E-308), 0.0);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
      
      double double2 = (-1372.216110227);
      double double3 = MathUtils.indicator((-1372.216110227));
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals((-1.0), double3, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-615), (-615));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -615
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      int int0 = MathUtils.hash((-3336.001376));
      assertEquals(1961948672, int0);
      
      MathUtils.checkNotNull((Object) "D|xgv,XtkX~[e>Z>M");
      int[] intArray0 = new int[8];
      intArray0[0] = 1961948672;
      intArray0[1] = (-2495);
      intArray0[2] = 1961948672;
      intArray0[3] = 1961948672;
      intArray0[4] = 1961948672;
      intArray0[5] = 1961948672;
      intArray0[6] = 1961948672;
      intArray0[7] = 1961948672;
      int[] intArray1 = new int[4];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = 1961948672;
      intArray1[1] = (-2495);
      intArray1[2] = (-2495);
      intArray1[3] = 1961948672;
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float[]) null, (float[]) null);
      assertTrue(boolean0);
      
      byte byte0 = MathUtils.sign((byte) (-107));
      assertEquals((byte) (-1), byte0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(2023.81917, 0.0);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) (byte) (-107);
      doubleArray0[1] = (double) (byte) (-107);
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (double) (byte) (-107);
      doubleArray0[4] = (double) (byte) (-1);
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {(-107.0), (-107.0), 0.0, (-107.0), (-1.0)}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      int int0 = MathUtils.mulAndCheck(514, (int) (byte) (-1));
      assertEquals((-514), int0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 2047L);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
      
      float[] floatArray0 = new float[6];
      floatArray0[0] = Float.NEGATIVE_INFINITY;
      floatArray0[1] = (-3.4028235E38F);
      floatArray0[2] = 0.0F;
      floatArray0[3] = (-1585.3765F);
      floatArray0[4] = (-270.0F);
      floatArray0[5] = 950.0F;
      boolean boolean0 = MathUtils.equals((float[]) null, floatArray0);
      assertFalse(boolean0);
      assertArrayEquals(new float[] {Float.NEGATIVE_INFINITY, (-3.4028235E38F), 0.0F, (-1585.3765F), (-270.0F), 950.0F}, floatArray0, 0.01F);
      assertEquals(6, floatArray0.length);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-3.4028235E38F), (-3.4028235E38F), (-3.4028235E38F));
      assertTrue(boolean0);
      
      float[] floatArray0 = new float[9];
      floatArray0[0] = (-3.4028235E38F);
      floatArray0[1] = (-3.4028235E38F);
      floatArray0[2] = (-3.4028235E38F);
      floatArray0[3] = 0.0F;
      floatArray0[4] = (-3.4028235E38F);
      floatArray0[5] = (-3.4028235E38F);
      floatArray0[6] = (-3.4028235E38F);
      floatArray0[7] = (-3.4028235E38F);
      floatArray0[8] = (-3.4028235E38F);
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray0);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      assertArrayEquals(new float[] {(-3.4028235E38F), (-3.4028235E38F), (-3.4028235E38F), 0.0F, (-3.4028235E38F), (-3.4028235E38F), (-3.4028235E38F), (-3.4028235E38F), (-3.4028235E38F)}, floatArray0, 0.01F);
      assertEquals(9, floatArray0.length);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(572L, 572L);
      assertEquals(327184L, long0);
      
      Integer integer0 = new Integer(2372);
      assertEquals(2372, (int)integer0);
      assertNotNull(integer0);
      
      MathUtils.checkNotNull((Object) integer0);
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 669.470434;
      doubleArray0[1] = (double) 327184L;
      doubleArray0[2] = (double) 572L;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {669.470434, 327184.0, 572.0}, doubleArray0, 0.01);
      assertEquals(3, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-292), (-292));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -292
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      int int0 = 4;
      double double0 = MathUtils.round((-2436.6), 4, 4);
      assertEquals((-2436.6), double0, 0.01);
      
      int int1 = MathUtils.indicator(4);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      // Undeclared exception!
      try { 
        MathUtils.factorial(246);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      double double0 = MathUtils.round(1135.5719645, (-1946));
      assertEquals(0.0, double0, 0.01);
      
      double double1 = MathUtils.sign((-1876.5));
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-1.0), double1, 0.01);
      
      double double2 = MathUtils.cosh(1117.522644165966);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double2, 0.01);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-1876.5);
      doubleArray0[1] = 1117.522644165966;
      doubleArray0[2] = Double.POSITIVE_INFINITY;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 1117.522644165966;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = 1117.522644165966;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 2 and 3 are not increasing (\u221E > 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      double double0 = 0.375;
      MathUtils.checkFinite(0.375);
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 0.375;
      doubleArray0[1] = 0.375;
      doubleArray0[2] = 0.375;
      doubleArray0[3] = 0.375;
      doubleArray0[4] = 0.375;
      double[] doubleArray1 = new double[4];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 3.834E-20;
      doubleArray1[1] = 0.375;
      doubleArray1[2] = 0.375;
      double double1 = (-2.356194490192345);
      doubleArray1[3] = (-2.356194490192345);
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[2] = 0.030589580535888672;
      doubleArray0[3] = (-73.3);
      doubleArray0[4] = 1890.426;
      doubleArray0[5] = 0.2857142686843872;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 2 and 3 are not increasing (0.031 > -73.3)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 0.5;
      doubleArray0[1] = 0.11764700710773468;
      doubleArray0[2] = 0.030589580535888672;
      doubleArray0[3] = (-73.3);
      doubleArray0[4] = 1890.426;
      doubleArray0[5] = 0.2857142686843872;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not increasing (0.5 > 0.118)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 5709.08197;
      doubleArray0[2] = 9.0;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 715.396;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 372);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 5709.08197, 9.0, 0.0, 715.396}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(372, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle((-4288.938201), (-4288.938201));
      assertEquals((-4288.938201), double0, 0.01);
      
      double double1 = MathUtils.normalizeAngle((-4288.938201), 0.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(2.4773638036576813, double1, 0.01);
      
      short short0 = MathUtils.indicator((short) (-3707));
      assertEquals((short) (-1), short0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      int int0 = 3787;
      int int1 = MathUtils.lcm(3787, 3787);
      assertTrue(int1 == int0);
      assertEquals(3787, int1);
      
      int int2 = 0;
      int int3 = MathUtils.sign(0);
      assertFalse(int3 == int0);
      assertTrue(int3 == int2);
      assertFalse(int3 == int1);
      assertEquals(0, int3);
      
      byte byte0 = (byte) (-96);
      byte byte1 = MathUtils.indicator((byte) (-96));
      assertFalse(byte1 == byte0);
      assertEquals((byte) (-1), byte1);
      
      // Undeclared exception!
      try { 
        MathUtils.equals(1357.0, (double) 0, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      int int0 = MathUtils.hash(0.0);
      assertEquals(0, int0);
      
      double double0 = MathUtils.log(16.0, 0.0);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      
      double double1 = MathUtils.cosh(0.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      byte byte0 = MathUtils.indicator((byte)0);
      assertEquals((byte)1, byte0);
      
      double double2 = MathUtils.round(0.0, 0, 0);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      boolean boolean0 = MathUtils.equals((-2225.661959866106), (double) 0, (-2225.661959866106));
      assertFalse(boolean0);
      
      double double3 = MathUtils.normalizeAngle(0.0, 0.0);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(0.0, double3, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = bigInteger0.setBit(0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger0.pow(63);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger0);
      assertNotNull(bigInteger2);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, 176);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger3);
      assertSame(bigInteger3, bigInteger0);
      assertSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotNull(bigInteger3);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger3.byteValue());
      assertEquals((short)0, bigInteger3.shortValue());
      
      BigInteger bigInteger4 = MathUtils.pow(bigInteger0, bigInteger3);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertFalse(bigInteger4.equals((Object)bigInteger0));
      assertFalse(bigInteger4.equals((Object)bigInteger2));
      assertFalse(bigInteger4.equals((Object)bigInteger3));
      assertTrue(bigInteger4.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger4);
      assertSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger4);
      assertSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotSame(bigInteger4, bigInteger1);
      assertNotSame(bigInteger4, bigInteger0);
      assertNotSame(bigInteger4, bigInteger2);
      assertNotSame(bigInteger4, bigInteger3);
      assertNotNull(bigInteger4);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger3.byteValue());
      assertEquals((short)0, bigInteger3.shortValue());
      assertEquals((short)1, bigInteger4.shortValue());
      assertEquals((byte)1, bigInteger4.byteValue());
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.0;
      int int1 = MathUtils.hash(doubleArray0);
      assertFalse(int1 == int0);
      assertEquals(31, int1);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      double[][] doubleArray1 = new double[5][2];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      double[] doubleArray2 = new double[0];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray1[3] = doubleArray2;
      double[] doubleArray3 = new double[4];
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      
      doubleArray3[0] = (double) 176;
      doubleArray3[1] = 0.0;
      doubleArray3[2] = (double) 176;
      doubleArray3[3] = (double) 0;
      doubleArray1[4] = doubleArray3;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 0 != 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, 1089);
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long0 = 2561L;
      long long1 = MathUtils.lcm((long) 1089, 2561L);
      assertFalse(long1 == long0);
      assertEquals(2788929L, long1);
      
      // Undeclared exception!
      try { 
        MathUtils.round((double) 0.0F, (-2497), (-723));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)5);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      int int0 = MathUtils.sign(0);
      assertEquals(0, int0);
      
      double double0 = MathUtils.factorialLog(1730);
      assertEquals(11173.313594407355, double0, 0.01);
      
      int[] intArray0 = new int[2];
      intArray0[0] = 0;
      intArray0[1] = 0;
      int int1 = MathUtils.distance1(intArray0, intArray0);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      assertArrayEquals(new int[] {0, 0}, intArray0);
      assertEquals(2, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      long long0 = MathUtils.indicator(118L);
      assertEquals(1L, long0);
      
      long long1 = MathUtils.sign(118L);
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
      
      int int0 = MathUtils.gcd((-2133), (-2133));
      assertEquals(2133, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(-0.0F, 3010.0F, 3995);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) 3010.0F, 0.0, (double) 3995);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      double double0 = MathUtils.cosh((-808.25));
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (-808.25);
      doubleArray0[1] = (-1624.99672);
      doubleArray0[2] = (double) 1L;
      double[][] doubleArray1 = new double[4][5];
      double[] doubleArray2 = new double[5];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 3995;
      doubleArray2[1] = Double.POSITIVE_INFINITY;
      doubleArray2[2] = (double) 3995;
      doubleArray2[3] = (double) 1L;
      doubleArray2[4] = (double) 1L;
      doubleArray1[0] = doubleArray2;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 5 != 3
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      int int0 = bigInteger0.compareTo(bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals(1, int0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, 91);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotNull(bigInteger2);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      
      long long0 = MathUtils.subAndCheck(39916800L, 39916800L);
      assertEquals(0L, long0);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) 1;
      doubleArray0[1] = (double) 39916800L;
      doubleArray0[2] = (double) 1;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[4][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {1.0, 1.0, 3.99168E7}, doubleArray0, 0.01);
      assertEquals(3, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      boolean boolean0 = bigInteger0.isProbablePrime(334);
      assertFalse(boolean0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = BigInteger.ZERO;
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = bigInteger0.or(bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertTrue(bigInteger2.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotNull(bigInteger2);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-835));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-835)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      Double double0 = new Double(1149.953717259403);
      assertEquals(1149.953717259403, (double)double0, 0.01);
      assertNotNull(double0);
      
      double double1 = Double.sum(596.0, 0.0);
      assertEquals(596.0, double1, 0.01);
      
      double double2 = Double.sum(1149.953717259403, 0.0);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(1149.953717259403, double2, 0.01);
      
      boolean boolean0 = Double.isFinite(1149.953717259403);
      assertTrue(boolean0);
      
      MathUtils.checkNotNull((Object) double0);
      assertEquals((double)double0, (double)double2, 0.01);
      assertNotEquals((double)double0, (double)double1, 0.01);
      
      long long0 = MathUtils.gcd(1393L, 1393L);
      assertEquals(1393L, long0);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 1393L;
      doubleArray0[1] = 1149.953717259403;
      doubleArray0[2] = (double) 1393L;
      doubleArray0[3] = (double) 1393L;
      double double3 = MathUtils.distance(doubleArray0, doubleArray0);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(0.0, double3, 0.01);
      assertArrayEquals(new double[] {1393.0, 1149.953717259403, 1393.0, 1393.0}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      
      double double4 = MathUtils.log(753.576, 0.0);
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertEquals(Double.NEGATIVE_INFINITY, double4, 0.01);
      
      double double5 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(double5, double3, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double4, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(0.0, double5, 0.01);
      assertArrayEquals(new double[] {1393.0, 1149.953717259403, 1393.0, 1393.0}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      int int0 = MathUtils.addAndCheck((-2407), (-807));
      assertEquals((-3214), int0);
      
      double double0 = MathUtils.binomialCoefficientDouble(3041, 228);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      int int1 = MathUtils.gcd((-1370), (-1277));
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      byte byte0 = MathUtils.sign((byte)14);
      assertEquals((byte)1, byte0);
      
      boolean boolean0 = MathUtils.equals((-2506.13F), (float) (-2407));
      assertFalse(boolean0);
      
      int int2 = MathUtils.subAndCheck((-3214), (int) (byte)14);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals((-3228), int2);
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      int int0 = MathUtils.indicator(934);
      assertEquals(1, int0);
      
      double double0 = MathUtils.sign((double) 1);
      assertEquals(1.0, double0, 0.01);
      
      long long0 = MathUtils.lcm(1637L, (-833L));
      assertEquals(1363621L, long0);
      
      int[] intArray0 = new int[5];
      intArray0[0] = 1;
      intArray0[1] = 1;
      intArray0[2] = 934;
      intArray0[3] = 1;
      intArray0[4] = 1;
      double double1 = MathUtils.distance(intArray0, intArray0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new int[] {1, 1, 934, 1, 1}, intArray0);
      assertEquals(5, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(97.05F, 0.0F, 2274);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equals((-2446.43289256214), (-969.126350328), 1277);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      byte byte0 = MathUtils.indicator((byte) (-9));
      assertEquals((byte) (-1), byte0);
      
      int int0 = MathUtils.addAndCheck(1277, (-1320));
      assertEquals((-43), int0);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      int int0 = MathUtils.indicator((-2098));
      assertEquals((-1), int0);
      
      int int1 = MathUtils.subAndCheck((-550), (-1));
      assertFalse(int1 == int0);
      assertEquals((-549), int1);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) (-2098);
      doubleArray0[1] = (double) (-550);
      doubleArray0[2] = (-1110.49);
      doubleArray0[3] = (double) (-549);
      doubleArray0[4] = (double) (-2098);
      doubleArray0[5] = (double) (-1);
      doubleArray0[6] = (double) (-549);
      doubleArray0[7] = (double) (-549);
      doubleArray0[8] = (double) (-1);
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 3188.40714);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {(-2098.0), (-550.0), (-1110.49), (-549.0), (-2098.0), (-1.0), (-549.0), (-549.0), (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {891.2513612995286, 233.64549509758857, 471.7472470016748, 233.22068510650203, 891.2513612995286, 0.4248099910865247, 233.22068510650203, 233.22068510650203, 0.4248099910865247}, doubleArray1, 0.01);
      assertEquals(9, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      
      double double0 = MathUtils.distance(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(4777.492441714673, double0, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {(-2098.0), (-550.0), (-1110.49), (-549.0), (-2098.0), (-1.0), (-549.0), (-549.0), (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {891.2513612995286, 233.64549509758857, 471.7472470016748, 233.22068510650203, 891.2513612995286, 0.4248099910865247, 233.22068510650203, 233.22068510650203, 0.4248099910865247}, doubleArray1, 0.01);
      assertEquals(9, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, 0.0F, 1544.5F);
      assertTrue(boolean0);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      long long0 = MathUtils.lcm(1073741824L, 1073741824L);
      assertEquals(1073741824L, long0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) (-549), Float.NaN);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      Object[] objectArray0 = new Object[0];
      MathUtils.checkNotNull(object0, (Localizable) null, objectArray0);
      assertEquals(0, objectArray0.length);
      
      double[] doubleArray2 = new double[0];
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray2);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertArrayEquals(new double[] {(-2098.0), (-550.0), (-1110.49), (-549.0), (-2098.0), (-1.0), (-549.0), (-549.0), (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray2, 0.01);
      assertEquals(9, doubleArray0.length);
      assertEquals(0, doubleArray2.length);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-0.3058015757857271);
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {(-0.3058015757857271)}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      double double0 = MathUtils.binomialCoefficientDouble(61, 61);
      assertEquals(1.0, double0, 0.01);
      
      boolean boolean1 = MathUtils.equals((-531.986431679233), (-0.3058015757857271), 61);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      boolean boolean2 = MathUtils.equals((-0.3058015757857271), (-0.3058015757857271), 3473);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2);
      
      boolean boolean3 = MathUtils.equals(0.0, (-0.3058015757857271), 1.0);
      assertTrue(boolean3 == boolean0);
      assertTrue(boolean3 == boolean2);
      assertFalse(boolean3 == boolean1);
      assertTrue(boolean3);
      
      double double1 = MathUtils.sinh((-439.7444107312));
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-4.759277551223821E190), double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double double0 = 56.92;
      doubleArray0[0] = 56.92;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals((-821011117), int0);
      assertArrayEquals(new double[] {56.92}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      double double1 = (-3508.775688997);
      double double2 = 0.0;
      // Undeclared exception!
      try { 
        MathUtils.equals((-3508.775688997), 0.0, (-821011117));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(3.834E-20, 251.02602, (-1276));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      long long0 = 2202L;
      long long1 = MathUtils.addAndCheck(2202L, 2202L);
      assertFalse(long1 == long0);
      assertEquals(4404L, long1);
      
      int int0 = 1878;
      // Undeclared exception!
      try { 
        MathUtils.round((double) 2202L, 1571, 1878);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      int int0 = (-1);
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-1), 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 0, n = -1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      long long0 = MathUtils.pow(9223372036854775807L, 9223372036854775807L);
      assertEquals(9223372036854775807L, long0);
      
      long long1 = MathUtils.pow((-546L), 0);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      float float0 = MathUtils.indicator(1.0F);
      assertEquals(1.0F, float0, 0.01F);
      
      double double0 = MathUtils.binomialCoefficientDouble(0, 0);
      assertEquals(1.0, double0, 0.01);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) (-546L);
      doubleArray0[1] = (double) 1.0F;
      doubleArray0[2] = (double) 9223372036854775807L;
      doubleArray0[3] = (double) 0;
      doubleArray0[4] = (double) 0;
      doubleArray0[5] = (double) 1L;
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {(-546.0), 1.0, 9.223372036854776E18, 0.0, 0.0, 1.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      long long2 = MathUtils.lcm(9223372036854775807L, 0L);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(0L, long2);
      
      short short0 = MathUtils.indicator((short) (-127));
      assertEquals((short) (-1), short0);
      
      double double2 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      assertArrayEquals(new double[] {(-546.0), 1.0, 9.223372036854776E18, 0.0, 0.0, 1.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1110.25993500894), (double) 9223372036854775807L, (-2.2250738585072014E-308));
      assertFalse(boolean0);
      
      double double3 = MathUtils.binomialCoefficientDouble(205, 0);
      assertEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(1.0, double3, 0.01);
      
      int[] intArray0 = new int[4];
      intArray0[0] = 0;
      intArray0[1] = (int) (short) (-1);
      intArray0[2] = (int) (short) (-127);
      intArray0[3] = 0;
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
      assertArrayEquals(new int[] {0, (-1), (-127), 0}, intArray0);
      assertEquals(4, intArray0.length);
      
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 2 and 3 are not strictly increasing (9,223,372,036,854,776,000 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      int int0 = 896;
      double double0 = MathUtils.binomialCoefficientDouble(896, 0);
      assertEquals(1.0, double0, 0.01);
      
      long long0 = 0L;
      long long1 = MathUtils.gcd(0L, (long) 0);
      assertTrue(long1 == long0);
      assertEquals(0L, long1);
      
      double double1 = MathUtils.indicator(1.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      double double2 = MathUtils.round(1.0, 896);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(1.0, double2, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("zero not allowed here");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.zero not allowed here
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      double double0 = MathUtils.indicator((-366.842));
      assertEquals((-1.0), double0, 0.01);
      
      double double1 = MathUtils.binomialCoefficientLog(0, 0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 3143L);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 3143L, 0.0, Double.NEGATIVE_INFINITY);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      short short0 = MathUtils.sign((short)365);
      assertEquals((short)1, short0);
      
      MathUtils.checkFinite(611.157313250891);
      long long0 = MathUtils.pow((-262L), 0L);
      assertEquals(1L, long0);
      
      long long1 = MathUtils.addAndCheck((long) (short)1, (-1473L));
      assertFalse(long1 == long0);
      assertEquals((-1472L), long1);
      
      boolean boolean0 = MathUtils.equals((-673.43F), 0.0F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(0, 0);
      assertEquals(0, int0);
      
      int int1 = MathUtils.addAndCheck((-1942), 72);
      assertFalse(int1 == int0);
      assertEquals((-1870), int1);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) (-1870);
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = (double) (-1942);
      doubleArray0[3] = (double) 0;
      doubleArray0[4] = (double) 0;
      doubleArray0[5] = (double) 0;
      doubleArray0[6] = (double) (-1870);
      doubleArray0[7] = (double) 0;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly increasing (0 >= -1,942)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      long long0 = MathUtils.pow(0L, 524);
      assertEquals(0L, long0);
      
      long long1 = MathUtils.pow((long) 524, 524);
      assertTrue(long1 == long0);
      assertEquals(0L, long1);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 0L, (double) 524);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equals(1705.584F, 0.0F, (-1.4E-45F));
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      int int0 = MathUtils.sign(524);
      assertEquals(1, int0);
      
      long long2 = MathUtils.lcm((long) 1, 0L);
      assertTrue(long2 == long0);
      assertTrue(long2 == long1);
      assertEquals(0L, long2);
      
      short short0 = MathUtils.indicator((short)1156);
      assertEquals((short)1, short0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((-2376.221F), (float) 1);
      assertTrue(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, 0, 0);
      assertEquals(1.0F, float0, 0.01F);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 1.0F;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {1.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(1498.6225898, 1.0, 1.375);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      double double0 = MathUtils.indicator(0.0);
      assertEquals(1.0, double0, 0.01);
      
      float float0 = MathUtils.round(699.70636F, 0, 0);
      assertEquals(700.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(126, 126);
      assertEquals(15876, int0);
      
      int[] intArray0 = new int[4];
      intArray0[0] = 15876;
      intArray0[1] = 15876;
      intArray0[2] = 15876;
      intArray0[3] = 126;
      int[] intArray1 = MathUtils.copyOf(intArray0, 2611);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {15876, 15876, 15876, 126}, intArray0);
      assertEquals(4, intArray0.length);
      assertEquals(2611, intArray1.length);
      
      boolean boolean0 = MathUtils.equals(294.3593292531, (double) 15876, 15876);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float[]) null, (float[]) null);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      long long0 = MathUtils.pow(2112L, 2611);
      assertEquals(0L, long0);
      
      float float0 = MathUtils.indicator((-3368.7314F));
      assertEquals((-1.0F), float0, 0.01F);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(610.0F, 610.0F, 0.0F);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertTrue(boolean2);
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (-803.426645081585);
      doubleArray0[3] = (-2510.0485396);
      doubleArray0[4] = (-1.0E-6);
      doubleArray0[5] = 700.7924088271;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {0.0, 0.0, (-803.426645081585), (-2510.0485396), (-1.0E-6), 700.7924088271}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      long long0 = MathUtils.indicator((-1654L));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 167.440062;
      doubleArray0[1] = 630.0125433514978;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {167.440062, 630.0125433514978}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = bigInteger0.flipBit(11);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)2048, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, 0L);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotNull(bigInteger2);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      
      long long0 = MathUtils.pow(0L, 60);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      int int0 = MathUtils.indicator(31);
      assertEquals(1, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, 0.0, 1);
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equals(0.0, 2.1913591, 31);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float[]) null, (float[]) null);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2);
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      float[] floatArray0 = new float[0];
      float[] floatArray1 = new float[3];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = 1767.606F;
      floatArray1[1] = (-309.32397F);
      floatArray1[2] = 1301.7F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {1767.606F, (-309.32397F), 1301.7F}, floatArray1, 0.01F);
      assertEquals(0, floatArray0.length);
      assertEquals(3, floatArray1.length);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) 1767.606F, (double) 1301.7F, Double.NEGATIVE_INFINITY);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      long long0 = (-2324L);
      long long1 = MathUtils.pow((-2324L), 3823L);
      assertFalse(long1 == long0);
      assertEquals(0L, long1);
      
      int int0 = 1052;
      int int1 = MathUtils.addAndCheck(1052, 1052);
      assertFalse(int1 == int0);
      assertEquals(2104, int1);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 1301.7F;
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {1301.699951171875}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float) 1052, (float) 1052, (float) (-2324L));
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2);
      
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1791.95F), (-1791.95F), 429.32F);
      assertTrue(boolean0);
      
      int int0 = MathUtils.indicator(9977);
      assertEquals(1, int0);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-3272), (-3272));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -3,272
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(2.0, 2.0);
      assertTrue(boolean0);
      
      long long0 = MathUtils.pow(3914L, 3914L);
      assertEquals(0L, long0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 0L;
      doubleArray0[2] = (double) 3914L;
      doubleArray0[3] = 2.0;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (double) 3914L;
      doubleArray0[6] = (double) 3914L;
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 3914.0, 2.0, 0.0, 3914.0, 3914.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      int int0 = MathUtils.hash(0.0);
      assertEquals(0, int0);
      
      float[] floatArray0 = new float[5];
      floatArray0[0] = 2481.5913F;
      floatArray0[1] = (float) 0;
      floatArray0[2] = (float) 0;
      floatArray0[3] = (float) 0;
      floatArray0[4] = (float) 0;
      float[] floatArray1 = new float[0];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertArrayEquals(new float[] {2481.5913F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {}, floatArray1, 0.01F);
      assertEquals(5, floatArray0.length);
      assertEquals(0, floatArray1.length);
      
      int[] intArray0 = new int[8];
      intArray0[0] = 0;
      intArray0[1] = 0;
      intArray0[2] = 0;
      intArray0[3] = 0;
      intArray0[4] = 0;
      intArray0[5] = 0;
      intArray0[6] = 0;
      intArray0[7] = 0;
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0, 0}, intArray0);
      assertEquals(8, intArray0.length);
      
      double double0 = MathUtils.round((double) 0.0F, 0);
      assertEquals(0.0, double0, 0.01);
      
      long long0 = MathUtils.sign((-1825L));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(3.834E-20, (-2731.4532414857613));
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-2731.4532414857613);
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals((-807285735), int0);
      assertArrayEquals(new double[] {(-2731.4532414857613)}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {(-2731.4532414857613)}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      int int1 = MathUtils.subAndCheck(2427, 93);
      assertFalse(int1 == int0);
      assertEquals(2334, int1);
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      float float0 = MathUtils.sign((-878.85F));
      assertEquals((-1.0F), float0, 0.01F);
      
      boolean boolean0 = MathUtils.equals((double) (-1.0F), (double) (-878.85F));
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equals(1537.32, 1171.12812, 0.0);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      long long0 = MathUtils.subAndCheck(355687428096000L, (-1469L));
      assertEquals(355687428097469L, long0);
      
      byte[] byteArray0 = new byte[7];
      byteArray0[0] = (byte)0;
      byteArray0[1] = (byte) (-39);
      byteArray0[2] = (byte) (-78);
      byteArray0[3] = (byte) (-10);
      byteArray0[4] = (byte)20;
      byteArray0[5] = (byte)9;
      byteArray0[6] = (byte)90;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((short)2394, bigInteger0.shortValue());
      assertEquals((byte)90, bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte)0, (byte) (-39), (byte) (-78), (byte) (-10), (byte)20, (byte)9, (byte)90}, byteArray0);
      assertEquals(7, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (long) (byte)0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)2394, bigInteger0.shortValue());
      assertEquals((byte)90, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertArrayEquals(new byte[] {(byte)0, (byte) (-39), (byte) (-78), (byte) (-10), (byte)20, (byte)9, (byte)90}, byteArray0);
      assertEquals(7, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      double double0 = MathUtils.SAFE_MIN;
      assertEquals(2.2250738585072014E-308, double0, 0.01);
      
      long long0 = MathUtils.subAndCheck(2600L, (-4561758734726358630L));
      assertEquals(4561758734726361230L, long0);
      
      long long1 = MathUtils.subAndCheck((-117L), 479001600L);
      assertFalse(long1 == long0);
      assertEquals((-479001717L), long1);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) 2600L;
      doubleArray0[1] = (double) 479001600L;
      doubleArray0[2] = (double) 4561758734726361230L;
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {2600.0, 4.790016E8, 4.5617587347263611E18}, doubleArray0, 0.01);
      assertEquals(3, doubleArray0.length);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(2146, (-197L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-197)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      double double0 = 0.0;
      int int0 = MathUtils.compareTo(3.814697265625E-6, 0.09090909090909091, 0.0);
      assertEquals((-1), int0);
      
      int[] intArray0 = new int[7];
      intArray0[0] = (-1);
      intArray0[1] = 661;
      intArray0[2] = (-1);
      intArray0[3] = (-1);
      intArray0[4] = (-1);
      intArray0[5] = (-1);
      intArray0[6] = (-1);
      // Undeclared exception!
      try { 
        MathUtils.copyOf(intArray0, (-1));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      int[] intArray0 = new int[1];
      intArray0[0] = 3974;
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new int[] {3974}, intArray0);
      assertEquals(1, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      float float0 = MathUtils.round(462.3633F, 3191);
      assertEquals(Float.NaN, float0, 0.01F);
      
      boolean boolean0 = MathUtils.equals((double) 462.3633F, (-653.748), (double) Float.NaN);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      long long0 = MathUtils.indicator(0L);
      assertEquals(1L, long0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 1L;
      doubleArray0[1] = (double) 0L;
      doubleArray0[2] = (double) 0L;
      doubleArray0[3] = (double) 0L;
      doubleArray0[4] = (double) 0L;
      doubleArray0[5] = (double) 1L;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(1.4142135623730951, double0, 0.01);
      assertArrayEquals(new double[] {1.0, 0.0, 0.0, 0.0, 0.0, 1.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      long long1 = MathUtils.gcd(0L, 0L);
      assertFalse(long1 == long0);
      assertEquals(0L, long1);
      
      int int0 = MathUtils.compareTo((-2090.0), 0L, 1.0);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      double double0 = 3818.1268309568;
      int int0 = MathUtils.hash(3818.1268309568);
      assertEquals((-1353620633), int0);
      
      int int1 = 7;
      int int2 = Integer.MIN_VALUE;
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(7, Integer.MIN_VALUE);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      long long0 = MathUtils.lcm((-4872L), (-4872L));
      assertEquals(4872L, long0);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (-0.33333333333333287);
      doubleArray0[1] = (double) (-4872L);
      doubleArray0[2] = (double) 4872L;
      doubleArray0[3] = (double) (-4872L);
      doubleArray0[4] = (double) 4872L;
      doubleArray0[5] = (double) 4872L;
      doubleArray0[6] = (-3350.0);
      doubleArray0[7] = (double) 4872L;
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {(-0.33333333333333287), (-4872.0), 4872.0, (-4872.0), 4872.0, 4872.0, (-3350.0), 4872.0}, doubleArray0, 0.01);
      assertEquals(8, doubleArray0.length);
      
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {(-0.33333333333333287), (-4872.0), 4872.0, (-4872.0), 4872.0, 4872.0, (-3350.0), 4872.0}, doubleArray0, 0.01);
      assertEquals(8, doubleArray0.length);
      
      // Undeclared exception!
      try { 
        MathUtils.factorialDouble((-1115));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1,115
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)31);
      assertEquals((byte)1, byte0);
      
      double double0 = MathUtils.factorialDouble(3864);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      long long0 = MathUtils.indicator(0L);
      assertEquals(1L, long0);
      
      short short0 = MathUtils.indicator((short)3047);
      assertEquals((short)1, short0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) (byte)1;
      doubleArray0[1] = 621.4;
      doubleArray0[2] = (double) 3864;
      doubleArray0[3] = (double) 0L;
      doubleArray0[4] = (double) (short)1;
      doubleArray0[5] = (double) (byte)1;
      doubleArray0[6] = (double) 3864;
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {1.0, 621.4, 3864.0, 0.0, 1.0, 1.0, 3864.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      float float0 = (-467.866F);
      float float1 = 0.0F;
      int int0 = 14;
      boolean boolean0 = MathUtils.equals((-467.866F), 0.0F, 14);
      assertFalse(boolean0);
      
      int int1 = 0;
      // Undeclared exception!
      try { 
        MathUtils.round((-1052.260265756), 0, 14);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      int[] intArray0 = new int[4];
      int int0 = 0;
      intArray0[0] = 0;
      int int1 = (-2870);
      intArray0[1] = (-2870);
      intArray0[2] = (-2208);
      int int2 = 0;
      intArray0[3] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {0, (-2870), (-2208), 0}, intArray0);
      assertArrayEquals(new int[] {0, (-2870), (-2208), 0}, intArray1);
      assertEquals(4, intArray0.length);
      assertEquals(4, intArray1.length);
      
      long long0 = MathUtils.sign((long) 0);
      assertEquals(0L, long0);
      
      long long1 = MathUtils.subAndCheck(0L, 0L);
      assertTrue(long1 == long0);
      assertEquals(0L, long1);
      
      // Undeclared exception!
      try { 
        MathUtils.equals((float) 0, (float) (-2870), 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      double double0 = 0.0;
      double double1 = (-623.82677485);
      int int0 = MathUtils.compareTo(0.0, 0.0, (-623.82677485));
      assertEquals(0, int0);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (double) 0;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      
      byte byte0 = MathUtils.sign((byte) (-83));
      assertEquals((byte) (-1), byte0);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = BigInteger.ZERO;
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger0.and(bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertTrue(bigInteger2.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotNull(bigInteger2);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      assertEquals((short)0, bigInteger2.shortValue());
      
      BigInteger bigInteger3 = bigInteger0.abs();
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotNull(bigInteger3);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((byte)10, bigInteger3.byteValue());
      assertEquals((short)10, bigInteger3.shortValue());
      
      BigInteger bigInteger4 = MathUtils.pow(bigInteger0, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger4.equals((Object)bigInteger0));
      assertFalse(bigInteger4.equals((Object)bigInteger3));
      assertFalse(bigInteger4.equals((Object)bigInteger2));
      assertFalse(bigInteger4.equals((Object)bigInteger1));
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger4, bigInteger0);
      assertNotSame(bigInteger4, bigInteger3);
      assertNotSame(bigInteger4, bigInteger2);
      assertNotSame(bigInteger4, bigInteger1);
      assertNotNull(bigInteger4);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger4.byteValue());
      assertEquals((short) (-7168), bigInteger4.shortValue());
      
      float float0 = 0.0F;
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(0.0F, 0.0F, (int) (byte) (-83));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      int int0 = 0;
      double double0 = MathUtils.binomialCoefficientDouble(0, 0);
      assertEquals(1.0, double0, 0.01);
      
      MathUtils.checkFinite((double) 0);
      double[][] doubleArray0 = new double[5][8];
      doubleArray0[0] = null;
      doubleArray0[1] = null;
      doubleArray0[2] = null;
      doubleArray0[3] = null;
      doubleArray0[4] = null;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-1.0F), 0.0F, 720.0F);
      assertTrue(boolean0);
      
      int int0 = MathUtils.lcm(800, 800);
      assertEquals(800, int0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) (-1.0F);
      doubleArray0[1] = (-1740.3305271);
      doubleArray0[2] = (double) 800;
      doubleArray0[3] = (double) 800;
      doubleArray0[4] = (double) (-1.0F);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[0][8];
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {(-1740.3305271), (-1.0), (-1.0), 800.0, 800.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      
      MathUtils.checkNotNull((Object) mathUtils_OrderDirection0);
      int int1 = MathUtils.pow(5, 5);
      assertFalse(int1 == int0);
      assertEquals(3125, int1);
      
      long long0 = MathUtils.binomialCoefficient(3514, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      int int0 = MathUtils.gcd(1389, 1389);
      assertEquals(1389, int0);
      
      long long0 = MathUtils.sign(0L);
      assertEquals(0L, long0);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 1389;
      doubleArray0[2] = (double) 1389;
      doubleArray0[3] = (double) 0L;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 1389.0, 1389.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 1389.0, 1389.0, 0.0}, doubleArray1, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      
      boolean boolean0 = MathUtils.equals((float) 1389, (-1010.0F));
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) 0L, (-167.27F), 3.4028235E38F);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      long long1 = MathUtils.pow(0L, 0L);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float) 0L, 2903.653F, (float) 0L);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2);
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      int int0 = MathUtils.gcd(332, 332);
      assertEquals(332, int0);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0L);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      boolean boolean0 = MathUtils.equals((double) 332, (double) 332);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F, 0.0F);
      assertTrue(boolean0);
      
      int[] intArray0 = new int[2];
      intArray0[0] = 0;
      intArray0[1] = 182;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {0, 182}, intArray0);
      assertArrayEquals(new int[] {0, 182}, intArray1);
      assertEquals(2, intArray0.length);
      assertEquals(2, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, 0.0);
      assertTrue(boolean0);
      
      int int0 = MathUtils.lcm(0, 0);
      assertEquals(0, int0);
      
      float float0 = MathUtils.sign((float) 0);
      assertEquals(0.0F, float0, 0.01F);
      
      long long0 = MathUtils.binomialCoefficient(1896, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      int int0 = (-1805);
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-1805), 177);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 177, n = -1,805
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      int int0 = MathUtils.addAndCheck((-313), (-313));
      assertEquals((-626), int0);
      
      double double0 = MathUtils.cosh((-313));
      assertEquals(4.296777251231721E135, double0, 0.01);
      
      double double1 = MathUtils.cosh((-484.1));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(8.728280480128627E209, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(6146, 6146);
      assertEquals(0, int0);
      
      long long0 = MathUtils.subAndCheck(347L, (long) 0);
      assertEquals(347L, long0);
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double[]) null, (double[]) null);
      assertTrue(boolean0);
      
      long long0 = MathUtils.gcd(0L, 0L);
      assertEquals(0L, long0);
      
      double double0 = MathUtils.normalizeAngle(0L, 0L);
      assertEquals(0.0, double0, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) 0L, 0.0);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      byte byte0 = MathUtils.sign((byte)61);
      assertEquals((byte)1, byte0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((double) (byte)61, 0.0, (int) (byte)61);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      float float0 = MathUtils.indicator(0.0F);
      assertEquals(1.0F, float0, 0.01F);
      
      Integer integer0 = new Integer(0);
      assertEquals(0, (int)integer0);
      assertNotNull(integer0);
      
      MathUtils.checkNotNull((Object) integer0);
      double double0 = MathUtils.sinh(0);
      assertEquals(0.0, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(1.0F, 50.59949F, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.875, 0.875);
      assertTrue(boolean0);
      
      int int0 = MathUtils.compareTo(0.875, 0.875, 4469.975122232336);
      assertEquals(0, int0);
      
      int int1 = MathUtils.subAndCheck(117, 117);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      int int0 = MathUtils.lcm(174, 0);
      assertEquals(0, int0);
      
      int int1 = MathUtils.compareTo(1.375, 0, 0);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-2022.349F), 0.0F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      float float0 = MathUtils.indicator(0.0F);
      assertEquals(1.0F, float0, 0.01F);
      
      boolean boolean0 = MathUtils.equals((double[]) null, (double[]) null);
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double[]) null, (double[]) null);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      double[][] doubleArray0 = new double[8][5];
      doubleArray0[0] = null;
      doubleArray0[1] = null;
      doubleArray0[2] = null;
      doubleArray0[3] = null;
      doubleArray0[4] = null;
      doubleArray0[5] = null;
      doubleArray0[6] = null;
      doubleArray0[7] = null;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      MathUtils.checkFinite(0.0);
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(3, 3);
      assertEquals(1.0, double0, 0.01);
      
      int[] intArray0 = new int[7];
      intArray0[0] = 3;
      intArray0[1] = 3;
      intArray0[2] = 3;
      intArray0[3] = 3;
      intArray0[4] = 3;
      intArray0[5] = 3;
      intArray0[6] = 3;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {3, 3, 3, 3, 3, 3, 3}, intArray0);
      assertArrayEquals(new int[] {3, 3, 3, 3, 3, 3, 3}, intArray1);
      assertEquals(7, intArray0.length);
      assertEquals(7, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorial(63);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck((-478), 951);
      assertEquals((-454578), int0);
      
      long long0 = MathUtils.mulAndCheck((long) 951, (long) (-478));
      assertEquals((-454578L), long0);
      
      double double0 = MathUtils.factorialDouble(951);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      float[] floatArray0 = new float[1];
      floatArray0[0] = (float) (-454578);
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertArrayEquals(new float[] {(-454578.0F)}, floatArray0, 0.01F);
      assertEquals(1, floatArray0.length);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) (-454578.0F);
      doubleArray0[1] = (double) (-454578.0F);
      doubleArray0[2] = (double) (-454578L);
      doubleArray0[3] = 6.283185307179586;
      doubleArray0[4] = (double) (-454578L);
      doubleArray0[5] = Double.POSITIVE_INFINITY;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean1 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, false);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      assertArrayEquals(new double[] {(-454578.0), (-454578.0), (-454578.0), 6.283185307179586, (-454578.0), Double.POSITIVE_INFINITY}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      
      byte byte0 = MathUtils.sign((byte) (-49));
      assertEquals((byte) (-1), byte0);
      
      int int1 = MathUtils.indicator(951);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      int int2 = MathUtils.subAndCheck((-454578), 11);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals((-454589), int2);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((byte) (-49), 86);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 86, n = -49
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      int int0 = MathUtils.sign(17);
      assertEquals(1, int0);
      
      byte byte0 = MathUtils.indicator((byte) (-17));
      assertEquals((byte) (-1), byte0);
      
      int int1 = 4604;
      double double0 = MathUtils.factorialDouble(4604);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      int int2 = MathUtils.pow((int) (byte) (-1), (long) 4604);
      assertTrue(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(1, int2);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) 17;
      doubleArray0[1] = Double.POSITIVE_INFINITY;
      doubleArray0[2] = (double) 17;
      doubleArray0[3] = (double) (byte) (-1);
      doubleArray0[4] = (double) (byte) (-1);
      doubleArray0[5] = (double) 17;
      doubleArray0[6] = (double) (byte) (-17);
      doubleArray0[7] = (double) 17;
      doubleArray0[8] = (double) (byte) (-17);
      double double1 = MathUtils.safeNorm(doubleArray0);
      assertEquals(double1, double0, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      assertArrayEquals(new double[] {17.0, Double.POSITIVE_INFINITY, 17.0, (-1.0), (-1.0), 17.0, (-17.0), 17.0, (-17.0)}, doubleArray0, 0.01);
      assertEquals(9, doubleArray0.length);
      
      double[] doubleArray1 = new double[2];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) 4604;
      doubleArray1[1] = (double) 17;
      double double2 = MathUtils.safeNorm(doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(4604.031385644542, double2, 0.01);
      assertNotSame(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {4604.0, 17.0}, doubleArray1, 0.01);
      assertEquals(2, doubleArray1.length);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((byte) (-1), 1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1, n = -1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test226()  throws Throwable  {
      short short0 = MathUtils.indicator((short) (-569));
      assertEquals((short) (-1), short0);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) (short) (-1);
      doubleArray0[1] = (double) (short) (-569);
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (double) (short) (-1);
      doubleArray0[4] = (double) (short) (-1);
      doubleArray0[5] = (double) (short) (-1);
      doubleArray0[6] = (double) (short) (-1);
      doubleArray0[7] = (double) (short) (-1);
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(1146107649, int0);
      assertArrayEquals(new double[] {(-1.0), (-569.0), 0.0, (-1.0), (-1.0), (-1.0), (-1.0), (-1.0)}, doubleArray0, 0.01);
      assertEquals(8, doubleArray0.length);
      
      long long0 = MathUtils.mulAndCheck(6227020800L, (long) (short) (-1));
      assertEquals((-6227020800L), long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, (float) (short) (-1), (-87.8228F));
      assertFalse(boolean0);
      
      int int1 = MathUtils.hash((-1.0));
      assertFalse(int1 == int0);
      assertEquals((-1074790400), int1);
      
      int int2 = MathUtils.indicator(1146107649);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(1, int2);
      
      int int3 = MathUtils.lcm(1146107649, 1146107649);
      assertFalse(int3 == int2);
      assertTrue(int3 == int0);
      assertFalse(int3 == int1);
      assertEquals(1146107649, int3);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly decreasing (-569 <= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test227()  throws Throwable  {
      double double0 = MathUtils.round(Double.NaN, 4194304, 4194304);
      assertEquals(Double.NaN, double0, 0.01);
      
      Integer integer0 = new Integer(4194304);
      assertEquals(4194304, (int)integer0);
      assertNotNull(integer0);
      
      int int0 = MathUtils.gcd(1332, 1332);
      assertEquals(1332, int0);
      
      long long0 = MathUtils.subAndCheck(85L, 9L);
      assertEquals(76L, long0);
      
      int int1 = MathUtils.compareTo((double) integer0, 0.0, (-939.14));
      assertFalse(integer0.equals((Object)int0));
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0L);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger1.and(bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotNull(bigInteger2);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      
      BigInteger bigInteger3 = bigInteger1.shiftRight(388);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertTrue(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotNull(bigInteger3);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((short)0, bigInteger3.shortValue());
      assertEquals((byte)0, bigInteger3.byteValue());
      
      BigInteger bigInteger4 = bigInteger0.mod(bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger3));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger4.equals((Object)bigInteger1));
      assertFalse(bigInteger4.equals((Object)bigInteger0));
      assertTrue(bigInteger4.equals((Object)bigInteger2));
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger4);
      assertNotSame(bigInteger4, bigInteger1);
      assertNotSame(bigInteger4, bigInteger0);
      assertNotSame(bigInteger4, bigInteger2);
      assertSame(bigInteger4, bigInteger3);
      assertNotNull(bigInteger4);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger4.byteValue());
      assertEquals((short)0, bigInteger4.shortValue());
      
      BigInteger bigInteger5 = MathUtils.pow(bigInteger0, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger4));
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger5.equals((Object)bigInteger0));
      assertFalse(bigInteger5.equals((Object)bigInteger2));
      assertFalse(bigInteger5.equals((Object)bigInteger4));
      assertFalse(bigInteger5.equals((Object)bigInteger1));
      assertFalse(bigInteger5.equals((Object)bigInteger3));
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger5);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger5, bigInteger0);
      assertNotSame(bigInteger5, bigInteger2);
      assertNotSame(bigInteger5, bigInteger4);
      assertNotSame(bigInteger5, bigInteger1);
      assertNotSame(bigInteger5, bigInteger3);
      assertNotNull(bigInteger5);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger5.byteValue());
      assertEquals((short) (-7168), bigInteger5.shortValue());
      
      int[] intArray0 = new int[0];
      int int2 = MathUtils.distance1(intArray0, intArray0);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(0, int2);
      assertArrayEquals(new int[] {}, intArray0);
      assertEquals(0, intArray0.length);
      
      boolean boolean0 = MathUtils.equals((double) 388, 0.0, (double) 1332);
      assertTrue(boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.factorialDouble((-1599));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1,599
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test228()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(1255, 86);
      assertEquals(310.3988276674329, double0, 0.01);
      
      float[] floatArray0 = new float[9];
      floatArray0[0] = (float) 1255;
      floatArray0[1] = (float) 86;
      floatArray0[2] = (float) 1255;
      floatArray0[3] = (float) 1255;
      floatArray0[4] = (float) 1255;
      floatArray0[5] = (float) 1255;
      floatArray0[6] = (float) 1255;
      floatArray0[7] = (float) 86;
      floatArray0[8] = (float) 1255;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertArrayEquals(new float[] {1255.0F, 86.0F, 1255.0F, 1255.0F, 1255.0F, 1255.0F, 1255.0F, 86.0F, 1255.0F}, floatArray0, 0.01F);
      assertEquals(9, floatArray0.length);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) 1255.0F, (double) 1255.0F);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      boolean boolean2 = MathUtils.equals((double) 1255.0F, 310.3988276674329, 2674);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 310.3988276674329;
      double double1 = MathUtils.distance(doubleArray0, doubleArray0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {310.3988276674329}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      
      double double2 = MathUtils.binomialCoefficientLog(2674, 143);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(554.5063350608394, double2, 0.01);
      
      float float0 = MathUtils.round(1255.0F, 143, 0);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test229()  throws Throwable  {
      short short0 = MathUtils.sign((short)1192);
      assertEquals((short)1, short0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) (short)1, 1227.0);
      assertFalse(boolean0);
      
      double double0 = (-429.7055715791);
      int int0 = 286;
      boolean boolean1 = MathUtils.equals((-429.7055715791), 1403.1138328, 286);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double double1 = 4801.5;
      double double2 = MathUtils.sign(4801.5);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(1.0, double2, 0.01);
      
      int int1 = MathUtils.compareTo(1.0, 232.3556838, 1835.0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      int int2 = 1143400512;
      // Undeclared exception!
      MathUtils.factorialLog(1143400512);
  }

  @Test(timeout = 4000)
  public void test230()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte) (-64));
      assertEquals((byte) (-1), byte0);
      
      double double0 = MathUtils.EPSILON;
      assertEquals(1.1102230246251565E-16, double0, 0.01);
      
      int int0 = MathUtils.pow(158, 886L);
      assertEquals(0, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1.0F), 1092.4F, (float) (byte) (-64));
      assertFalse(boolean0);
      
      int[] intArray0 = new int[19];
      intArray0[0] = (int) (byte) (-1);
      intArray0[1] = 158;
      intArray0[2] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0, 158);
      assertFalse(intArray1.equals((Object)intArray0));
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertEquals(19, intArray0.length);
      assertEquals(158, intArray1.length);
      
      long long0 = MathUtils.subAndCheck((long) (-1), 36L);
      assertEquals((-37L), long0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 1.1102230246251565E-16;
      doubleArray0[1] = (double) (byte) (-1);
      doubleArray0[2] = (double) (byte) (-1);
      doubleArray0[3] = (double) (byte) (-64);
      doubleArray0[4] = (double) (-37L);
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {1.1102230246251565E-16, (-1.0), (-1.0), (-64.0), (-37.0)}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new double[] {1.1102230246251565E-16, (-1.0), (-1.0), (-64.0), (-37.0)}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      
      long long1 = MathUtils.binomialCoefficient(692, 3);
      assertFalse(long1 == long0);
      assertEquals(54989780L, long1);
      
      int int1 = MathUtils.pow((-1), (long) 692);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[1][3];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {(-64.0), (-37.0), (-1.0), (-1.0), 1.1102230246251565E-16}, doubleArray0, 0.01);
      assertEquals(1, doubleArray1.length);
      assertEquals(5, doubleArray0.length);
      
      long long2 = MathUtils.gcd(0L, 36L);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(36L, long2);
      
      int[] intArray2 = new int[3];
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray2.equals((Object)intArray0));
      
      intArray2[0] = 3;
      intArray2[1] = (int) (byte) (-64);
      intArray2[2] = (int) (byte) (-1);
      double double2 = MathUtils.distance(intArray2, intArray1);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray0.equals((Object)intArray2));
      assertFalse(intArray1.equals((Object)intArray0));
      assertFalse(intArray1.equals((Object)intArray2));
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray2.equals((Object)intArray0));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(222.03828498707153, double2, 0.01);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray0, intArray2);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray1, intArray2);
      assertNotSame(intArray2, intArray1);
      assertNotSame(intArray2, intArray0);
      assertArrayEquals(new int[] {3, (-64), (-1)}, intArray2);
      assertEquals(19, intArray0.length);
      assertEquals(158, intArray1.length);
      assertEquals(3, intArray2.length);
  }

  @Test(timeout = 4000)
  public void test231()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((-298L), (-298L));
      assertEquals(0L, long0);
      
      int int0 = 9;
      long long1 = MathUtils.pow((-298L), 9);
      assertFalse(long1 == long0);
      assertEquals(5950015712561403392L, long1);
      
      int int1 = MathUtils.gcd((-711), 215);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      int[] intArray0 = new int[4];
      intArray0[0] = (-711);
      intArray0[1] = 1076;
      intArray0[2] = 1;
      intArray0[3] = (-711);
      int int2 = MathUtils.distanceInf(intArray0, intArray0);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(0, int2);
      assertArrayEquals(new int[] {(-711), 1076, 1, (-711)}, intArray0);
      assertEquals(4, intArray0.length);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 1076;
      doubleArray0[1] = (double) 215;
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = (double) 0;
      doubleArray0[4] = (double) 5950015712561403392L;
      doubleArray0[5] = (double) 0;
      double[] doubleArray1 = new double[6];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) (-298L);
      doubleArray1[1] = (double) 1;
      doubleArray1[2] = (double) (-711);
      doubleArray1[3] = (double) 1;
      doubleArray1[4] = (double) 0L;
      doubleArray1[5] = (double) 1;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertEquals(5.950015712561406E18, double0, 0.01);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-298.0), 1.0, (-711.0), 1.0, 0.0, 1.0}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {1076.0, 215.0, 0.0, 0.0, 5.9500157125614039E18, 0.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray1.length);
      assertEquals(6, doubleArray0.length);
      
      double double1 = MathUtils.distance(intArray0, intArray0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      assertArrayEquals(new int[] {(-711), 1076, 1, (-711)}, intArray0);
      assertEquals(4, intArray0.length);
      
      int int3 = MathUtils.sign(1076);
      assertTrue(int3 == int1);
      assertFalse(int3 == int2);
      assertFalse(int3 == int0);
      assertEquals(1, int3);
      
      // Undeclared exception!
      try { 
        MathUtils.factorial(215);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test232()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(8, 6);
      assertEquals(3.332204510175204, double0, 0.01);
      
      MathUtils.indicator((byte) (-34));
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) (byte) (-34);
      doubleArray0[1] = 3.332204510175204;
      doubleArray0[2] = (double) (byte) (-34);
      doubleArray0[3] = 2.0;
      doubleArray0[4] = 10.0;
      doubleArray0[5] = (double) (byte) (-1);
      doubleArray0[6] = (double) 6;
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double1, 0.01);
      
      MathUtils.binomialCoefficientLog(8, (-1));
      MathUtils.hash(doubleArray0);
      long long0 = MathUtils.gcd((long) (-1), (long) 6);
      assertEquals(1L, long0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = (int) (byte) (-34);
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
      
      int int1 = MathUtils.gcd((-874), 15);
      assertEquals(1, int1);
      
      float float0 = MathUtils.sign((-365.0306F));
      assertEquals((-1.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test233()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      MathUtils.distance1(doubleArray0, doubleArray0);
      MathUtils.factorial(0);
      double double0 = MathUtils.sinh(0.0);
      int int0 = MathUtils.pow(0, 0);
      assertEquals(1, int0);
      
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double1, 0.01);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      short short0 = MathUtils.sign((short) (-3170));
      assertEquals((short) (-1), short0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 1;
      floatArray0[1] = (float) (short) (-1);
      floatArray0[2] = 1927.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      MathUtils.sign((long) 1);
      int int1 = MathUtils.mulAndCheck((-2028), (int) (short) (-1));
      assertEquals(2028, int1);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) (short) (-3170), 0.0, 0.0);
      assertFalse(boolean1 == boolean0);
      
      int int2 = MathUtils.subAndCheck(0, 2578);
      assertEquals((-2578), int2);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float) 1L, (-1.0F), (-5200.0F));
      assertFalse(boolean2);
      
      double double2 = MathUtils.distance1(doubleArray1, doubleArray1);
      assertEquals(double2, double0, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger1, bigInteger0);
      
      long long0 = MathUtils.mulAndCheck((-906L), (long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test234()  throws Throwable  {
      MathUtils.checkNotNull((Object) "");
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3.940510424527919E-20);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 2587.042528;
      doubleArray0[3] = (-718.602714);
      MathUtils.safeNorm(doubleArray0);
      double[][] doubleArray1 = new double[6][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-718.602714), (-3.940510424527919E-20), 0.0, 2587.042528}, doubleArray0, 0.01);
      
      MathUtils.subAndCheck(4398046511103L, 4398046511103L);
      MathUtils.sign((-718.602714));
      MathUtils.pow(1, 3291);
      boolean boolean0 = MathUtils.equalsIncludingNaN((-533.75342941714), (-143.30217), 1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test235()  throws Throwable  {
      MathUtils.sign(0.0);
      MathUtils.pow(580L, 580L);
      MathUtils.lcm(580L, 2028L);
      BigInteger bigInteger0 = BigInteger.ONE;
      byte[] byteArray0 = new byte[5];
      bigInteger0.signum();
      MathUtils.binomialCoefficientDouble(135, 135);
      byteArray0[0] = (byte) (-3);
      byteArray0[1] = (byte)0;
      bigInteger0.negate();
      byteArray0[2] = (byte) (-1);
      byteArray0[3] = (byte) (-36);
      byteArray0[4] = (byte)49;
      BigInteger bigInteger1 = new BigInteger(byteArray0);
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) (byte) (-36);
      doubleArray0[1] = (double) (byte)0;
      doubleArray0[2] = (double) 294060L;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[2][4];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      // Undeclared exception!
      try { 
        bigInteger1.pow((-104));
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // Negative exponent
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test236()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(0, (-5095));
      boolean boolean0 = MathUtils.equalsIncludingNaN(602.932908, 2251.5, 0.0);
      float[] floatArray0 = new float[0];
      MathUtils.equals(floatArray0, floatArray0);
      long long0 = MathUtils.mulAndCheck(0L, 1880L);
      assertEquals(0L, long0);
      
      MathUtils.OrderDirection.values();
      MathUtils.round((double) 0L, (-5095));
      double double1 = MathUtils.cosh(0.0);
      assertEquals(double1, double0, 0.01);
      
      long long1 = MathUtils.pow((-2644281811660520851L), 0);
      assertEquals(1L, long1);
      
      boolean boolean1 = MathUtils.equals((-622.7F), (float) 0L);
      assertTrue(boolean1 == boolean0);
      
      byte byte0 = MathUtils.sign((byte) (-1));
      assertEquals((byte) (-1), byte0);
      
      double[] doubleArray0 = new double[0];
      double double2 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double2, 0.01);
      
      double double3 = MathUtils.sign(0.0);
      assertEquals(0.0, double3, 0.01);
      
      int int0 = MathUtils.subAndCheck((int) (byte) (-1), (-2424));
      assertEquals(2423, int0);
      
      float float0 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float0, 0.01F);
      
      MathUtils.equals(0.0F, 667.6289F);
      int int1 = MathUtils.hash(1178.26);
      assertEquals(2112015069, int1);
      
      boolean boolean2 = MathUtils.equals((float) 0L, (float) (-2424), (-5218.6274F));
      assertFalse(boolean2);
  }

  @Test(timeout = 4000)
  public void test237()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 0.2857142686843872;
      doubleArray0[1] = (-846.73);
      doubleArray0[2] = (-2674.6849653157337);
      doubleArray0[3] = 2709.7172793142;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 8.0E298;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[4][0];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      double[] doubleArray2 = MathUtils.normalizeArray(doubleArray0, (-3489.36525));
      MathUtils.equals((-846.73), (-2674.6849653157337), 0.0);
      MathUtils.mulAndCheck(53, 53);
      MathUtils.equals(0.0, (-2674.6849653157337), 148);
      MathUtils.gcd((long) 148, (long) 2809);
      MathUtils.factorialDouble(148);
      MathUtils.equalsIncludingNaN(3749.972F, Float.NEGATIVE_INFINITY, 2321);
      MathUtils.pow((long) 53, 548L);
      MathUtils.equals(doubleArray2, doubleArray0);
      MathUtils.addAndCheck(11, 148);
  }
}
