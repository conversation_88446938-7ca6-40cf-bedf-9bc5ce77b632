/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 11:44:06 GMT 2019
 */

package org.apache.commons.math.util;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.math.BigInteger;
import java.util.Locale;
import java.util.stream.DoubleStream;
import org.apache.commons.math.exception.util.Localizable;
import org.apache.commons.math.exception.util.LocalizedFormats;
import org.apache.commons.math.util.MathUtils;
import org.apache.commons.math.util.Pair;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.Random;
import org.evosuite.runtime.mock.java.util.MockRandom;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MathUtils_ESTest extends MathUtils_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Float.NEGATIVE_INFINITY, (-5.0F), (-5.0F));
      assertFalse(boolean0);
      
      int[] intArray0 = new int[3];
      intArray0[0] = (-182);
      intArray0[1] = 602;
      intArray0[2] = 262;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(3, intArray0.length);
      assertEquals(3, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {(-182), 602, 262}, intArray0);
      assertArrayEquals(new int[] {(-182), 602, 262}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      
      double[] doubleArray0 = new double[8];
      intArray0[0] = 262;
      doubleArray0[1] = (double) Float.NEGATIVE_INFINITY;
      doubleArray0[2] = 40.19140625;
      doubleArray0[3] = (double) 602;
      doubleArray0[4] = (double) (-182);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, Double.NEGATIVE_INFINITY, 40.19140625, 602.0, (-182.0), 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      float float0 = MathUtils.indicator(194.20296F);
      assertEquals(1.0F, float0, 0.01F);
      
      double[] doubleArray1 = new double[3];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) 262;
      doubleArray1[1] = (double) 262;
      doubleArray1[2] = 40.19140625;
      boolean boolean1 = MathUtils.equals(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(8, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, Double.NEGATIVE_INFINITY, 40.19140625, 602.0, (-182.0), 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {262.0, 262.0, 40.19140625}, doubleArray1, 0.01);
      assertTrue(boolean1 == boolean0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean1);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-182));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-182)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      long long0 = MathUtils.gcd((-1L), (-1L));
      assertEquals(1L, long0);
      
      byte byte0 = MathUtils.sign((byte)2);
      assertEquals((byte)1, byte0);
      
      double[] doubleArray0 = new double[0];
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      
      float float0 = MathUtils.round((float) (byte)1, (int) (byte)2, (int) (byte)2);
      assertEquals(1.01F, float0, 0.01F);
      
      double double1 = MathUtils.sign(3761.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      int int0 = MathUtils.addAndCheck((-2813), (-2813));
      assertEquals((-5626), int0);
      
      int int1 = MathUtils.addAndCheck(1865, 1104);
      assertFalse(int1 == int0);
      assertEquals(2969, int1);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-812.6854), 5.253003017911569E7, 2969);
      assertFalse(boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-2054), (-2813));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -2,054
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      int int0 = MathUtils.lcm((-1539), (-1539));
      assertEquals(1539, int0);
      
      double[] doubleArray0 = new double[0];
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      long long0 = MathUtils.gcd((long) (-1539), (long) 1539);
      assertEquals(1539L, long0);
      
      Random.setNextRandom(901);
      int[] intArray0 = new int[9];
      intArray0[0] = 1539;
      intArray0[1] = 901;
      intArray0[2] = 901;
      intArray0[3] = 1539;
      intArray0[4] = 901;
      intArray0[5] = 901;
      intArray0[6] = 1539;
      intArray0[7] = (-1539);
      intArray0[8] = 1863;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(9, intArray0.length);
      assertEquals(9, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {1539, 901, 901, 1539, 901, 901, 1539, (-1539), 1863}, intArray0);
      assertArrayEquals(new int[] {1539, 901, 901, 1539, 901, 901, 1539, (-1539), 1863}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      
      int[] intArray2 = new int[9];
      assertFalse(intArray2.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      
      intArray2[0] = 1539;
      intArray2[1] = 1863;
      intArray2[2] = 901;
      intArray2[3] = 1863;
      intArray2[4] = (-1539);
      intArray2[5] = 1539;
      intArray2[6] = 1539;
      intArray2[7] = (-1539);
      intArray2[8] = 4;
      int int1 = MathUtils.distanceInf(intArray0, intArray2);
      assertNotSame(intArray2, intArray0);
      assertNotSame(intArray2, intArray1);
      assertNotSame(intArray0, intArray2);
      assertNotSame(intArray0, intArray1);
      assertEquals(9, intArray2.length);
      assertEquals(9, intArray0.length);
      assertArrayEquals(new int[] {1539, 1863, 901, 1863, (-1539), 1539, 1539, (-1539), 4}, intArray2);
      assertArrayEquals(new int[] {1539, 901, 901, 1539, 901, 901, 1539, (-1539), 1863}, intArray0);
      assertFalse(intArray2.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray0.equals((Object)intArray2));
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(int1 == int0);
      assertEquals(2440, int1);
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      byte byte0 = (byte) (-16);
      byte byte1 = MathUtils.sign((byte) (-16));
      assertFalse(byte1 == byte0);
      assertEquals((byte) (-1), byte1);
      
      short short0 = MathUtils.indicator((short) (byte) (-16));
      assertEquals((short) (-1), short0);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short) (-7168), bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) (byte) (-16);
      doubleArray0[1] = (double) (byte) (-1);
      // Undeclared exception!
      try { 
        bigInteger1.shortValueExact();
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // BigInteger out of short range
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-4120.5225F), (-4120.5225F), 3761);
      assertTrue(boolean0);
      
      int int0 = MathUtils.pow(3761, 8976876);
      assertEquals(1658960961, int0);
      
      long long0 = MathUtils.lcm((long) 3761, (-2147483648L));
      assertEquals(8076686000128L, long0);
      
      float[] floatArray0 = new float[4];
      floatArray0[0] = (-4120.5225F);
      floatArray0[1] = (float) 1658960961;
      floatArray0[2] = (-5725.6284F);
      floatArray0[3] = (float) 8076686000128L;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(4, floatArray0.length);
      assertArrayEquals(new float[] {(-4120.5225F), 1.65896102E9F, (-5725.6284F), 8.076686E12F}, floatArray0, 0.01F);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      short short0 = MathUtils.sign((short)19);
      assertEquals((short)1, short0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 3761;
      doubleArray0[1] = (double) (short)1;
      doubleArray0[2] = (double) (-4120.5225F);
      doubleArray0[3] = (double) (short)19;
      doubleArray0[4] = (double) (-5725.6284F);
      doubleArray0[5] = 2366.7206;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(6, doubleArray1.length);
      assertEquals(6, doubleArray0.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {3761.0, 1.0, (-4120.5224609375), 19.0, (-5725.62841796875), 2366.7206}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {3761.0, 1.0, (-4120.5224609375), 19.0, (-5725.62841796875), 2366.7206}, doubleArray0, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      long long1 = MathUtils.indicator((long) 3761);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      double double0 = MathUtils.binomialCoefficientDouble(3330, 2055);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      MathUtils.checkFinite(1.304E19);
      float float0 = MathUtils.indicator((-5725.6284F));
      assertEquals((-1.0F), float0, 0.01F);
      
      byte byte0 = MathUtils.indicator((byte)11);
      assertEquals((byte)1, byte0);
      
      boolean boolean2 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(4, floatArray0.length);
      assertArrayEquals(new float[] {(-4120.5225F), 1.65896102E9F, (-5725.6284F), 8.076686E12F}, floatArray0, 0.01F);
      assertTrue(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2);
      
      byte byte1 = MathUtils.indicator((byte)1);
      assertTrue(byte1 == byte0);
      assertEquals((byte)1, byte1);
      
      double double1 = MathUtils.binomialCoefficientLog(1658960961, (short)19);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(364.01980471813727, double1, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-4120.9194F), (-4120.9194F), 3761);
      assertTrue(boolean0);
      
      int int0 = 8976900;
      int int1 = MathUtils.pow(3761, 8976900);
      assertFalse(int1 == int0);
      assertEquals((-834719551), int1);
      
      long long0 = MathUtils.lcm((long) 3761, (-2147483648L));
      assertEquals(8076686000128L, long0);
      
      float[] floatArray0 = new float[4];
      floatArray0[3] = (-4120.9194F);
      floatArray0[1] = (float) (-834719551);
      floatArray0[2] = (-5725.6284F);
      floatArray0[3] = (float) 8076686000128L;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(4, floatArray0.length);
      assertArrayEquals(new float[] {0.0F, (-8.3471955E8F), (-5725.6284F), 8.076686E12F}, floatArray0, 0.01F);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      short short0 = MathUtils.sign((short)19);
      assertEquals((short)1, short0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 3761;
      doubleArray0[1] = (double) (short)19;
      doubleArray0[2] = (double) 0.0F;
      doubleArray0[3] = (double) (short)19;
      doubleArray0[4] = (double) (-5725.6284F);
      doubleArray0[5] = 2366.7206;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(6, doubleArray1.length);
      assertEquals(6, doubleArray0.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {3761.0, 19.0, 0.0, 19.0, (-5725.62841796875), 2366.7206}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {3761.0, 19.0, 0.0, 19.0, (-5725.62841796875), 2366.7206}, doubleArray0, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      long long1 = new Integer((short)19);
      assertFalse(long1 == long0);
      assertEquals(19, long1);
      
      double double0 = MathUtils.binomialCoefficientDouble(3330, 2055);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      MathUtils.checkFinite((-1547.5595870289185));
      float float0 = MathUtils.indicator((-5725.6284F));
      assertEquals((-1.0F), float0, 0.01F);
      
      byte byte0 = MathUtils.indicator((byte)11);
      assertEquals((byte)1, byte0);
      
      boolean boolean2 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(4, floatArray0.length);
      assertArrayEquals(new float[] {0.0F, (-8.3471955E8F), (-5725.6284F), 8.076686E12F}, floatArray0, 0.01F);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertTrue(boolean2);
      
      byte byte1 = MathUtils.indicator((byte)1);
      assertTrue(byte1 == byte0);
      assertEquals((byte)1, byte1);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-834719551), (short)19);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 19, n = -834,719,551
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      int int0 = MathUtils.sign(11);
      assertEquals(1, int0);
      
      int int1 = 2275;
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 2275);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-4120.5225F), (-4120.5225F), 3761);
      assertTrue(boolean0);
      
      int int0 = MathUtils.pow(3761, 8976876);
      assertEquals(1658960961, int0);
      
      long long0 = MathUtils.lcm((long) 3761, (-2147483648L));
      assertEquals(8076686000128L, long0);
      
      float[] floatArray0 = new float[4];
      floatArray0[0] = (-4120.5225F);
      floatArray0[1] = (float) 1658960961;
      floatArray0[2] = (-5725.6284F);
      floatArray0[3] = (float) 8076686000128L;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(4, floatArray0.length);
      assertArrayEquals(new float[] {(-4120.5225F), 1.65896102E9F, (-5725.6284F), 8.076686E12F}, floatArray0, 0.01F);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      short short0 = MathUtils.sign((short)19);
      assertEquals((short)1, short0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 3761;
      doubleArray0[1] = (double) (short)1;
      doubleArray0[2] = (double) (-4120.5225F);
      doubleArray0[3] = (double) (short)19;
      doubleArray0[4] = (double) (-5725.6284F);
      doubleArray0[5] = 2366.7206;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(6, doubleArray1.length);
      assertEquals(6, doubleArray0.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {3761.0, 1.0, (-4120.5224609375), 19.0, (-5725.62841796875), 2366.7206}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {3761.0, 1.0, (-4120.5224609375), 19.0, (-5725.62841796875), 2366.7206}, doubleArray0, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      long long1 = MathUtils.indicator((long) 3761);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      double double0 = MathUtils.binomialCoefficientDouble(3330, 2055);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      MathUtils.checkFinite(1.304E19);
      float float0 = MathUtils.indicator((-5725.6284F));
      assertEquals((-1.0F), float0, 0.01F);
      
      byte byte0 = MathUtils.indicator((byte)11);
      assertEquals((byte)1, byte0);
      
      boolean boolean2 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(4, floatArray0.length);
      assertArrayEquals(new float[] {(-4120.5225F), 1.65896102E9F, (-5725.6284F), 8.076686E12F}, floatArray0, 0.01F);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertTrue(boolean2);
      
      byte byte1 = MathUtils.indicator((byte)1);
      assertTrue(byte1 == byte0);
      assertEquals((byte)1, byte1);
      
      double double1 = MathUtils.binomialCoefficientLog(1658960961, (short)19);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(364.01980471813727, double1, 0.01);
      
      int[] intArray0 = new int[5];
      intArray0[0] = (int) (byte)1;
      intArray0[1] = (int) (byte)11;
      intArray0[2] = (-1142);
      intArray0[3] = 1658960961;
      intArray0[4] = (int) (short)1;
      int[] intArray1 = new int[3];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = 2055;
      intArray1[1] = 8976876;
      intArray1[2] = 3330;
      // Undeclared exception!
      try { 
        MathUtils.distance1(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 3
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      long long0 = MathUtils.pow(4499201580859392L, 2005L);
      assertEquals(0L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 4499201580859392L, (float) 2005L, (float) 4499201580859392L);
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) 2005L, (float) 0L);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 2005L;
      doubleArray0[0] = (double) 4499201580859392L;
      doubleArray0[2] = (double) 2005L;
      doubleArray0[3] = (double) 2005L;
      doubleArray0[4] = (double) 0L;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {4.499201580859392E15, 0.0, 2005.0, 2005.0, 0.0}, doubleArray0, 0.01);
      assertEquals(4.499201580859392E15, double0, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = bigInteger0.not();
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte) (-2), bigInteger1.byteValue());
      assertEquals((short) (-2), bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      String string0 = bigInteger0.toString();
      assertNotSame(bigInteger0, bigInteger1);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertNotNull(string0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertEquals("1", string0);
      
      Double double1 = new Double(2005.0);
      assertNotNull(double1);
      assertNotEquals((double)double1, (double)double0, 0.01);
      assertEquals(2005.0, (double)double1, 0.01);
      
      boolean boolean2 = bigInteger0.equals(double1);
      assertNotSame(bigInteger0, bigInteger1);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertNotEquals((double)double1, (double)double0, 0.01);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
      
      float float0 = MathUtils.round((float) 2041L, (-532), 7);
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long1 = MathUtils.sign(2041L);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-532));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-532)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-980.85), (-980.85));
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equals((-1.0F), (-1707.1012F), 993);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double double0 = MathUtils.sign((double) (-1707.1012F));
      assertEquals((-1.0), double0, 0.01);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-980.85);
      boolean boolean2 = MathUtils.equals(doubleArray0, (double[]) null);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {(-980.85)}, doubleArray0, 0.01);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
      
      int[] intArray0 = new int[4];
      intArray0[0] = 993;
      intArray0[1] = 993;
      intArray0[2] = 993;
      intArray0[3] = 993;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(4, intArray0.length);
      assertArrayEquals(new int[] {993, 993, 993, 993}, intArray0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 1232.7964;
      doubleArray0[1] = 2801.29;
      doubleArray0[2] = (-182.09419);
      doubleArray0[4] = 1716.710110170975;
      doubleArray0[5] = 1.625;
      doubleArray0[6] = 0.0;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {1232.7964, 2801.29, (-182.09419), 0.0, 1716.710110170975, 1.625, 0.0}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      byte byte0 = MathUtils.indicator((byte)25);
      assertEquals((byte)1, byte0);
      
      double double0 = MathUtils.TWO_PI;
      assertEquals(6.283185307179586, double0, 0.01);
      
      float[] floatArray0 = new float[8];
      floatArray0[0] = (float) (byte)1;
      floatArray0[1] = (float) (byte)1;
      floatArray0[2] = (float) (byte)1;
      floatArray0[3] = (float) (byte)25;
      floatArray0[4] = (float) (byte)1;
      floatArray0[5] = (float) (byte)25;
      floatArray0[6] = 52.007F;
      floatArray0[7] = (float) (byte)25;
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(8, floatArray0.length);
      assertArrayEquals(new float[] {1.0F, 1.0F, 1.0F, 25.0F, 1.0F, 25.0F, 52.007F, 25.0F}, floatArray0, 0.01F);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      long long0 = MathUtils.binomialCoefficient((byte)25, 9);
      assertEquals(2042975L, long0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      int int0 = 919;
      int int1 = MathUtils.lcm(919, 919);
      assertTrue(int1 == int0);
      assertEquals(919, int1);
      
      double double0 = MathUtils.factorialDouble(919);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      long long0 = 6722166367014452318L;
      long long1 = MathUtils.pow(6722166367014452318L, 0L);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 919, (float) 6722166367014452318L, (float) 919);
      assertFalse(boolean0);
      
      Integer integer0 = new Integer(919);
      assertNotNull(integer0);
      assertTrue(integer0.equals((Object)int1));
      assertTrue(integer0.equals((Object)int0));
      assertEquals(919, (int)integer0);
      
      Integer integer1 = new Integer(919);
      assertNotNull(integer1);
      assertTrue(integer1.equals((Object)int0));
      assertTrue(integer1.equals((Object)int1));
      assertTrue(integer1.equals((Object)integer0));
      assertEquals(919, (int)integer1);
      
      Pair<Integer, Object> pair0 = new Pair<Integer, Object>(integer0, integer1);
      assertNotNull(pair0);
      assertTrue(integer0.equals((Object)int1));
      assertTrue(integer0.equals((Object)integer1));
      assertTrue(integer0.equals((Object)int0));
      assertTrue(integer1.equals((Object)int0));
      assertTrue(integer1.equals((Object)int1));
      assertTrue(integer1.equals((Object)integer0));
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.INVALID_INTERVAL_INITIAL_VALUE_PARAMETERS;
      assertEquals("invalid interval, initial value parameters:  lower={0}, initial={1}, upper={2}", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.INVALID_INTERVAL_INITIAL_VALUE_PARAMETERS, localizedFormats0);
      
      double double1 = MathUtils.sign(Double.POSITIVE_INFINITY);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      long long2 = 3952090531849364496L;
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(3952090531849364496L, 6722166367014452318L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      float float0 = MathUtils.round((-1.0F), 918, 0);
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.compareTo(693.7, 0.0, 0);
      assertEquals(1, int0);
      
      double double0 = MathUtils.indicator((-1153.06772167073));
      assertEquals((-1.0), double0, 0.01);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[1] = (double) 918;
      doubleArray0[2] = (double) (-1.0F);
      doubleArray0[3] = 693.7;
      doubleArray0[4] = 693.7;
      doubleArray0[5] = 693.7;
      doubleArray0[6] = (double) (-1.0F);
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 693.7);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 212.47759500850822, (-0.2314570751726669), 160.56177304727905, 160.56177304727905, 160.56177304727905, (-0.2314570751726669)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      float float1 = MathUtils.round(5757.0F, 1, 1);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(5756.9F, float1, 0.01F);
      
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      doubleArray2[0] = (-1.0);
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = (double) 5757.0F;
      doubleArray2[3] = 693.7;
      doubleArray2[4] = (double) 0;
      doubleArray2[5] = 693.7;
      doubleArray2[6] = 0.0;
      doubleArray2[7] = (double) (-1.0F);
      double double2 = MathUtils.TWO_PI;
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(6.283185307179586, double2, 0.01);
      
      long long0 = MathUtils.pow((long) 1, 1);
      assertEquals(1L, long0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray1, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray2);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 212.47759500850822, (-0.2314570751726669), 160.56177304727905, 160.56177304727905, 160.56177304727905, (-0.2314570751726669)}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertTrue(boolean1 == boolean0);
      assertFalse(doubleArray1.equals((Object)doubleArray2));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertTrue(boolean1);
      
      MathUtils.checkFinite(doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(Float.NaN, (-2517.0F), 227);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(7168.0, 7168.0);
      assertTrue(boolean0);
      
      float[] floatArray0 = new float[1];
      floatArray0[0] = 680.0F;
      float[] floatArray1 = new float[1];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = 680.0F;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertEquals(1, floatArray0.length);
      assertEquals(1, floatArray1.length);
      assertArrayEquals(new float[] {680.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {680.0F}, floatArray1, 0.01F);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      long long0 = MathUtils.pow(4499201580859392L, 2005L);
      assertEquals(0L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 4499201580859392L, (float) 2005L, (float) 4499201580859392L);
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) 2005L, (float) 0L);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 2005L;
      doubleArray0[0] = (double) 4499201580859392L;
      doubleArray0[2] = (double) 2005L;
      doubleArray0[3] = (double) 2005L;
      doubleArray0[4] = (double) 0L;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {4.499201580859392E15, 0.0, 2005.0, 2005.0, 0.0}, doubleArray0, 0.01);
      assertEquals(4.499201580859392E15, double0, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = bigInteger0.not();
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte) (-2), bigInteger1.byteValue());
      assertEquals((short) (-2), bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      int int0 = bigInteger0.compareTo(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte) (-2), bigInteger1.byteValue());
      assertEquals((short) (-2), bigInteger1.shortValue());
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals(1, int0);
      
      String string0 = bigInteger0.toString();
      assertNotSame(bigInteger0, bigInteger1);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertNotNull(string0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertEquals("1", string0);
      
      long long1 = MathUtils.sign(2041L);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      int int1 = MathUtils.sign((-326));
      assertFalse(int1 == int0);
      assertEquals((-1), int1);
      
      int[] intArray0 = new int[2];
      intArray0[0] = (-532);
      intArray0[1] = 2146767366;
      int[] intArray1 = new int[9];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = 1;
      intArray1[1] = 2146767366;
      intArray1[2] = (-2249);
      intArray1[3] = (-532);
      intArray1[4] = (-532);
      intArray1[5] = 2146767366;
      intArray1[6] = 129;
      intArray1[7] = 1;
      intArray1[8] = 19;
      int int2 = MathUtils.distance1(intArray0, intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(2, intArray0.length);
      assertEquals(9, intArray1.length);
      assertArrayEquals(new int[] {(-532), 2146767366}, intArray0);
      assertArrayEquals(new int[] {1, 2146767366, (-2249), (-532), (-532), 2146767366, 129, 1, 19}, intArray1);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(533, int2);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-532));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-532)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      int[] intArray0 = new int[2];
      int int0 = 222;
      intArray0[0] = 222;
      intArray0[1] = 1666;
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(2, intArray0.length);
      assertArrayEquals(new int[] {222, 1666}, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      int[] intArray1 = new int[1];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = 0;
      int[] intArray2 = MathUtils.copyOf(intArray1);
      assertNotSame(intArray1, intArray2);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray2, intArray1);
      assertNotSame(intArray2, intArray0);
      assertEquals(1, intArray1.length);
      assertEquals(1, intArray2.length);
      assertNotNull(intArray2);
      assertArrayEquals(new int[] {0}, intArray1);
      assertArrayEquals(new int[] {0}, intArray2);
      assertFalse(intArray1.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray2.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.checkFinite((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      float[] floatArray0 = new float[0];
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(0, floatArray0.length);
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertTrue(boolean0);
      
      long long0 = MathUtils.lcm((-326L), (-1L));
      assertEquals(326L, long0);
      
      double double0 = MathUtils.factorialDouble(645);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      double double1 = MathUtils.round((double) 326L, 645);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(326.0, double1, 0.01);
      
      float float0 = new Integer(645);
      assertEquals(645, float0, 0.01F);
      
      int int0 = MathUtils.compareTo(2020.84300815, (-1L), (-1L));
      assertEquals(1, int0);
      
      int int1 = MathUtils.subAndCheck((-3777), 1);
      assertFalse(int1 == int0);
      assertEquals((-3778), int1);
      
      float float1 = MathUtils.indicator(0.0F);
      assertEquals(1.0F, float1, 0.01F);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      double double2 = MathUtils.factorialLog(645);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(3531.8201472238347, double2, 0.01);
      
      long long1 = MathUtils.lcm((long) (-3777), (-1L));
      assertFalse(long1 == long0);
      assertEquals(3777L, long1);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.MAX_ITERATIONS_EXCEEDED;
      assertEquals("maximal number of iterations ({0}) exceeded", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.MAX_ITERATIONS_EXCEEDED, localizedFormats0);
      
      double[][] doubleArray0 = new double[9][1];
      double[] doubleArray1 = new double[5];
      doubleArray1[0] = Double.POSITIVE_INFINITY;
      doubleArray1[1] = 326.0;
      doubleArray1[2] = (double) 1.0F;
      doubleArray1[3] = 326.0;
      doubleArray1[4] = (double) 0.0F;
      doubleArray0[7] = doubleArray1;
      short short0 = MathUtils.indicator((short)2696);
      assertEquals((short)1, short0);
      
      double double3 = MathUtils.round(Double.POSITIVE_INFINITY, 1, 995);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double3, 0.01);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      int[] intArray0 = new int[1];
      intArray0[0] = 7;
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(1, intArray0.length);
      assertArrayEquals(new int[] {7}, intArray0);
      assertEquals(0, int0);
      
      long long0 = MathUtils.pow((-89L), 3447L);
      assertEquals((-1131283639398208553L), long0);
      
      long long1 = MathUtils.pow(1888L, (long) 7);
      assertFalse(long1 == long0);
      assertEquals(8755125871792095232L, long1);
      
      int int1 = MathUtils.hash((double) 0);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      
      int int2 = (-689);
      int int3 = MathUtils.mulAndCheck((-3605), (-689));
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      assertFalse(int3 == int0);
      assertEquals(2483845, int3);
      
      int int4 = MathUtils.lcm((-3605), 215);
      assertFalse(int4 == int1);
      assertFalse(int4 == int3);
      assertFalse(int4 == int2);
      assertFalse(int4 == int0);
      assertEquals(155015, int4);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 1888L;
      doubleArray0[1] = (double) 7;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {1888.0, 7.0}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-4347), (-4347));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -4,347
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1089.62552368178), (-1089.62552368178));
      assertTrue(boolean0);
      
      long long0 = MathUtils.gcd((-820L), (-3547L));
      assertEquals(1L, long0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = bigInteger0.setBit(585);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertNotNull(bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      
      int[] intArray0 = new int[2];
      long long1 = bigInteger2.longValue();
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
      
      intArray0[0] = 585;
      BigInteger bigInteger3 = bigInteger2.pow(29);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertSame(bigInteger2, bigInteger3);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger1);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      assertEquals((short)1, bigInteger3.shortValue());
      assertNotNull(bigInteger3);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      
      intArray0[1] = 585;
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(2, intArray0.length);
      assertArrayEquals(new int[] {585, 585}, intArray0);
      assertEquals(0, int0);
      
      Random.setNextRandom((-343));
      BigInteger bigInteger4 = MathUtils.pow(bigInteger2, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger4);
      assertSame(bigInteger2, bigInteger3);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger4);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger4, bigInteger1);
      assertNotSame(bigInteger4, bigInteger0);
      assertSame(bigInteger4, bigInteger2);
      assertSame(bigInteger4, bigInteger3);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger4.shortValue());
      assertEquals((byte)1, bigInteger4.byteValue());
      assertNotNull(bigInteger4);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger4.equals((Object)bigInteger1));
      assertFalse(bigInteger4.equals((Object)bigInteger0));
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double[]) null, (double[]) null);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      long long0 = MathUtils.indicator(88L);
      assertEquals(1L, long0);
      
      float float0 = MathUtils.round(2833.8F, (-286), 5);
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long1 = MathUtils.sign(1L);
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      long long0 = 6L;
      long long1 = MathUtils.sign(6L);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      double double0 = MathUtils.sign((double) 6L);
      assertEquals(1.0, double0, 0.01);
      
      short short0 = MathUtils.indicator((short) (-72));
      assertEquals((short) (-1), short0);
      
      int int0 = 46;
      double double1 = MathUtils.binomialCoefficientDouble(910, 46);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(7.463445358352185E77, double1, 0.01);
      
      double double2 = MathUtils.indicator((double) 1L);
      assertEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(1.0, double2, 0.01);
      
      int int1 = 1678;
      // Undeclared exception!
      try { 
        MathUtils.pow(1678, (-1352L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,352)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1497.269164096;
      doubleArray0[1] = 10.0;
      boolean boolean0 = MathUtils.equalsIncludingNaN((double[]) null, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {1497.269164096, 10.0}, doubleArray0, 0.01);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equals(10.0, 10.0, 1497.269164096);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      int int0 = new Integer(13);
      assertEquals(13, int0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(Double.NaN, 10.0, 10.0);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2);
      
      boolean boolean3 = MathUtils.equals((float) 13, (float) 13, 13);
      assertTrue(boolean3 == boolean1);
      assertFalse(boolean3 == boolean2);
      assertFalse(boolean3 == boolean0);
      assertTrue(boolean3);
      
      int int1 = new Integer(13);
      assertTrue(int1 == int0);
      assertEquals(13, int1);
      
      boolean boolean4 = MathUtils.equals((-1252.358348660933), (double) 13, (double) 13);
      assertTrue(boolean4 == boolean2);
      assertFalse(boolean4 == boolean1);
      assertTrue(boolean4 == boolean0);
      assertFalse(boolean4 == boolean3);
      assertFalse(boolean4);
      
      int[] intArray0 = new int[7];
      intArray0[0] = 13;
      intArray0[1] = 13;
      intArray0[2] = 13;
      intArray0[1] = 13;
      intArray0[4] = 13;
      intArray0[5] = 13;
      intArray0[6] = 13;
      int int2 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(7, intArray0.length);
      assertArrayEquals(new int[] {13, 13, 13, 0, 13, 13, 13}, intArray0);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(0, int2);
      
      double double0 = MathUtils.log(642.4327146891254, 6.123233995736766E-17);
      assertEquals((-5.7742216310073236), double0, 0.01);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 6.123233995736766E-17);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {1497.269164096, 10.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {6.082609307448863E-17, 4.06246882879027E-19}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      long long0 = MathUtils.mulAndCheck(39916800L, 1544L);
      assertEquals(61631539200L, long0);
      
      int int3 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(7, intArray0.length);
      assertArrayEquals(new int[] {13, 13, 13, 0, 13, 13, 13}, intArray0);
      assertTrue(int3 == int2);
      assertFalse(int3 == int0);
      assertFalse(int3 == int1);
      assertEquals(0, int3);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      int int0 = 1073741824;
      // Undeclared exception!
      try { 
        MathUtils.equals((-1.0F), (-1.0F), 1073741824);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      float float0 = MathUtils.indicator((-2499.8005F));
      assertEquals((-1.0F), float0, 0.01F);
      
      int int0 = 66;
      Random.setNextRandom(66);
      int int1 = 2145949468;
      // Undeclared exception!
      try { 
        MathUtils.equals((float) 66, 2341.31F, 2145949468);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 304.0;
      doubleArray0[1] = 1.625;
      double double0 = 0.0;
      doubleArray0[2] = 0.0;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {304.0, 1.625, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {304.0, 1.625, 0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double1 = MathUtils.distance(doubleArray1, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertArrayEquals(new double[] {304.0, 1.625, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {304.0, 1.625, 0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      double double2 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {304.0, 1.625, 0.0}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(1.625, 0.0, 1708);
      assertFalse(boolean0);
      
      double double3 = MathUtils.safeNorm(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertArrayEquals(new double[] {304.0, 1.625, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {304.0, 1.625, 0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(304.00434310219975, double3, 0.01);
      
      float float0 = MathUtils.sign(1048.533F);
      assertEquals(1.0F, float0, 0.01F);
      
      int int0 = MathUtils.mulAndCheck(1708, 1708);
      assertEquals(2917264, int0);
      
      double[] doubleArray2 = MathUtils.copyOf(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray1, doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray1);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertEquals(3, doubleArray2.length);
      assertNotNull(doubleArray2);
      assertArrayEquals(new double[] {304.0, 1.625, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {304.0, 1.625, 0.0}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {304.0, 1.625, 0.0}, doubleArray2, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(2917264, 1708);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle((-1738.3264629638), (-1738.3264629638));
      assertEquals((-1738.3264629638), double0, 0.01);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      boolean boolean0 = MathUtils.equals((-1738.3264629638), (-1738.3264629638));
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[14];
      doubleArray0[0] = (-1738.3264629638);
      doubleArray0[1] = 2.921263525786901;
      doubleArray0[2] = (-1738.3264629638);
      doubleArray0[3] = (-1738.3264629638);
      doubleArray0[4] = (-1738.3264629638);
      double double1 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(14, doubleArray0.length);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      int int0 = (-2132);
      int int1 = MathUtils.gcd((-2132), (-2132));
      assertFalse(int1 == int0);
      assertEquals(2132, int1);
      
      int int2 = 1616;
      double double2 = MathUtils.binomialCoefficientLog(1616, (-2132));
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[0][7];
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(14, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      int int3 = (-1150);
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-1150));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,150)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      long long0 = 2432902008176640000L;
      long long1 = (-3958705157555305932L);
      // Undeclared exception!
      try { 
        MathUtils.lcm(2432902008176640000L, (-3958705157555305932L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      float float0 = MathUtils.indicator(2627.807F);
      assertEquals(1.0F, float0, 0.01F);
      
      long long0 = MathUtils.lcm(9218868437227405312L, 9218868437227405312L);
      assertEquals(9218868437227405312L, long0);
      
      int int0 = 61;
      boolean boolean0 = MathUtils.equals((float) 9218868437227405312L, 0.0F, 61);
      assertFalse(boolean0);
      
      int int1 = (-703);
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN((float) 9218868437227405312L, 0.0F, (-703));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1114.8821), (-1153.06772167073));
      assertFalse(boolean0);
      
      float float0 = MathUtils.round((-1.0F), 918, 0);
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.compareTo(693.7, 0.0, 0);
      assertEquals(1, int0);
      
      double double0 = MathUtils.indicator((-1153.06772167073));
      assertEquals((-1.0), double0, 0.01);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[1] = (double) 918;
      doubleArray0[2] = (double) (-1.0F);
      doubleArray0[3] = 693.7;
      doubleArray0[4] = 693.7;
      doubleArray0[5] = 693.7;
      doubleArray0[6] = (double) Float.NaN;
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, Double.NaN}, doubleArray0, 0.01);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, Double.NaN}, doubleArray0, 0.01);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      boolean boolean2 = MathUtils.equals((-1.0), 0.0);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 693.7);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, Double.NaN}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 212.40672425869718, (-0.23137987392014941), 160.50821853840765, 160.50821853840765, 160.50821853840765, Double.NaN}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      float float1 = MathUtils.round(5757.0F, 1, 1);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(5756.9F, float1, 0.01F);
      
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (-1.0);
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = (double) 5757.0F;
      doubleArray2[3] = 693.7;
      doubleArray2[4] = (double) 0;
      doubleArray2[5] = 693.7;
      doubleArray2[6] = 0.0;
      doubleArray2[7] = (double) (-1.0F);
      double double2 = MathUtils.TWO_PI;
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(6.283185307179586, double2, 0.01);
      
      long long0 = MathUtils.pow((long) 1, 1);
      assertEquals(1L, long0);
      
      boolean boolean3 = MathUtils.equalsIncludingNaN(doubleArray1, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray1, doubleArray2);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, Double.NaN}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 212.40672425869718, (-0.23137987392014941), 160.50821853840765, 160.50821853840765, 160.50821853840765, Double.NaN}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(boolean3 == boolean0);
      assertTrue(boolean3 == boolean1);
      assertFalse(boolean3 == boolean2);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(doubleArray1.equals((Object)doubleArray2));
      assertTrue(boolean3);
      
      double double3 = MathUtils.distance(doubleArray1, doubleArray2);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray1, doubleArray2);
      assertEquals(8, doubleArray2.length);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertArrayEquals(new double[] {(-1.0), Double.NaN, 5757.0, 693.7, 0.0, 693.7, 0.0, (-1.0)}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, Double.NaN}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 212.40672425869718, (-0.23137987392014941), 160.50821853840765, 160.50821853840765, 160.50821853840765, Double.NaN}, doubleArray1, 0.01);
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(doubleArray1.equals((Object)doubleArray2));
      assertEquals(Double.NaN, double3, 0.01);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean4 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, false);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, Double.NaN}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(boolean4 == boolean3);
      assertTrue(boolean4 == boolean0);
      assertFalse(boolean4 == boolean1);
      assertTrue(boolean4 == boolean2);
      assertFalse(boolean4);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      int int0 = MathUtils.addAndCheck((-254), (-1349));
      assertEquals((-1603), int0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) (-1603);
      doubleArray0[1] = (double) (-1603);
      doubleArray0[2] = 0.1538461446762085;
      doubleArray0[4] = (double) (-1603);
      doubleArray0[6] = (double) (-1603);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {(-1603.0), (-1603.0), 0.1538461446762085, 0.0, (-1603.0), 0.0, (-1603.0)}, doubleArray0, 0.01);
      assertEquals(3206.000003691303, double0, 0.01);
      
      double[] doubleArray1 = new double[9];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 458.8;
      doubleArray1[1] = (double) (-1349);
      doubleArray1[2] = (double) (-254);
      doubleArray1[3] = (double) (-254);
      doubleArray1[4] = (double) (-1349);
      doubleArray1[5] = (double) (-254);
      doubleArray1[6] = (double) (-1349);
      doubleArray1[7] = 3206.000003691303;
      doubleArray1[8] = (double) (-1603);
      double double1 = MathUtils.distance(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      assertArrayEquals(new double[] {(-1603.0), (-1603.0), 0.1538461446762085, 0.0, (-1603.0), 0.0, (-1603.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {458.8, (-1349.0), (-254.0), (-254.0), (-1349.0), (-254.0), (-1349.0), 3206.000003691303, (-1603.0)}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertNotEquals(double1, double0, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(2153.6465395951427, double1, 0.01);
      
      double double2 = MathUtils.distance(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      assertArrayEquals(new double[] {(-1603.0), (-1603.0), 0.1538461446762085, 0.0, (-1603.0), 0.0, (-1603.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {458.8, (-1349.0), (-254.0), (-254.0), (-1349.0), (-254.0), (-1349.0), 3206.000003691303, (-1603.0)}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(2153.6465395951427, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      byteArray0[0] = (byte)3;
      byteArray0[1] = (byte) (-8);
      byteArray0[2] = (byte)22;
      byteArray0[3] = (byte) (-114);
      byteArray0[4] = (byte) (-37);
      byteArray0[5] = (byte)51;
      byteArray0[6] = (byte) (-75);
      byteArray0[7] = (byte)23;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(8, byteArray0.length);
      assertEquals((byte)23, bigInteger0.byteValue());
      assertEquals((short) (-19177), bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte)3, (byte) (-8), (byte)22, (byte) (-114), (byte) (-37), (byte)51, (byte) (-75), (byte)23}, byteArray0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 187);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals(8, byteArray0.length);
      assertEquals((byte)23, bigInteger0.byteValue());
      assertEquals((short) (-19177), bigInteger0.shortValue());
      assertEquals((byte) (-57), bigInteger1.byteValue());
      assertEquals((short) (-25401), bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertArrayEquals(new byte[] {(byte)3, (byte) (-8), (byte)22, (byte) (-114), (byte) (-37), (byte)51, (byte) (-75), (byte)23}, byteArray0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      float[] floatArray0 = new float[7];
      floatArray0[0] = (float) 501L;
      BigInteger bigInteger0 = BigInteger.TEN;
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 1389);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      floatArray0[2] = (float) 501L;
      floatArray0[3] = 961.0F;
      floatArray0[4] = (float) 501L;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(7, floatArray0.length);
      assertArrayEquals(new float[] {501.0F, 0.0F, 501.0F, 961.0F, 501.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertTrue(boolean0);
      
      int int0 = (-1818);
      int int1 = MathUtils.subAndCheck(1389, (-1818));
      assertFalse(int1 == int0);
      assertEquals(3207, int1);
      
      double double0 = MathUtils.indicator(2.0242814865207652E31);
      assertEquals(1.0, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) (-630L);
      doubleArray0[1] = (double) (-370);
      doubleArray0[2] = (double) (-630L);
      doubleArray0[3] = (double) (short)8166;
      doubleArray0[4] = (double) (short)8166;
      doubleArray0[5] = (double) (-630L);
      doubleArray0[6] = 0.19775390625045475;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {(-630.0), (-370.0), (-630.0), 8166.0, 8166.0, (-630.0), 0.19775390625045475}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      short short0 = MathUtils.indicator((short)8166);
      assertEquals((short)1, short0);
      
      int int0 = MathUtils.compareTo(717.153382588, (-204.5368450906), 1141.73736387);
      assertEquals(0, int0);
      
      int int1 = MathUtils.hash(1896.0);
      assertFalse(int1 == int0);
      assertEquals(1084071936, int1);
      
      boolean boolean1 = MathUtils.equals((double[]) null, (double[]) null);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      int int2 = MathUtils.indicator((int) (short)8166);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(1, int2);
      
      Random.setNextRandom((-1368));
      int int3 = MathUtils.indicator(153);
      assertFalse(int3 == int0);
      assertTrue(int3 == int2);
      assertFalse(int3 == int1);
      assertEquals(1, int3);
      
      short short1 = MathUtils.sign((short)18);
      assertTrue(short1 == short0);
      assertEquals((short)1, short1);
      
      // Undeclared exception!
      try { 
        MathUtils.safeNorm((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      int int0 = (-2);
      MockRandom mockRandom0 = new MockRandom();
      assertNotNull(mockRandom0);
      
      DoubleStream doubleStream0 = mockRandom0.doubles();
      assertNotNull(doubleStream0);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, mathUtils_OrderDirection0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      double double0 = MathUtils.cosh(2.0);
      assertEquals(3.7621956910836314, double0, 0.01);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray0 = new double[2][8];
      double[] doubleArray1 = new double[9];
      doubleArray1[0] = 2.0;
      doubleArray1[1] = 2.0;
      doubleArray1[2] = 3.7621956910836314;
      doubleArray1[3] = 922.8338;
      doubleArray1[4] = 3.7621956910836314;
      doubleArray1[5] = 2.0;
      doubleArray1[6] = 3.7621956910836314;
      doubleArray1[7] = 2.0;
      doubleArray1[8] = 2.0;
      doubleArray0[0] = doubleArray1;
      doubleArray0[1] = null;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, mathUtils_OrderDirection0, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1114.8821), (-1153.06772167073));
      assertFalse(boolean0);
      
      float float0 = MathUtils.round((-1.0F), 918, 0);
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.compareTo(693.7, 0.0, 0);
      assertEquals(1, int0);
      
      double double0 = MathUtils.indicator((-1153.06772167073));
      assertEquals((-1.0), double0, 0.01);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[1] = (double) 918;
      doubleArray0[2] = (double) (-1.0F);
      doubleArray0[3] = 693.7;
      doubleArray0[4] = 693.7;
      doubleArray0[5] = 693.7;
      doubleArray0[6] = (double) (-1.0F);
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      boolean boolean2 = MathUtils.equals((-1.0), 0.0);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 693.7);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 212.47759500850822, (-0.2314570751726669), 160.56177304727905, 160.56177304727905, 160.56177304727905, (-0.2314570751726669)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      float float1 = MathUtils.round(5757.0F, 1, 1);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(5756.9F, float1, 0.01F);
      
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (-1.0);
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = (double) 5757.0F;
      doubleArray2[3] = 693.7;
      doubleArray2[4] = (double) 0;
      doubleArray2[5] = 693.7;
      doubleArray2[6] = 0.0;
      doubleArray2[7] = (double) (-1.0F);
      double double2 = MathUtils.TWO_PI;
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(6.283185307179586, double2, 0.01);
      
      long long0 = MathUtils.pow((long) 1, 1);
      assertEquals(1L, long0);
      
      boolean boolean3 = MathUtils.equalsIncludingNaN(doubleArray1, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray2);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 212.47759500850822, (-0.2314570751726669), 160.56177304727905, 160.56177304727905, 160.56177304727905, (-0.2314570751726669)}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(boolean3 == boolean0);
      assertTrue(boolean3 == boolean1);
      assertFalse(boolean3 == boolean2);
      assertFalse(doubleArray1.equals((Object)doubleArray2));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertTrue(boolean3);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(4147, 3654);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1114.8821), (-1153.06772167073));
      assertFalse(boolean0);
      
      float float0 = MathUtils.round((-1.0F), 918, 0);
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.compareTo(693.7, (-1114.8821), 0);
      assertEquals(1, int0);
      
      double double0 = MathUtils.indicator((-1153.06772167073));
      assertEquals((-1.0), double0, 0.01);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[1] = (double) 918;
      doubleArray0[2] = (double) (-1.0F);
      doubleArray0[3] = 693.7;
      doubleArray0[4] = 693.7;
      doubleArray0[5] = 693.7;
      doubleArray0[6] = (double) (-1.0F);
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      boolean boolean2 = MathUtils.equals((-1.0), 0.0);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 693.7);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 212.47759500850822, (-0.2314570751726669), 160.56177304727905, 160.56177304727905, 160.56177304727905, (-0.2314570751726669)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      float float1 = MathUtils.round(5757.0F, 1, 1);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(5756.9F, float1, 0.01F);
      
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      doubleArray2[0] = (-1.0);
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = (double) 5757.0F;
      doubleArray2[3] = 693.7;
      doubleArray2[4] = (double) 0;
      doubleArray2[5] = 693.7;
      doubleArray2[6] = (double) Float.NaN;
      doubleArray2[7] = (double) (-1.0F);
      double double2 = MathUtils.TWO_PI;
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(6.283185307179586, double2, 0.01);
      
      long long0 = MathUtils.pow((long) 1, 1);
      assertEquals(1L, long0);
      
      boolean boolean3 = MathUtils.equalsIncludingNaN(doubleArray1, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray2);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 212.47759500850822, (-0.2314570751726669), 160.56177304727905, 160.56177304727905, 160.56177304727905, (-0.2314570751726669)}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(boolean3 == boolean0);
      assertFalse(boolean3 == boolean2);
      assertTrue(boolean3 == boolean1);
      assertFalse(doubleArray1.equals((Object)doubleArray2));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertTrue(boolean3);
      
      long long1 = MathUtils.pow((-2705L), 7910884519577875640L);
      assertFalse(long1 == long0);
      assertEquals((-901883291643847807L), long1);
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      int int0 = MathUtils.pow(2934, (long) 2934);
      assertEquals(0, int0);
      
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
      
      int int1 = MathUtils.gcd(0, 0);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      
      long long1 = MathUtils.pow(1L, (long) 0);
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
      
      int int2 = 19;
      int int3 = MathUtils.mulAndCheck(19, (-1281));
      assertFalse(int3 == int0);
      assertFalse(int3 == int1);
      assertFalse(int3 == int2);
      assertEquals((-24339), int3);
      
      int int4 = 1459;
      int int5 = MathUtils.addAndCheck(19, 1459);
      assertFalse(int5 == int1);
      assertFalse(int5 == int4);
      assertFalse(int5 == int3);
      assertFalse(int5 == int2);
      assertFalse(int5 == int0);
      assertEquals(1478, int5);
      
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      int[] intArray0 = new int[5];
      intArray0[0] = 222;
      intArray0[1] = 222;
      intArray0[2] = (-1);
      intArray0[4] = 43935035;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(5, intArray0.length);
      assertArrayEquals(new int[] {222, 222, (-1), 0, 43935035}, intArray0);
      assertEquals(0, int0);
      
      long long0 = MathUtils.binomialCoefficient(222, (-2566));
      assertEquals(1L, long0);
      
      float[] floatArray0 = new float[9];
      floatArray0[0] = Float.NaN;
      floatArray0[2] = (float) 2113396605;
      floatArray0[3] = (float) 2113396605;
      floatArray0[4] = (float) 222;
      floatArray0[5] = (float) 0;
      floatArray0[6] = (float) (-1);
      floatArray0[7] = (float) 43935035;
      floatArray0[8] = (float) 222;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(9, floatArray0.length);
      assertArrayEquals(new float[] {Float.NaN, 0.0F, 2.11339661E9F, 2.11339661E9F, 222.0F, 0.0F, (-1.0F), 4.3935036E7F, 222.0F}, floatArray0, 0.01F);
      assertTrue(boolean0);
      
      long long1 = MathUtils.indicator((long) 222);
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
      
      // Undeclared exception!
      try { 
        MathUtils.lcm(632, 43935035);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      int int0 = MathUtils.gcd((-958), (-958));
      assertEquals(958, int0);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      int int1 = bigInteger0.compareTo(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      int[] intArray0 = new int[7];
      intArray0[0] = 2146912812;
      intArray0[1] = 1;
      intArray0[2] = (-958);
      intArray0[3] = 1;
      intArray0[5] = 209;
      intArray0[6] = (-958);
      int int2 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(7, intArray0.length);
      assertArrayEquals(new int[] {2146912812, 1, (-958), 1, 0, 209, (-958)}, intArray0);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(0, int2);
      
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(2146912812, 12);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 233.6218;
      doubleArray0[4] = (-278.31744);
      doubleArray0[3] = (-2215.0454694992377);
      doubleArray0[4] = 10.0;
      doubleArray0[5] = (-1490.38223);
      doubleArray0[6] = 3.141592653589793;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 240.93);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 233.6218, 0.0, (-2215.0454694992377), 10.0, (-1490.38223), 3.141592653589793}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {-0.0, (-16.274057057978577), -0.0, 154.29971157078464, (-0.6965983935565335), 103.8197867203204, (-0.21884283956996572)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double[] doubleArray2 = new double[4];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      doubleArray2[0] = (-2215.0454694992377);
      doubleArray2[1] = 10.0;
      doubleArray2[2] = 240.93;
      doubleArray2[3] = 240.93;
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(doubleArray1, doubleArray2);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(4723.713F, 2614.7048F, 807.0F);
      assertFalse(boolean0);
      
      double double0 = MathUtils.binomialCoefficientLog(5, (-3815));
      assertEquals(0.0, double0, 0.01);
      
      long long0 = MathUtils.sign(22L);
      assertEquals(1L, long0);
      
      float[] floatArray0 = new float[0];
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(0, floatArray0.length);
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      long long1 = MathUtils.lcm(1L, (long) (-3815));
      assertFalse(long1 == long0);
      assertEquals(3815L, long1);
      
      int[] intArray0 = new int[7];
      intArray0[0] = (-625);
      intArray0[1] = 5;
      intArray0[2] = (-3815);
      intArray0[3] = (-3815);
      intArray0[4] = 1584;
      intArray0[5] = (-3815);
      intArray0[6] = (-3815);
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(7, intArray0.length);
      assertArrayEquals(new int[] {(-625), 5, (-3815), (-3815), 1584, (-3815), (-3815)}, intArray0);
      assertEquals(0, int0);
      
      byte byte0 = MathUtils.sign((byte)31);
      assertEquals((byte)1, byte0);
      
      long long2 = MathUtils.lcm((long) (-625), (-2789L));
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(1743125L, long2);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 1743125L;
      doubleArray0[1] = (double) 1584;
      doubleArray0[2] = (double) (-625);
      doubleArray0[3] = (double) 2614.7048F;
      doubleArray0[4] = (double) 1584;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = (double) (-3815);
      double double1 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {1743125.0, 1584.0, (-625.0), 2614.704833984375, 1584.0, 0.0, (-3815.0)}, doubleArray0, 0.01);
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      MathUtils.checkFinite(1.86285714285714278E18);
      boolean boolean2 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(0, floatArray0.length);
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertTrue(boolean2);
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 233.6218;
      doubleArray0[2] = 233.6218;
      doubleArray0[3] = (-2215.438635);
      doubleArray0[4] = 10.0;
      doubleArray0[5] = (-1490.38223);
      doubleArray0[6] = 3.141592653589793;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 240.93);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 233.6218, 233.6218, (-2215.438635), 10.0, (-1490.38223), 3.141592653589793}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {-0.0, (-17.450820909738752), (-17.450820909738752), 165.48636666570104, (-0.7469688577752055), 111.32691119915636, (-0.2346671876046945)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double0 = MathUtils.indicator(3.141592653589793);
      assertEquals(1.0, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-1497), (-1497));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -1,497
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 233.6218;
      doubleArray0[2] = (-278.31744);
      doubleArray0[3] = (-2215.438635);
      doubleArray0[4] = 10.0;
      doubleArray0[5] = (-1490.38223);
      doubleArray0[6] = 3.141592653589793;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 240.93);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 233.6218, (-278.31744), (-2215.438635), 10.0, (-1490.38223), 3.141592653589793}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {-0.0, (-15.060437230436174), 17.941743173178555, 142.81832645883517, (-0.6446503378724149), 96.07754081285431, (-0.20252287655941567)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      float float0 = MathUtils.sign(1.0F);
      assertEquals(1.0F, float0, 0.01F);
      
      long long0 = MathUtils.pow(4654299874869687887L, 1750);
      assertEquals((-6400074776269002719L), long0);
      
      // Undeclared exception!
      try { 
        MathUtils.distance1((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      int int0 = 2147483594;
      // Undeclared exception!
      try { 
        MathUtils.addAndCheck(2147483594, 2860);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in addition: 2,147,483,594 + 2,860
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      int[] intArray0 = new int[5];
      intArray0[0] = 222;
      intArray0[1] = 222;
      intArray0[2] = (-1);
      intArray0[3] = 2113396605;
      intArray0[4] = 43935035;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(5, intArray0.length);
      assertArrayEquals(new int[] {222, 222, (-1), 2113396605, 43935035}, intArray0);
      assertEquals(0, int0);
      
      long long0 = MathUtils.binomialCoefficient(222, (-2566));
      assertEquals(1L, long0);
      
      float[] floatArray0 = new float[9];
      floatArray0[0] = Float.NaN;
      floatArray0[1] = (float) 222;
      floatArray0[2] = (float) 2113396605;
      floatArray0[3] = (float) 2113396605;
      floatArray0[4] = (float) 222;
      floatArray0[5] = (float) 0;
      floatArray0[6] = (float) (-1);
      floatArray0[7] = (float) 43935035;
      floatArray0[8] = (float) 222;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(9, floatArray0.length);
      assertArrayEquals(new float[] {Float.NaN, 222.0F, 2.11339661E9F, 2.11339661E9F, 222.0F, 0.0F, (-1.0F), 4.3935036E7F, 222.0F}, floatArray0, 0.01F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 10.0;
      doubleArray0[1] = 2537.8565218160716;
      doubleArray0[2] = 2837.2616630997;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (-1906.0);
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {10.0, 2537.8565218160716, 2837.2616630997, 0.0, 0.0, (-1906.0)}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      
      long long0 = MathUtils.lcm(720L, 720L);
      assertEquals(720L, long0);
      
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((-4570149291142443753L), (-2391L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      long long0 = MathUtils.lcm((-72L), (-72L));
      assertEquals(72L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (-72L), 173.9F, 3235.9385F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      int[] intArray0 = new int[5];
      intArray0[0] = 222;
      intArray0[1] = 6;
      intArray0[2] = (-1);
      intArray0[3] = 2113396605;
      intArray0[4] = 43935035;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(5, intArray0.length);
      assertArrayEquals(new int[] {222, 6, (-1), 2113396605, 43935035}, intArray0);
      assertEquals(0, int0);
      
      long long0 = MathUtils.binomialCoefficient(6, (-2566));
      assertEquals(1L, long0);
      
      float[] floatArray0 = new float[9];
      floatArray0[0] = Float.NaN;
      floatArray0[1] = (float) 222;
      floatArray0[2] = (float) 2113396605;
      floatArray0[3] = (float) 2113396605;
      floatArray0[4] = (float) 6;
      floatArray0[5] = (float) 0;
      floatArray0[6] = (float) (-1);
      floatArray0[7] = (float) 43935035;
      floatArray0[8] = (float) 6;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(9, floatArray0.length);
      assertArrayEquals(new float[] {Float.NaN, 222.0F, 2.11339661E9F, 2.11339661E9F, 6.0F, 0.0F, (-1.0F), 4.3935036E7F, 6.0F}, floatArray0, 0.01F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      float[] floatArray0 = new float[9];
      floatArray0[0] = Float.NaN;
      floatArray0[1] = (float) 222;
      floatArray0[2] = (float) 2113396605;
      floatArray0[3] = (float) 2113396605;
      floatArray0[7] = (float) 43935035;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(9, floatArray0.length);
      assertArrayEquals(new float[] {Float.NaN, 222.0F, 2.11339661E9F, 2.11339661E9F, 0.0F, 0.0F, 0.0F, 4.3935036E7F, 0.0F}, floatArray0, 0.01F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      float float0 = MathUtils.indicator(1138.0F);
      assertEquals(1.0F, float0, 0.01F);
      
      short short0 = MathUtils.sign((short)1273);
      assertEquals((short)1, short0);
      
      double double0 = MathUtils.factorialDouble((short)1);
      assertEquals(1.0, double0, 0.01);
      
      short short1 = MathUtils.indicator((short)1);
      assertTrue(short1 == short0);
      assertEquals((short)1, short1);
      
      int int0 = MathUtils.mulAndCheck((int) (short)1273, 3223);
      assertEquals(4102879, int0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) (short)1273;
      doubleArray0[1] = (double) (short)1273;
      doubleArray0[2] = (double) 1138.0F;
      doubleArray0[3] = (double) (short)1;
      doubleArray0[4] = (double) (short)1;
      doubleArray0[5] = (double) (short)1273;
      doubleArray0[6] = (double) (short)1;
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle((-1738.3264629638), (-1738.3264629638));
      assertEquals((-1738.3264629638), double0, 0.01);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      boolean boolean0 = MathUtils.equals((-1738.3264629638), (-1738.3264629638));
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[14];
      doubleArray0[0] = (-1738.3264629638);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (-1738.3264629638);
      doubleArray0[3] = (-1738.3264629638);
      doubleArray0[4] = (-1738.3264629638);
      double double1 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(14, doubleArray0.length);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      int int0 = MathUtils.gcd((-2132), (-2132));
      assertEquals(2132, int0);
      
      double double2 = MathUtils.binomialCoefficientLog(192, (-2132));
      assertNotEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 10214006191389L);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(20.0F, 20.0F);
      assertTrue(boolean0);
      
      int int0 = MathUtils.addAndCheck((-285), (-285));
      assertEquals((-570), int0);
      
      float[] floatArray0 = new float[8];
      floatArray0[0] = 1.0F;
      floatArray0[1] = (float) (-285);
      floatArray0[2] = 20.0F;
      floatArray0[3] = (float) (-285);
      floatArray0[4] = 20.0F;
      floatArray0[5] = Float.NaN;
      floatArray0[6] = 20.0F;
      floatArray0[7] = (float) (-285);
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(8, floatArray0.length);
      assertArrayEquals(new float[] {1.0F, (-285.0F), 20.0F, (-285.0F), 20.0F, Float.NaN, 20.0F, (-285.0F)}, floatArray0, 0.01F);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      boolean boolean2 = MathUtils.equals(20.0F, 1.0F, 563.241F);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2);
      
      double double0 = MathUtils.cosh(20.0F);
      assertEquals(2.4258259770489514E8, double0, 0.01);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) Float.NaN;
      doubleArray0[1] = (double) (-570);
      doubleArray0[2] = (double) (-285.0F);
      doubleArray0[3] = (double) 20.0F;
      doubleArray0[4] = (double) Float.NaN;
      doubleArray0[5] = 0.5;
      // Undeclared exception!
      try { 
        MathUtils.copyOf(doubleArray0, (-1073741823));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(2313, (-1));
      assertEquals((-2313), int0);
      
      byte byte0 = MathUtils.indicator((byte)8);
      assertEquals((byte)1, byte0);
      
      double double0 = MathUtils.binomialCoefficientLog(3489, 3489);
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) (-2313);
      doubleArray0[1] = (double) 3489;
      doubleArray0[2] = 0.0;
      double[] doubleArray1 = new double[8];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 0.5;
      doubleArray1[1] = 816.0;
      doubleArray1[2] = (-12.69453441);
      doubleArray1[3] = (-2121.0);
      doubleArray1[4] = (double) (-1);
      doubleArray1[5] = 2163.450684549389;
      doubleArray1[6] = 754.2025173;
      doubleArray1[7] = (double) (byte)8;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {(-2313.0), 3489.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.5, 816.0, (-12.69453441), (-2121.0), (-1.0), 2163.450684549389, 754.2025173, 8.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      long long0 = MathUtils.lcm((-72L), (-72L));
      assertEquals(72L, long0);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 72L;
      MathUtils.checkOrder(doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {72.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte) (-16));
      assertEquals((byte) (-1), byte0);
      
      short short0 = MathUtils.indicator((short) (byte) (-16));
      assertEquals((short) (-1), short0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) (byte) (-16);
      doubleArray0[1] = (double) (byte) (-1);
      short short1 = bigInteger1.shortValueExact();
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(short1 == short0);
      assertEquals((short)1, short1);
      
      doubleArray0[2] = (double) (short) (-1);
      doubleArray0[3] = (double) (short) (-1);
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {(-16.0), (-1.0), (-1.0), (-1.0)}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      int int0 = (-184);
      int int1 = MathUtils.gcd((-184), (int) (short) (-1));
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      int int2 = 1030;
      boolean boolean1 = MathUtils.equals((float) (-184), (float) (byte) (-16), 1030);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {(-16.0), (-1.0), (-1.0), (-1.0)}, doubleArray0, 0.01);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2);
      
      int int3 = MathUtils.sign((-1));
      assertFalse(int3 == int0);
      assertFalse(int3 == int1);
      assertFalse(int3 == int2);
      assertEquals((-1), int3);
      
      // Undeclared exception!
      try { 
        MathUtils.round((-4120.5225F), 257, (-1));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method -1, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, (-2552.703F));
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) (-2552.703F);
      doubleArray0[1] = (double) Float.NaN;
      doubleArray0[2] = (double) (-2552.703F);
      doubleArray0[3] = (double) (-2552.703F);
      doubleArray0[4] = (double) (-2552.703F);
      doubleArray0[5] = (double) (-2552.703F);
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-2552.702880859375));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {(-2552.702880859375), Double.NaN, (-2552.702880859375), (-2552.702880859375), (-2552.702880859375), (-2552.702880859375)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-510.540576171875), Double.NaN, (-510.540576171875), (-510.540576171875), (-510.540576171875), (-510.540576171875)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-2552.702880859375), Double.NaN, (-2552.702880859375), (-2552.702880859375), (-2552.702880859375), (-2552.702880859375)}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)63);
      assertEquals((byte)1, byte0);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      Object[] objectArray0 = new Object[3];
      objectArray0[0] = (Object) null;
      objectArray0[1] = object0;
      objectArray0[2] = (Object) null;
      MathUtils.checkNotNull(object0, (Localizable) null, objectArray0);
      assertEquals(3, objectArray0.length);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) (byte)1;
      doubleArray0[1] = (double) (byte)63;
      doubleArray0[2] = (double) (byte)63;
      doubleArray0[3] = (double) (byte)63;
      doubleArray0[4] = (double) (byte)1;
      MathUtils.checkFinite(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {1.0, 63.0, 63.0, 63.0, 1.0}, doubleArray0, 0.01);
      
      double[] doubleArray1 = new double[6];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) (byte)1;
      doubleArray1[1] = (double) (byte)1;
      doubleArray1[2] = (double) (byte)63;
      doubleArray1[3] = (double) (byte)63;
      doubleArray1[4] = (double) (byte)1;
      doubleArray1[5] = (double) (byte)1;
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      assertArrayEquals(new double[] {1.0, 63.0, 63.0, 63.0, 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.0, 1.0, 63.0, 63.0, 1.0, 1.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(62.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      int int0 = (-814);
      int int1 = new Integer((-814));
      assertTrue(int1 == int0);
      assertEquals((-814), int1);
      
      long long0 = MathUtils.mulAndCheck((long) (-814), (-1L));
      assertEquals(814L, long0);
      
      int int2 = 517;
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null, 517);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      float[] floatArray0 = new float[8];
      floatArray0[4] = (-3857.265F);
      floatArray0[6] = 4645.0F;
      float[] floatArray1 = new float[2];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = 4645.0F;
      floatArray1[1] = 4645.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertEquals(8, floatArray0.length);
      assertEquals(2, floatArray1.length);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, (-3857.265F), 0.0F, 4645.0F, 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {4645.0F, 4645.0F}, floatArray1, 0.01F);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 4645.0F;
      doubleArray0[1] = (double) 0.0F;
      double[] doubleArray1 = new double[9];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) 4645.0F;
      doubleArray1[1] = (double) 4645.0F;
      doubleArray1[2] = (double) (-3857.265F);
      doubleArray1[3] = (double) 0.0F;
      doubleArray1[4] = (double) 0.0F;
      doubleArray1[5] = (double) 4645.0F;
      doubleArray1[6] = (double) 0.0F;
      doubleArray1[7] = (double) 4645.0F;
      doubleArray1[8] = (double) 4645.0F;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      assertArrayEquals(new double[] {4645.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {4645.0, 4645.0, (-3857.264892578125), 0.0, 0.0, 4645.0, 0.0, 4645.0, 4645.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(4645.0, double0, 0.01);
      
      double double1 = MathUtils.binomialCoefficientDouble(270, 0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      int int0 = MathUtils.hash(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {4645.0, 0.0}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertEquals((-711819583), int0);
      
      double double2 = MathUtils.cosh(4645.0);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double2, 0.01);
      
      int int1 = MathUtils.compareTo(4645.0F, 1203.509992755, 0.0);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, 4194302);
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long0 = MathUtils.indicator(1534L);
      assertEquals(1L, long0);
      
      short short0 = MathUtils.indicator((short)904);
      assertEquals((short)1, short0);
      
      double double0 = MathUtils.factorialDouble((short)904);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) Float.NaN;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // value \uFFFD at index 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (-246.606218611318);
      doubleArray0[1] = 2103.864425;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 2.0;
      doubleArray0[4] = 1.633123935319537E16;
      doubleArray0[5] = 1629.877921626772;
      doubleArray0[6] = 2249.497661848;
      doubleArray0[7] = 0.0;
      doubleArray0[8] = 0.0;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {(-246.606218611318), 2103.864425, 0.0, 2.0, 1.633123935319537E16, 1629.877921626772, 2249.497661848, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(1481340067, int0);
      
      int[] intArray0 = new int[6];
      intArray0[0] = 1481340067;
      intArray0[1] = 1481340067;
      intArray0[2] = 1481340067;
      intArray0[3] = 1481340067;
      intArray0[4] = 1481340067;
      intArray0[5] = 1481340067;
      int int1 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(6, intArray0.length);
      assertArrayEquals(new int[] {1481340067, 1481340067, 1481340067, 1481340067, 1481340067, 1481340067}, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      // Undeclared exception!
      try { 
        MathUtils.equals(0.0, 14.0, 1481340067);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      double double0 = (-244.2738);
      int int0 = 2110458067;
      // Undeclared exception!
      try { 
        MathUtils.equals((-244.2738), (-244.2738), 2110458067);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 1.1102230246251565E-16;
      doubleArray0[1] = 822.88451;
      doubleArray0[2] = 3284.8713593589036;
      doubleArray0[3] = 1.2675934823758863E-8;
      doubleArray0[4] = Double.NaN;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 2 and 3 are not strictly increasing (3,284.871 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      int int0 = (-3198);
      // Undeclared exception!
      try { 
        MathUtils.factorial((-3198));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -3,198
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, (BigInteger) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      long long0 = MathUtils.pow(720L, 720L);
      assertEquals(0L, long0);
      
      int int0 = MathUtils.gcd(12, 12);
      assertEquals(12, int0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 12;
      doubleArray0[1] = (double) 0L;
      doubleArray0[2] = (double) 0L;
      doubleArray0[3] = (double) 720L;
      doubleArray0[4] = (double) 0L;
      doubleArray0[5] = (double) 12;
      doubleArray0[6] = 0.0;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 2 and 3 are not decreasing (0 < 720)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      double double0 = (-3165.725);
      int int0 = MathUtils.compareTo((-375.085375478424), (-3165.725), 2986.36198);
      assertEquals(0, int0);
      
      boolean boolean0 = MathUtils.equals(3.834E-20, (-375.085375478424), (-375.085375478424));
      assertFalse(boolean0);
      
      float[] floatArray0 = new float[7];
      floatArray0[0] = (float) 0;
      floatArray0[1] = (float) 0;
      floatArray0[2] = (float) 0;
      floatArray0[3] = (float) 0;
      floatArray0[4] = (float) 0;
      floatArray0[5] = (float) 0;
      floatArray0[6] = (float) 0;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(7, floatArray0.length);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      short short0 = MathUtils.indicator((short)390);
      assertEquals((short)1, short0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((double) (short)390, 0.0, 1.0);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2);
      
      short short1 = (short)830;
      short short2 = MathUtils.indicator((short)830);
      assertFalse(short2 == short1);
      assertTrue(short2 == short0);
      assertEquals((short)1, short2);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 3.834E-20;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (double) (short)830;
      doubleArray0[3] = (double) (short)390;
      doubleArray0[4] = 1.0;
      doubleArray0[5] = (double) (short)390;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = 2612.456;
      double double0 = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 184.6;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 2109.35876674889;
      doubleArray0[6] = 0.0;
      doubleArray0[7] = 2278.0;
      doubleArray0[8] = (-707.74729);
      int int0 = (-1320);
      // Undeclared exception!
      try { 
        MathUtils.copyOf(doubleArray0, (-1320));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-615.25271), (-615.25271), 0.0);
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equals(1423.1879F, 1423.1879F, 1423.1879F);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null, 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      int int0 = MathUtils.pow(53, (long) 53);
      assertEquals((-1690305179), int0);
      
      boolean boolean0 = MathUtils.equals((double) 53, (-861.5630230928331), (-861.5630230928331));
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[0];
      boolean boolean1 = MathUtils.equals(doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      
      float float0 = MathUtils.sign((float) 53);
      assertEquals(1.0F, float0, 0.01F);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((-872.37), 0.0, (double) 53);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2);
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      int int0 = Integer.MIN_VALUE;
      // Undeclared exception!
      try { 
        MathUtils.pow(Integer.MIN_VALUE, (long) Integer.MIN_VALUE);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-2,147,483,648)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      long long0 = 806L;
      long long1 = MathUtils.indicator(806L);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      long long2 = MathUtils.subAndCheck(806L, 806L);
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      assertEquals(0L, long2);
      
      long long3 = MathUtils.sign(2039L);
      assertTrue(long3 == long1);
      assertFalse(long3 == long2);
      assertFalse(long3 == long0);
      assertEquals(1L, long3);
      
      long long4 = MathUtils.pow(1L, 821L);
      assertTrue(long4 == long1);
      assertTrue(long4 == long3);
      assertFalse(long4 == long2);
      assertFalse(long4 == long0);
      assertEquals(1L, long4);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-612), (-612));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -612
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      float float0 = MathUtils.indicator(0.0F);
      assertEquals(1.0F, float0, 0.01F);
      
      short short0 = MathUtils.sign((short) (-165));
      assertEquals((short) (-1), short0);
      
      int int0 = MathUtils.addAndCheck(63, 2722);
      assertEquals(2785, int0);
      
      double double0 = MathUtils.round(1.1921056801463227E-8, 1284);
      assertEquals(1.1921056801463227E-8, double0, 0.01);
      
      int int1 = MathUtils.hash((double) 2722);
      assertFalse(int1 == int0);
      assertEquals(1084572672, int1);
      
      byte byte0 = MathUtils.sign((byte)0);
      assertEquals((byte)0, byte0);
      
      float float1 = MathUtils.round((float) 63, 887);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(Float.NaN, float1, 0.01F);
      
      float float2 = MathUtils.round((float) 1284, 0);
      assertNotEquals(float2, float0, 0.01F);
      assertNotEquals(float2, float1, 0.01F);
      assertEquals(1284.0F, float2, 0.01F);
      
      double double1 = MathUtils.normalizeAngle((-1545.13689491329), 1.1921056801463227E-8);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.5266906528881918, double1, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (long) 2785);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      
      long long0 = MathUtils.pow((long) (byte)0, 9193070505571053912L);
      assertEquals(0L, long0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 1.1921056801463227E-8;
      doubleArray0[1] = 10.0;
      doubleArray0[2] = 1.1921056801463227E-8;
      doubleArray0[3] = 0.5266906528881918;
      doubleArray0[4] = (double) 0.0F;
      doubleArray0[5] = (double) (byte)0;
      double double2 = MathUtils.safeNorm(doubleArray0);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {1.1921056801463227E-8, 10.0, 1.1921056801463227E-8, 0.5266906528881918, 0.0, 0.0}, doubleArray0, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(10.013860546454588, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      int[] intArray0 = new int[9];
      intArray0[0] = (-239);
      int int0 = 3976;
      intArray0[1] = 3976;
      intArray0[2] = (-2078);
      intArray0[3] = 0;
      intArray0[4] = 0;
      intArray0[5] = 156;
      intArray0[6] = 3865;
      intArray0[7] = 17;
      intArray0[8] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0, 3976);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(9, intArray0.length);
      assertEquals(3976, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {(-239), 3976, (-2078), 0, 0, 156, 3865, 17, 0}, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 17;
      doubleArray0[1] = 0.0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      int int0 = MathUtils.pow(1030, (long) 1030);
      assertEquals(0, int0);
      
      int int1 = MathUtils.mulAndCheck((-4602), 0);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      
      byte[] byteArray0 = new byte[5];
      byteArray0[0] = (byte) (-17);
      byteArray0[1] = (byte)0;
      byteArray0[2] = (byte)103;
      byteArray0[3] = (byte)72;
      byteArray0[4] = (byte)26;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(5, byteArray0.length);
      assertEquals((byte)26, bigInteger0.byteValue());
      assertEquals((short)18458, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte) (-17), (byte)0, (byte)103, (byte)72, (byte)26}, byteArray0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, bigInteger0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-73,007,675,366)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      float[] floatArray0 = new float[5];
      floatArray0[0] = 0.0F;
      floatArray0[1] = (-1622.3424F);
      floatArray0[2] = 0.0F;
      floatArray0[3] = 1348.1698F;
      floatArray0[4] = Float.NaN;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(5, floatArray0.length);
      assertArrayEquals(new float[] {0.0F, (-1622.3424F), 0.0F, 1348.1698F, Float.NaN}, floatArray0, 0.01F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      int[] intArray0 = new int[7];
      intArray0[0] = 63;
      intArray0[1] = 688;
      int int0 = (-2131);
      intArray0[2] = (-2131);
      intArray0[3] = 8;
      intArray0[4] = (-1095);
      intArray0[5] = 192;
      intArray0[6] = 0;
      // Undeclared exception!
      try { 
        MathUtils.distance((int[]) null, intArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      double[] doubleArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.safeNorm((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Double.NaN, Double.NaN, (-1856.00490995698));
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(Float.NaN, 0.0F, 0.0F);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double[] doubleArray0 = new double[0];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[4][0];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(0, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      double double0 = MathUtils.log(Float.NaN, (-1168.0));
      assertEquals(Double.NaN, double0, 0.01);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      int int0 = MathUtils.indicator((-680));
      assertEquals((-1), int0);
      
      int[] intArray0 = new int[8];
      intArray0[0] = (-680);
      intArray0[1] = (-680);
      intArray0[2] = (-1);
      intArray0[3] = (-680);
      intArray0[4] = (-680);
      intArray0[5] = (-680);
      intArray0[6] = (-1);
      intArray0[7] = (-1);
      int[] intArray1 = MathUtils.copyOf(intArray0, 2561);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray0, intArray1);
      assertEquals(2561, intArray1.length);
      assertEquals(8, intArray0.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {(-680), (-680), (-1), (-680), (-680), (-680), (-1), (-1)}, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      
      double double1 = MathUtils.round((double) (-1), 1045);
      assertNotEquals(double1, double0, 0.01);
      assertEquals((-1.0), double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle(0.0, 0.0);
      assertEquals(0.0, double0, 0.01);
      
      long long0 = MathUtils.gcd(270L, 7910884519577875640L);
      assertEquals(10L, long0);
      
      double double1 = MathUtils.log(0.0, 270L);
      assertEquals(double1, double0, 0.01);
      assertEquals(-0.0, double1, 0.01);
      
      String string0 = "";
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      long long0 = MathUtils.indicator(1800L);
      assertEquals(1L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 1800L, (float) 1800L, (-920.2F));
      assertTrue(boolean0);
      
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
      
      short short0 = MathUtils.sign((short)1309);
      assertEquals((short)1, short0);
      
      long long1 = MathUtils.indicator(479001600L);
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
      
      int int0 = MathUtils.gcd(210, 0);
      assertEquals(210, int0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((-1093.4F), 365.48F);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 365.48F;
      doubleArray0[1] = (double) 1800L;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (-2089.98469650249);
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 0.041666663879186654);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {365.4800109863281, 1800.0, 0.0, (-2089.98469650249)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.20171229004667313, 0.9934390696338078, 0.0, (-1.153484695801294)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float) 210, (float) (short)1309, (int) (short)1309);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
      
      MathUtils.checkFinite(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {365.4800109863281, 1800.0, 0.0, (-2089.98469650249)}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      long long0 = MathUtils.indicator(1800L);
      assertEquals(1L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 1800L, (float) 1800L, (-920.2F));
      assertTrue(boolean0);
      
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
      
      short short0 = MathUtils.sign((short)1309);
      assertEquals((short)1, short0);
      
      long long1 = MathUtils.indicator(479001600L);
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
      
      int int0 = MathUtils.gcd(210, 0);
      assertEquals(210, int0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((-1093.4F), (float) 1L);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float) 210, (float) (short)1309, (int) (short)1309);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      int int0 = (-283);
      // Undeclared exception!
      try { 
        MathUtils.addAndCheck((-283), Integer.MIN_VALUE);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in addition: -283 + -2,147,483,648
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      short short0 = (short) (-1459);
      short short1 = MathUtils.indicator((short) (-1459));
      assertFalse(short1 == short0);
      assertEquals((short) (-1), short1);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) (short) (-1459);
      doubleArray0[1] = (double) (short) (-1459);
      doubleArray0[2] = (double) (short) (-1);
      doubleArray0[3] = (double) (short) (-1);
      doubleArray0[4] = (double) (short) (-1459);
      doubleArray0[5] = (double) (short) (-1);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly decreasing (-1,459 <= -1,459)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (-1911.55750016);
      doubleArray0[1] = (-1599.1612);
      doubleArray0[2] = 207.445132228356;
      double[] doubleArray1 = new double[1];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 207.445132228356;
      // Undeclared exception!
      try { 
        MathUtils.distance1(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, 4194304);
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long0 = MathUtils.indicator(1534L);
      assertEquals(1L, long0);
      
      short short0 = MathUtils.indicator((short)904);
      assertEquals((short)1, short0);
      
      // Undeclared exception!
      MathUtils.factorialDouble(4194304);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      int int0 = MathUtils.indicator((-552));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      int int0 = MathUtils.hash(1048.2);
      assertEquals((-1940083711), int0);
      
      long long0 = (-338L);
      long long1 = MathUtils.gcd(5360L, (-338L));
      assertFalse(long1 == long0);
      assertEquals(2L, long1);
      
      int int1 = MathUtils.mulAndCheck((-819), (-819));
      assertFalse(int1 == int0);
      assertEquals(670761, int1);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      byte byte0 = (byte) (-40);
      byte byte1 = MathUtils.indicator((byte) (-40));
      assertFalse(byte1 == byte0);
      assertEquals((byte) (-1), byte1);
      
      long long2 = (-2020L);
      long long3 = MathUtils.sign((-2020L));
      assertFalse(long3 == long2);
      assertFalse(long3 == long0);
      assertFalse(long3 == long1);
      assertEquals((-1L), long3);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(0, 6042);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 6,042, n = 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Float.POSITIVE_INFINITY, Float.POSITIVE_INFINITY, 1030);
      assertTrue(boolean0);
      
      byte byte0 = MathUtils.indicator((byte) (-97));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-2925.46449);
      doubleArray0[1] = 1.1102230246251565E-16;
      doubleArray0[2] = 348.808693090655;
      doubleArray0[3] = (-1047.1);
      doubleArray0[4] = 2906.3040884642205;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {(-2925.46449), 1.1102230246251565E-16, 348.808693090655, (-1047.1), 2906.3040884642205}, doubleArray0, 0.01);
      assertEquals(4268.844322676855, double0, 0.01);
      
      int[] intArray0 = new int[1];
      intArray0[0] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0, 0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(1, intArray0.length);
      assertEquals(0, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {0}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      
      int int0 = MathUtils.gcd(0, 29);
      assertEquals(29, int0);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {(-2925.46449), 1.1102230246251565E-16, 348.808693090655, (-1047.1), 2906.3040884642205}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double1 = MathUtils.round(3.834E-20, 0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      short short0 = MathUtils.indicator((short) (-495));
      assertEquals((short) (-1), short0);
      
      double double2 = MathUtils.safeNorm(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertArrayEquals(new double[] {(-2925.46449), 1.1102230246251565E-16, 348.808693090655, (-1047.1), 2906.3040884642205}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      byte byte0 = MathUtils.sign((byte) (-95));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      float float0 = 2524.7825F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(2524.7825F, 2524.7825F, 596);
      assertTrue(boolean0);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      int int0 = 513;
      BigInteger bigInteger1 = BigInteger.TEN;
      assertSame(bigInteger1, bigInteger0);
      assertEquals((short)10, bigInteger1.shortValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      
      BigInteger bigInteger2 = bigInteger1.and(bigInteger0);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger2);
      assertSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((byte)10, bigInteger2.byteValue());
      assertEquals((short)10, bigInteger2.shortValue());
      assertNotNull(bigInteger2);
      assertTrue(bigInteger2.equals((Object)bigInteger1));
      assertTrue(bigInteger2.equals((Object)bigInteger0));
      
      BigInteger[] bigIntegerArray0 = bigInteger1.divideAndRemainder(bigInteger0);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger2);
      assertSame(bigInteger1, bigInteger0);
      assertEquals(2, bigIntegerArray0.length);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      assertNotNull(bigIntegerArray0);
      assertTrue(bigInteger0.equals((Object)bigInteger2));
      assertTrue(bigInteger1.equals((Object)bigInteger2));
      
      BigInteger bigInteger3 = bigInteger0.andNot(bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger1, bigInteger2);
      assertSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger3.shortValue());
      assertEquals((byte)0, bigInteger3.byteValue());
      assertNotNull(bigInteger3);
      assertTrue(bigInteger0.equals((Object)bigInteger2));
      assertTrue(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      
      BigInteger bigInteger4 = bigInteger0.subtract(bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger1, bigInteger2);
      assertSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger4);
      assertNotSame(bigInteger4, bigInteger3);
      assertNotSame(bigInteger4, bigInteger2);
      assertNotSame(bigInteger4, bigInteger0);
      assertNotSame(bigInteger4, bigInteger1);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger4.shortValue());
      assertEquals((byte)0, bigInteger4.byteValue());
      assertNotNull(bigInteger4);
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertTrue(bigInteger0.equals((Object)bigInteger2));
      assertTrue(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger3));
      assertFalse(bigInteger4.equals((Object)bigInteger2));
      assertFalse(bigInteger4.equals((Object)bigInteger0));
      assertTrue(bigInteger4.equals((Object)bigInteger3));
      assertFalse(bigInteger4.equals((Object)bigInteger1));
      
      long long0 = bigInteger0.longValueExact();
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger4);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertTrue(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger4));
      assertEquals(10L, long0);
      
      BigInteger bigInteger5 = bigInteger0.clearBit(513);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger5);
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger5, bigInteger4);
      assertNotSame(bigInteger5, bigInteger0);
      assertNotSame(bigInteger5, bigInteger3);
      assertNotSame(bigInteger5, bigInteger1);
      assertNotSame(bigInteger5, bigInteger2);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((byte)10, bigInteger5.byteValue());
      assertEquals((short)10, bigInteger5.shortValue());
      assertNotNull(bigInteger5);
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertTrue(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger4));
      assertTrue(bigInteger5.equals((Object)bigInteger1));
      assertTrue(bigInteger5.equals((Object)bigInteger2));
      assertFalse(bigInteger5.equals((Object)bigInteger4));
      assertFalse(bigInteger5.equals((Object)bigInteger3));
      assertTrue(bigInteger5.equals((Object)bigInteger0));
      
      BigInteger bigInteger6 = MathUtils.pow(bigInteger0, (long) 596);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger6);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger5);
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger6, bigInteger0);
      assertNotSame(bigInteger6, bigInteger4);
      assertNotSame(bigInteger6, bigInteger1);
      assertNotSame(bigInteger6, bigInteger2);
      assertNotSame(bigInteger6, bigInteger3);
      assertNotSame(bigInteger6, bigInteger5);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger6.byteValue());
      assertEquals((short)0, bigInteger6.shortValue());
      assertNotNull(bigInteger6);
      assertTrue(bigInteger0.equals((Object)bigInteger5));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertTrue(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger4));
      assertFalse(bigInteger6.equals((Object)bigInteger0));
      assertFalse(bigInteger6.equals((Object)bigInteger4));
      assertFalse(bigInteger6.equals((Object)bigInteger1));
      assertFalse(bigInteger6.equals((Object)bigInteger2));
      assertFalse(bigInteger6.equals((Object)bigInteger3));
      assertFalse(bigInteger6.equals((Object)bigInteger5));
      
      String string0 = "sj]@E0jy@*p>sNf(6:<";
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("sj]@E0jy@*p>sNf(6:<");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.sj]@E0jy@*p>sNf(6:<
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      int[] intArray0 = new int[2];
      intArray0[0] = 0;
      intArray0[1] = 2;
      int[] intArray1 = new int[0];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = MathUtils.copyOf(intArray0, 1618);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(0, intArray0.length);
      assertEquals(1618, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {}, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      
      boolean boolean0 = MathUtils.equals((float) 1618, (float) 1618);
      assertTrue(boolean0);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      int int0 = 20;
      boolean boolean1 = bigInteger0.testBit(20);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 4147);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 1618;
      doubleArray0[1] = (double) 4147;
      doubleArray0[2] = (double) 4147;
      doubleArray0[3] = (double) 4147;
      doubleArray0[4] = (double) 4147;
      doubleArray0[5] = (double) 1618;
      doubleArray0[6] = 324.7807831;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {1618.0, 4147.0, 4147.0, 4147.0, 4147.0, 1618.0, 324.7807831}, doubleArray0, 0.01);
      assertEquals(8609.980636277358, double0, 0.01);
      
      long long0 = MathUtils.lcm((long) 1618, (long) 20);
      assertEquals(16180L, long0);
      
      // Undeclared exception!
      try { 
        MathUtils.round((float) 20, 20, 1618);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 1,618, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      int int0 = (-1224);
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(0.0, 0.0, (-1224));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      double double0 = 1.1102230246251565E-16;
      int int0 = MathUtils.hash(1.1102230246251565E-16);
      assertEquals(1017118720, int0);
      
      short short0 = (short)0;
      short short1 = MathUtils.sign((short)0);
      assertTrue(short1 == short0);
      assertEquals((short)0, short1);
      
      // Undeclared exception!
      MathUtils.factorialLog(1017118720);
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-2603.6283174);
      doubleArray0[1] = (-0.49999999999999994);
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 1.304E19;
      doubleArray0[4] = (-1959.4095012135);
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {(-2603.6283174), (-0.49999999999999994), 0.0, 1.304E19, (-1959.4095012135)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-2603.6283174), (-0.49999999999999994), 0.0, 1.304E19, (-1959.4095012135)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      byte byte0 = MathUtils.sign((byte) (-119));
      assertEquals((byte) (-1), byte0);
      
      int int0 = MathUtils.gcd((-2763), (-2763));
      assertEquals(2763, int0);
      
      double double0 = MathUtils.round((double) (byte) (-1), (int) (byte) (-1));
      assertEquals(0.0, double0, 0.01);
      
      boolean boolean0 = MathUtils.equals(2879.7158F, (-2270.799F), 0.0F);
      assertFalse(boolean0);
      
      long long0 = MathUtils.indicator((long) (byte) (-119));
      assertEquals((-1L), long0);
      
      double double1 = MathUtils.round(0.99, 2763, 0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.99, double1, 0.01);
      
      double double2 = MathUtils.sign(0.99);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(1.0, double2, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = BigInteger.ZERO;
      assertSame(bigInteger1, bigInteger0);
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      
      // Undeclared exception!
      try { 
        bigInteger0.remainder(bigInteger1);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // BigInteger divide by zero
         //
         verifyException("java.math.MutableBigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(1300.3F, 535.95F);
      assertFalse(boolean0);
      
      int int0 = MathUtils.gcd((-3719), 1006);
      assertEquals(1, int0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray1 = MathUtils.OrderDirection.values();
      assertNotSame(mathUtils_OrderDirectionArray1, mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray1.length);
      assertNotNull(mathUtils_OrderDirectionArray1);
      assertFalse(mathUtils_OrderDirectionArray1.equals((Object)mathUtils_OrderDirectionArray0));
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (double) 1;
      doubleArray0[2] = (double) 1300.3F;
      doubleArray0[3] = (double) 1300.3F;
      doubleArray0[4] = (double) 1006;
      doubleArray0[5] = (double) (-3719);
      doubleArray0[6] = (double) 1300.3F;
      doubleArray0[7] = (double) 535.95F;
      doubleArray0[8] = (double) 1300.3F;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 1.0, 1300.300048828125, 1300.300048828125, 1006.0, (-3719.0), 1300.300048828125, 535.9500122070312, 1300.300048828125}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      
      boolean boolean1 = MathUtils.equals(18.3F, 394.5F, 1275);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      double double0 = MathUtils.round(0.0, (-759));
      assertEquals(0.0, double0, 0.01);
      
      MathUtils.checkFinite(0.0);
      int int0 = MathUtils.addAndCheck((-759), (-759));
      assertEquals((-1518), int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) (-1518), 0.0);
      assertFalse(boolean0);
      
      long long0 = MathUtils.gcd(0L, 0L);
      assertEquals(0L, long0);
      
      float[] floatArray0 = new float[1];
      floatArray0[0] = Float.NEGATIVE_INFINITY;
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(1, floatArray0.length);
      assertArrayEquals(new float[] {Float.NEGATIVE_INFINITY}, floatArray0, 0.01F);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      long long1 = MathUtils.mulAndCheck((long) (-1518), 0L);
      assertTrue(long1 == long0);
      assertEquals(0L, long1);
      
      boolean boolean2 = MathUtils.equals((double) Float.NEGATIVE_INFINITY, 0.167, 10);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2);
      
      int[] intArray0 = new int[3];
      intArray0[0] = (-1518);
      intArray0[1] = (-1518);
      intArray0[2] = 92;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(3, intArray0.length);
      assertEquals(3, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {(-1518), (-1518), 92}, intArray0);
      assertArrayEquals(new int[] {(-1518), (-1518), 92}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.pow(0L, (-1518));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,518)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Float.NaN, (-2552.703F), Float.NaN);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      long long0 = MathUtils.pow(2363L, 0);
      assertEquals(1L, long0);
      
      double[] doubleArray0 = new double[0];
      double[][] doubleArray1 = new double[6][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertEquals(0, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(1, int0);
      
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck((-880), 1690);
      assertEquals((-1487200), int0);
      
      float float0 = MathUtils.indicator((float) 1690);
      assertEquals(1.0F, float0, 0.01F);
      
      double double0 = MathUtils.log((-880), (-880));
      assertEquals(Double.NaN, double0, 0.01);
      
      float float1 = MathUtils.sign((float) (-880));
      assertNotEquals(float1, float0, 0.01F);
      assertEquals((-1.0F), float1, 0.01F);
      
      double double1 = MathUtils.cosh(0.0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      int int1 = MathUtils.mulAndCheck((-1487200), (-880));
      assertFalse(int1 == int0);
      assertEquals(1308736000, int1);
      
      double double2 = MathUtils.sinh((-23.132762251));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals((-5.56417782512371E9), double2, 0.01);
      
      Double double3 = new Double((-880));
      assertNotNull(double3);
      assertNotEquals((double)double3, (double)double0, 0.01);
      assertNotEquals((double)double3, (double)double1, 0.01);
      assertNotEquals((double)double3, (double)double2, 0.01);
      assertEquals((-880.0), (double)double3, 0.01);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.ARRAY_SUMS_TO_ZERO;
      assertEquals("array sums to zero", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.ARRAY_SUMS_TO_ZERO, localizedFormats0);
      
      Object[] objectArray0 = new Object[8];
      objectArray0[0] = (Object) double3;
      objectArray0[1] = (Object) double3;
      objectArray0[2] = (Object) double3;
      objectArray0[3] = (Object) double3;
      objectArray0[4] = (Object) localizedFormats0;
      Object object0 = new Object();
      assertNotNull(object0);
      
      objectArray0[5] = object0;
      objectArray0[6] = (Object) localizedFormats0;
      objectArray0[7] = (Object) localizedFormats0;
      MathUtils.checkNotNull((Object) double3, (Localizable) localizedFormats0, objectArray0);
      assertEquals(8, objectArray0.length);
      assertEquals("array sums to zero", localizedFormats0.getSourceString());
      assertNotEquals((double)double3, (double)double0, 0.01);
      assertNotEquals((double)double3, (double)double1, 0.01);
      assertNotEquals((double)double3, (double)double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      int int0 = MathUtils.pow(0, 0);
      assertEquals(1, int0);
      
      double double0 = MathUtils.indicator(411.34127230418);
      assertEquals(1.0, double0, 0.01);
      
      double double1 = MathUtils.indicator(1.0);
      assertEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, 2705.0F, (float) 0);
      assertFalse(boolean0);
      
      long long0 = MathUtils.sign(0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      int[] intArray0 = new int[5];
      intArray0[0] = (-741);
      intArray0[1] = 0;
      intArray0[2] = 2395;
      intArray0[3] = 691;
      intArray0[4] = 253;
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((int[]) null, intArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-2240.627);
      doubleArray0[1] = (-3152.420959555);
      doubleArray0[2] = (-3063.4127388959);
      doubleArray0[3] = 1.2958646899018938E-9;
      doubleArray0[4] = 2489.8232735;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {(-2240.627), (-3152.420959555), (-3063.4127388959), 1.2958646899018938E-9, 2489.8232735}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-2240.627), (-3152.420959555), (-3063.4127388959), 1.2958646899018938E-9, 2489.8232735}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, 2489.8232735, 4);
      assertFalse(boolean0);
      
      double double0 = MathUtils.sign(0.0);
      assertEquals(0.0, double0, 0.01);
      
      float float0 = MathUtils.indicator(1.4E-45F);
      assertEquals(1.0F, float0, 0.01F);
      
      double[] doubleArray2 = MathUtils.normalizeArray(doubleArray0, 0.0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray2.length);
      assertNotNull(doubleArray2);
      assertArrayEquals(new double[] {(-2240.627), (-3152.420959555), (-3063.4127388959), 1.2958646899018938E-9, 2489.8232735}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, -0.0, -0.0}, doubleArray2, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      long long0 = MathUtils.pow((long) 4, 4);
      assertEquals(256L, long0);
      
      short short0 = MathUtils.sign((short)140);
      assertEquals((short)1, short0);
      
      int int0 = MathUtils.hash(doubleArray2);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray2.length);
      assertArrayEquals(new double[] {(-2240.627), (-3152.420959555), (-3063.4127388959), 1.2958646899018938E-9, 2489.8232735}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, -0.0, -0.0}, doubleArray2, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertEquals(28629151, int0);
      
      double[] doubleArray3 = MathUtils.copyOf(doubleArray1, 231);
      assertNotSame(doubleArray0, doubleArray3);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray1, doubleArray2);
      assertNotSame(doubleArray1, doubleArray3);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray3, doubleArray1);
      assertNotSame(doubleArray3, doubleArray0);
      assertNotSame(doubleArray3, doubleArray2);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      assertEquals(231, doubleArray3.length);
      assertNotNull(doubleArray3);
      assertArrayEquals(new double[] {(-2240.627), (-3152.420959555), (-3063.4127388959), 1.2958646899018938E-9, 2489.8232735}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-2240.627), (-3152.420959555), (-3063.4127388959), 1.2958646899018938E-9, 2489.8232735}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray1.equals((Object)doubleArray2));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray1));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      
      double double1 = MathUtils.binomialCoefficientLog(761, (-507));
      assertEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(0, 4771);
      assertEquals(0, int0);
      
      int int1 = MathUtils.addAndCheck(31, 154);
      assertFalse(int1 == int0);
      assertEquals(185, int1);
      
      int int2 = MathUtils.lcm(0, 4182);
      assertFalse(int2 == int1);
      assertTrue(int2 == int0);
      assertEquals(0, int2);
      
      long long0 = MathUtils.binomialCoefficient(1805, 0);
      assertEquals(1L, long0);
      
      int int3 = MathUtils.pow(4771, 0);
      assertFalse(int3 == int1);
      assertFalse(int3 == int2);
      assertFalse(int3 == int0);
      assertEquals(1, int3);
      
      boolean boolean0 = MathUtils.equals((double) 1L, (double) 0, (double) 1);
      assertTrue(boolean0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-604L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-604)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      int int0 = MathUtils.compareTo((-1312.6644), (-1312.6644), (-1312.6644));
      assertEquals(0, int0);
      
      long long0 = 5002L;
      long long1 = MathUtils.lcm(5002L, (-909L));
      assertFalse(long1 == long0);
      assertEquals(4546818L, long1);
      
      long long2 = MathUtils.mulAndCheck(4546818L, (-726L));
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      assertEquals((-3300989868L), long2);
      
      double double0 = MathUtils.factorialDouble(0);
      assertEquals(1.0, double0, 0.01);
      
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      int int1 = 0;
      long long3 = MathUtils.factorial(0);
      assertFalse(long3 == long0);
      assertFalse(long3 == long2);
      assertFalse(long3 == long1);
      assertEquals(1L, long3);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      long long0 = (-975L);
      long long1 = MathUtils.addAndCheck((-975L), (-975L));
      assertFalse(long1 == long0);
      assertEquals((-1950L), long1);
      
      short short0 = (short)2124;
      short short1 = MathUtils.indicator((short)2124);
      assertFalse(short1 == short0);
      assertEquals((short)1, short1);
      
      // Undeclared exception!
      try { 
        MathUtils.distance1((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      long long0 = MathUtils.indicator(1429L);
      assertEquals(1L, long0);
      
      long long1 = MathUtils.sign(1L);
      assertTrue(long1 == long0);
      assertEquals(1L, long1);
      
      float float0 = MathUtils.sign((float) 1L);
      assertEquals(1.0F, float0, 0.01F);
      
      boolean boolean0 = MathUtils.equals(1.986821492305628E-8, (-866.8698470955), (double) 1L);
      assertFalse(boolean0);
      
      long long2 = MathUtils.addAndCheck(0L, 0L);
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      assertEquals(0L, long2);
      
      float[] floatArray0 = new float[8];
      floatArray0[0] = (float) 0L;
      floatArray0[1] = (float) 0L;
      floatArray0[2] = (float) 0L;
      floatArray0[3] = (float) 1L;
      floatArray0[4] = (float) 1L;
      floatArray0[5] = (float) 1L;
      floatArray0[6] = (float) 1L;
      floatArray0[7] = (-1466.8252F);
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(8, floatArray0.length);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 1.0F, 1.0F, 1.0F, 1.0F, (-1466.8252F)}, floatArray0, 0.01F);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      double double0 = MathUtils.binomialCoefficientLog(13, 13);
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 1.0F;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (double) 13;
      doubleArray0[3] = (double) 0L;
      doubleArray0[4] = 0.625;
      MathUtils.checkFinite(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {1.0, 0.0, 13.0, 0.0, 0.625}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(0L, 2147483647L);
      assertEquals(2147483647L, long0);
      
      double double0 = MathUtils.round((double) 2147483647L, 0, 0);
      assertEquals(2.147483647E9, double0, 0.01);
      
      int[] intArray0 = new int[3];
      intArray0[0] = 0;
      intArray0[1] = 0;
      intArray0[2] = 0;
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(3, intArray0.length);
      assertArrayEquals(new int[] {0, 0, 0}, intArray0);
      assertEquals(0, int0);
      
      double double1 = MathUtils.sinh(2.147483647E9);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 0L;
      doubleArray0[2] = (double) 0;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      int int1 = MathUtils.sign(0);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      
      boolean boolean1 = MathUtils.equals((-1485.0), 0.0, (-2250.082216));
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double double2 = MathUtils.sinh(0.5);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(0.5210953054937474, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      int int0 = MathUtils.pow(2718, (long) 2718);
      assertEquals(0, int0);
      
      long long0 = MathUtils.indicator((long) 0);
      assertEquals(1L, long0);
      
      int int1 = MathUtils.lcm(2718, 2718);
      assertFalse(int1 == int0);
      assertEquals(2718, int1);
      
      double double0 = MathUtils.round(1884.405, 0);
      assertEquals(1884.0, double0, 0.01);
      
      boolean boolean0 = MathUtils.equals((float) 2718, 0.0F);
      assertFalse(boolean0);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.NOT_POSITIVE_POISSON_MEAN;
      assertEquals("the Poisson mean must be positive ({0})", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.NOT_POSITIVE_POISSON_MEAN, localizedFormats0);
      
      long long1 = MathUtils.addAndCheck((long) 2718, (long) 2718);
      assertFalse(long1 == long0);
      assertEquals(5436L, long1);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 1L;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = 1884.405;
      doubleArray0[3] = 1884.0;
      doubleArray0[4] = (double) 2718;
      doubleArray0[5] = (double) 2718;
      doubleArray0[6] = (double) 0.0F;
      doubleArray0[7] = 1884.405;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not increasing (1 > 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      float float0 = MathUtils.sign((-2231.05F));
      assertEquals((-1.0F), float0, 0.01F);
      
      double double0 = MathUtils.sinh(638.38678591);
      assertEquals(8.847660405265854E276, double0, 0.01);
      
      long long0 = MathUtils.indicator((-912L));
      assertEquals((-1L), long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float[]) null, (float[]) null);
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (-3.141592653589793);
      doubleArray0[2] = 638.38678591;
      doubleArray0[3] = (double) (-1L);
      doubleArray0[4] = (double) (-1L);
      doubleArray0[5] = (double) (-912L);
      doubleArray0[6] = (double) (-1.0F);
      MathUtils.checkFinite(doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, (-3.141592653589793), 638.38678591, (-1.0), (-1.0), (-912.0), (-1.0)}, doubleArray0, 0.01);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-912L));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, (-3.141592653589793), 638.38678591, (-1.0), (-1.0), (-912.0), (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-10.241584526910158), 2081.139393195647, (-3.2599976051024444), (-3.2599976051024444), (-2973.1178158534294), (-3.2599976051024444)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) (-1L), (-1187.68F), 2729);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      short short0 = MathUtils.sign((short)750);
      assertEquals((short)1, short0);
      
      boolean boolean2 = MathUtils.equals((float[]) null, (float[]) null);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2);
      
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (0 >= -3.142)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 107.0;
      doubleArray0[1] = (-3.141592653589793);
      double[][] doubleArray1 = new double[8][1];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertEquals(2, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {(-3.141592653589793), 107.0}, doubleArray0, 0.01);
      
      long long0 = MathUtils.mulAndCheck(0L, 1115L);
      assertEquals(0L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1.4E-45F), 0.0F, (-1.0F));
      assertTrue(boolean0);
      
      double[] doubleArray2 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(2, doubleArray2.length);
      assertNotNull(doubleArray2);
      assertArrayEquals(new double[] {(-3.141592653589793), 107.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-3.141592653589793), 107.0}, doubleArray2, 0.01);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      int int0 = MathUtils.hash(doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {(-3.141592653589793), 107.0}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertEquals(900682558, int0);
      
      byte byte0 = MathUtils.sign((byte) (-127));
      assertEquals((byte) (-1), byte0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(1405.7, (-3.141592653589793), (double) (-1.4E-45F));
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {(-3.141592653589793), 107.0}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertEquals(107.04610971166159, double0, 0.01);
      
      int int1 = MathUtils.hash(doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {(-3.141592653589793), 107.0}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertTrue(int1 == int0);
      assertEquals(900682558, int1);
      
      MathUtils.checkNotNull((Object) doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {(-3.141592653589793), 107.0}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      
      int int2 = MathUtils.subAndCheck(585, (int) (byte) (-127));
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertEquals(712, int2);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      int int0 = MathUtils.lcm(0, 0);
      assertEquals(0, int0);
      
      int int1 = MathUtils.sign(0);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = 0.10526403784751892;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.10526403784751892}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 783.06403416;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.19999998807907104;
      doubleArray0[3] = (-59.9378561348);
      doubleArray0[4] = Double.NaN;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = (-1004.0610062078007);
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {783.06403416, 0.0, 0.19999998807907104, (-59.9378561348), Double.NaN, 0.0, (-1004.0610062078007)}, doubleArray0, 0.01);
      assertEquals(Double.NaN, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-2739), (-2739));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -2,739
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-690L), 2383L);
      assertEquals((-1644270L), long0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      int[] intArray0 = new int[9];
      intArray0[0] = 783;
      intArray0[1] = (-710);
      intArray0[2] = 0;
      intArray0[3] = (-3528);
      intArray0[4] = (-3370);
      intArray0[5] = 0;
      intArray0[6] = 2900;
      intArray0[7] = 15;
      intArray0[8] = (-2096);
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(9, intArray0.length);
      assertArrayEquals(new int[] {783, (-710), 0, (-3528), (-3370), 0, 2900, 15, (-2096)}, intArray0);
      assertEquals(0, int0);
      
      double double0 = MathUtils.round(0.0875862700108075, 2);
      assertEquals(0.09, double0, 0.01);
      
      int int1 = MathUtils.mulAndCheck(2104, (-3528));
      assertFalse(int1 == int0);
      assertEquals((-7422912), int1);
      
      int int2 = MathUtils.pow((-3370), (long) 783);
      assertFalse(int2 == int1);
      assertTrue(int2 == int0);
      assertEquals(0, int2);
      
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(1545, (-7422912));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      int[] intArray0 = new int[5];
      intArray0[0] = 56;
      intArray0[1] = (-3);
      intArray0[2] = 131;
      intArray0[3] = 897;
      intArray0[4] = 0;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(5, intArray0.length);
      assertArrayEquals(new int[] {56, (-3), 131, 897, 0}, intArray0);
      assertEquals(0, int0);
      
      int int1 = MathUtils.gcd(2491, 897);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) (-3);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {(-3.0)}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      double[] doubleArray1 = new double[8];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) (-3);
      doubleArray1[1] = (double) 0;
      doubleArray1[2] = (double) 2491;
      doubleArray1[3] = (double) 56;
      doubleArray1[4] = (double) 0;
      doubleArray1[5] = (double) 897;
      doubleArray1[6] = (double) 2491;
      doubleArray1[7] = (double) 897;
      int int2 = MathUtils.hash(doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {(-3.0), 0.0, 2491.0, 56.0, 0.0, 897.0, 2491.0, 897.0}, doubleArray1, 0.01);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals((-526380287), int2);
      
      double double0 = MathUtils.normalizeAngle((-839.6988926990576), 897);
      assertEquals(894.4602520825082, double0, 0.01);
      
      double double1 = MathUtils.binomialCoefficientLog(1, 0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      boolean boolean1 = MathUtils.equals(doubleArray1, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {(-3.0), 0.0, 2491.0, 56.0, 0.0, 897.0, 2491.0, 897.0}, doubleArray1, 0.01);
      assertTrue(boolean1 == boolean0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertTrue(boolean1);
      
      // Undeclared exception!
      try { 
        MathUtils.round(0.0F, (-9), 3312);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 3,312, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(3.4028235E38F, 1.0F, Float.NEGATIVE_INFINITY);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 1.0F;
      doubleArray0[1] = (double) Float.NEGATIVE_INFINITY;
      doubleArray0[2] = (-212.09586660773);
      doubleArray0[3] = (double) 1.0F;
      doubleArray0[4] = (double) 1.0F;
      doubleArray0[5] = (-342.1);
      doubleArray0[6] = (double) 1.0F;
      doubleArray0[7] = (double) 1.0F;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {1.0, Double.NEGATIVE_INFINITY, (-212.09586660773), 1.0, 1.0, (-342.1), 1.0, 1.0}, doubleArray0, 0.01);
      assertEquals((-329906207), int0);
      
      long long0 = MathUtils.lcm((-1612L), (-3365L));
      assertEquals(5424380L, long0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(1.0F, (float) (-3365L), 2425);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = BigInteger.valueOf(23L);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)23, bigInteger1.byteValue());
      assertEquals((short)23, bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, 0L);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertNotNull(bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      
      int[] intArray0 = new int[0];
      int[] intArray1 = new int[0];
      assertFalse(intArray1.equals((Object)intArray0));
      
      int int1 = MathUtils.distanceInf(intArray0, intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(0, intArray0.length);
      assertEquals(0, intArray1.length);
      assertArrayEquals(new int[] {}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      double double0 = MathUtils.distance(intArray1, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(0, intArray1.length);
      assertArrayEquals(new int[] {}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      double double0 = MathUtils.cosh(15.0);
      assertEquals(1634508.6862362083, double0, 0.01);
      
      int[] intArray0 = new int[3];
      intArray0[0] = 50;
      intArray0[1] = 937;
      intArray0[2] = (-743);
      double double1 = MathUtils.distance(intArray0, intArray0);
      assertEquals(3, intArray0.length);
      assertArrayEquals(new int[] {50, 937, (-743)}, intArray0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-743), 1431);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1,431, n = -743
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F, 0.0F);
      assertTrue(boolean0);
      
      float float0 = MathUtils.sign(Float.NaN);
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long0 = MathUtils.mulAndCheck((-594L), (-594L));
      assertEquals(352836L, long0);
      
      double double0 = MathUtils.binomialCoefficientDouble(31, 31);
      assertEquals(1.0, double0, 0.01);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 0.0F;
      doubleArray0[1] = (double) 352836L;
      doubleArray0[2] = (double) 0.0F;
      doubleArray0[3] = (double) Float.NaN;
      doubleArray0[4] = (double) 0.0F;
      double[] doubleArray1 = new double[4];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) 0.0F;
      doubleArray1[1] = (double) (-594L);
      doubleArray1[2] = (-2408.20960077197);
      doubleArray1[3] = (double) (-594L);
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 352836.0, 0.0, Double.NaN, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-594.0), (-2408.20960077197), (-594.0)}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      boolean boolean2 = MathUtils.equals(225.0802F, 0.0F);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
      
      short short0 = MathUtils.indicator((short) (-1245));
      assertEquals((short) (-1), short0);
      
      double double1 = MathUtils.factorialLog(31);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(78.09222355331532, double1, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.distance(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 729.29528;
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 729.29528}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray1 = new double[0];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 729.29528}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
      
      long long0 = MathUtils.lcm((-2470L), 10L);
      assertEquals(2470L, long0);
      
      // Undeclared exception!
      try { 
        MathUtils.distance(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-776.0F), (-776.0F), 5728.82F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, 0.0, 680);
      assertTrue(boolean0);
      
      float float0 = (-1802.41F);
      boolean boolean1 = MathUtils.equals((-1802.41F), (float) 680, 680);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      long long0 = MathUtils.pow(3002L, 0L);
      assertEquals(1L, long0);
      
      byte[] byteArray0 = new byte[4];
      byte byte0 = (byte)40;
      byteArray0[0] = (byte)40;
      byteArray0[1] = (byte)0;
      byteArray0[2] = (byte)67;
      byteArray0[3] = (byte) (-6);
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(4, byteArray0.length);
      assertEquals((short)17402, bigInteger0.shortValue());
      assertEquals((byte) (-6), bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte)40, (byte)0, (byte)67, (byte) (-6)}, byteArray0);
      
      BigInteger bigInteger1 = new BigInteger(byteArray0);
      assertEquals(4, byteArray0.length);
      assertEquals((byte) (-6), bigInteger1.byteValue());
      assertEquals((short)17402, bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertArrayEquals(new byte[] {(byte)40, (byte)0, (byte)67, (byte) (-6)}, byteArray0);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      
      BigInteger bigInteger2 = bigInteger0.divide(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertEquals(4, byteArray0.length);
      assertEquals((short)17402, bigInteger0.shortValue());
      assertEquals((byte) (-6), bigInteger0.byteValue());
      assertEquals((byte) (-6), bigInteger1.byteValue());
      assertEquals((short)17402, bigInteger1.shortValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertNotNull(bigInteger2);
      assertArrayEquals(new byte[] {(byte)40, (byte)0, (byte)67, (byte) (-6)}, byteArray0);
      assertTrue(bigInteger0.equals((Object)bigInteger1));
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-961L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-961)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1497.269164096;
      doubleArray0[1] = 10.0;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {1497.269164096, 10.0}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equals(10.0, 10.0, 1497.269164096);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      int int0 = MathUtils.gcd(13, 13);
      assertEquals(13, int0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(Double.NaN, 10.0, 10.0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
      
      boolean boolean3 = MathUtils.equals((float) 13, (float) 13, 13);
      assertTrue(boolean3 == boolean0);
      assertFalse(boolean3 == boolean2);
      assertTrue(boolean3 == boolean1);
      assertTrue(boolean3);
      
      int int1 = MathUtils.compareTo(Double.NaN, 13, Double.NaN);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      boolean boolean4 = MathUtils.equals((-1252.358348660933), (double) 1, (double) 1);
      assertTrue(boolean4 == boolean2);
      assertFalse(boolean4 == boolean0);
      assertFalse(boolean4 == boolean3);
      assertFalse(boolean4 == boolean1);
      assertFalse(boolean4);
      
      int[] intArray0 = new int[7];
      intArray0[0] = 1;
      intArray0[1] = 13;
      intArray0[2] = 13;
      intArray0[3] = 1;
      intArray0[4] = 13;
      intArray0[5] = 0;
      intArray0[6] = 1;
      int int2 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(7, intArray0.length);
      assertArrayEquals(new int[] {1, 13, 13, 1, 13, 0, 1}, intArray0);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(0, int2);
      
      double double0 = MathUtils.log(642.4327146891254, 6.123233995736766E-17);
      assertEquals((-5.7742216310073236), double0, 0.01);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 6.123233995736766E-17);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {1497.269164096, 10.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {6.082609307448863E-17, 4.06246882879027E-19}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      long long0 = MathUtils.mulAndCheck(39916800L, 1544L);
      assertEquals(61631539200L, long0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 0.2222222089767456;
      double double0 = 1218.006974;
      doubleArray0[4] = 1218.006974;
      doubleArray0[5] = (-420.636779);
      double double1 = MathUtils.safeNorm(doubleArray0);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.2222222089767456, 1218.006974, (-420.636779)}, doubleArray0, 0.01);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1288.5947143857302, double1, 0.01);
      
      boolean boolean0 = MathUtils.equals((-717.0748019889901), (-0.1428571423679182), 1218.006974);
      assertTrue(boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-1387), 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 0, n = -1,387
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1497.269164096;
      doubleArray0[1] = 10.0;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {1497.269164096, 10.0}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equals(10.0, 10.0, 1497.269164096);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(Double.NaN, 10.0, 10.0);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2);
      
      int[] intArray0 = new int[7];
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(7, intArray0.length);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0}, intArray0);
      assertEquals(0, int0);
      
      double double0 = MathUtils.log(642.4327146891254, 6.123233995736766E-17);
      assertEquals((-5.7742216310073236), double0, 0.01);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 6.123233995736766E-17);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {1497.269164096, 10.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {6.082609307448863E-17, 4.06246882879027E-19}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      long long0 = MathUtils.mulAndCheck(39916800L, 1544L);
      assertEquals(61631539200L, long0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 1.5;
      doubleArray0[1] = (-1.0);
      doubleArray0[2] = 2.384185791015625E-7;
      doubleArray0[3] = 0.07490822288864472;
      doubleArray0[4] = 3.834E-20;
      doubleArray0[5] = 443.666163313018;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, false);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {1.5, (-1.0), 2.384185791015625E-7, 0.07490822288864472, 3.834E-20, 443.666163313018}, doubleArray0, 0.01);
      assertFalse(boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.round(0.0F, (-1192), (-1192));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method -1,192, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      int int0 = MathUtils.addAndCheck((-2792), 0);
      assertEquals((-2792), int0);
      
      byte[] byteArray0 = new byte[9];
      byteArray0[0] = (byte)0;
      byteArray0[1] = (byte)0;
      byteArray0[2] = (byte)0;
      byteArray0[3] = (byte)28;
      byteArray0[4] = (byte) (-115);
      byteArray0[5] = (byte) (-119);
      byteArray0[6] = (byte) (-55);
      byteArray0[7] = (byte)0;
      byteArray0[8] = (byte)0;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(9, byteArray0.length);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)28, (byte) (-115), (byte) (-119), (byte) (-55), (byte)0, (byte)0}, byteArray0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-753));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-753)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      
      float float0 = MathUtils.indicator(0.0F);
      assertEquals(1.0F, float0, 0.01F);
      
      int int0 = (-1928);
      int int1 = 12;
      // Undeclared exception!
      try { 
        MathUtils.round(224.520340827005, (-1928), 12);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-969L), (-14L));
      assertEquals(13566L, long0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow((-997), (-997));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-997)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(0, 0);
      assertEquals(1.0, double0, 0.01);
      
      int int0 = MathUtils.pow(5, (long) 5);
      assertEquals(3125, int0);
      
      MathUtils.checkFinite(1.0);
      int int1 = MathUtils.mulAndCheck(0, 5);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      float float0 = MathUtils.sign((float) 3125);
      assertEquals(1.0F, float0, 0.01F);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 3125;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = 1.0;
      doubleArray0[3] = (double) 1.0F;
      double[] doubleArray1 = new double[0];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      float float0 = 1246.789F;
      float float1 = MathUtils.indicator(1246.789F);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(1.0F, float1, 0.01F);
      
      long long0 = MathUtils.gcd(1808L, 1290L);
      assertEquals(2L, long0);
      
      double[] doubleArray0 = new double[0];
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(1, int0);
      
      byte byte0 = (byte)0;
      byte byte1 = MathUtils.indicator((byte)0);
      assertFalse(byte1 == byte0);
      assertEquals((byte)1, byte1);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(1041.636468698, (double) 1, (int) (byte)0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      float float0 = MathUtils.indicator((-1.0F));
      assertEquals((-1.0F), float0, 0.01F);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 1119.0;
      doubleArray0[1] = Double.NEGATIVE_INFINITY;
      doubleArray0[2] = (double) (-1.0F);
      doubleArray0[3] = (double) (-1.0F);
      doubleArray0[4] = (double) (-1.0F);
      doubleArray0[5] = (double) (-1.0F);
      doubleArray0[6] = (double) (-1.0F);
      doubleArray0[7] = (double) (-1.0F);
      double[][] doubleArray1 = new double[4][4];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertEquals(8, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      assertArrayEquals(new double[] {Double.NEGATIVE_INFINITY, (-1.0), (-1.0), (-1.0), (-1.0), (-1.0), (-1.0), 1119.0}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // value -\u221E at index 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)10);
      assertEquals((byte)1, byte0);
      
      double double0 = MathUtils.round((double) (byte)10, 633, 0);
      assertEquals(10.0, double0, 0.01);
      
      double double1 = MathUtils.sinh((byte)1);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.1752011936438014, double1, 0.01);
      
      double double2 = MathUtils.sinh(0.0);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = 1354.486202;
      doubleArray0[2] = (double) (byte)10;
      double[] doubleArray1 = new double[9];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) (byte)10;
      doubleArray1[1] = 0.0;
      doubleArray1[2] = 8.251545029714408E-9;
      doubleArray1[3] = 1.1752011936438014;
      doubleArray1[4] = (double) (byte)1;
      doubleArray1[5] = 1354.486202;
      doubleArray1[6] = (double) 633;
      doubleArray1[7] = (double) 633;
      doubleArray1[8] = 1354.486202;
      double double3 = MathUtils.distance1(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 1354.486202, 10.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {10.0, 0.0, 8.251545029714408E-9, 1.1752011936438014, 1.0, 1354.486202, 633.0, 633.0, 1354.486202}, doubleArray1, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(1374.4862019917484, double3, 0.01);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(618L, 0L);
      assertEquals(0L, long0);
      
      int int0 = MathUtils.pow((-1044), 0L);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-2669.6704F), 1.0F);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 1.0F;
      doubleArray0[1] = (double) (-2669.6704F);
      doubleArray0[2] = (double) (-2669.6704F);
      doubleArray0[3] = 1901.23986;
      doubleArray0[4] = (double) (-2669.6704F);
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {1.0, (-2669.67041015625), (-2669.67041015625), 1901.23986, (-2669.67041015625)}, doubleArray0, 0.01);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      long long0 = MathUtils.indicator(0L);
      assertEquals(1L, long0);
      
      int int0 = MathUtils.compareTo((-1638.84763246), (-3256.0), 485.417367928);
      assertEquals(1, int0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      int int1 = MathUtils.hash(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {1.0, (-2669.67041015625), (-2669.67041015625), 1901.23986, (-2669.67041015625)}, doubleArray0, 0.01);
      assertFalse(int1 == int0);
      assertEquals(1890160074, int1);
      
      float float0 = MathUtils.round((float) 0L, 1);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      int int0 = (-2218);
      // Undeclared exception!
      try { 
        MathUtils.factorialLog((-2218));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -2,218
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      int int0 = 15;
      int int1 = MathUtils.subAndCheck(15, 15);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      double double0 = MathUtils.sign(1289.2434);
      assertEquals(1.0, double0, 0.01);
      
      boolean boolean0 = MathUtils.equals((float) 0, (float) 15, 15);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 1289.2434;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = 1289.2434;
      double[] doubleArray1 = new double[3];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 1289.2434;
      doubleArray1[1] = (double) 0;
      doubleArray1[2] = 1289.2434;
      double double1 = MathUtils.distance(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertArrayEquals(new double[] {1289.2434, 0.0, 1289.2434}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1289.2434, 0.0, 1289.2434}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean1 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {1289.2434, 0.0, 1289.2434}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray1, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (1,289.243 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      long long0 = MathUtils.gcd((-7278142539171889152L), (-7278142539171889152L));
      assertEquals(7278142539171889152L, long0);
      
      long long1 = MathUtils.gcd(7278142539171889152L, (-7278142539171889152L));
      assertTrue(long1 == long0);
      assertEquals(7278142539171889152L, long1);
      
      long long2 = MathUtils.pow((-1237L), 3394L);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals(2484127937774346297L, long2);
      
      boolean boolean0 = MathUtils.equals((double) 7278142539171889152L, (double) 7278142539171889152L);
      assertTrue(boolean0);
      
      float float0 = MathUtils.sign((float) 2484127937774346297L);
      assertEquals(1.0F, float0, 0.01F);
      
      int int0 = MathUtils.mulAndCheck(4169, 4169);
      assertEquals(17380561, int0);
      
      int int1 = MathUtils.compareTo(2.3841857910019882E-8, 539.317419, 3394L);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      long long3 = MathUtils.gcd(7278142539171889152L, 7278142539171889152L);
      assertTrue(long3 == long1);
      assertFalse(long3 == long2);
      assertTrue(long3 == long0);
      assertEquals(7278142539171889152L, long3);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      float[] floatArray0 = new float[0];
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(0, floatArray0.length);
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertTrue(boolean0);
      
      long long0 = MathUtils.lcm((-326L), (-1L));
      assertEquals(326L, long0);
      
      double double0 = MathUtils.factorialDouble(645);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      double double1 = MathUtils.round(0.0, 645);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      float float0 = MathUtils.round((float) (-1L), 645);
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.compareTo(2020.84300815, (-1L), (-2099.594856688901));
      assertEquals(1, int0);
      
      int int1 = MathUtils.subAndCheck((-3792), 1);
      assertFalse(int1 == int0);
      assertEquals((-3793), int1);
      
      float float1 = MathUtils.indicator(0.0F);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(1.0F, float1, 0.01F);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      double double2 = MathUtils.factorialLog(645);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(3531.8201472238347, double2, 0.01);
      
      long long1 = MathUtils.lcm((long) (-3792), (-1L));
      assertFalse(long1 == long0);
      assertEquals(3792L, long1);
      
      Pair<String, Object> pair0 = new Pair<String, Object>("", "");
      assertNotNull(pair0);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.MAX_ITERATIONS_EXCEEDED;
      assertEquals("maximal number of iterations ({0}) exceeded", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.MAX_ITERATIONS_EXCEEDED, localizedFormats0);
      
      MathUtils.checkNotNull((Object) pair0, (Localizable) localizedFormats0, (Object[]) mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertEquals("maximal number of iterations ({0}) exceeded", localizedFormats0.getSourceString());
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (-2099.594856688901);
      doubleArray0[1] = (double) 1.0F;
      doubleArray0[2] = (-2099.594856688901);
      doubleArray0[3] = (double) 1.0F;
      doubleArray0[4] = (double) (-326L);
      doubleArray0[5] = Double.POSITIVE_INFINITY;
      doubleArray0[6] = (double) (-3792);
      doubleArray0[7] = (double) 1;
      doubleArray0[8] = (double) 1;
      double double3 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {(-2099.594856688901), 1.0, (-2099.594856688901), 1.0, (-326.0), Double.POSITIVE_INFINITY, (-3792.0), 1.0, 1.0}, doubleArray0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertEquals(Double.NaN, double3, 0.01);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[9][1];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[5];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 1.7976931348623157E308;
      doubleArray2[1] = 0.0;
      doubleArray2[2] = (double) 1.0F;
      doubleArray2[3] = 0.0;
      doubleArray2[4] = (double) 0.0F;
      doubleArray1[7] = doubleArray2;
      doubleArray1[8] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 5 != 9
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      long long0 = MathUtils.lcm((-1032L), (-1032L));
      assertEquals(1032L, long0);
      
      double double0 = MathUtils.normalizeAngle((-1032L), (-1032L));
      assertEquals((-1032.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(851.81547322, 851.81547322, 851.81547322);
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = 851.81547322;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 851.81547322;
      doubleArray0[3] = 851.81547322;
      doubleArray0[4] = 851.81547322;
      doubleArray0[5] = 851.81547322;
      doubleArray0[6] = 851.81547322;
      doubleArray0[7] = 851.81547322;
      doubleArray0[8] = 851.81547322;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {851.81547322, 0.0, 851.81547322, 851.81547322, 851.81547322, 851.81547322, 851.81547322, 851.81547322, 851.81547322}, doubleArray0, 0.01);
      assertEquals((-597981103), int0);
      
      int[] intArray0 = new int[6];
      intArray0[0] = (-597981103);
      intArray0[1] = (-597981103);
      intArray0[2] = (-597981103);
      intArray0[3] = (-597981103);
      intArray0[4] = (-597981103);
      intArray0[5] = (-597981103);
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(6, intArray0.length);
      assertArrayEquals(new int[] {(-597981103), (-597981103), (-597981103), (-597981103), (-597981103), (-597981103)}, intArray0);
      assertEquals(0.0, double0, 0.01);
      
      int int1 = MathUtils.indicator(0);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      int int2 = 0;
      int int3 = MathUtils.mulAndCheck(0, 0);
      assertFalse(int3 == int0);
      assertTrue(int3 == int2);
      assertFalse(int3 == int1);
      assertEquals(0, int3);
      
      long long0 = MathUtils.lcm((-1017L), (-169L));
      assertEquals(171873L, long0);
      
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (851.815 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      int int0 = 93;
      int int1 = 0;
      double double0 = MathUtils.binomialCoefficientDouble(93, 0);
      assertEquals(1.0, double0, 0.01);
      
      int int2 = 4365;
      int int3 = MathUtils.addAndCheck(0, 4365);
      assertFalse(int3 == int1);
      assertFalse(int3 == int0);
      assertTrue(int3 == int2);
      assertEquals(4365, int3);
      
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("w;q&:|*k\"$X+iTM");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.w;q&:|*k\"$X+iTM
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(3411.0, 0.0, 0.125);
      assertFalse(boolean0);
      
      int int0 = MathUtils.indicator(20);
      assertEquals(1, int0);
      
      double double0 = MathUtils.binomialCoefficientDouble(20, (-185));
      assertEquals(1.0, double0, 0.01);
      
      int[] intArray0 = new int[7];
      intArray0[0] = (-4458);
      intArray0[1] = 1;
      intArray0[2] = 20;
      intArray0[3] = (-185);
      intArray0[4] = 1;
      intArray0[5] = (-185);
      intArray0[6] = (-185);
      int[] intArray1 = new int[5];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = (-185);
      intArray1[1] = (-185);
      intArray1[2] = 20;
      intArray1[3] = 1702;
      intArray1[4] = 0;
      // Undeclared exception!
      try { 
        MathUtils.distance(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 5
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      int int0 = 0;
      int int1 = MathUtils.subAndCheck(0, 0);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      
      double double0 = 0.0;
      // Undeclared exception!
      try { 
        MathUtils.equals(0.0, (double) 0, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      float[] floatArray0 = new float[8];
      floatArray0[4] = (-3857.265F);
      floatArray0[6] = 4645.0F;
      float[] floatArray1 = new float[2];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = 4645.0F;
      floatArray1[1] = 4645.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertEquals(8, floatArray0.length);
      assertEquals(2, floatArray1.length);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, (-3857.265F), 0.0F, 4645.0F, 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {4645.0F, 4645.0F}, floatArray1, 0.01F);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 4645.0F;
      doubleArray0[1] = (double) 0.0F;
      double double0 = MathUtils.binomialCoefficientDouble(270, 0);
      assertEquals(1.0, double0, 0.01);
      
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {4645.0, 0.0}, doubleArray0, 0.01);
      assertEquals((-711819583), int0);
      
      int int1 = MathUtils.compareTo(4645.0F, 1203.509992755, 0.0);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(270, 30);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(2774L, 2774L);
      assertEquals(7695076L, long0);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 2.2250738585072014E-308;
      doubleArray0[1] = (double) 2774L;
      doubleArray0[2] = (double) 7695076L;
      doubleArray0[3] = (double) 7695076L;
      doubleArray0[4] = (double) 2774L;
      doubleArray0[5] = (double) 2774L;
      doubleArray0[6] = (double) 2774L;
      doubleArray0[7] = (double) 2774L;
      MathUtils.checkFinite(doubleArray0);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {2.2250738585072014E-308, 2774.0, 7695076.0, 7695076.0, 2774.0, 2774.0, 2774.0, 2774.0}, doubleArray0, 0.01);
      
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {2.2250738585072014E-308, 2774.0, 7695076.0, 7695076.0, 2774.0, 2774.0, 2774.0, 2774.0}, doubleArray0, 0.01);
      assertEquals(1.0882482610458516E7, double0, 0.01);
      
      Integer integer0 = new Integer(2700);
      assertNotNull(integer0);
      assertEquals(2700, (int)integer0);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.NOT_POSITIVE_SAMPLE_SIZE;
      assertEquals("sample size must be positive ({0})", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.NOT_POSITIVE_SAMPLE_SIZE, localizedFormats0);
      
      Object[] objectArray0 = new Object[0];
      MathUtils.checkNotNull((Object) integer0, (Localizable) localizedFormats0, objectArray0);
      assertEquals(0, objectArray0.length);
      assertEquals("sample size must be positive ({0})", localizedFormats0.getSourceString());
      
      double double1 = MathUtils.cosh(2.2250738585072014E-308);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 2774L, (float) 7695076L);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equals(0.0, 2774.0, 2700);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double double2 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {2.2250738585072014E-308, 2774.0, 7695076.0, 7695076.0, 2774.0, 2774.0, 2774.0, 2774.0}, doubleArray0, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(0.0, double2, 0.01);
      
      long long1 = MathUtils.pow(4453L, 2700);
      assertFalse(long1 == long0);
      assertEquals(6912357135829071569L, long1);
      
      long long2 = MathUtils.sign(757L);
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      assertEquals(1L, long2);
      
      int int0 = MathUtils.hash(2774.0);
      assertEquals(1084599296, int0);
      
      int int1 = MathUtils.addAndCheck(0, 2700);
      assertFalse(int1 == int0);
      assertEquals(2700, int1);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 6.283185307179586;
      doubleArray0[4] = 0.0;
      MathUtils.checkFinite(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 6.283185307179586, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      int int0 = MathUtils.subAndCheck((-2564), (-2564));
      assertEquals(0, int0);
      
      int int1 = MathUtils.mulAndCheck(0, (-2564));
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      MathUtils.checkFinite((double) 0);
      double[] doubleArray0 = new double[0];
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(134);
      assertEquals(1.9929427461617248E228, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.subAndCheck(245, Integer.MIN_VALUE);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in subtraction: 245 - -2,147,483,648
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-79.35), (-1282.219598));
      assertFalse(boolean0);
      
      short short0 = MathUtils.sign((short)5);
      assertEquals((short)1, short0);
      
      int int0 = MathUtils.hash((-1282.219598));
      assertEquals(517495281, int0);
      
      long long0 = MathUtils.pow((long) (short)1, (int) (short)5);
      assertEquals(1L, long0);
      
      short short1 = MathUtils.indicator((short) (-190));
      assertFalse(short1 == short0);
      assertEquals((short) (-1), short1);
      
      long long1 = MathUtils.addAndCheck((long) (short)1, (long) 517495281);
      assertFalse(long1 == long0);
      assertEquals(517495282L, long1);
      
      short short2 = MathUtils.indicator((short)0);
      assertTrue(short2 == short0);
      assertFalse(short2 == short1);
      assertEquals((short)1, short2);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) (short) (-1);
      doubleArray0[1] = (double) 1L;
      doubleArray0[2] = (double) 517495282L;
      doubleArray0[3] = (double) (short) (-190);
      int int1 = MathUtils.hash(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {(-1.0), 1.0, 5.17495282E8, (-190.0)}, doubleArray0, 0.01);
      assertFalse(int1 == int0);
      assertEquals((-2104358328), int1);
      
      long long2 = MathUtils.subAndCheck((long) (short)5, (long) (short) (-1));
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      assertEquals(6L, long2);
      
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {(-1.0), 1.0, 5.17495282E8, (-190.0)}, doubleArray0, 0.01);
      assertEquals(5.1749528200003487E8, double0, 0.01);
      
      Object[] objectArray0 = new Object[5];
      objectArray0[0] = (Object) 1.0;
      objectArray0[1] = (Object) (-1.0);
      objectArray0[2] = (Object) 1.0;
      objectArray0[3] = (Object) null;
      objectArray0[4] = (Object) 1.0;
      MathUtils.checkNotNull((Object) "iI1", (Localizable) null, objectArray0);
      assertEquals(5, objectArray0.length);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      short short0 = (short)173;
      short short1 = MathUtils.indicator((short)173);
      assertFalse(short1 == short0);
      assertEquals((short)1, short1);
      
      double[] doubleArray0 = new double[0];
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertTrue(boolean0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      BigInteger bigInteger2 = bigInteger0.gcd(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger0);
      assertSame(bigInteger2, bigInteger1);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertNotNull(bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, 1020L);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger3, bigInteger2);
      assertSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger1);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger3.shortValue());
      assertEquals((byte)0, bigInteger3.byteValue());
      assertNotNull(bigInteger3);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      
      // Undeclared exception!
      try { 
        bigInteger0.divide(bigInteger3);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // BigInteger divide by zero
         //
         verifyException("java.math.MutableBigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-3.4028235E38F), (-309.072F), (-309.072F));
      assertFalse(boolean0);
      
      long long0 = 0L;
      int int0 = 204;
      long long1 = MathUtils.pow(0L, 204);
      assertTrue(long1 == long0);
      assertEquals(0L, long1);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 204;
      doubleArray0[1] = (double) (-309.072F);
      doubleArray0[2] = (double) (-309.072F);
      doubleArray0[3] = (double) 0L;
      doubleArray0[4] = (double) 0L;
      doubleArray0[5] = (double) 0L;
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {204.0, (-309.0719909667969), (-309.0719909667969), 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      short short0 = (short)0;
      short short1 = MathUtils.sign((short)0);
      assertTrue(short1 == short0);
      assertEquals((short)0, short1);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      BigInteger bigInteger2 = bigInteger1.clearBit(0);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertEquals((byte)0, bigInteger2.byteValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertNotNull(bigInteger2);
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertTrue(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      // Undeclared exception!
      try { 
        bigInteger1.setBit((-5790));
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // Negative bit address
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      int int0 = MathUtils.compareTo(0.0, 3491.699148871, 3491.699148871);
      assertEquals(0, int0);
      
      float float0 = MathUtils.indicator(677.316F);
      assertEquals(1.0F, float0, 0.01F);
      
      int int1 = 66;
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog(0, 66);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 66, n = 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      int int0 = (-1536);
      // Undeclared exception!
      try { 
        MathUtils.factorialDouble((-1536));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1,536
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-3.4028235E38F), (-309.072F), (-309.072F));
      assertFalse(boolean0);
      
      long long0 = MathUtils.pow(0L, 204);
      assertEquals(0L, long0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 204;
      doubleArray0[1] = (double) (-309.072F);
      doubleArray0[2] = (double) (-309.072F);
      doubleArray0[3] = (double) 0L;
      doubleArray0[4] = (double) 0L;
      doubleArray0[5] = (double) 0L;
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {204.0, (-309.0719909667969), (-309.0719909667969), 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      short short0 = MathUtils.sign((short)0);
      assertEquals((short)0, short0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      // Undeclared exception!
      try { 
        bigInteger1.setBit((-5790));
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // Negative bit address
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      int int0 = (-1354);
      // Undeclared exception!
      try { 
        MathUtils.factorialDouble((-1354));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1,354
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      
      long long0 = MathUtils.lcm(0L, 0L);
      assertEquals(0L, long0);
      
      float float0 = MathUtils.sign(3.4028235E38F);
      assertEquals(1.0F, float0, 0.01F);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      int[] intArray0 = new int[1];
      intArray0[0] = 157;
      int[] intArray1 = MathUtils.copyOf(intArray0, 157);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(1, intArray0.length);
      assertEquals(157, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {157}, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      
      short short0 = MathUtils.indicator((short) (-472));
      assertEquals((short) (-1), short0);
      
      long long0 = MathUtils.pow((long) 157, 4607182418800017408L);
      assertEquals(4521614025879977985L, long0);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 4607182418800017408L;
      doubleArray0[1] = (double) 157;
      doubleArray0[2] = (double) 157;
      doubleArray0[3] = (double) 157;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 3);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {4.6071824188000174E18, 157.0, 157.0, 157.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {4.6071824188000174E18, 157.0, 157.0}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      long long1 = MathUtils.sign((long) 157);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      short short1 = (short)4;
      short short2 = MathUtils.indicator((short)4);
      assertFalse(short2 == short0);
      assertFalse(short2 == short1);
      assertEquals((short)1, short2);
      
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {4.6071824188000174E18, 157.0, 157.0, 157.0}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertEquals(4.6071824188000174E18, double0, 0.01);
      
      double[] doubleArray2 = MathUtils.copyOf(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray1, doubleArray2);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertEquals(3, doubleArray2.length);
      assertNotNull(doubleArray2);
      assertArrayEquals(new double[] {4.6071824188000174E18, 157.0, 157.0, 157.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {4.6071824188000174E18, 157.0, 157.0}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {4.6071824188000174E18, 157.0, 157.0}, doubleArray2, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      double double1 = MathUtils.factorialLog(17);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(33.50507345013689, double1, 0.01);
      
      int int0 = (-318);
      // Undeclared exception!
      try { 
        MathUtils.pow((long) (short)4, (-318));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-318)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(840.95363614, 840.95363614, (-4631.15173376));
      assertTrue(boolean0);
      
      double double0 = MathUtils.cosh(0.0);
      assertEquals(1.0, double0, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      boolean boolean1 = bigInteger0.isProbablePrime((-2391));
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 19);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (-4631.15173376);
      doubleArray0[2] = (-4631.15173376);
      doubleArray0[3] = (-4631.15173376);
      doubleArray0[4] = 1356.62;
      doubleArray0[5] = 0.0;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-300.4));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, (-4631.15173376), (-4631.15173376), (-4631.15173376), 1356.62, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-110.9688337196507), (-110.9688337196507), (-110.9688337196507), 32.50650115895211, 0.0}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.pow(640L, (-177));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-177)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
      
      int int0 = 0;
      // Undeclared exception!
      try { 
        MathUtils.equals(0.0, 2955.18404, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 1.2393345855018391E-8;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 551.0;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not increasing (0 > 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      int int0 = 18;
      double double0 = MathUtils.binomialCoefficientDouble(18, 18);
      assertEquals(1.0, double0, 0.01);
      
      float float0 = MathUtils.round((-2190.4836F), 18);
      assertEquals((-2190.4836F), float0, 0.01F);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      byte byte0 = bigInteger0.byteValueExact();
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)0, byte0);
      
      // Undeclared exception!
      try { 
        bigInteger0.modPow((BigInteger) null, (BigInteger) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      long long0 = (-770L);
      long long1 = 0L;
      long long2 = MathUtils.addAndCheck((-770L), 0L);
      assertTrue(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals((-770L), long2);
      
      // Undeclared exception!
      try { 
        MathUtils.distance1((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(0L, 897L);
      assertEquals((-897L), long0);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 897L;
      doubleArray0[1] = (double) (-897L);
      doubleArray0[2] = (double) 897L;
      doubleArray0[3] = (double) 897L;
      doubleArray0[4] = 507.4326808;
      doubleArray0[5] = (-1.0);
      doubleArray0[6] = (double) (-897L);
      doubleArray0[7] = (double) 0L;
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, Double.POSITIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // Cannot normalize to an infinite value
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-541.290067777828), (-541.290067777828), 252);
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 359.19832589;
      doubleArray0[1] = (-1744.1328343966265);
      doubleArray0[2] = (-541.290067777828);
      doubleArray0[3] = (double) 252;
      doubleArray0[4] = (-1738.7276152);
      doubleArray0[5] = (double) 252;
      doubleArray0[6] = (-541.290067777828);
      boolean boolean1 = MathUtils.equals(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {359.19832589, (-1744.1328343966265), (-541.290067777828), 252.0, (-1738.7276152), 252.0, (-541.290067777828)}, doubleArray0, 0.01);
      assertTrue(boolean1 == boolean0);
      assertTrue(boolean1);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      int[] intArray0 = new int[1];
      intArray0[0] = 252;
      int[] intArray1 = MathUtils.copyOf(intArray0, 4238);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(1, intArray0.length);
      assertEquals(4238, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {252}, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((-398.280872895371), 0.0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2);
      
      int[] intArray2 = new int[8];
      assertFalse(intArray2.equals((Object)intArray0));
      assertFalse(intArray2.equals((Object)intArray1));
      
      intArray2[0] = 194;
      intArray2[1] = 4238;
      intArray2[2] = (-1675);
      intArray2[3] = 4238;
      intArray2[4] = 0;
      intArray2[5] = 252;
      intArray2[6] = (-1004);
      intArray2[7] = 252;
      // Undeclared exception!
      try { 
        MathUtils.distance(intArray2, intArray0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = 1.1102230246251565E-16;
      doubleArray0[1] = 4.503599627370496E15;
      doubleArray0[2] = (-3473.6);
      doubleArray0[3] = 0.0;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly increasing (4,503,599,627,370,496 >= -3,473.6)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1180.178878), (-1180.178878), 31);
      assertTrue(boolean0);
      
      byte[] byteArray0 = new byte[4];
      byteArray0[0] = (byte)0;
      byteArray0[1] = (byte) (-72);
      byteArray0[2] = (byte)30;
      byteArray0[3] = (byte)0;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals(4, byteArray0.length);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)7680, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      assertArrayEquals(new byte[] {(byte)0, (byte) (-72), (byte)30, (byte)0}, byteArray0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (int) (byte)0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals(4, byteArray0.length);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)7680, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertArrayEquals(new byte[] {(byte)0, (byte) (-72), (byte)30, (byte)0}, byteArray0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      int int0 = 919;
      int int1 = MathUtils.lcm(919, 919);
      assertTrue(int1 == int0);
      assertEquals(919, int1);
      
      double double0 = MathUtils.factorialDouble(919);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      long long0 = 6722166367014452318L;
      long long1 = MathUtils.pow(6722166367014452318L, 0L);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-962.7), (-2552.43), (double) 919);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) 919, (float) 6722166367014452318L, (float) 919);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      Integer integer0 = new Integer(919);
      assertNotNull(integer0);
      assertTrue(integer0.equals((Object)int0));
      assertTrue(integer0.equals((Object)int1));
      assertEquals(919, (int)integer0);
      
      Integer integer1 = new Integer(919);
      assertNotNull(integer1);
      assertTrue(integer1.equals((Object)int1));
      assertTrue(integer1.equals((Object)integer0));
      assertTrue(integer1.equals((Object)int0));
      assertEquals(919, (int)integer1);
      
      Pair<Integer, Object> pair0 = new Pair<Integer, Object>(integer0, integer1);
      assertNotNull(pair0);
      assertTrue(integer0.equals((Object)integer1));
      assertTrue(integer0.equals((Object)int0));
      assertTrue(integer0.equals((Object)int1));
      assertTrue(integer1.equals((Object)int1));
      assertTrue(integer1.equals((Object)integer0));
      assertTrue(integer1.equals((Object)int0));
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.INVALID_INTERVAL_INITIAL_VALUE_PARAMETERS;
      assertEquals("invalid interval, initial value parameters:  lower={0}, initial={1}, upper={2}", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.INVALID_INTERVAL_INITIAL_VALUE_PARAMETERS, localizedFormats0);
      
      Locale locale0 = new Locale("<pef$zN", "<pef$zN", "ROOTS_OF_UNITY_NOT_COMPUTED_YET");
      assertEquals("ROOTS_OF_UNITY_NOT_COMPUTED_YET", locale0.getVariant());
      assertEquals("<pef$zn", locale0.getLanguage());
      assertEquals("<PEF$ZN", locale0.getCountry());
      assertEquals("<pef$zn_<PEF$ZN_ROOTS_OF_UNITY_NOT_COMPUTED_YET", locale0.toString());
      assertNotNull(locale0);
      
      String string0 = localizedFormats0.getLocalizedString(locale0);
      assertEquals("invalid interval, initial value parameters:  lower={0}, initial={1}, upper={2}", localizedFormats0.getSourceString());
      assertEquals("ROOTS_OF_UNITY_NOT_COMPUTED_YET", locale0.getVariant());
      assertEquals("<pef$zn", locale0.getLanguage());
      assertEquals("<PEF$ZN", locale0.getCountry());
      assertEquals("<pef$zn_<PEF$ZN_ROOTS_OF_UNITY_NOT_COMPUTED_YET", locale0.toString());
      assertNotNull(string0);
      assertEquals("invalid interval, initial value parameters:  lower={0}, initial={1}, upper={2}", string0);
      
      Object[] objectArray0 = new Object[0];
      MathUtils.checkNotNull((Object) pair0, (Localizable) localizedFormats0, objectArray0);
      assertEquals(0, objectArray0.length);
      assertEquals("invalid interval, initial value parameters:  lower={0}, initial={1}, upper={2}", localizedFormats0.getSourceString());
      assertTrue(integer0.equals((Object)integer1));
      assertTrue(integer0.equals((Object)int0));
      assertTrue(integer0.equals((Object)int1));
      assertTrue(integer1.equals((Object)int1));
      assertTrue(integer1.equals((Object)integer0));
      assertTrue(integer1.equals((Object)int0));
      
      double double1 = MathUtils.sign(Double.POSITIVE_INFINITY);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      long long2 = 3952090531849364496L;
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(3952090531849364496L, 1307674368000L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1114.8821), (-1153.06772167073));
      assertFalse(boolean0);
      
      float float0 = MathUtils.round((-1.0F), 918, 0);
      assertEquals(Float.NaN, float0, 0.01F);
      
      int int0 = MathUtils.compareTo(693.7, 0.0, 0);
      assertEquals(1, int0);
      
      double double0 = MathUtils.indicator((-1153.06772167073));
      assertEquals((-1.0), double0, 0.01);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 918;
      doubleArray0[2] = (double) (-1.0F);
      doubleArray0[3] = 693.7;
      doubleArray0[4] = 693.7;
      doubleArray0[5] = 693.7;
      doubleArray0[6] = (double) (-1.0F);
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      boolean boolean2 = MathUtils.equals((-1.0), 0.0);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 693.7);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {0.0, 918.0, (-1.0), 693.7, 693.7, 693.7, (-1.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 212.47759500850822, (-0.2314570751726669), 160.56177304727905, 160.56177304727905, 160.56177304727905, (-0.2314570751726669)}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      float float1 = MathUtils.round(5757.0F, 1, 1);
      assertNotEquals(float1, float0, 0.01F);
      assertEquals(5756.9F, float1, 0.01F);
      
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      doubleArray2[0] = (-1.0);
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = (double) 5757.0F;
      doubleArray2[3] = 693.7;
      doubleArray2[4] = (double) 0;
      doubleArray2[5] = 693.7;
      doubleArray2[6] = 0.0;
      doubleArray2[7] = (double) (-1.0F);
      double double2 = MathUtils.safeNorm(doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray1);
      assertEquals(8, doubleArray2.length);
      assertArrayEquals(new double[] {(-1.0), Double.NaN, 5757.0, 693.7, 0.0, 693.7, 0.0, (-1.0)}, doubleArray2, 0.01);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(Double.NaN, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      float float0 = MathUtils.indicator(0.0F);
      assertEquals(1.0F, float0, 0.01F);
      
      float[] floatArray0 = new float[8];
      floatArray0[0] = 0.0F;
      floatArray0[1] = 1.0F;
      floatArray0[2] = 0.0F;
      floatArray0[3] = 0.0F;
      floatArray0[4] = 1.0F;
      floatArray0[5] = 0.0F;
      floatArray0[6] = 1.0F;
      floatArray0[7] = 1.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(8, floatArray0.length);
      assertArrayEquals(new float[] {0.0F, 1.0F, 0.0F, 0.0F, 1.0F, 0.0F, 1.0F, 1.0F}, floatArray0, 0.01F);
      assertTrue(boolean0);
      
      int[] intArray0 = new int[0];
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, intArray0.length);
      assertArrayEquals(new int[] {}, intArray0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      int int0 = MathUtils.addAndCheck(0, 0);
      assertEquals(0, int0);
      
      long long0 = MathUtils.sign((long) 0);
      assertEquals(0L, long0);
      
      Integer integer0 = new Integer(0);
      assertNotNull(integer0);
      assertTrue(integer0.equals((Object)int0));
      assertEquals(0, (int)integer0);
      
      MathUtils.checkNotNull((Object) integer0);
      assertTrue(integer0.equals((Object)int0));
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.0;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-851.795372466);
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 9.140260083262505E-9);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {(-851.795372466)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {9.140260083262505E-9}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-851.795372466), (-851.795372466), 9.140260083262505E-9);
      assertTrue(boolean0);
      
      int int0 = MathUtils.pow(0, 0);
      assertEquals(1, int0);
      
      long long0 = MathUtils.pow((long) 0, 1);
      assertEquals(0L, long0);
      
      double double0 = MathUtils.cosh(452.860436185377);
      assertEquals(2.364604501108433E196, double0, 0.01);
      
      boolean boolean1 = MathUtils.equals((float) 1, (-774.10065F));
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((double) 0, 3049.51285);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
      
      int[] intArray0 = new int[6];
      intArray0[0] = 0;
      intArray0[1] = 1;
      intArray0[2] = 0;
      intArray0[3] = 6;
      intArray0[4] = 0;
      intArray0[5] = (-2553);
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray0, intArray1);
      assertEquals(6, intArray1.length);
      assertEquals(6, intArray0.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {0, 1, 0, 6, 0, (-2553)}, intArray1);
      assertArrayEquals(new int[] {0, 1, 0, 6, 0, (-2553)}, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-2662.2696718);
      doubleArray0[1] = 0.008333333333333333;
      doubleArray0[2] = (-224.03054);
      doubleArray0[3] = 3465.0565122193;
      doubleArray0[4] = 3.834E-20;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {(-2662.2696718), 0.008333333333333333, (-224.03054), 3465.0565122193, 3.834E-20}, doubleArray0, 0.01);
      assertEquals((-659842278), int0);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      MockRandom mockRandom0 = new MockRandom(0);
      assertNotNull(mockRandom0);
      
      BigInteger bigInteger0 = new BigInteger(0, mockRandom0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      int int0 = bigInteger1.getLowestSetBit();
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals(0, int0);
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger1, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertNotNull(bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertTrue(bigInteger2.equals((Object)bigInteger1));
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = (double) 0;
      doubleArray0[4] = 10.0;
      doubleArray0[5] = (double) 0;
      doubleArray0[6] = (double) 0;
      doubleArray0[7] = (double) 0;
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 10.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      int[] intArray0 = new int[5];
      int int0 = 2155;
      intArray0[0] = 2155;
      intArray0[1] = 0;
      intArray0[2] = 2008;
      intArray0[3] = 1500;
      intArray0[4] = (-4215);
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(5, intArray0.length);
      assertArrayEquals(new int[] {2155, 0, 2008, 1500, (-4215)}, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
      
      // Undeclared exception!
      try { 
        MathUtils.equals((float) 2008, (float) 2155, (-4215));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      double double0 = MathUtils.log(434.3376, 434.3376);
      assertEquals(1.0, double0, 0.01);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 434.3376;
      // Undeclared exception!
      try { 
        MathUtils.distance((double[]) null, doubleArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      int int0 = 0;
      double double0 = MathUtils.binomialCoefficientLog(0, 0);
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = (double) 0;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = 0.0;
      doubleArray0[7] = (double) 0;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-2454L), (-2454L));
      assertEquals(6022116L, long0);
      
      int int0 = MathUtils.compareTo((-2454L), (-0.1666666567325592), Double.NaN);
      assertEquals((-1), int0);
      
      int int1 = MathUtils.lcm((-2737), (-1));
      assertFalse(int1 == int0);
      assertEquals(2737, int1);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 1.1102230246251565E-16;
      doubleArray0[1] = (-0.1666666567325592);
      doubleArray0[2] = (double) (-1);
      doubleArray0[3] = (-1572.78);
      doubleArray0[4] = (double) (-1);
      double[][] doubleArray1 = new double[2][0];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[8];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = Double.NaN;
      doubleArray2[1] = 0.0;
      doubleArray2[2] = Double.NaN;
      doubleArray2[3] = (double) (-2454L);
      doubleArray2[4] = 1.1102230246251565E-16;
      doubleArray2[5] = (-0.1666666567325592);
      doubleArray2[6] = (double) 2737;
      doubleArray2[7] = Double.NaN;
      doubleArray1[1] = doubleArray2;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 8 != 5
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle((-1738.3264629638), (-1738.3264629638));
      assertEquals((-1738.3264629638), double0, 0.01);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1738.3264629638), (-1738.3264629638), (-1738.3264629638));
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[14];
      doubleArray0[0] = (-1738.3264629638);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (-1738.3264629638);
      doubleArray0[3] = (-1738.3264629638);
      doubleArray0[4] = (-1738.3264629638);
      double double1 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(14, doubleArray0.length);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      int int0 = MathUtils.gcd((-2132), (-2132));
      assertEquals(2132, int0);
      
      double double2 = MathUtils.TWO_PI;
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(6.283185307179586, double2, 0.01);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      float[] floatArray0 = new float[3];
      floatArray0[0] = 0.0F;
      floatArray0[1] = 2690.8928F;
      floatArray0[2] = 0.0F;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(3, floatArray0.length);
      assertArrayEquals(new float[] {0.0F, 2690.8928F, 0.0F}, floatArray0, 0.01F);
      assertTrue(boolean0);
      
      byte byte0 = MathUtils.sign((byte)20);
      assertEquals((byte)1, byte0);
      
      double double0 = MathUtils.binomialCoefficientLog(0, 0);
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.0;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(31, int0);
      
      double double1 = MathUtils.round((double) 31, 0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(31.0, double1, 0.01);
      
      double double2 = MathUtils.factorialDouble(0);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(1.0, double2, 0.01);
      
      MathUtils.checkFinite(0.0);
      int[] intArray0 = new int[2];
      intArray0[0] = (int) (byte)1;
      intArray0[1] = 31;
      double double3 = MathUtils.distance(intArray0, intArray0);
      assertEquals(2, intArray0.length);
      assertArrayEquals(new int[] {1, 31}, intArray0);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertEquals(0.0, double3, 0.01);
      
      double double4 = MathUtils.round((double) 31, 61);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(31.0, double4, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(0.0F, (-736.0F), (-518.88F));
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double double0 = (-2102.865989255327);
      doubleArray0[0] = (-2102.865989255327);
      double[][] doubleArray1 = new double[5][6];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[0];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 0 != 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 1.304E19;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (-2498.730843017398);
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 0.0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(113236205062349959L, 113236205062349959L);
      assertEquals(0L, long0);
      
      double double0 = (-2323.0922);
      double double1 = 1048.4;
      int int0 = MathUtils.compareTo((-2323.0922), 0.0, 1048.4);
      assertEquals((-1), int0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow((-5474), (-1));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      double double0 = MathUtils.sign(2544.0);
      assertEquals(1.0, double0, 0.01);
      
      int int0 = MathUtils.compareTo(Double.POSITIVE_INFINITY, 0.0, 2.0);
      assertEquals(1, int0);
      
      int[] intArray0 = new int[9];
      intArray0[0] = 1;
      intArray0[1] = 1;
      intArray0[2] = 1;
      intArray0[3] = 1;
      intArray0[4] = 13;
      intArray0[5] = 1;
      intArray0[6] = 1;
      intArray0[7] = 1;
      intArray0[8] = 1;
      int[] intArray1 = MathUtils.copyOf(intArray0, 13);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(9, intArray0.length);
      assertEquals(13, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {1, 1, 1, 1, 13, 1, 1, 1, 1}, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      long long0 = MathUtils.lcm(1764L, 1764L);
      assertEquals(1764L, long0);
      
      double double0 = MathUtils.cosh(2.2250738585072014E-308);
      assertEquals(1.0, double0, 0.01);
      
      long long1 = MathUtils.indicator(2236L);
      assertFalse(long1 == long0);
      assertEquals(1L, long1);
      
      long long2 = MathUtils.pow(1L, 1L);
      assertTrue(long2 == long1);
      assertFalse(long2 == long0);
      assertEquals(1L, long2);
      
      short short0 = MathUtils.sign((short) (-328));
      assertEquals((short) (-1), short0);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      
      BigInteger bigInteger2 = bigInteger0.andNot(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertNotNull(bigInteger2);
      assertTrue(bigInteger0.equals((Object)bigInteger1));
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, 1764L);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotSame(bigInteger3, bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      assertEquals((short)1, bigInteger3.shortValue());
      assertNotNull(bigInteger3);
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertTrue(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertTrue(bigInteger3.equals((Object)bigInteger0));
      assertTrue(bigInteger3.equals((Object)bigInteger1));
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      double double0 = 1647.7;
      double double1 = MathUtils.sign(1647.7);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(1.0, double1, 0.01);
      
      double double2 = MathUtils.sign(1647.7);
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(1.0, double2, 0.01);
      
      float float0 = 0.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, 1.0F);
      assertFalse(boolean0);
      
      float float1 = 0.0F;
      float float2 = (-2123.0F);
      int int0 = 0;
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(0.0F, (-2123.0F), 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((-2147483648L), (-2147483648L));
      assertEquals(0L, long0);
      
      // Undeclared exception!
      try { 
        MathUtils.equals(Float.NaN, 1443.2319F, (-1398));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      float float0 = MathUtils.sign(53.0F);
      assertEquals(1.0F, float0, 0.01F);
      
      int int0 = MathUtils.lcm(2023, 23);
      assertEquals(46529, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(111.085153, 0.04168701738764507, 223);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((-2190.4836F), (-1645.348F), 152);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-442.0), 0.0, 710.7225);
      assertTrue(boolean0);
      
      float float0 = 2045.0F;
      boolean boolean1 = MathUtils.equalsIncludingNaN(0.0F, 2045.0F);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(0L, (-1107L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,107)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      double[] doubleArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 2.0;
      doubleArray0[1] = (-81.4956100020434);
      doubleArray0[2] = Double.NaN;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[7][3];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(3, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertArrayEquals(new double[] {Double.NaN, 2.0, (-81.4956100020434)}, doubleArray0, 0.01);
      
      byte byte0 = MathUtils.sign((byte) (-55));
      assertEquals((byte) (-1), byte0);
      
      double[] doubleArray2 = MathUtils.normalizeArray(doubleArray0, (-1578.6966847));
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray2.length);
      assertNotNull(doubleArray2);
      assertArrayEquals(new double[] {Double.NaN, 2.0, (-81.4956100020434)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {Double.NaN, 39.71783308938494, (-1618.414517789385)}, doubleArray2, 0.01);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      double double0 = MathUtils.sign(956.4);
      assertEquals(1.0, double0, 0.01);
      
      double[] doubleArray3 = MathUtils.normalizeArray(doubleArray0, (-81.4956100020434));
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray3);
      assertNotSame(doubleArray3, doubleArray0);
      assertNotSame(doubleArray3, doubleArray2);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray3.length);
      assertNotNull(doubleArray3);
      assertArrayEquals(new double[] {Double.NaN, 2.0, (-81.4956100020434)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {Double.NaN, 2.0503172439320507, (-83.54592724597546)}, doubleArray3, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      
      double[] doubleArray4 = MathUtils.copyOf(doubleArray0, 67);
      assertNotSame(doubleArray0, doubleArray4);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray3);
      assertNotSame(doubleArray4, doubleArray3);
      assertNotSame(doubleArray4, doubleArray2);
      assertNotSame(doubleArray4, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(67, doubleArray4.length);
      assertNotNull(doubleArray4);
      assertArrayEquals(new double[] {Double.NaN, 2.0, (-81.4956100020434)}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray3));
      assertFalse(doubleArray4.equals((Object)doubleArray3));
      assertFalse(doubleArray4.equals((Object)doubleArray2));
      assertFalse(doubleArray4.equals((Object)doubleArray0));
      
      MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
      assertNotSame(doubleArray0, doubleArray4);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray3);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {Double.NaN, 2.0, (-81.4956100020434)}, doubleArray0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray4));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray3));
      
      int int0 = MathUtils.sign((int) (byte) (-1));
      assertEquals((-1), int0);
      
      int[] intArray0 = new int[2];
      intArray0[0] = (-2945);
      intArray0[1] = (int) (byte) (-1);
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(2, intArray0.length);
      assertEquals(2, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {(-2945), (-1)}, intArray0);
      assertArrayEquals(new int[] {(-2945), (-1)}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 32.749;
      doubleArray0[1] = 6.283185307179586;
      doubleArray0[2] = 2.2250738585072014E-308;
      doubleArray0[3] = 836.5478112592999;
      doubleArray0[4] = 10.0;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 1.570796251296997);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {32.749, 6.283185307179586, 2.2250738585072014E-308, 836.5478112592999, 10.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.058088491873318486, 0.01114479094490383, 3.946721571573E-311, 1.4838254827927024, 0.017737485686072394}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      long long0 = (-3183605296591799669L);
      long long1 = (-302932621132653753L);
      // Undeclared exception!
      try { 
        MathUtils.pow((-3183605296591799669L), (-302932621132653753L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-302,932,621,132,653,753)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      long long0 = MathUtils.pow(0L, 4357);
      assertEquals(0L, long0);
      
      short short0 = MathUtils.sign((short)223);
      assertEquals((short)1, short0);
      
      boolean boolean0 = MathUtils.equals((double) 4357, 0.0);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 4357;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (double) 0L;
      doubleArray0[4] = 0.6366197723675814;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean1 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 4357.0, 0.0, 0.0, 0.6366197723675814}, doubleArray0, 0.01);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(4357.0, 0.0, 4357.0);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2);
      
      short short1 = MathUtils.indicator((short)1);
      assertTrue(short1 == short0);
      assertEquals((short)1, short1);
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(274);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = Double.POSITIVE_INFINITY;
      doubleArray0[1] = Double.POSITIVE_INFINITY;
      doubleArray0[2] = Double.POSITIVE_INFINITY;
      doubleArray0[3] = (double) 274;
      doubleArray0[4] = Double.POSITIVE_INFINITY;
      doubleArray0[5] = (double) 274;
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY, 274.0, Double.POSITIVE_INFINITY, 274.0}, doubleArray0, 0.01);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      long long0 = MathUtils.addAndCheck(79L, 215L);
      assertEquals(294L, long0);
      
      int int0 = MathUtils.sign((-203));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, 0.0, 21);
      assertTrue(boolean0);
      
      int int0 = MathUtils.addAndCheck(1219, (-1057));
      assertEquals(162, int0);
      
      int int1 = MathUtils.sign(303);
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) 21;
      doubleArray0[1] = (double) 21;
      doubleArray0[2] = (double) 21;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {21.0, 21.0, 21.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {21.0, 21.0, 21.0}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      float[] floatArray0 = new float[4];
      floatArray0[0] = Float.NaN;
      floatArray0[1] = (-3873.43F);
      floatArray0[2] = (-1143.5637F);
      floatArray0[3] = (-157.0F);
      float[] floatArray1 = new float[7];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = Float.NaN;
      floatArray1[1] = Float.NaN;
      floatArray1[2] = (-157.0F);
      floatArray1[3] = 0.0F;
      floatArray1[4] = (-3873.43F);
      floatArray1[5] = (-157.0F);
      floatArray1[6] = (-1143.5637F);
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertEquals(4, floatArray0.length);
      assertEquals(7, floatArray1.length);
      assertArrayEquals(new float[] {Float.NaN, (-3873.43F), (-1143.5637F), (-157.0F)}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {Float.NaN, Float.NaN, (-157.0F), 0.0F, (-3873.43F), (-157.0F), (-1143.5637F)}, floatArray1, 0.01F);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
      
      float float0 = MathUtils.round(0.0F, 0);
      assertEquals(0.0F, float0, 0.01F);
      
      double double0 = MathUtils.sinh((-1883.0));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
      
      boolean boolean1 = MathUtils.equals((double) Float.NaN, (double) (-1143.5637F), 10);
      assertTrue(boolean1 == boolean0);
      assertFalse(boolean1);
      
      long long0 = MathUtils.binomialCoefficient(0, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.0;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(31, int0);
      
      int[] intArray0 = new int[5];
      intArray0[0] = 31;
      intArray0[1] = 31;
      intArray0[2] = 31;
      intArray0[3] = 10;
      intArray0[4] = 31;
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(5, intArray0.length);
      assertArrayEquals(new int[] {31, 31, 31, 10, 31}, intArray0);
      assertFalse(int1 == int0);
      assertEquals(0, int1);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      long long0 = MathUtils.pow(4499201580859392L, 2005L);
      assertEquals(0L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 4499201580859392L, (float) 2005L, (float) 4499201580859392L);
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equals((float) 2005L, Float.NEGATIVE_INFINITY);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 2005L;
      doubleArray0[1] = (double) 4499201580859392L;
      doubleArray0[2] = (double) 2005L;
      doubleArray0[3] = (double) 2005L;
      doubleArray0[4] = (double) 0L;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {2005.0, 4.499201580859392E15, 2005.0, 2005.0, 0.0}, doubleArray0, 0.01);
      assertEquals(4.499201580859392E15, double0, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-532));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-532)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      double double0 = MathUtils.round(0.0, (-759));
      assertEquals(0.0, double0, 0.01);
      
      MathUtils.checkFinite(0.0);
      int int0 = MathUtils.addAndCheck((-759), (-759));
      assertEquals((-1518), int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) (-1518), 0.0);
      assertFalse(boolean0);
      
      long long0 = 0L;
      long long1 = MathUtils.gcd(0L, 0L);
      assertTrue(long1 == long0);
      assertEquals(0L, long1);
      
      float[] floatArray0 = new float[1];
      floatArray0[0] = Float.NEGATIVE_INFINITY;
      boolean boolean1 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(1, floatArray0.length);
      assertArrayEquals(new float[] {Float.NEGATIVE_INFINITY}, floatArray0, 0.01F);
      assertFalse(boolean1 == boolean0);
      assertTrue(boolean1);
      
      // Undeclared exception!
      try { 
        MathUtils.factorial(327);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = BigInteger.ZERO;
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      BigInteger bigInteger2 = bigInteger0.gcd(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)10, bigInteger2.shortValue());
      assertEquals((byte)10, bigInteger2.byteValue());
      assertNotNull(bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      
      long long0 = (-1526L);
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-1526L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,526)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-4374.0F), (-4374.0F));
      assertTrue(boolean0);
      
      int[] intArray0 = new int[7];
      intArray0[0] = 0;
      intArray0[1] = (-2411);
      intArray0[2] = (-2210);
      intArray0[3] = 756;
      intArray0[4] = 11;
      intArray0[5] = (-2183);
      intArray0[6] = 382;
      // Undeclared exception!
      try { 
        MathUtils.copyOf(intArray0, (-4527));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      int int0 = (-1491);
      int int1 = (-576);
      // Undeclared exception!
      try { 
        MathUtils.round(0.0F, (-1491), (-576));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method -576, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      float[] floatArray0 = new float[1];
      floatArray0[0] = (-2328.473F);
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertEquals(1, floatArray0.length);
      assertArrayEquals(new float[] {(-2328.473F)}, floatArray0, 0.01F);
      assertTrue(boolean0);
      
      int int0 = MathUtils.compareTo((-2328.473F), (-2328.473F), (-2328.473F));
      assertEquals(0, int0);
      
      double[] doubleArray0 = new double[1];
      boolean boolean1 = MathUtils.equals((-2328.473F), (float) 0);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      long long0 = MathUtils.gcd((long) 0, (long) 0);
      assertEquals(0L, long0);
      
      double double0 = MathUtils.binomialCoefficientDouble(0, 0);
      assertEquals(1.0, double0, 0.01);
      
      double double1 = MathUtils.sign((double) 0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      boolean boolean2 = MathUtils.equals(2328.049121318901, 0.0, 282);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(boolean2);
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = Double.NaN;
      doubleArray0[1] = (-819.0);
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = 1.8014398509481984E16;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {Double.NaN, (-819.0), 0.0, 1.8014398509481984E16}, doubleArray0, 0.01);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      double double0 = MathUtils.sign((-1.9841269659586505E-4));
      assertEquals((-1.0), double0, 0.01);
      
      MathUtils.checkFinite((-1.9841269659586505E-4));
      // Undeclared exception!
      try { 
        MathUtils.factorialLog((-2254));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -2,254
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      double double0 = MathUtils.cosh(393.04161);
      assertEquals(2.4818320330323617E170, double0, 0.01);
      
      double double1 = MathUtils.sinh(2.4818320330323617E170);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      
      float[] floatArray0 = new float[0];
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(0, floatArray0.length);
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertTrue(boolean0);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      long long0 = MathUtils.gcd(24L, 0L);
      assertEquals(24L, long0);
      
      long long1 = MathUtils.mulAndCheck((-2071L), (-2071L));
      assertFalse(long1 == long0);
      assertEquals(4289041L, long1);
      
      double double2 = MathUtils.round((double) (-2071L), 2515);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertEquals((-2071.0), double2, 0.01);
      
      boolean boolean1 = MathUtils.equals(0.0F, 1331.4369F, 2515);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 24L;
      doubleArray0[1] = (double) 4289041L;
      doubleArray0[2] = (double) 24L;
      doubleArray0[3] = (double) 24L;
      doubleArray0[4] = (double) 1331.4369F;
      doubleArray0[5] = (double) 4289041L;
      doubleArray0[6] = (double) 0L;
      doubleArray0[7] = (double) 1331.4369F;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertEquals(8, doubleArray1.length);
      assertEquals(8, doubleArray0.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {24.0, 4289041.0, 24.0, 24.0, 1331.4368896484375, 4289041.0, 0.0, 1331.4368896484375}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {24.0, 4289041.0, 24.0, 24.0, 1331.4368896484375, 4289041.0, 0.0, 1331.4368896484375}, doubleArray0, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorial(75);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[8][7];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(0, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-3371.0993990894663), (-3371.0993990894663));
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-1941.1);
      doubleArray0[1] = 1.304E19;
      doubleArray0[2] = (-0.0013888888689039883);
      doubleArray0[3] = 148.0;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (-955.473);
      doubleArray0[6] = 1.1102230246251565E-16;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly decreasing (-1,941.1 <= 13,040,000,000,000,000,000)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      int int0 = MathUtils.hash((double[]) null);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, 0.0F, 1970.9F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      int int0 = MathUtils.addAndCheck(2448, 2448);
      assertEquals(4896, int0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = 2448;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(1, intArray0.length);
      assertEquals(1, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {2448}, intArray0);
      assertArrayEquals(new int[] {2448}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      long long0 = (-131L);
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-131L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-131)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-3326.1F), (-264.9F));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      float float0 = 2629.6282F;
      // Undeclared exception!
      try { 
        MathUtils.round(2629.6282F, 129, 129);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 129, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      float float0 = MathUtils.indicator((-548.9245F));
      assertEquals((-1.0F), float0, 0.01F);
      
      int int0 = MathUtils.pow(1840, (long) 1840);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 1.2393345855018391E-8;
      doubleArray0[1] = 1.2393345855018391E-8;
      doubleArray0[2] = 551.0;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {1.2393345855018391E-8, 1.2393345855018391E-8, 551.0}, doubleArray0, 0.01);
      
      int int0 = MathUtils.pow(0, 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      long long0 = MathUtils.sign((-2323L));
      assertEquals((-1L), long0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) (-1L);
      doubleArray0[1] = (double) (-1L);
      doubleArray0[2] = (double) (-2323L);
      doubleArray0[3] = (double) (-1L);
      doubleArray0[4] = (double) (-1L);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 2 and 3 are not decreasing (-2,323 < -1)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)40);
      assertEquals((byte)1, byte0);
      
      double double0 = MathUtils.log((byte)40, (byte)1);
      assertEquals(0.0, double0, 0.01);
      
      long long0 = MathUtils.pow((long) (byte)40, 0L);
      assertEquals(1L, long0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) (byte)1;
      doubleArray0[1] = (double) (byte)40;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertNotNull(doubleArray1);
      assertArrayEquals(new double[] {1.0, 40.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.0, 40.0}, doubleArray1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertArrayEquals(new double[] {1.0, 40.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.0, 40.0}, doubleArray1, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertTrue(boolean0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = (int) (byte)1;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(1, intArray0.length);
      assertArrayEquals(new int[] {1}, intArray0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
      int int0 = MathUtils.compareTo(2141.26673244, 2141.26673244, (-1.0));
      assertEquals(0, int0);
      
      double double0 = MathUtils.round((double) 0, 0);
      assertEquals(0.0, double0, 0.01);
      
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
      
      boolean boolean0 = MathUtils.equals(0.0, (-1.0), (-1.0));
      assertFalse(boolean0);
      
      int int1 = 1313;
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog(0, 1313);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1,313, n = 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = MathUtils.copyOf(intArray0, 0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(0, intArray0.length);
      assertEquals(0, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      
      Integer integer0 = new Integer(0);
      assertNotNull(integer0);
      assertEquals(0, (int)integer0);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.NO_BIN_SELECTED;
      assertEquals("no bin selected", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.NO_BIN_SELECTED, localizedFormats0);
      
      Object[] objectArray0 = new Object[8];
      objectArray0[0] = (Object) integer0;
      objectArray0[1] = (Object) integer0;
      objectArray0[2] = (Object) integer0;
      objectArray0[3] = (Object) localizedFormats0;
      objectArray0[4] = (Object) localizedFormats0;
      objectArray0[5] = (Object) integer0;
      objectArray0[6] = (Object) integer0;
      objectArray0[7] = (Object) localizedFormats0;
      MathUtils.checkNotNull((Object) integer0, (Localizable) localizedFormats0, objectArray0);
      assertEquals(8, objectArray0.length);
      assertEquals("no bin selected", localizedFormats0.getSourceString());
      
      float float0 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      LocalizedFormats localizedFormats0 = LocalizedFormats.UNKNOWN_MODE;
      assertEquals("unknown mode {0}, known modes: {1} ({2}), {3} ({4}), {5} ({6}), {7} ({8}), {9} ({10}) and {11} ({12})", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.UNKNOWN_MODE, localizedFormats0);
      
      Object[] objectArray0 = new Object[3];
      Object object0 = new Object();
      assertNotNull(object0);
      
      objectArray0[0] = object0;
      objectArray0[1] = null;
      objectArray0[2] = (Object) localizedFormats0;
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null, (Localizable) localizedFormats0, objectArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // unknown mode java.lang.Object@7ed65edf, known modes: null (UNKNOWN_MODE), {3} ({4}), {5} ({6}), {7} ({8}), {9} ({10}) and {11} ({12})
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)0);
      assertEquals((byte)0, byte0);
      
      double double0 = MathUtils.indicator(470.6209122);
      assertEquals(1.0, double0, 0.01);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertNotNull(mathUtils_OrderDirectionArray0);
      
      double[] doubleArray0 = new double[0];
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, 0.0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test226()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray((double[]) null, 0.0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test227()  throws Throwable  {
      double double0 = MathUtils.indicator(0.0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test228()  throws Throwable  {
      double double0 = Double.NEGATIVE_INFINITY;
      int int0 = MathUtils.hash(Double.NEGATIVE_INFINITY);
      assertEquals((-1048576), int0);
      
      int int1 = 0;
      int int2 = MathUtils.mulAndCheck((-1736), 0);
      assertFalse(int2 == int0);
      assertTrue(int2 == int1);
      assertEquals(0, int2);
      
      double double1 = MathUtils.log(3644.6548240699, (-1736));
      assertNotEquals(double1, double0, 0.01);
      assertEquals(Double.NaN, double1, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("{684G0");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.{684G0
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test229()  throws Throwable  {
      int int0 = MathUtils.gcd(0, 1393);
      assertEquals(1393, int0);
      
      int int1 = MathUtils.hash((double) 1393);
      assertFalse(int1 == int0);
      assertEquals(1083556864, int1);
      
      long long0 = MathUtils.lcm((long) 1393, (-4358L));
      assertEquals(6070694L, long0);
  }

  @Test(timeout = 4000)
  public void test230()  throws Throwable  {
      int int0 = MathUtils.pow(0, 67);
      assertEquals(0, int0);
      
      int[] intArray0 = new int[2];
      intArray0[0] = 0;
      intArray0[1] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(2, intArray0.length);
      assertEquals(2, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {0, 0}, intArray0);
      assertArrayEquals(new int[] {0, 0}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      
      double double0 = MathUtils.round(786.588145, 67, 0);
      assertEquals(786.588145, double0, 0.01);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 786.588145;
      doubleArray0[1] = 786.588145;
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = (double) 67;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (double) 0;
      doubleArray0[6] = 786.588145;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (786.588 >= 786.588)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test231()  throws Throwable  {
      double double0 = MathUtils.round(Double.POSITIVE_INFINITY, 247);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      int[] intArray0 = new int[3];
      intArray0[0] = 247;
      intArray0[1] = 247;
      intArray0[2] = 247;
      double double1 = MathUtils.distance(intArray0, intArray0);
      assertEquals(3, intArray0.length);
      assertArrayEquals(new int[] {247, 247, 247}, intArray0);
      assertNotEquals(double1, double0, 0.01);
      assertEquals(0.0, double1, 0.01);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 247;
      floatArray0[1] = (float) 247;
      floatArray0[2] = (float) 247;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertEquals(3, floatArray0.length);
      assertArrayEquals(new float[] {247.0F, 247.0F, 247.0F}, floatArray0, 0.01F);
      assertTrue(boolean0);
      
      short short0 = MathUtils.sign((short)66);
      assertEquals((short)1, short0);
      
      double double2 = MathUtils.factorialDouble(0);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertEquals(1.0, double2, 0.01);
      
      int int0 = MathUtils.indicator(247);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test232()  throws Throwable  {
      short short0 = MathUtils.indicator((short)0);
      assertEquals((short)1, short0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) (short)1, (-0.16624879837036133), (int) (short)1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test233()  throws Throwable  {
      int int0 = MathUtils.compareTo(2.0, 2.0, (-1162.1));
      assertEquals(0, int0);
      
      double double0 = MathUtils.sinh(0);
      assertEquals(0.0, double0, 0.01);
      
      int int1 = MathUtils.mulAndCheck(0, 6);
      assertTrue(int1 == int0);
      assertEquals(0, int1);
      
      boolean boolean0 = MathUtils.equals((double) 6, 0.0, 0.1111111111111111);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test234()  throws Throwable  {
      long long0 = MathUtils.factorial(17);
      assertEquals(355687428096000L, long0);
      
      int int0 = MathUtils.sign(17);
      assertEquals(1, int0);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog(1, 17);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 17, n = 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test235()  throws Throwable  {
      int int0 = MathUtils.gcd((-958), (-958));
      assertEquals(958, int0);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      int int1 = bigInteger0.compareTo(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(int1 == int0);
      assertEquals(1, int1);
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, (long) 958);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      assertNotNull(bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      
      int[] intArray0 = new int[7];
      intArray0[0] = 2146912812;
      intArray0[1] = 1;
      intArray0[2] = (-958);
      intArray0[3] = 1;
      intArray0[4] = (-958);
      intArray0[5] = 209;
      intArray0[6] = (-958);
      int int2 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(7, intArray0.length);
      assertArrayEquals(new int[] {2146912812, 1, (-958), 1, (-958), 209, (-958)}, intArray0);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertEquals(0, int2);
  }

  @Test(timeout = 4000)
  public void test236()  throws Throwable  {
      long long0 = MathUtils.gcd((-1L), (-1L));
      assertEquals(1L, long0);
      
      int[] intArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.distance((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test237()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Float.NEGATIVE_INFINITY, (-5.0F), (-5.0F));
      assertFalse(boolean0);
      
      int[] intArray0 = new int[3];
      intArray0[0] = (-182);
      intArray0[1] = 602;
      intArray0[2] = 262;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(3, intArray0.length);
      assertEquals(3, intArray1.length);
      assertNotNull(intArray1);
      assertArrayEquals(new int[] {(-182), 602, 262}, intArray0);
      assertArrayEquals(new int[] {(-182), 602, 262}, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 262;
      doubleArray0[1] = (double) Float.NEGATIVE_INFINITY;
      doubleArray0[2] = 40.19140625;
      doubleArray0[3] = (double) 262;
      doubleArray0[4] = (double) (-182);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {262.0, Double.NEGATIVE_INFINITY, 40.19140625, 262.0, (-182.0)}, doubleArray0, 0.01);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      float float0 = MathUtils.indicator(194.20296F);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test238()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte) (-47));
      assertEquals((byte) (-1), byte0);
      
      long long0 = MathUtils.subAndCheck((long) (byte) (-47), (long) (byte) (-1));
      assertEquals((-46L), long0);
      
      long long1 = MathUtils.mulAndCheck((long) (byte) (-47), 2147483647L);
      assertFalse(long1 == long0);
      assertEquals((-100931731409L), long1);
      
      boolean boolean0 = MathUtils.equals((-256.0), (-1827.994219887717), 1670);
      assertFalse(boolean0);
      
      int int0 = 96;
      int int1 = MathUtils.gcd(96, 406);
      assertFalse(int1 == int0);
      assertEquals(2, int1);
      
      long long2 = MathUtils.subAndCheck((-384L), 2147483647L);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      assertEquals((-2147484031L), long2);
      
      // Undeclared exception!
      try { 
        MathUtils.factorial((byte) (-47));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -47
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test239()  throws Throwable  {
      MathUtils.equalsIncludingNaN((-4120.5225F), (-4120.5225F), 3761);
      MathUtils.pow(3761, 8976876);
      MathUtils.lcm((long) 3761, (-2147483648L));
      float[] floatArray0 = new float[4];
      floatArray0[0] = (-4120.5225F);
      floatArray0[1] = (float) 1658960961;
      floatArray0[2] = (-5725.6284F);
      floatArray0[3] = (float) 8076686000128L;
      MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      MathUtils.sign((short)19);
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 3761;
      doubleArray0[1] = (double) (short)19;
      doubleArray0[2] = (double) (-4120.5225F);
      doubleArray0[3] = (double) (short)19;
      doubleArray0[4] = (double) (-5725.6284F);
      doubleArray0[5] = 2366.7206;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      MathUtils.indicator((long) 3761);
      MathUtils.binomialCoefficientDouble(3330, 2055);
      MathUtils.checkFinite((-1547.0193521407239));
      MathUtils.indicator((-5725.6284F));
      byte byte0 = MathUtils.indicator((byte)11);
      MathUtils.equals(floatArray0, floatArray0);
      MathUtils.indicator((byte)1);
      MathUtils.binomialCoefficientLog(1658960961, (short)19);
      MathUtils.distanceInf(doubleArray0, doubleArray1);
      MathUtils.safeNorm(doubleArray1);
      byte byte1 = MathUtils.sign((byte)1);
      assertTrue(byte1 == byte0);
  }

  @Test(timeout = 4000)
  public void test240()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 1232.7964;
      doubleArray0[1] = 2801.29;
      doubleArray0[2] = (-182.09419);
      doubleArray0[3] = 1879.84300815;
      doubleArray0[4] = 1716.710110170975;
      doubleArray0[5] = 1.625;
      doubleArray0[6] = 0.0;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      
      byte byte0 = MathUtils.indicator((byte)25);
      assertEquals((byte)1, byte0);
      
      double double0 = MathUtils.cosh(3.141592653589793);
      assertEquals(11.591953275521519, double0, 0.01);
      
      int[] intArray0 = new int[7];
      intArray0[0] = (int) (byte)1;
      intArray0[1] = (int) (byte)1;
      intArray0[2] = (int) (byte)1;
      intArray0[3] = (int) (byte)25;
      intArray0[4] = (int) (byte)1;
      intArray0[5] = (int) (byte)25;
      intArray0[6] = (int) (byte)1;
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test241()  throws Throwable  {
      MathUtils.lcm(2138460607, 2138460607);
      MathUtils.equals((float) 2138460607, (float) 2138460607, 2275);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 2138460607;
      doubleArray0[1] = (double) 2275;
      MathUtils.distance1(doubleArray0, doubleArray0);
      MathUtils.sign((short) (-144));
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
      MathUtils.subAndCheck(4607182418800017408L, (-1028L));
      MathUtils.distance1(doubleArray0, doubleArray0);
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("j>R1?::V?j?!4Db ");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.j>R1?::V?j?!4Db 
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test242()  throws Throwable  {
      MathUtils.pow(2934, (long) 2934);
      MathUtils.factorial(0);
      MathUtils.gcd(0, 0);
      MathUtils.pow(1L, (long) 0);
      MathUtils.mulAndCheck(19, (-1281));
      MathUtils.addAndCheck(19, 1459);
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) (-24339);
      doubleArray0[1] = (double) 1459;
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = (-2344.24);
      doubleArray0[4] = (double) 1L;
      doubleArray0[5] = (double) 19;
      doubleArray0[6] = (double) 0;
      doubleArray0[7] = (double) 0;
      MathUtils.copyOf(doubleArray0);
      MathUtils.mulAndCheck((long) 1459, (long) 19);
      MathUtils.pow(0, 2147033776);
      MathUtils.gcd(0, 1478);
      MathUtils.gcd((long) 1478, (long) 0);
      MathUtils.OrderDirection.values();
      MathUtils.sign((-1832.31));
      float[] floatArray0 = new float[0];
      MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("Us");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.Us
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test243()  throws Throwable  {
      MathUtils.sign((byte) (-16));
      MathUtils.indicator((short) (byte) (-16));
      BigInteger bigInteger0 = BigInteger.ZERO;
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) (byte) (-16);
      doubleArray0[1] = (double) (byte) (-1);
      bigInteger1.shortValueExact();
      doubleArray0[2] = (double) (short) (-1);
      doubleArray0[1] = (double) (short) (-1);
      MathUtils.equals(doubleArray0, doubleArray0);
      int int0 = (-184);
      MathUtils.gcd((-184), (int) (short) (-1));
      bigInteger0.setBit(1454);
      MathUtils.equals((double) 1030, (double) (short)1);
      MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      MathUtils.sign((-1));
      // Undeclared exception!
      try { 
        MathUtils.round((-4120.5225F), 257, (-1));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method -1, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test244()  throws Throwable  {
      MathUtils.sign((byte) (-16));
      MathUtils.indicator((short) (byte) (-16));
      BigInteger bigInteger0 = BigInteger.ZERO;
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) (byte) (-16);
      doubleArray0[1] = (double) (byte) (-1);
      bigInteger1.shortValueExact();
      doubleArray0[2] = (double) (short) (-1);
      doubleArray0[3] = (double) (short) (-1);
      MathUtils.equals(doubleArray0, doubleArray0);
      MathUtils.gcd((-184), (int) (short) (-1));
      MathUtils.equals((float) (-184), (float) (byte) (-16), 1046);
      MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      MathUtils.sign((-1));
      // Undeclared exception!
      try { 
        MathUtils.round((-4120.5225F), 257, (-1));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method -1, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test245()  throws Throwable  {
      int[] intArray0 = new int[5];
      intArray0[0] = 222;
      intArray0[1] = 6;
      intArray0[2] = (-1);
      intArray0[3] = (-2147483647);
      intArray0[4] = 43935035;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      
      long long0 = MathUtils.binomialCoefficient(6, (-2566));
      assertEquals(1L, long0);
      
      float[] floatArray0 = new float[9];
      floatArray0[1] = (float) 222;
      floatArray0[2] = (float) (-2147483647);
      floatArray0[3] = (float) (-2147483647);
      floatArray0[5] = (float) 0;
      floatArray0[6] = (float) (-1);
      floatArray0[7] = (float) 43935035;
      floatArray0[8] = (float) 6;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      MathUtils.equals((float) 222, 5758.811F, Float.NaN);
      long long1 = MathUtils.lcm((long) (-2566), 2147483647L);
      assertEquals(5510443038202L, long1);
      
      int int1 = MathUtils.indicator((-1));
      assertEquals((-1), int1);
      
      MathUtils.OrderDirection.values();
      boolean boolean1 = MathUtils.equalsIncludingNaN(1076.0792F, 0.0F);
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      long long2 = MathUtils.sign((long) (-2566));
      assertFalse(long2 == long0);
  }

  @Test(timeout = 4000)
  public void test246()  throws Throwable  {
      MathUtils.indicator(1L);
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 1L;
      doubleArray0[1] = (double) 1L;
      doubleArray0[2] = (double) 1L;
      doubleArray0[3] = (double) 1L;
      doubleArray0[4] = (double) 1L;
      doubleArray0[5] = (double) 1L;
      doubleArray0[6] = (double) 1L;
      MathUtils.safeNorm(doubleArray0);
      MathUtils.indicator((short)21845);
      MathUtils.subAndCheck((long) (short)21845, 27766942120L);
      MathUtils.copyOf(doubleArray0);
      MathUtils.indicator(1.0);
      float[] floatArray0 = new float[2];
      floatArray0[0] = 0.0F;
      floatArray0[1] = (float) (-27766920275L);
      MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      // Undeclared exception!
      try { 
        MathUtils.pow((-4565540768852884849L), (-4565540768852884849L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-4,565,540,768,852,884,849)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test247()  throws Throwable  {
      int[] intArray0 = new int[5];
      intArray0[0] = 222;
      intArray0[1] = 6;
      intArray0[2] = (-1);
      intArray0[3] = 2113396605;
      intArray0[4] = 43935035;
      MathUtils.distance1(intArray0, intArray0);
      MathUtils.binomialCoefficient(6, (-2566));
      float[] floatArray0 = new float[9];
      floatArray0[1] = (float) 222;
      floatArray0[2] = (float) 2113396605;
      floatArray0[3] = (float) 2113396605;
      floatArray0[4] = (float) 6;
      floatArray0[6] = (float) (-1);
      floatArray0[7] = (float) 43935035;
      floatArray0[8] = (float) 6;
      MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      // Undeclared exception!
      try { 
        MathUtils.equals((double) (-1), 1.0, (-2566));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test248()  throws Throwable  {
      float[] floatArray0 = new float[1];
      int int0 = MathUtils.compareTo((-2328.473F), (-2328.473F), 0.0F);
      assertEquals(0, int0);
      
      double[] doubleArray0 = new double[0];
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      long long0 = MathUtils.gcd((-2582L), (-2582L));
      assertEquals(2582L, long0);
      
      double double0 = MathUtils.binomialCoefficientDouble(0, 0);
      assertEquals(1.0, double0, 0.01);
      
      double double1 = MathUtils.sign((double) 0);
      assertEquals(0.0, double1, 0.01);
      
      boolean boolean1 = MathUtils.equals(2328.049121318901, 0.0, 282);
      assertFalse(boolean1 == boolean0);
      assertFalse(boolean1);
  }

  @Test(timeout = 4000)
  public void test249()  throws Throwable  {
      int int0 = 919;
      MathUtils.lcm(919, 919);
      MathUtils.factorialDouble(919);
      long long0 = 6722166367014452318L;
      long long1 = 0L;
      MathUtils.pow(6722166367014452318L, 0L);
      MathUtils.equalsIncludingNaN((-962.7), (-2552.43), (double) 919);
      MathUtils.equalsIncludingNaN((float) 919, (float) 6722166367014452318L, (float) 919);
      Integer integer0 = new Integer(919);
      Integer integer1 = new Integer(919);
      Pair<Integer, Object> pair0 = new Pair<Integer, Object>(integer0, integer1);
      LocalizedFormats localizedFormats0 = LocalizedFormats.INVALID_INTERVAL_INITIAL_VALUE_PARAMETERS;
      Locale locale0 = new Locale("<pef$zN", "<pef$zN", "ROOTS_OF_UNITY_NOT_COMPUTED_YET");
      localizedFormats0.getLocalizedString(locale0);
      Object[] objectArray0 = new Object[0];
      MathUtils.checkNotNull((Object) pair0, (Localizable) localizedFormats0, objectArray0);
      MathUtils.sign(Double.POSITIVE_INFINITY);
      long long2 = 3952090531849364496L;
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(3952090531849364496L, (-1291L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test250()  throws Throwable  {
      MathUtils.normalizeAngle((-1738.3264629638), (-1738.3264629638));
      MathUtils.OrderDirection.values();
      MathUtils.equals((-1738.3264629638), (-1738.3264629638));
      double[] doubleArray0 = new double[14];
      doubleArray0[0] = (-1738.3264629638);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (-1738.3264629638);
      doubleArray0[3] = (-1738.3264629638);
      doubleArray0[4] = (-1738.3264629638);
      MathUtils.distance(doubleArray0, doubleArray0);
      int int0 = (-2132);
      MathUtils.gcd((-2132), (-2132));
      int int1 = 192;
      MathUtils.binomialCoefficientLog(192, (-2132));
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[0][7];
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      BigInteger bigInteger0 = BigInteger.ZERO;
      int int2 = (-1150);
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-1150));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,150)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test251()  throws Throwable  {
      float[] floatArray0 = new float[0];
      MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      MathUtils.lcm((-326L), (-1L));
      MathUtils.factorialDouble(645);
      MathUtils.round((double) 326L, 645);
      MathUtils.round((float) (-1L), 645);
      MathUtils.compareTo(2020.84300815, (-1L), (-1L));
      MathUtils.subAndCheck((-3777), 1);
      MathUtils.indicator(0.0F);
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      MathUtils.factorialLog(645);
      MathUtils.lcm((long) (-3777), (-1L));
      Pair<String, Object> pair0 = new Pair<String, Object>("", "");
      LocalizedFormats localizedFormats0 = LocalizedFormats.MAX_ITERATIONS_EXCEEDED;
      MathUtils.checkNotNull((Object) pair0, (Localizable) localizedFormats0, (Object[]) mathUtils_OrderDirectionArray0);
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = 2020.84300815;
      doubleArray0[1] = (double) 1.0F;
      doubleArray0[2] = (double) Float.NaN;
      doubleArray0[3] = (double) 1.0F;
      doubleArray0[4] = (double) (-326L);
      doubleArray0[5] = Double.POSITIVE_INFINITY;
      doubleArray0[6] = (double) (-3777);
      doubleArray0[7] = (double) 1;
      doubleArray0[8] = (double) 1;
      MathUtils.distance1(doubleArray0, doubleArray0);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[9][1];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      double[] doubleArray2 = new double[5];
      doubleArray2[0] = 1.7976931348623157E308;
      doubleArray2[1] = 326.0;
      doubleArray2[2] = (double) 1.0F;
      doubleArray2[3] = 326.0;
      doubleArray2[4] = (double) 0.0F;
      doubleArray1[7] = doubleArray2;
      doubleArray1[8] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 5 != 9
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test252()  throws Throwable  {
      int[] intArray0 = new int[3];
      intArray0[0] = (-989);
      intArray0[1] = 11;
      intArray0[2] = (-5);
      MathUtils.distance(intArray0, intArray0);
      MathUtils.round((-2180.1978F), (-5));
      MathUtils.addAndCheck(4364L, 2L);
      MathUtils.checkFinite(0.0);
      float[] floatArray0 = new float[9];
      floatArray0[0] = (float) 11;
      floatArray0[1] = (float) (-5);
      floatArray0[2] = (float) 2L;
      floatArray0[3] = (-2180.1978F);
      floatArray0[4] = (float) (-5);
      floatArray0[5] = (-2180.1978F);
      floatArray0[6] = (float) 4366L;
      floatArray0[8] = (float) 4366L;
      MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      MathUtils.round(0.0F, 113, 0);
      MathUtils.distanceInf(intArray0, intArray0);
      // Undeclared exception!
      try { 
        MathUtils.factorial((-5));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -5
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test253()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 304.0;
      doubleArray0[1] = 1.625;
      doubleArray0[2] = 0.0;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      MathUtils.distance(doubleArray1, doubleArray1);
      MathUtils.distance1(doubleArray0, doubleArray0);
      MathUtils.equalsIncludingNaN(1.625, 0.0, 1708);
      MathUtils.safeNorm(doubleArray1);
      MathUtils.sign(1048.533F);
      MathUtils.mulAndCheck(1708, 1708);
      double[] doubleArray2 = MathUtils.copyOf(doubleArray1);
      MathUtils.mulAndCheck(1622, 1708);
      double[] doubleArray3 = new double[1];
      doubleArray3[0] = 0.0;
      MathUtils.hash(doubleArray3);
      MathUtils.sign(0.0F);
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray2);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (304 >= 1.625)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test254()  throws Throwable  {
      MathUtils.equalsIncludingNaN((-1114.8821), (-1153.06772167073));
      MathUtils.round((-1.0F), 918, 0);
      MathUtils.compareTo(693.7, 0.0, 0);
      MathUtils.indicator((-1153.06772167073));
      double[] doubleArray0 = new double[7];
      doubleArray0[1] = (double) 918;
      doubleArray0[2] = (double) (-1.0F);
      doubleArray0[3] = 693.7;
      doubleArray0[4] = 693.7;
      doubleArray0[5] = 693.7;
      doubleArray0[6] = (double) (-1.0F);
      MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      MathUtils.distanceInf(doubleArray0, doubleArray0);
      MathUtils.equals((-1.0), 0.0);
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 693.7);
      MathUtils.round(5757.0F, 1, 1);
      double[] doubleArray2 = new double[8];
      doubleArray2[0] = (-1.0);
      doubleArray2[1] = (double) Float.NaN;
      doubleArray2[2] = (double) 5757.0F;
      doubleArray2[3] = 693.7;
      doubleArray2[4] = (double) 0;
      doubleArray2[5] = 693.7;
      doubleArray2[6] = 0.0;
      doubleArray2[7] = (double) (-1.0F);
      double double0 = MathUtils.TWO_PI;
      MathUtils.pow((long) 1, 1);
      MathUtils.equalsIncludingNaN(doubleArray1, doubleArray1);
      MathUtils.checkFinite(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
  }

  @Test(timeout = 4000)
  public void test255()  throws Throwable  {
      MathUtils.pow(4499201580859392L, 2005L);
      MathUtils.equalsIncludingNaN((float) 4499201580859392L, (float) 2005L, (float) 4499201580859392L);
      MathUtils.equalsIncludingNaN((float) 2005L, (float) 0L);
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 2005L;
      doubleArray0[0] = (double) 4499201580859392L;
      doubleArray0[2] = (double) 2005L;
      doubleArray0[3] = (double) 2005L;
      doubleArray0[4] = (double) 0L;
      MathUtils.safeNorm(doubleArray0);
      BigInteger bigInteger0 = BigInteger.ONE;
      bigInteger0.not();
      bigInteger0.toString();
      MathUtils.sign(2041L);
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-532));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-532)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test256()  throws Throwable  {
      byte byte0 = (byte)0;
      MathUtils.sign((byte)0);
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) (byte)0;
      doubleArray0[1] = (double) (byte)0;
      doubleArray0[2] = (double) (byte)0;
      doubleArray0[3] = (double) (byte)0;
      double double0 = 4771.5521175158;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (double) (byte)0;
      doubleArray0[6] = (double) (byte)0;
      doubleArray0[7] = (double) (byte)0;
      doubleArray0[8] = (double) (byte)0;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }
}
