/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 11:48:13 GMT 2019
 */

package org.apache.commons.math.util;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.math.BigInteger;
import org.apache.commons.math.exception.util.Localizable;
import org.apache.commons.math.exception.util.LocalizedFormats;
import org.apache.commons.math.util.MathUtils;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MathUtils_ESTest extends MathUtils_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[7][6];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      double[] doubleArray2 = new double[1];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray1[5] = doubleArray2;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 1 != 3
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[2] = 3.834E-20;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 3.834E-20, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(3.834E-20, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 1216.2419262693747;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertArrayEquals(new double[] {1216.2419262693747, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(3, doubleArray0.length);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[1] = (double) 36;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, false);
      assertArrayEquals(new double[] {0.0, 36.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[1] = (double) (byte)35;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not increasing (35 > 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      long long0 = MathUtils.pow(1L, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[2] = (-2498.162);
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-0.2499999701976776));
      assertArrayEquals(new double[] {0.0, 0.0, (-2498.162), 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, (-0.24999997019767764), 0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertNotNull(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      short short0 = MathUtils.indicator((short)0);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      long long0 = MathUtils.indicator(0L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      double double0 = MathUtils.indicator(0.0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)0);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = new double[2];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals((-1239.681), 1305.80014, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 1, (double) (byte)0, 1163.80465606);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(1.0F, 0.0F, (-1129));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (-896), Float.POSITIVE_INFINITY, Float.POSITIVE_INFINITY);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(496.941F, 13.98F, 496.941F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((long) 0, 4208363204685324176L);
      assertEquals((-4208363204685324176L), long0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(1186L, 87178291200L);
      assertEquals(87178292386L, long0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(2432902008176640000L, 1547L);
      assertEquals(2432902008176638453L, long0);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(2737, 0);
      assertEquals(2737, int0);
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      int int0 = MathUtils.subAndCheck((-337), 0);
      assertEquals((-337), int0);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      double double0 = MathUtils.sinh((-6.032174644509064E-23));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      double double0 = MathUtils.sinh(2737);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, 0);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      float float0 = MathUtils.round(370.7F, (int) (short) (-1));
      assertEquals(370.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      float float0 = MathUtils.round((-3.4028235E38F), 0);
      assertEquals((-3.4028235E38F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      double double0 = MathUtils.round(1216.2419262693747, 4648, 1);
      assertEquals(1216.2419262693747, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      double double0 = MathUtils.round((double) 0, 0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      double double0 = MathUtils.round((double) (-1L), 8);
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertNotNull(bigInteger1);
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, bigInteger1);
      assertNotNull(bigInteger2);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte) (-71);
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertArrayEquals(new byte[] {(byte) (-71)}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((short) (-71), bigInteger0.shortValue());
      assertEquals((byte) (-71), bigInteger0.byteValue());
      assertEquals(1, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 86L);
      assertArrayEquals(new byte[] {(byte) (-71)}, byteArray0);
      assertNotNull(bigInteger1);
      assertEquals((short) (-71), bigInteger0.shortValue());
      assertEquals((byte) (-71), bigInteger0.byteValue());
      assertEquals((short) (-4719), bigInteger1.shortValue());
      assertEquals((byte) (-111), bigInteger1.byteValue());
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals(1, byteArray0.length);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 18);
      assertNotNull(bigInteger1);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      long long0 = MathUtils.pow(978L, 978L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      long long0 = MathUtils.pow(939L, (long) 637);
      assertEquals((-3632245072015872229L), long0);
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      long long0 = MathUtils.pow(121645100408832000L, 2147418041);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      long long0 = MathUtils.pow(619L, 21);
      assertEquals((-2170822527608016069L), long0);
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      int int0 = MathUtils.pow(42, (long) 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      int int0 = MathUtils.pow(73, (long) 73);
      assertEquals((-1880654455), int0);
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      int int0 = MathUtils.pow(20, 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      int int0 = MathUtils.pow(1074, 13);
      assertEquals((-1311301632), int0);
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle(18, 18);
      assertEquals(18.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle((-172.6464), (-1149L));
      assertEquals((-1146.5401226128358), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(35066043587L, (-2715L));
      assertEquals((-95204308338705L), long0);
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(6, 6);
      assertEquals(36, int0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(111, (-1428));
      assertEquals((-158508), int0);
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      double double0 = MathUtils.log(2635, 1);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      double double0 = MathUtils.log(1.304E19, 0);
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      int int0 = MathUtils.hash((double[]) null);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-213.9426);
      int int0 = MathUtils.hash(doubleArray0);
      assertArrayEquals(new double[] {(-213.9426), 0.0}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertEquals((-615514459), int0);
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      int int0 = MathUtils.hash(0.0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      int int0 = MathUtils.hash(988.41970133);
      assertEquals((-858779429), int0);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      int int0 = MathUtils.gcd(0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-730.628F), (-730.628F));
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-2283.3168486539707), 6.0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      int[] intArray0 = new int[2];
      intArray0[0] = 8;
      int[] intArray1 = new int[4];
      assertFalse(intArray1.equals((Object)intArray0));
      
      int int0 = MathUtils.distanceInf(intArray0, intArray1);
      assertArrayEquals(new int[] {8, 0}, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0}, intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(2, intArray0.length);
      assertEquals(4, intArray1.length);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(8, int0);
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 872.678135032;
      double[] doubleArray1 = new double[2];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double0 = MathUtils.distance1(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {872.678135032, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(872.678135032, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      int[] intArray0 = new int[5];
      intArray0[0] = (int) (byte) (-128);
      int[] intArray1 = new int[7];
      assertFalse(intArray1.equals((Object)intArray0));
      
      double double0 = MathUtils.distance(intArray0, intArray1);
      assertArrayEquals(new int[] {(-128), 0, 0, 0, 0}, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0}, intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(5, intArray0.length);
      assertEquals(7, intArray1.length);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(128.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 915.2;
      double[] doubleArray1 = new double[9];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double0 = MathUtils.distance(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {915.2, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(915.2, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = MathUtils.copyOf(intArray0, 283);
      assertArrayEquals(new int[] {}, intArray0);
      assertNotNull(intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(0, intArray0.length);
      assertEquals(283, intArray1.length);
      assertFalse(intArray1.equals((Object)intArray0));
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      int[] intArray0 = new int[9];
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0, 0, 0}, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0, 0, 0}, intArray1);
      assertNotNull(intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(9, intArray0.length);
      assertEquals(9, intArray1.length);
      assertFalse(intArray1.equals((Object)intArray0));
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 4938);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertNotNull(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertEquals(4938, doubleArray1.length);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertNotNull(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertNotNull(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      long long0 = MathUtils.addAndCheck((long) 0, (long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      long long0 = MathUtils.addAndCheck((long) 1924, (-2257L));
      assertEquals((-333L), long0);
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      int int0 = MathUtils.addAndCheck(0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      int int0 = MathUtils.addAndCheck((-2190), (-2190));
      assertEquals((-4380), int0);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      double[][] doubleArray0 = new double[0][6];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.safeNorm((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.round(0.0, 907, (-1160));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (BigInteger) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, 2093796556);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // BigInteger would overflow supported range
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray((double[]) null, 15.0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      // Undeclared exception!
      MathUtils.factorialLog(2147479552);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(0.0F, 0.0F, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      int[] intArray0 = new int[9];
      int[] intArray1 = new int[3];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 3
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      double[] doubleArray1 = new double[1];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance1((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance1((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      double[] doubleArray1 = new double[8];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 8
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null, 591);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null, (-1361));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null, 544);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null, mathUtils_OrderDirection0, false, false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkFinite((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((short) (-1), (short) (-575));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog(0, 4);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 4, n = 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-182), (short)0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 0, n = -182
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[6][3];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 3 != 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(50L, 1073741824L);
      assertEquals(53687091200L, long0);
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(121645100408832000L, 121645100408832000L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((-4035746434778044925L), (-4035746434778044925L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-2968L), (-2968L));
      assertEquals(8809024L, long0);
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(0L, 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((-1563), 2147418041);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(1932735283, 1932735283);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(0, (-2080));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      float float0 = MathUtils.indicator(0.0F);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      long long0 = MathUtils.gcd(163L, 163L);
      assertEquals(163L, long0);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      long long0 = MathUtils.gcd(148L, (-390L));
      assertEquals(2L, long0);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      int int0 = MathUtils.gcd(62, 1343);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      int int0 = MathUtils.gcd(90, 90);
      assertEquals(90, int0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      double double0 = MathUtils.factorialLog(67);
      assertEquals(217.73693411395422, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(35.05675727, 2549.1, 502);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals((-1625.496276378389), (double) 0.0F, (-1546));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(10.0, (-3510.0), 4194304);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-3872.3901144959973), (-3872.3901144959973), 2038);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(720.88, 720.88, 720.88);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, (-499.49230803), 0.0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-4669.65F), (-4669.65F), (int) (byte)125);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float) (byte)1, 1353.4417F, 3850);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-661.4399F), 0.0F, 20);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-2136.0F), (-2136.0F));
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(1, (-2495));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null, mathUtils_OrderDirection0, true);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      int[] intArray0 = new int[8];
      int[] intArray1 = MathUtils.copyOf(intArray0, 0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0, 0}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertNotNull(intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(8, intArray0.length);
      assertEquals(0, intArray1.length);
      assertFalse(intArray1.equals((Object)intArray0));
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      MathUtils.checkNotNull((Object) "Yu;gVo");
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      Object object0 = new Object();
      assertNotNull(object0);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.NEGATIVE_ELEMENT_AT_2D_INDEX;
      assertEquals("element ({0}, {1}) is negative: {2}", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.NEGATIVE_ELEMENT_AT_2D_INDEX, localizedFormats0);
      
      Object[] objectArray0 = new Object[0];
      MathUtils.checkNotNull(object0, (Localizable) localizedFormats0, objectArray0);
      assertEquals("element ({0}, {1}) is negative: {2}", localizedFormats0.getSourceString());
      assertEquals(0, objectArray0.length);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[8][0];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(9, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray0 = new double[9][1];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, mathUtils_OrderDirection0, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-2108.8086216996853);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertArrayEquals(new double[] {(-2108.8086216996853)}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(2108.8086216996853, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkFinite((double) Float.NaN);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // \uFFFD is not a finite number
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      MathUtils.checkFinite(1690.4546440521103);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkFinite((double) Float.NEGATIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -\u221E is not a finite number
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[1] = (double) 2001;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not decreasing (0 < 2,001)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 1772;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not increasing (1,772 > 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[1] = 212.667;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly increasing (212.667 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      MathUtils.checkOrder(doubleArray0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      int[] intArray0 = new int[8];
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0, 0}, intArray0);
      assertEquals(8, intArray0.length);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      int[] intArray0 = new int[7];
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0}, intArray0);
      assertEquals(7, intArray0.length);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      int[] intArray0 = new int[3];
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertArrayEquals(new int[] {0, 0, 0}, intArray0);
      assertEquals(3, intArray0.length);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(8, doubleArray0.length);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short) (-7168), bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 40320L);
      assertNotNull(bigInteger1);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 1L);
      assertNotNull(bigInteger1);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (long) (-337));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-337)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      BigInteger bigInteger0 = MathUtils.pow((BigInteger) null, (long) 0);
      assertNotNull(bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-247));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-247)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals(9, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (int) (byte)0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray0);
      assertNotNull(bigInteger1);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals(9, byteArray0.length);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      long long0 = MathUtils.pow(163L, 163L);
      assertEquals(709173818026507899L, long0);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow(2929L, (-820L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-820)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      long long0 = MathUtils.pow((-862L), 0L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((-955L), (-3207));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-3,207)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow(4479, (-714L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-714)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      int int0 = MathUtils.pow(0, 234L);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((-2140129250), (-838));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-838)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      int int0 = MathUtils.pow(854, 738);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((-9223372036854775808L), (-9223372036854775808L));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      short short0 = MathUtils.sign((short)586);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      short short0 = MathUtils.sign((short)0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      short short0 = MathUtils.sign((short) (-1));
      assertEquals((short) (-1), short0);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      long long0 = MathUtils.sign((-885L));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      long long0 = MathUtils.sign((long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      long long0 = MathUtils.sign(170L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      int int0 = MathUtils.sign((-696));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      int int0 = MathUtils.sign(0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      int int0 = MathUtils.sign(1);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      float float0 = MathUtils.sign((-1065.9F));
      assertEquals((-1.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      float float0 = MathUtils.sign((float) 0);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      float float0 = MathUtils.sign(Float.NaN);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      float float0 = MathUtils.sign((float) 6L);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      double double0 = MathUtils.sign((double) (-4813572198693462574L));
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      double double0 = MathUtils.sign(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      double double0 = MathUtils.sign(1.0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte) (-12));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)3);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      float float0 = MathUtils.round((float) (-4813572198693462568L), (int) (short)1, 2);
      assertEquals((-4.8135723E18F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.round(0.0F, (-1129), 269);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 269, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, (-1129), 2);
      assertEquals(Float.POSITIVE_INFINITY, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, (-1129), 1);
      assertEquals(Float.NEGATIVE_INFINITY, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, 149, 0);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) (byte)1;
      doubleArray0[4] = (double) Float.NaN;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 2.4384203044354907E-8);
      assertArrayEquals(new double[] {1.0, 0.0, 0.0, 0.0, Double.NaN}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {2.4384203044354907E-8, 0.0, 0.0, 0.0, Double.NaN}, doubleArray1, 0.01);
      assertNotNull(doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[4] = (double) Float.NaN;
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, 2.4384203044354907E-8);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[1] = Double.POSITIVE_INFINITY;
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, 0.5);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // Array contains an infinite element, \u221E at index 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((-1639L), 9154082963658192752L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-2008L), 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.lcm((-4813572198693462568L), (-832L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      long long0 = MathUtils.lcm((-2846L), (-586L));
      assertEquals(833878L, long0);
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.lcm(11, 1073741824);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.lcm((-1880654455), 73);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      long long0 = MathUtils.lcm((long) (byte) (-1), 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      long long0 = MathUtils.lcm(0L, 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      int int0 = MathUtils.lcm(0, 2273);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      short short0 = MathUtils.indicator((short)1076);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      short short0 = MathUtils.indicator((short) (-2142));
      assertEquals((short) (-1), short0);
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      long long0 = MathUtils.indicator(2524967415494076825L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      long long0 = MathUtils.indicator((-1L));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      int int0 = MathUtils.indicator((int) (short)249);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      int int0 = MathUtils.indicator((-2471));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      float float0 = MathUtils.indicator((-1334.3452F));
      assertEquals((-1.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      float float0 = MathUtils.indicator(Float.NaN);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      double double0 = MathUtils.indicator(2.0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      double double0 = MathUtils.indicator(Double.NEGATIVE_INFINITY);
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)3);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte) (-12));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      long long0 = MathUtils.lcm(2047L, 2047L);
      assertEquals(2047L, long0);
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      long long0 = MathUtils.gcd((-3958705157555305932L), (-3958705157555305932L));
      assertEquals(3958705157555305932L, long0);
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      long long0 = MathUtils.gcd((-755L), 0L);
      assertEquals(755L, long0);
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
      long long0 = MathUtils.gcd(0L, 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      int int0 = MathUtils.lcm(18, (int) (short) (-2142));
      assertEquals(2142, int0);
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      int int0 = MathUtils.gcd((-373), (-373));
      assertEquals(373, int0);
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      int int0 = MathUtils.gcd((-2024), 0);
      assertEquals(2024, int0);
  }

  @Test(timeout = 4000)
  public void test226()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorialLog((-703));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -703
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test227()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(15);
      assertEquals(1.307674368E12, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test228()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorialDouble((-148));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -148
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test229()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(2481);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test230()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorial(2047);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test231()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorial((-611));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -611
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test232()  throws Throwable  {
      double double0 = MathUtils.factorialLog(20);
      assertEquals(42.335616460753485, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test233()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      double[] doubleArray1 = new double[6];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(7, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test234()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test235()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double[]) null, (double[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test236()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(3, doubleArray0.length);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test237()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      double[] doubleArray1 = new double[2];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test238()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((double[]) null, (double[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test239()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-540.334094822435), (-540.334094822435), 1766);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test240()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Double.NaN, 1.304E19, 239);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test241()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[3] = (double) Float.NaN;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, Double.NaN, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(9, doubleArray0.length);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test242()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(872.678135032, 0.25, 2143289344);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test243()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(963.052, (-650.18018831831), (-3186.35));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test244()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, 0.5, 0.5);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test245()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-0.0013888888689039883), (-0.0013888888689039883), (-0.0013888888689039883));
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test246()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(1.2599210498948732, (double) 1L, 1369.8989891525152);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test247()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1841.54258480194), (-1841.54258480194));
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test248()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) Float.NaN, (-2108.8086216996853));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test249()  throws Throwable  {
      float[] floatArray0 = new float[1];
      float[] floatArray1 = new float[2];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertArrayEquals(new float[] {0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {0.0F, 0.0F}, floatArray1, 0.01F);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertEquals(1, floatArray0.length);
      assertEquals(2, floatArray1.length);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test250()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float[]) null, (float[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test251()  throws Throwable  {
      float[] floatArray0 = new float[1];
      boolean boolean0 = MathUtils.equalsIncludingNaN((float[]) null, floatArray0);
      assertArrayEquals(new float[] {0.0F}, floatArray0, 0.01F);
      assertEquals(1, floatArray0.length);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test252()  throws Throwable  {
      float[] floatArray0 = new float[5];
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertEquals(5, floatArray0.length);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test253()  throws Throwable  {
      float[] floatArray0 = new float[3];
      float[] floatArray1 = new float[1];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equals(floatArray1, floatArray0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {0.0F}, floatArray1, 0.01F);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertEquals(3, floatArray0.length);
      assertEquals(1, floatArray1.length);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test254()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float[]) null, (float[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test255()  throws Throwable  {
      float[] floatArray0 = new float[3];
      boolean boolean0 = MathUtils.equals((float[]) null, floatArray0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertEquals(3, floatArray0.length);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test256()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, 0.0F, 21);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test257()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, 187.637F, (int) (short)766);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test258()  throws Throwable  {
      float[] floatArray0 = new float[6];
      floatArray0[3] = Float.NaN;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, Float.NaN, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertEquals(6, floatArray0.length);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test259()  throws Throwable  {
      float[] floatArray0 = new float[6];
      floatArray0[3] = (-799.22F);
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, (-799.22F), 0.0F, 0.0F}, floatArray0, 0.01F);
      assertEquals(6, floatArray0.length);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test260()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(1048.652F, (-1184.5F), 2090970920);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test261()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (-879), (float) (byte)13, Float.POSITIVE_INFINITY);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test262()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 0, (float) 0, (float) 0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test263()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float) 0, 1.0F, 1.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test264()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Float.NEGATIVE_INFINITY, (float) 1, (-2268.9F));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test265()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F, 0.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test266()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, 0.5F, 25.7813F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test267()  throws Throwable  {
      float[] floatArray0 = new float[8];
      floatArray0[1] = Float.NaN;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertArrayEquals(new float[] {0.0F, Float.NaN, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertEquals(8, floatArray0.length);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test268()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(-0.0F, Float.POSITIVE_INFINITY);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test269()  throws Throwable  {
      int int0 = MathUtils.compareTo(Double.NEGATIVE_INFINITY, 80, (-489.335885));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test270()  throws Throwable  {
      int int0 = MathUtils.compareTo((-1308.5576), (-1308.5576), 212.667);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test271()  throws Throwable  {
      int int0 = MathUtils.compareTo(3677.8220959613, 697.0, 697.0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test272()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-4915), (-4915));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -4,915
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test273()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((byte)3, 31);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 31, n = 3
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test274()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(603204, 260);
      assertEquals(2271.070138481004, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test275()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(18, (-359));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test276()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(2401, 0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test277()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(239, 239);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test278()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(781, 373);
      assertEquals(1.6581673004581443E233, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test279()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(250, (-100));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test280()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(0, 0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test281()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(75668880, 1799);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test282()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(55, 31);
      assertEquals(2.4885895447413E15, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test283()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(854, (-1433));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test284()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(837, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test285()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(0, (-329));
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test286()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(836, 836);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test287()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((long) (byte) (-1), (long) 2401);
      assertEquals((-2402L), long0);
  }

  @Test(timeout = 4000)
  public void test288()  throws Throwable  {
      int int0 = MathUtils.addAndCheck((int) (byte)1, 268);
      assertEquals(269, int0);
  }

  @Test(timeout = 4000)
  public void test289()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      double[][] doubleArray1 = new double[1][2];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test290()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double[][] doubleArray1 = new double[9][9];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 9 != 2
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test291()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle(0.0, 2.4384203044354907E-8);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test292()  throws Throwable  {
      double double0 = MathUtils.sinh((-986.5607739740426));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test293()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((double) 0L, (double) 0L);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test294()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(2, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test295()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(961, int0);
  }

  @Test(timeout = 4000)
  public void test296()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float) 637, Float.NaN);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test297()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertEquals(0, intArray1.length);
      assertNotSame(intArray1, intArray0);
  }

  @Test(timeout = 4000)
  public void test298()  throws Throwable  {
      int int0 = MathUtils.hash(0.16666666666666666);
      assertEquals(1787822080, int0);
  }

  @Test(timeout = 4000)
  public void test299()  throws Throwable  {
      double double0 = MathUtils.log(36.583433482, 1934.199079275874);
      assertEquals(2.1023052744425215, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test300()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      // Undeclared exception!
      try { 
        MathUtils.copyOf(doubleArray0, (-2158));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test301()  throws Throwable  {
      double double0 = MathUtils.round(531.0, 10);
      assertEquals(531.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test302()  throws Throwable  {
      MathUtils.cosh(1928.401);
  }
}
