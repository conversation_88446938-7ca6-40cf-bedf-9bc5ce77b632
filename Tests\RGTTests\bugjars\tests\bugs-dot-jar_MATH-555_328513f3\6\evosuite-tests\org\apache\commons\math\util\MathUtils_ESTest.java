/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 11:52:28 GMT 2019
 */

package org.apache.commons.math.util;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.math.BigInteger;
import org.apache.commons.math.exception.util.Localizable;
import org.apache.commons.math.exception.util.LocalizedFormats;
import org.apache.commons.math.util.MathUtils;
import org.apache.commons.math.util.Pair;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MathUtils_ESTest extends MathUtils_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[1] = (-292.462656857637);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly decreasing (-292.463 <= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 1773.188887221397;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (1,773.189 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0L);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotNull(bigInteger1);
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotNull(bigInteger1);
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.036;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 0.036);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.036}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.036}, doubleArray1, 0.01);
      assertNotNull(doubleArray1);
      assertEquals(1, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      int int0 = MathUtils.lcm((-5008), (int) (short) (-5186));
      assertEquals(12985744, int0);
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      short short0 = MathUtils.indicator((short)0);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      long long0 = MathUtils.indicator(0L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      int int0 = MathUtils.indicator(0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      double double0 = MathUtils.indicator(0.0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)0);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      double double0 = MathUtils.factorialLog(21);
      assertEquals(45.38013889847691, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(20);
      assertEquals(2.43290200817664E18, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double[] doubleArray1 = new double[5];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double[] doubleArray1 = new double[7];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(1071.0, (double) 4194304, 4194304);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(0.0, 0.0, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((double) 1, (-2365.8631), 1.304E19);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      float[] floatArray0 = new float[2];
      float[] floatArray1 = new float[7];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
      assertArrayEquals(new float[] {0.0F, 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray1, 0.01F);
      assertEquals(2, floatArray0.length);
      assertEquals(7, floatArray1.length);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      float[] floatArray0 = new float[6];
      float[] floatArray1 = new float[5];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray1);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray1, 0.01F);
      assertEquals(6, floatArray0.length);
      assertEquals(5, floatArray1.length);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(1.4E-45F, 0.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 594L, (float) 0L, (float) 594L);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, (float) 1L, (float) 1L);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      int int0 = MathUtils.addAndCheck(Integer.MIN_VALUE, 0);
      assertEquals(Integer.MIN_VALUE, int0);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      MathUtils.checkOrder(doubleArray0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(721L, 721L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(646L, (-2668L));
      assertEquals(3314L, long0);
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(1122, 1122);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(8, (-504));
      assertEquals(512, int0);
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      double double0 = MathUtils.sinh(61);
      assertEquals(1.55214896785096E26, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      double double0 = MathUtils.sinh((-2964.371));
      assertEquals(Double.NEGATIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, 0);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      float float0 = MathUtils.round(1617.0F, 0);
      assertEquals(1617.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      double double0 = MathUtils.round(0.0, 172, 0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      double double0 = MathUtils.round((-467.78774070668), 724, (int) (byte)1);
      assertEquals((-467.78774070668), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      double double0 = MathUtils.round(0.0, (int) (short)3038);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      double double0 = MathUtils.round((double) 2245.6152F, 48);
      assertEquals(2245.615234375, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short) (-7168), bigInteger1.shortValue());
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotNull(bigInteger1);
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotNull(bigInteger1);
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte)121;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals((short)121, bigInteger0.shortValue());
      assertEquals((byte)121, bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte)121}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals(1, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)121, bigInteger0.shortValue());
      assertEquals((byte)121, bigInteger0.byteValue());
      assertEquals((short)14265, bigInteger1.shortValue());
      assertEquals((byte) (-71), bigInteger1.byteValue());
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertArrayEquals(new byte[] {(byte)121}, byteArray0);
      assertNotNull(bigInteger1);
      assertEquals(1, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      byteArray0[7] = (byte) (-104);
      byteArray0[8] = (byte) (-53);
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals((short) (-26421), bigInteger0.shortValue());
      assertEquals((byte) (-53), bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte) (-104), (byte) (-53)}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals(9, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (long) (byte)7);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short) (-26421), bigInteger0.shortValue());
      assertEquals((byte) (-53), bigInteger0.byteValue());
      assertEquals((byte)3, bigInteger1.byteValue());
      assertEquals((short) (-30461), bigInteger1.shortValue());
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte) (-104), (byte) (-53)}, byteArray0);
      assertNotNull(bigInteger1);
      assertEquals(9, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byteArray0[5] = (byte)73;
      byteArray0[6] = (byte)77;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals((byte)77, bigInteger0.byteValue());
      assertEquals((short)18765, bigInteger0.shortValue());
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)73, (byte)77}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals(7, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 12);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((byte)77, bigInteger0.byteValue());
      assertEquals((short)18765, bigInteger0.shortValue());
      assertEquals((byte) (-79), bigInteger1.byteValue());
      assertEquals((short) (-29519), bigInteger1.shortValue());
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)73, (byte)77}, byteArray0);
      assertNotNull(bigInteger1);
      assertEquals(7, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      long long0 = MathUtils.pow(658L, 1093L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      long long0 = MathUtils.pow(4294967295L, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      long long0 = MathUtils.pow(4398046511103L, 206);
      assertEquals((-905997581287423L), long0);
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      int int0 = MathUtils.pow(0, (long) 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      int int0 = MathUtils.pow(119, 1577L);
      assertEquals((-1274425801), int0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      int int0 = MathUtils.pow(12985744, 12985744);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      int int0 = MathUtils.pow(0, 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle(0.0, 0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle((-1157.45341432), (-1157.45341432));
      assertEquals((-1157.45341432), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck((-348), (-348));
      assertEquals(121104, int0);
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck((-89), 115);
      assertEquals((-10235), int0);
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      double double0 = MathUtils.log(0.0, 1.0);
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      double double0 = MathUtils.log(60.882082, 60.882082);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      double double0 = MathUtils.log(2.2250738585072014E-308, 256);
      assertEquals((-0.007827788649706457), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      int int0 = MathUtils.hash((double[]) null);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[2] = (double) (-1L);
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals((-2052794209), int0);
      assertArrayEquals(new double[] {0.0, 0.0, (-1.0), 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      int int0 = MathUtils.hash((double) 0.0F);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      int int0 = MathUtils.hash((double) 1);
      assertEquals(1072693248, int0);
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      double double0 = MathUtils.factorialLog((short)0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(1246.0F, 1246.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(2983.265139449325, 2983.265139449325);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3006.6308);
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-0.16624879837036133));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {(-3006.6308), 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-0.16624879837036133), 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertNotNull(doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      
      double double0 = MathUtils.distanceInf(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(3006.4645512016295, double0, 0.01);
      assertArrayEquals(new double[] {(-3006.6308), 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-0.16624879837036133), 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-3192.0);
      double[] doubleArray1 = new double[7];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double0 = MathUtils.distance1(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(3192.0, double0, 0.01);
      assertArrayEquals(new double[] {(-3192.0), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      int[] intArray0 = new int[2];
      intArray0[0] = (-8988076);
      int[] intArray1 = new int[6];
      assertFalse(intArray1.equals((Object)intArray0));
      
      double double0 = MathUtils.distance(intArray0, intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(8988076.0, double0, 0.01);
      assertArrayEquals(new int[] {(-8988076), 0}, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0}, intArray1);
      assertEquals(2, intArray0.length);
      assertEquals(6, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      double[] doubleArray1 = new double[4];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (-519.0);
      double double0 = MathUtils.distance(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(519.0, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-519.0), 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      int[] intArray0 = new int[1];
      int[] intArray1 = MathUtils.copyOf(intArray0, 20);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {0}, intArray0);
      assertNotNull(intArray1);
      assertEquals(1, intArray0.length);
      assertEquals(20, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      int[] intArray0 = new int[1];
      int[] intArray1 = MathUtils.copyOf(intArray0, 0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {0}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertNotNull(intArray1);
      assertEquals(1, intArray0.length);
      assertEquals(0, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertNotNull(intArray1);
      assertEquals(0, intArray0.length);
      assertEquals(0, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertNotNull(doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(0L, 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(0L, (-4193L));
      assertEquals((-4193L), long0);
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      int int0 = MathUtils.addAndCheck((-355), (-355));
      assertEquals((-710), int0);
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.subAndCheck(7278142539171886712L, (-3958705157555305932L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in addition: 3,958,705,157,555,305,932 + 7,278,142,539,171,886,712
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.safeNorm((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.round(0.0, (int) (byte)107, 1766);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, (BigInteger) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 1784L);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 2279);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray((double[]) null, (-2645.375));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      // Undeclared exception!
      MathUtils.factorialLog(2147483314);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(1186.61F, (float) 0, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      int[] intArray0 = new int[2];
      int[] intArray1 = new int[1];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      double[] doubleArray1 = new double[6];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 6
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance1((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      int[] intArray0 = new int[3];
      int[] intArray1 = new int[1];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance1(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance1((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      double[] doubleArray0 = new double[21];
      double[] doubleArray1 = new double[9];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance1(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 9
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      int[] intArray0 = new int[4];
      int[] intArray1 = new int[5];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance(intArray1, intArray0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      double[] doubleArray1 = new double[0];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null, (int) (byte)56);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null, 2155);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      // Undeclared exception!
      try { 
        MathUtils.copyOf(doubleArray0, (-1));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertNotNull(doubleArray1);
      assertEquals(6, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray1, mathUtils_OrderDirection0, false);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkFinite((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-1312), (-1312));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -1,312
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog(3, 1278);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1,278, n = 3
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-1), (-1241));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble(8, 863);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 863, n = 8
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[5][9];
      double[] doubleArray2 = new double[6];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray1[4] = doubleArray2;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 6 != 9
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[5][9];
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(9, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 3.834E-20;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly decreasing (0 <= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-513.23485809);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((long) 3527, 87178291200L);
      assertEquals(307477833062400L, long0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(4657529056235880448L, 4657529056235880448L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-2413L), (-984L));
      assertEquals(2374392L, long0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-1756L), (-2147481052L));
      assertEquals(3770976727312L, long0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((-1016266752), (-990));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(373, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      float float0 = MathUtils.indicator((float) (-2863221430593058543L));
      assertEquals((-1.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      int int0 = MathUtils.gcd(1278, 723);
      assertEquals(3, int0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      int int0 = MathUtils.gcd(2, 2);
      assertEquals(2, int0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-1390.9499904250247), 1888.9, 201);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-3392.9622761633), (-3392.9622761633), 1);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals((-785.8), (-1.0), 1159131136);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(2.2250738585072014E-308, 2.2250738585072014E-308, 2596);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, 0.0, 1098.894072);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, (-1356.048658), 0.0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(6.283185307179586, 3029.02956019);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, 0.0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F, 23);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-1.0F), (-1460.0F), 2596);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1259.37F), Float.POSITIVE_INFINITY);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient((short)0, (-106));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      int[] intArray0 = new int[2];
      // Undeclared exception!
      try { 
        MathUtils.copyOf(intArray0, (-2147145168));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(2749.782011363052, (-309.75724709726));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 863);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertNotNull(doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertEquals(863, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      Integer integer0 = new Integer(1);
      assertEquals(1, (int)integer0);
      assertNotNull(integer0);
      
      Double double0 = new Double(1);
      assertEquals(1.0, (double)double0, 0.01);
      assertNotNull(double0);
      
      Pair<Integer, Double> pair0 = new Pair<Integer, Double>(integer0, double0);
      assertNotNull(pair0);
      
      MathUtils.checkNotNull((Object) pair0);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      LocalizedFormats localizedFormats0 = LocalizedFormats.OVERFLOW_IN_ADDITION;
      assertEquals("overflow in addition: {0} + {1}", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.OVERFLOW_IN_ADDITION, localizedFormats0);
      
      Object[] objectArray0 = new Object[2];
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null, (Localizable) localizedFormats0, objectArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in addition: null + null
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      Double double0 = new Double(0.041666666666666664);
      assertEquals(0.041666666666666664, (double)double0, 0.01);
      assertNotNull(double0);
      
      Pair<Double, String> pair0 = new Pair<Double, String>(double0, "");
      assertNotNull(pair0);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.REAL_FORMAT;
      assertEquals("real format", localizedFormats0.getSourceString());
      assertEquals(LocalizedFormats.REAL_FORMAT, localizedFormats0);
      
      Object[] objectArray0 = new Object[3];
      MathUtils.checkNotNull((Object) pair0, (Localizable) localizedFormats0, objectArray0);
      assertEquals("real format", localizedFormats0.getSourceString());
      assertEquals(3, objectArray0.length);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[0][8];
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      double[][] doubleArray1 = new double[6][7];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double[][] doubleArray1 = new double[2][9];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 9 != 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray0 = new double[6][5];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, mathUtils_OrderDirection0, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[1] = 2.2250738585072014E-308;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(2.2250738585072014E-308, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 2.2250738585072014E-308, 0.0}, doubleArray0, 0.01);
      assertEquals(3, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) (-4267615245585081135L);
      doubleArray0[2] = (double) (-4267615245585081135L);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(6.0353193592966093E18, double0, 0.01);
      assertArrayEquals(new double[] {(-4.2676152455850813E18), 0.0, (-4.2676152455850813E18), 0.0}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 498;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(498.0, double0, 0.01);
      assertArrayEquals(new double[] {498.0, 0.0}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) Float.NaN;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // value \uFFFD at index 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(Double.NaN);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // \uFFFD is not a finite number
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      MathUtils.checkFinite(0.0);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[1] = (double) (-1346);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertFalse(boolean0);
      assertArrayEquals(new double[] {0.0, (-1346.0), 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 1906;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
      assertArrayEquals(new double[] {1906.0, 0.0}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[1] = (double) 474;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not increasing (474 > 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (-982.2389516314);
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      int[] intArray0 = new int[6];
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0}, intArray0);
      assertEquals(6, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(4, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      int[] intArray0 = new int[6];
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0}, intArray0);
      assertEquals(6, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      int[] intArray0 = new int[6];
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0}, intArray0);
      assertEquals(6, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(8, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byteArray0[0] = (byte) (-121);
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertArrayEquals(new byte[] {(byte) (-121), (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals(7, byteArray0.length);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, bigInteger0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-34,058,472,181,989,376)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.valueOf(1334L);
      assertEquals((short)1334, bigInteger0.shortValue());
      assertEquals((byte)54, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertEquals((short)1334, bigInteger0.shortValue());
      assertEquals((byte)54, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertNotNull(bigInteger1);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ONE;
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-484L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-484)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 88L);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertNotNull(bigInteger1);
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ONE;
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertNotNull(bigInteger0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-919));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-919)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals(9, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 596);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray0);
      assertNotNull(bigInteger1);
      assertEquals(9, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      long long0 = MathUtils.pow((long) 17, (long) 17);
      assertEquals((-2863221430593058543L), long0);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow(362880L, (-2147483648L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-2,147,483,648)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      long long0 = MathUtils.pow(143L, 0L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((long) 536, (-1214));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,214)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      long long0 = MathUtils.pow(121645100408832000L, 86);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow(2, (-539L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-539)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      int int0 = MathUtils.pow(0, (long) 23);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((int) (short) (-1), (int) (short) (-88));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-88)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      int int0 = MathUtils.pow(6457, 6457);
      assertEquals((-885613447), int0);
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.subAndCheck(488, Integer.MIN_VALUE);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in subtraction: 488 - -2,147,483,648
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(0, 1122);
      assertEquals((-1122), int0);
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      short short0 = MathUtils.sign((short) (-1149));
      assertEquals((short) (-1), short0);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      short short0 = MathUtils.sign((short)0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      short short0 = MathUtils.sign((short)1591);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      long long0 = MathUtils.sign((long) (-37));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      long long0 = MathUtils.sign(0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      long long0 = MathUtils.sign(3628800L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      int int0 = MathUtils.sign((-2146511609));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      int int0 = MathUtils.sign(0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      int int0 = MathUtils.sign(633);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      float float0 = MathUtils.sign((-3260.0F));
      assertEquals((-1.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      float float0 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      float float0 = MathUtils.sign((float) 5721);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      double double0 = MathUtils.sign((double) (byte)31);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      double double0 = MathUtils.sign(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      double double0 = MathUtils.sign((-1559.91314566));
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte) (-1));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)10);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      float float0 = MathUtils.round((float) 373, 2957, (int) (byte)1);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      float float0 = MathUtils.round(2128.13F, 3807, 0);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      double double0 = MathUtils.round(Double.POSITIVE_INFINITY, 159, (-4456));
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, (-2.356194490192345));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[5] = Double.POSITIVE_INFINITY;
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, (-1819.40238058101));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // Array contains an infinite element, \u221E at index 5
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(0L, 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.lcm((long) 12, (-9223372036854775808L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.lcm((-8L), (-3512299194304650054L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      long long0 = MathUtils.lcm((-594L), (-1755L));
      assertEquals(38610L, long0);
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((long) (-1241), (long) (byte)1);
      assertEquals((-1241L), long0);
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.lcm(2445960, 2144616130);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.lcm(4904, Integer.MIN_VALUE);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      long long0 = MathUtils.lcm((-2667L), 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      long long0 = MathUtils.lcm(0L, 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      int int0 = MathUtils.lcm(236, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      int int0 = MathUtils.lcm(0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      short short0 = MathUtils.indicator((short)26);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      short short0 = MathUtils.indicator((short) (-5186));
      assertEquals((short) (-1), short0);
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      long long0 = MathUtils.indicator((long) (byte)1);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
      long long0 = MathUtils.indicator((long) (-37));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      int int0 = MathUtils.indicator(2064942977);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      int int0 = MathUtils.indicator((-3986));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      float float0 = MathUtils.indicator((float) 0L);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test226()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.round(Float.NaN, (-1883), (-1883));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method -1,883, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test227()  throws Throwable  {
      double double0 = MathUtils.indicator((-1327.2394998));
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test228()  throws Throwable  {
      double double0 = MathUtils.indicator((double) 3L);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test229()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)94);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test230()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte) (-81));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test231()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.gcd((-9223372036854775808L), (-9223372036854775808L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow: gcd(-9,223,372,036,854,775,808, -9,223,372,036,854,775,808) is 2^63
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test232()  throws Throwable  {
      long long0 = MathUtils.lcm(4657529056235880448L, 4657529056235880448L);
      assertEquals(4657529056235880448L, long0);
  }

  @Test(timeout = 4000)
  public void test233()  throws Throwable  {
      long long0 = MathUtils.gcd((long) 196, (long) 2415);
      assertEquals(7L, long0);
  }

  @Test(timeout = 4000)
  public void test234()  throws Throwable  {
      long long0 = MathUtils.gcd((-2991L), (-2991L));
      assertEquals(2991L, long0);
  }

  @Test(timeout = 4000)
  public void test235()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.gcd((long) 0, (-9223372036854775808L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow: gcd(0, -9,223,372,036,854,775,808) is 2^63
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test236()  throws Throwable  {
      long long0 = MathUtils.gcd((-594L), 0L);
      assertEquals(594L, long0);
  }

  @Test(timeout = 4000)
  public void test237()  throws Throwable  {
      long long0 = MathUtils.gcd(0L, (long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test238()  throws Throwable  {
      int int0 = MathUtils.gcd((-89), (-89));
      assertEquals(89, int0);
  }

  @Test(timeout = 4000)
  public void test239()  throws Throwable  {
      int int0 = MathUtils.gcd((-942), 0);
      assertEquals(942, int0);
  }

  @Test(timeout = 4000)
  public void test240()  throws Throwable  {
      int int0 = MathUtils.gcd(0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test241()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorialLog((-727));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -727
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test242()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorialDouble((-355));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -355
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test243()  throws Throwable  {
      // Undeclared exception!
      MathUtils.factorialDouble(1073741824);
  }

  @Test(timeout = 4000)
  public void test244()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorial(30);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test245()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorial((-2513));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -2,513
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test246()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (-3006.6308);
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-0.16624879837036133));
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {(-3006.6308), 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-0.16624879837036133), 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertNotNull(doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
      assertArrayEquals(new double[] {(-3006.6308), 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-0.16624879837036133), 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test247()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double[] doubleArray1 = new double[3];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test248()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, (double[]) null);
      assertFalse(boolean0);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test249()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double[]) null, (double[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test250()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      double[] doubleArray1 = new double[0];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(boolean0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertEquals(3, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test251()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      boolean boolean0 = MathUtils.equals((double[]) null, doubleArray0);
      assertFalse(boolean0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertEquals(0, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test252()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      boolean boolean0 = MathUtils.equals(doubleArray0, (double[]) null);
      assertFalse(boolean0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test253()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((double[]) null, (double[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test254()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(3.834E-20, 3.834E-20, 5);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test255()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 7L, (double) 0, 2219);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test256()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = Double.NaN;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertFalse(boolean0);
      assertArrayEquals(new double[] {Double.NaN, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(8, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test257()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(3001.0, 0.0, (-2571));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test258()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(2.3841857910019882E-8, 188.416876, 2486.9028200624);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test259()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((double) 2064942977, 0.0, (double) 2064942977);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test260()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Double.NaN, Double.NaN, 1177.605954173);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test261()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Double.NaN, 2054.430338486974, Double.NaN);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test262()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(5, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test263()  throws Throwable  {
      float[] floatArray0 = new float[6];
      float[] floatArray1 = new float[2];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {0.0F, 0.0F}, floatArray1, 0.01F);
      assertEquals(6, floatArray0.length);
      assertEquals(2, floatArray1.length);
  }

  @Test(timeout = 4000)
  public void test264()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float[]) null, (float[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test265()  throws Throwable  {
      float[] floatArray0 = new float[3];
      floatArray0[1] = Float.NaN;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertFalse(boolean0);
      assertArrayEquals(new float[] {0.0F, Float.NaN, 0.0F}, floatArray0, 0.01F);
      assertEquals(3, floatArray0.length);
  }

  @Test(timeout = 4000)
  public void test266()  throws Throwable  {
      float[] floatArray0 = new float[5];
      float[] floatArray1 = new float[0];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equals(floatArray1, floatArray0);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertFalse(boolean0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {}, floatArray1, 0.01F);
      assertEquals(5, floatArray0.length);
      assertEquals(0, floatArray1.length);
  }

  @Test(timeout = 4000)
  public void test267()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float[]) null, (float[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test268()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1497.661F), (-1497.661F), 1153);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test269()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-3257.634F), (float) 91473258495L, 2188);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test270()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Float.NaN, Float.NaN);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test271()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals((-1.0F), (float) (short) (-1), 2143760137);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test272()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(3.4028235E38F, 0.0F, (-654));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test273()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-1350.4F), (-634.5654F), 1880.67F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test274()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(4073.0F, 4073.0F, 0.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test275()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float) (byte)121, (-1.0F), 1756.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test276()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-2128.13F), 0.0F, 0.0F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test277()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Float.POSITIVE_INFINITY, Float.POSITIVE_INFINITY, Float.POSITIVE_INFINITY);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test278()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(1502.0F, 4813.9717F, 0.0F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test279()  throws Throwable  {
      float[] floatArray0 = new float[9];
      floatArray0[4] = Float.NaN;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, Float.NaN, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertEquals(9, floatArray0.length);
  }

  @Test(timeout = 4000)
  public void test280()  throws Throwable  {
      int int0 = MathUtils.compareTo(1617.0F, 1797.3453595120343, (-81.8F));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test281()  throws Throwable  {
      int int0 = MathUtils.compareTo((-2301.23108884), (-2301.23108884), 1646.86634);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test282()  throws Throwable  {
      int int0 = MathUtils.compareTo(721L, (-2128.13F), (-0.010714690733195933));
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test283()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-3521), (-3521));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -3,521
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test284()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-681), 1030);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1,030, n = -681
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test285()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(115, (-97));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test286()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(0, (-97));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test287()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(373, (byte)1);
      assertEquals(5.921578419643816, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test288()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(2147483314, 197);
      assertEquals(3385.6976940794907, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test289()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(1, 0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test290()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(2147483314, 2147483314);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test291()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(68, 5);
      assertEquals(1.0424128E7, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test292()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(56, 1);
      assertEquals(56.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test293()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(131, 0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test294()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(0, 0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test295()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(1645, 6);
      assertEquals(27270830520368260L, long0);
  }

  @Test(timeout = 4000)
  public void test296()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(64, (-22));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test297()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(1247, 842);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test298()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(655, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test299()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble((byte)31, 15);
      assertEquals(3.00540195E8, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test300()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(1462, 1462);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test301()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(4577762542105553359L, 0L);
      assertEquals(4577762542105553359L, long0);
  }

  @Test(timeout = 4000)
  public void test302()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((-2604L), (long) 633);
      assertEquals((-3237L), long0);
  }

  @Test(timeout = 4000)
  public void test303()  throws Throwable  {
      int int0 = MathUtils.addAndCheck(0, 14);
      assertEquals(14, int0);
  }

  @Test(timeout = 4000)
  public void test304()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.addAndCheck((-2146603891), (-1841400967));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in addition: -2,146,603,891 + -1,841,400,967
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test305()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle(1.0, 0.0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test306()  throws Throwable  {
      double double0 = MathUtils.sinh(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test307()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.addAndCheck(4657529056235880448L, 4657529056235880448L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in addition: 4,657,529,056,235,880,448 + 4,657,529,056,235,880,448
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test308()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test309()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(961, int0);
  }

  @Test(timeout = 4000)
  public void test310()  throws Throwable  {
      float[] floatArray0 = new float[3];
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test311()  throws Throwable  {
      int[] intArray0 = new int[6];
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray1, intArray0);
      assertEquals(6, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test312()  throws Throwable  {
      int int0 = MathUtils.hash(0.008336750013465571);
      assertEquals((-1697889128), int0);
  }

  @Test(timeout = 4000)
  public void test313()  throws Throwable  {
      double double0 = MathUtils.round((-771.10956), 1250);
      assertEquals((-771.10956), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test314()  throws Throwable  {
      double double0 = MathUtils.cosh(0L);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test315()  throws Throwable  {
      float float0 = MathUtils.round((-4777.7124F), 12);
      assertEquals((-4777.7124F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test316()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }
}
