/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 12:00:28 GMT 2019
 */

package org.apache.commons.math.util;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.math.BigInteger;
import org.apache.commons.math.exception.util.Localizable;
import org.apache.commons.math.exception.util.LocalizedFormats;
import org.apache.commons.math.util.MathUtils;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MathUtils_ESTest extends MathUtils_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = 3.834E-20;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertArrayEquals(new double[] {3.834E-20, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(3.834E-20, double0, 0.01);
      assertEquals(9, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[1] = (-668.457541425);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, false);
      assertArrayEquals(new double[] {0.0, (-668.457541425), 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertFalse(boolean0);
      assertEquals(7, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 51;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (51 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      BigInteger bigInteger0 = MathUtils.pow((BigInteger) null, 0L);
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      int int0 = MathUtils.pow(1, 0L);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[3] = (-886.895614432503);
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-1389.1019));
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, (-886.895614432503)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, (-1389.1019)}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      short short0 = MathUtils.indicator((short)269);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      long long0 = MathUtils.indicator((long) 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      int int0 = MathUtils.indicator(0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      double double0 = MathUtils.indicator((double) 0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)0);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      double double0 = MathUtils.factorialLog(21);
      assertEquals(45.38013889847691, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      double[] doubleArray1 = new double[1];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(boolean0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(6, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 223);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(9, doubleArray0.length);
      assertEquals(223, doubleArray1.length);
      
      boolean boolean0 = MathUtils.equals(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(boolean0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(9, doubleArray0.length);
      assertEquals(223, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN((double) 250, Double.POSITIVE_INFINITY, (-2145353206));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(1994.39535, 0.0, 1994.39535);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      float[] floatArray0 = new float[1];
      float[] floatArray1 = new float[0];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertArrayEquals(new float[] {0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {}, floatArray1, 0.01F);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(boolean0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertEquals(1, floatArray0.length);
      assertEquals(0, floatArray1.length);
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      float[] floatArray0 = new float[8];
      float[] floatArray1 = new float[0];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray1);
      assertArrayEquals(new float[] {0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {}, floatArray1, 0.01F);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(boolean0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertEquals(8, floatArray0.length);
      assertEquals(0, floatArray1.length);
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN((float) 0, -0.0F, (-275));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(870.0F, 806.1F, 2405.43F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float) 0, 625.0F, 948.769F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(0L, 1L);
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[1] = 2371.5549966301;
      MathUtils.checkOrder(doubleArray0);
      assertArrayEquals(new double[] {0.0, 2371.5549966301}, doubleArray0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((long) 1136, (long) (-20));
      assertEquals(1156L, long0);
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((-1218L), (-887L));
      assertEquals((-331L), long0);
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(9958, 9958);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(1794, (-1230));
      assertEquals(3024, int0);
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      double double0 = MathUtils.sinh(0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      double double0 = MathUtils.sinh(3.141592653589793);
      assertEquals(11.548739357257748, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      float float0 = MathUtils.round((-1.0F), 0, 0);
      assertEquals((-2.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, 0);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      float float0 = MathUtils.round((float) 231, 0);
      assertEquals(231.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      double double0 = MathUtils.round((-2940.5), 23, 2);
      assertEquals((-2940.5), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      double double0 = MathUtils.round(2415.8094394368, (-1438));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      double double0 = MathUtils.round((-1623.8855872378), 0);
      assertEquals((-1624.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte)51;
      BigInteger bigInteger1 = new BigInteger(byteArray0);
      assertArrayEquals(new byte[] {(byte)51}, byteArray0);
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)51, bigInteger1.shortValue());
      assertEquals((byte)51, bigInteger1.byteValue());
      assertEquals(1, byteArray0.length);
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, bigInteger1);
      assertArrayEquals(new byte[] {(byte)51}, byteArray0);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotNull(bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)51, bigInteger1.shortValue());
      assertEquals((byte)51, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      assertEquals(1, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      byteArray0[8] = (byte) (-8);
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte) (-8)}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((byte) (-8), bigInteger0.byteValue());
      assertEquals((short)248, bigInteger0.shortValue());
      assertEquals(9, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (long) (byte)2);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte) (-8)}, byteArray0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte) (-8), bigInteger0.byteValue());
      assertEquals((short)248, bigInteger0.shortValue());
      assertEquals((byte)64, bigInteger1.byteValue());
      assertEquals((short) (-4032), bigInteger1.shortValue());
      assertEquals(9, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      byte[] byteArray0 = new byte[7];
      byteArray0[6] = (byte) (-82);
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte) (-82)}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((short)174, bigInteger0.shortValue());
      assertEquals((byte) (-82), bigInteger0.byteValue());
      assertEquals(7, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (int) (short)1);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte) (-82)}, byteArray0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)174, bigInteger0.shortValue());
      assertEquals((byte) (-82), bigInteger0.byteValue());
      assertEquals((short)174, bigInteger1.shortValue());
      assertEquals((byte) (-82), bigInteger1.byteValue());
      assertEquals(7, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      long long0 = MathUtils.pow((-5847L), 0L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      long long0 = MathUtils.pow(6722166367014452318L, (long) 31);
      assertEquals((-4362616503393058816L), long0);
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      long long0 = MathUtils.pow(0L, 212);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      long long0 = MathUtils.pow(39916800L, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      int int0 = MathUtils.pow((-218), 4451L);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      int int0 = MathUtils.pow((-123), 712426L);
      assertEquals((-238564743), int0);
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      int int0 = MathUtils.pow(1474, 1474);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      int int0 = MathUtils.pow(1123, 229);
      assertEquals((-565997229), int0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle(0, 0.0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle((short) (-611), 0.0);
      assertEquals((-1.5310252035801568), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(1, (-938));
      assertEquals((-938), int0);
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      double double0 = MathUtils.log(0.0, 637.249278);
      assertEquals(-0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      double double0 = MathUtils.log(1341.496814131, Double.POSITIVE_INFINITY);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      double double0 = MathUtils.log(862.0, 0.19999999999923582);
      assertEquals((-0.23810876316850366), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      int int0 = MathUtils.hash((double[]) null);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      int int0 = MathUtils.hash(doubleArray0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(31, int0);
      assertEquals(1, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      int int0 = MathUtils.hash((double) 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      int int0 = MathUtils.hash((-628.224743096));
      assertEquals((-2036851873), int0);
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float) 0, 0.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, 0.0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      int[] intArray0 = new int[6];
      intArray0[1] = 378;
      int[] intArray1 = new int[5];
      assertFalse(intArray1.equals((Object)intArray0));
      
      int int0 = MathUtils.distanceInf(intArray1, intArray0);
      assertArrayEquals(new int[] {0, 378, 0, 0, 0, 0}, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0}, intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(378, int0);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(6, intArray0.length);
      assertEquals(5, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (-5219.308736722418);
      double[] doubleArray1 = new double[6];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double0 = MathUtils.distance1(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {(-5219.308736722418), 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(5219.308736722418, double0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(5, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      int[] intArray0 = new int[2];
      intArray0[1] = 3;
      int[] intArray1 = new int[6];
      assertFalse(intArray1.equals((Object)intArray0));
      
      double double0 = MathUtils.distance(intArray0, intArray1);
      assertArrayEquals(new int[] {0, 3}, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0}, intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertEquals(3.0, double0, 0.01);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(2, intArray0.length);
      assertEquals(6, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) 269;
      double[] doubleArray1 = new double[3];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      double double0 = MathUtils.distance(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {269.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(269.0, double0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      int[] intArray0 = new int[3];
      int[] intArray1 = MathUtils.copyOf(intArray0, 0);
      assertArrayEquals(new int[] {0, 0, 0}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(3, intArray0.length);
      assertEquals(0, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      int[] intArray0 = new int[6];
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0}, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0}, intArray1);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(6, intArray0.length);
      assertEquals(6, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(89, (short)14);
      assertEquals(36.571206557417185, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(0L, 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      int int0 = MathUtils.addAndCheck(0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      int int0 = MathUtils.addAndCheck((-3561), (-3561));
      assertEquals((-7122), int0);
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      double[][] doubleArray1 = new double[1][0];
      doubleArray1[0] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, (MathUtils.OrderDirection) null, doubleArray1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils$1", e);
      }
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.safeNorm((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.round((-456.0), 210, 8);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.round((-261.8194053966579), 2080374784, 2);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // BigInteger would overflow supported range
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, (BigInteger) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 3411L);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray((double[]) null, 4.503599627370496E15);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      // Undeclared exception!
      MathUtils.factorialDouble(838253);
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance1((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      int[] intArray0 = new int[6];
      int[] intArray1 = new int[0];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance1(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance1((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      double[] doubleArray1 = new double[4];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance1(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      int[] intArray0 = new int[3];
      int[] intArray1 = new int[2];
      assertFalse(intArray1.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance(intArray0, intArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 2
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = new double[4];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance(doubleArray1, doubleArray0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      int[] intArray0 = new int[2];
      // Undeclared exception!
      try { 
        MathUtils.copyOf(intArray0, (-1230));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null, 157);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      // Undeclared exception!
      try { 
        MathUtils.copyOf(doubleArray0, (-26));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkFinite((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-110), (-110));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -110
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble(103, 1199);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1,199, n = 103
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-2703), (-2703));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -2,703
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-1419), (-75));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = -75, n = -1,419
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[7][8];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 8 != 7
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(1679L, 4660119505630920704L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(2370L, 2370L);
      assertEquals(5616900L, long0);
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((long) (-20), 1065L);
      assertEquals((-21300L), long0);
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((long) 90, (long) 1);
      assertEquals(90L, long0);
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(12, 1008);
      assertEquals(12096, int0);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      float float0 = MathUtils.indicator((float) 2370L);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      long long0 = MathUtils.gcd(2147483647L, (-3025L));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      int int0 = MathUtils.gcd((-480), 2142);
      assertEquals(6, int0);
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      int int0 = MathUtils.gcd(90, 12);
      assertEquals(6, int0);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      // Undeclared exception!
      MathUtils.factorialLog(2143431792);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, 0.0, 229);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-1358.155752734), (double) (byte) (-26), 311);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(8.0E298, (-2711.547851320221), 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, (double) 1L, 29);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(826.4, (-0.5), 1007.816);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((double) 0, (double) 0, (-3204.6718366518503));
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-695.25373414846), (-1189.574474), (-623.7179685565));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) (-1730), (-4643.0));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F, 196);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Float.NEGATIVE_INFINITY, 1501.1F, 14);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals((float) 1938L, 2820.0F, 2049870754);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(1561.0F, (-955.385F), 1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-3.4028235E38F), (-3.4028235E38F));
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(85, 31);
      assertEquals(1.484099803308449E23, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(0, (-2845));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertEquals(1, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      int[] intArray0 = new int[3];
      int[] intArray1 = MathUtils.copyOf(intArray0, 11);
      assertArrayEquals(new int[] {0, 0, 0}, intArray0);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertNotNull(intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      assertEquals(3, intArray0.length);
      assertEquals(11, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float) 6, (float) 1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-1.0), 0.0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(7, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      Integer integer0 = new Integer(197);
      assertNotNull(integer0);
      assertEquals(197, (int)integer0);
      
      MathUtils.checkNotNull((Object) integer0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      LocalizedFormats localizedFormats0 = LocalizedFormats.VECTOR_LENGTH_MISMATCH;
      assertEquals(LocalizedFormats.VECTOR_LENGTH_MISMATCH, localizedFormats0);
      assertEquals("vector length mismatch: got {0} but expected {1}", localizedFormats0.getSourceString());
      
      Object[] objectArray0 = new Object[1];
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null, (Localizable) localizedFormats0, objectArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // vector length mismatch: got null but expected {1}
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      Object object0 = new Object();
      assertNotNull(object0);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.NOT_ENOUGH_DATA_FOR_NUMBER_OF_PREDICTORS;
      assertEquals(LocalizedFormats.NOT_ENOUGH_DATA_FOR_NUMBER_OF_PREDICTORS, localizedFormats0);
      assertEquals("not enough data ({0} rows) for this many predictors ({1} predictors)", localizedFormats0.getSourceString());
      
      Object[] objectArray0 = new Object[4];
      MathUtils.checkNotNull(object0, (Localizable) localizedFormats0, objectArray0);
      assertEquals("not enough data ({0} rows) for this many predictors ({1} predictors)", localizedFormats0.getSourceString());
      assertEquals(4, objectArray0.length);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[7][5];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(7, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) Float.NaN;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertArrayEquals(new double[] {Double.NaN, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals(6, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[2] = 4.9E-324;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 4.9E-324, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(4.9E-324, double0, 0.01);
      assertEquals(9, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[1] = Double.POSITIVE_INFINITY;
      doubleArray0[3] = Double.POSITIVE_INFINITY;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertArrayEquals(new double[] {0.0, Double.POSITIVE_INFINITY, 0.0, Double.POSITIVE_INFINITY, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals(9, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(4, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[8] = Double.NaN;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // value \uFFFD at index 8
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[6] = Double.POSITIVE_INFINITY;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // value \u221E at index 6
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(6, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkFinite((double) Float.NaN);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // \uFFFD is not a finite number
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      MathUtils.checkFinite((-2940.52310532611));
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[2] = (double) (-236L);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 2 and 3 are not decreasing (-236 < 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[1] = (double) (short)1;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not increasing (1 > 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (-975.868);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly increasing (0 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertTrue(boolean0);
      assertEquals(4, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      int[] intArray0 = new int[6];
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0}, intArray0);
      assertEquals(0, int0);
      assertEquals(6, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(4, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      int[] intArray0 = new int[1];
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertArrayEquals(new int[] {0}, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      int[] intArray0 = new int[2];
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertArrayEquals(new int[] {0, 0}, intArray0);
      assertEquals(0, int0);
      assertEquals(2, intArray0.length);
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertEquals(0.0, double0, 0.01);
      assertEquals(9, doubleArray0.length);
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte) (-4);
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertArrayEquals(new byte[] {(byte) (-4)}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((short) (-4), bigInteger0.shortValue());
      assertEquals((byte) (-4), bigInteger0.byteValue());
      assertEquals(1, byteArray0.length);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, bigInteger0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-4)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short) (-7168), bigInteger1.shortValue());
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-625L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-625)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray0);
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals(9, byteArray0.length);
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, (long) (byte)2);
      assertArrayEquals(new byte[] {(byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0, (byte)0}, byteArray0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals(9, byteArray0.length);
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-2111));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-2,111)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 2675);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotNull(bigInteger1);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((long) (-2939), (long) (-5878));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-5,878)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      long long0 = MathUtils.pow(6227020800L, (long) 197);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      long long0 = MathUtils.pow((-301L), 21);
      assertEquals((-671333252323764029L), long0);
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((-308L), (-571));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-571)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((-1188), (-302932621132653753L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-302,932,621,132,653,753)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      int int0 = MathUtils.pow(21, 21);
      assertEquals(878082373, int0);
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow(0, (-1099));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,099)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      int int0 = MathUtils.pow((-1023), 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.subAndCheck(63, Integer.MIN_VALUE);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in subtraction: 63 - -2,147,483,648
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      int int0 = MathUtils.subAndCheck((-1023), 0);
      assertEquals((-1023), int0);
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      short short0 = MathUtils.sign((short) (-2));
      assertEquals((short) (-1), short0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      short short0 = MathUtils.sign((short)0);
      assertEquals((short)0, short0);
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      short short0 = MathUtils.sign((short)3444);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      long long0 = MathUtils.sign(4095L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      long long0 = MathUtils.sign(0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      long long0 = MathUtils.sign((-250L));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      int int0 = MathUtils.sign((-2098));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      int int0 = MathUtils.sign(0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      int int0 = MathUtils.sign(2178);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      float float0 = MathUtils.sign((-3.4028235E38F));
      assertEquals((-1.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      float float0 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      float float0 = MathUtils.sign(Float.NaN);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      float float0 = MathUtils.sign(655.29816F);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      double double0 = MathUtils.sign((-1.0));
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      double double0 = MathUtils.sign((double) 0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      double double0 = MathUtils.sign((double) 2178);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)19);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte)0);
      assertEquals((byte)0, byte0);
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte) (-26));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      float float0 = MathUtils.round((-3165.7F), 0);
      assertEquals((-3166.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.round(1.0F, (-1798), 436);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 436, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      float float0 = MathUtils.round((float) 0, 0, 6);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      float float0 = MathUtils.round((float) 0, 0, 0);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      double double0 = MathUtils.round(Double.POSITIVE_INFINITY, 311, 0);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) (byte)19;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-3760.7050670998));
      assertArrayEquals(new double[] {19.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-3760.7050670998005), -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotNull(doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(9, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray1);
      assertArrayEquals(new double[] {19.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-3760.7050670998005), -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0, -0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertEquals(3779.7050670998005, double0, 0.01);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(9, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[1] = Double.POSITIVE_INFINITY;
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, 2178.0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // Array contains an infinite element, \u221E at index 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, (-3760.7050670998));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((long) 0, 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((long) (-1075), 4636033603912859648L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-4172L), (long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((-7278142539171889152L), (long) (-984727142));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-457L), (-457L));
      assertEquals(208849L, long0);
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(Integer.MIN_VALUE, (-885));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.lcm(4194304, (-2381));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      long long0 = MathUtils.lcm((long) 242, (long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      long long0 = MathUtils.lcm((long) 0, (-881L));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      int int0 = MathUtils.lcm((-1023), 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      int int0 = MathUtils.lcm(242, 1181);
      assertEquals(285802, int0);
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      int int0 = MathUtils.lcm(0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      short short0 = MathUtils.indicator((short)0);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      short short0 = MathUtils.indicator((short) (-2600));
      assertEquals((short) (-1), short0);
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      long long0 = MathUtils.indicator(3628800L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      long long0 = MathUtils.indicator((-1324L));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      int int0 = MathUtils.indicator(2049870754);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
      int int0 = MathUtils.indicator((-1866));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      Integer integer0 = new Integer((-20));
      assertNotNull(integer0);
      assertEquals((-20), (int)integer0);
      
      float float0 = MathUtils.indicator((float) integer0);
      assertEquals((-1.0F), float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      float float0 = MathUtils.indicator(Float.NaN);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      double double0 = MathUtils.indicator((-1422.990513));
      assertEquals((-1.0), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test226()  throws Throwable  {
      double double0 = MathUtils.indicator(Double.NaN);
      assertEquals(Double.NaN, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test227()  throws Throwable  {
      double double0 = MathUtils.indicator(1730.0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test228()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)19);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test229()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte) (-117));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test230()  throws Throwable  {
      long long0 = MathUtils.gcd((long) (-2100), 2L);
      assertEquals(2L, long0);
  }

  @Test(timeout = 4000)
  public void test231()  throws Throwable  {
      long long0 = MathUtils.lcm(281L, (-960L));
      assertEquals(269760L, long0);
  }

  @Test(timeout = 4000)
  public void test232()  throws Throwable  {
      long long0 = MathUtils.lcm((-4281L), (long) 28);
      assertEquals(119868L, long0);
  }

  @Test(timeout = 4000)
  public void test233()  throws Throwable  {
      int int0 = MathUtils.gcd((-2391), (-2391));
      assertEquals(2391, int0);
  }

  @Test(timeout = 4000)
  public void test234()  throws Throwable  {
      int int0 = MathUtils.gcd((int) (short)1, 0);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test235()  throws Throwable  {
      int int0 = MathUtils.gcd(0, 0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test236()  throws Throwable  {
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test237()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorialLog((-4443));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -4,443
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test238()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test239()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorialDouble((-1190));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1,190
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test240()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorial(529);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test241()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.factorial((-2978));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -2,978
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test242()  throws Throwable  {
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test243()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      double[] doubleArray1 = new double[3];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray1, doubleArray0);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(boolean0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(6, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test244()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test245()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double[]) null, (double[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test246()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      double[] doubleArray1 = new double[7];
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test247()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((double[]) null, (double[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test248()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, 0.0, 197);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test249()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(2374.79979436539, (double) 1694, 1694);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test250()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals(1400.0, 1400.0, 1106771968);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test251()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 0, 465.4087617785106, (-0.2499999701976776));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test252()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.11111095942313305, 0.11111095942313305, 0.11111095942313305);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test253()  throws Throwable  {
      int int0 = MathUtils.compareTo(0.0, 0.0, 0.0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test254()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, (double) 0.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test255()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 45, (double) (-1359.1003F), 1872.4731097365755);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test256()  throws Throwable  {
      float[] floatArray0 = new float[0];
      float[] floatArray1 = new float[1];
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test257()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float[]) null, (float[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test258()  throws Throwable  {
      float[] floatArray0 = new float[1];
      float[] floatArray1 = new float[7];
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray1);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test259()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float[]) null, (float[]) null);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test260()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, 0.0F, 1030);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test261()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, 0.0F, 31);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test262()  throws Throwable  {
      float[] floatArray0 = new float[6];
      floatArray0[2] = Float.NaN;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test263()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equals((-494.38644F), (-494.38644F), 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test264()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, 1535.4896F, 1535.4896F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test265()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((-861.7904F), (-861.7904F), 0.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test266()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 1.0F, 1.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test267()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-4247.757F), (float) 90, 0.0F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test268()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F, 0.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test269()  throws Throwable  {
      float[] floatArray0 = new float[8];
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test270()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, (-1.0F));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test271()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, Float.NaN);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test272()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (-1324L), 1.0F, 1.0F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test273()  throws Throwable  {
      int int0 = MathUtils.compareTo((-566.28402879669), 1792.0, 1832.2);
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test274()  throws Throwable  {
      int int0 = MathUtils.compareTo((-1359.1003F), (-2940.52310532611), (-1359.1003F));
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test275()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-526548483), (-526548483));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -526,548,483
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test276()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-2340), 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 0, n = -2,340
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test277()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(790, (-204));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test278()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(865, 865);
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test279()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(85, 51);
      assertEquals(6.151673048339369E23, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test280()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(1, 0);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test281()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(93, 93);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test282()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(2675, 557);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test283()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientDouble(31, 12);
      assertEquals(1.41120525E8, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test284()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(215, (-2391));
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test285()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(21, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test286()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(28, (-363));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test287()  throws Throwable  {
      long long0 = MathUtils.binomialCoefficient(0, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test288()  throws Throwable  {
      long long0 = MathUtils.addAndCheck((-1866L), (-1866L));
      assertEquals((-3732L), long0);
  }

  @Test(timeout = 4000)
  public void test289()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((-250L), (-250L));
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test290()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(897L, 0L);
      assertEquals(897L, long0);
  }

  @Test(timeout = 4000)
  public void test291()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.addAndCheck(2098677201, 2049870754);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in addition: 2,098,677,201 + 2,049,870,754
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test292()  throws Throwable  {
      int int0 = MathUtils.addAndCheck(45, 45);
      assertEquals(90, int0);
  }

  @Test(timeout = 4000)
  public void test293()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      double[][] doubleArray1 = new double[4][1];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertEquals(4, doubleArray1.length);
  }

  @Test(timeout = 4000)
  public void test294()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      double[][] doubleArray1 = new double[4][1];
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 1 != 6
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test295()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle((-1.0F), 5022.44);
      assertEquals(5025.548245743669, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test296()  throws Throwable  {
      double double0 = MathUtils.sinh((-1.0F));
      assertEquals((-1.1752011936438014), double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test297()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test298()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals((-196513505), int0);
  }

  @Test(timeout = 4000)
  public void test299()  throws Throwable  {
      float[] floatArray0 = new float[2];
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test300()  throws Throwable  {
      int[] intArray0 = new int[0];
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotSame(intArray1, intArray0);
      assertEquals(0, intArray1.length);
  }

  @Test(timeout = 4000)
  public void test301()  throws Throwable  {
      int int0 = MathUtils.hash(5025.548245743669);
      assertEquals(426173111, int0);
  }

  @Test(timeout = 4000)
  public void test302()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertEquals(1, doubleArray1.length);
      assertNotSame(doubleArray1, doubleArray0);
  }

  @Test(timeout = 4000)
  public void test303()  throws Throwable  {
      double double0 = MathUtils.round(447.0634609, 0);
      assertEquals(447.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test304()  throws Throwable  {
      MathUtils.cosh(Double.POSITIVE_INFINITY);
  }

  @Test(timeout = 4000)
  public void test305()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }
}
