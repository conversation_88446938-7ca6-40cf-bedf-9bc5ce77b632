/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 12:05:03 GMT 2019
 */

package org.apache.commons.math.util;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.math.BigInteger;
import java.util.Locale;
import java.util.stream.IntStream;
import org.apache.commons.math.exception.util.Localizable;
import org.apache.commons.math.exception.util.LocalizedFormats;
import org.apache.commons.math.util.MathUtils;
import org.apache.commons.math.util.Pair;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.Random;
import org.evosuite.runtime.mock.java.util.MockRandom;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MathUtils_ESTest extends MathUtils_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test000()  throws Throwable  {
      long long0 = MathUtils.lcm((-5148L), (-5148L));
      assertEquals(5148L, long0);
      
      double double0 = MathUtils.normalizeAngle(25.092084683470652, 25.092084683470652);
      assertEquals(25.092084683470652, double0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 5148L, (float) 5148L);
      assertTrue(boolean0);
      
      int int0 = MathUtils.lcm((-2259), 3);
      assertEquals(2259, int0);
      
      int int1 = 1328;
      double double1 = MathUtils.binomialCoefficientDouble(2606, 1328);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 1328;
      doubleArray0[1] = (double) (-2259);
      doubleArray0[2] = 25.092084683470652;
      doubleArray0[3] = (double) 2259;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[1][8];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertArrayEquals(new double[] {2259.0, 1328.0, 25.092084683470652, (-2259.0)}, doubleArray0, 0.01);
      
      Integer integer0 = new Integer(2606);
      assertEquals(2606, (int)integer0);
      assertNotNull(integer0);
      assertFalse(integer0.equals((Object)int1));
      assertFalse(integer0.equals((Object)int0));
      
      float[] floatArray0 = new float[6];
      floatArray0[0] = (float) (int)integer0;
      floatArray0[1] = 3.4028235E38F;
      floatArray0[2] = (float) (int)integer0;
      floatArray0[3] = (float) 5148L;
      floatArray0[4] = (float) 3;
      floatArray0[5] = (float) 5148L;
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) 2259, (float) 2606, 3);
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {2259.0, 1328.0, 25.092084683470652, (-2259.0)}, doubleArray0, 0.01);
      
      int int2 = MathUtils.lcm(2479, 36);
      assertEquals(89244, int2);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(36, 186);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 186, n = 36
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test001()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[1] = Double.NaN;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (-1969.9);
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, Double.NaN, 0.0, 0.0, 0.0, (-1969.9)}, doubleArray0, 0.01);
      
      double double1 = MathUtils.safeNorm(doubleArray0);
      assertEquals(Double.NaN, double1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {0.0, Double.NaN, 0.0, 0.0, 0.0, (-1969.9)}, doubleArray0, 0.01);
      
      double double2 = MathUtils.indicator((-4009.750308));
      assertEquals((-1.0), double2, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      
      long long0 = MathUtils.gcd((-2672L), (-2672L));
      assertEquals(2672L, long0);
      
      double double3 = MathUtils.EPSILON;
      assertEquals(1.1102230246251565E-16, double3, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      
      double double4 = MathUtils.sign(Double.NaN);
      assertEquals(Double.NaN, double4, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertEquals(double4, double1, 0.01);
      assertEquals(double4, double0, 0.01);
      
      MathUtils.checkFinite((-0.1666666567325592));
  }

  @Test(timeout = 4000)
  public void test002()  throws Throwable  {
      double double0 = Double.NEGATIVE_INFINITY;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(Double.NEGATIVE_INFINITY);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // -\u221E is not a finite number
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test003()  throws Throwable  {
      long long0 = MathUtils.factorial(3);
      assertEquals(6L, long0);
      
      boolean boolean0 = MathUtils.equals((float) 6L, 2585.6F, 2618);
      assertFalse(boolean0);
      
      int int0 = MathUtils.addAndCheck((-3094), 0);
      assertEquals((-3094), int0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) (-3094), Float.NEGATIVE_INFINITY, (-1890.5284F));
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) (-1890.5284F);
      doubleArray0[2] = (double) 0;
      int int1 = MathUtils.hash(doubleArray0);
      assertEquals(856960482, int1);
      assertEquals(3, doubleArray0.length);
      assertFalse(int1 == int0);
      assertArrayEquals(new double[] {0.0, (-1890.5284423828125), 0.0}, doubleArray0, 0.01);
      
      double double0 = MathUtils.sign((double) 0);
      assertEquals(0.0, double0, 0.01);
      
      boolean boolean2 = MathUtils.equals((float) (-3094), 943.0F);
      assertFalse(boolean2);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      
      int int2 = MathUtils.hash((-1890.5284423828125));
      assertEquals((-526546403), int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertNotNull(doubleArray1);
      assertEquals(3, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, (-1890.5284423828125), 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      
      int int3 = MathUtils.subAndCheck((-277), (-277));
      assertEquals(0, int3);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      assertFalse(int3 == int0);
      
      double[] doubleArray2 = new double[7];
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 0;
      doubleArray2[1] = 0.0;
      doubleArray2[2] = (double) 2585.6F;
      doubleArray2[3] = (double) 0;
      doubleArray2[4] = 3.141592653589793;
      doubleArray2[5] = 0.0;
      doubleArray2[6] = (double) 0;
      double double1 = MathUtils.distance(doubleArray2, doubleArray2);
      assertEquals(0.0, double1, 0.01);
      assertEquals(7, doubleArray2.length);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertEquals(double1, double0, 0.01);
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 0.0, 2585.60009765625, 0.0, 3.141592653589793, 0.0, 0.0}, doubleArray2, 0.01);
      
      double double2 = MathUtils.binomialCoefficientDouble(1165615104, 0);
      assertEquals(1.0, double2, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      int int4 = MathUtils.sign(1165615104);
      assertEquals(1, int4);
      assertFalse(int4 == int3);
      assertFalse(int4 == int1);
      assertFalse(int4 == int2);
      assertFalse(int4 == int0);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble(1817, 2618);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 2,618, n = 1,817
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test004()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, 4018.0F, 592);
      assertFalse(boolean0);
      
      int[] intArray0 = new int[6];
      intArray0[0] = 592;
      intArray0[1] = 592;
      intArray0[2] = 592;
      intArray0[3] = 592;
      intArray0[4] = 592;
      intArray0[5] = 592;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(6, intArray0.length);
      assertEquals(6, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {592, 592, 592, 592, 592, 592}, intArray0);
      assertArrayEquals(new int[] {592, 592, 592, 592, 592, 592}, intArray1);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 592;
      doubleArray0[1] = (double) 4018.0F;
      doubleArray0[2] = (double) 592;
      doubleArray0[3] = (double) Float.NaN;
      doubleArray0[4] = (double) 592;
      doubleArray0[5] = (double) 592;
      doubleArray0[6] = (double) 592;
      doubleArray0[7] = (double) 4018.0F;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 278);
      assertNotNull(doubleArray1);
      assertEquals(278, doubleArray1.length);
      assertEquals(8, doubleArray0.length);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {592.0, 4018.0, 592.0, Double.NaN, 592.0, 592.0, 592.0, 4018.0}, doubleArray0, 0.01);
      
      byte byte0 = MathUtils.sign((byte) (-103));
      assertEquals((byte) (-1), byte0);
      
      double double0 = MathUtils.sign((double) 592);
      assertEquals(1.0, double0, 0.01);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 592;
      floatArray0[1] = (float) 592;
      floatArray0[2] = 4018.0F;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean1);
      assertEquals(3, floatArray0.length);
      assertFalse(boolean1 == boolean0);
      assertArrayEquals(new float[] {592.0F, 592.0F, 4018.0F}, floatArray0, 0.01F);
      
      int int0 = MathUtils.addAndCheck(592, 1159913472);
      assertEquals(1159914064, int0);
      
      long long0 = MathUtils.indicator((-1973L));
      assertEquals((-1L), long0);
      
      int int1 = MathUtils.sign(1001);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
  }

  @Test(timeout = 4000)
  public void test005()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, 4018.0F, 592);
      assertFalse(boolean0);
      
      int[] intArray0 = new int[6];
      intArray0[0] = 592;
      intArray0[1] = 592;
      intArray0[3] = 592;
      intArray0[4] = 592;
      intArray0[5] = 592;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(6, intArray0.length);
      assertEquals(6, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {592, 592, 0, 592, 592, 592}, intArray0);
      assertArrayEquals(new int[] {592, 592, 0, 592, 592, 592}, intArray1);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 592;
      doubleArray0[1] = (double) 4018.0F;
      doubleArray0[3] = (double) Float.NaN;
      doubleArray0[4] = (double) 592;
      doubleArray0[5] = (double) 0;
      doubleArray0[6] = (double) 592;
      doubleArray0[7] = (double) 4018.0F;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 278);
      assertNotNull(doubleArray1);
      assertEquals(278, doubleArray1.length);
      assertEquals(8, doubleArray0.length);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {592.0, 4018.0, 0.0, Double.NaN, 592.0, 0.0, 592.0, 4018.0}, doubleArray0, 0.01);
      
      byte byte0 = MathUtils.sign((byte) (-103));
      assertEquals((byte) (-1), byte0);
      
      double double0 = MathUtils.sign((double) 592);
      assertEquals(1.0, double0, 0.01);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 0;
      floatArray0[1] = (float) 592;
      floatArray0[2] = 4018.0F;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean1);
      assertEquals(3, floatArray0.length);
      assertFalse(boolean1 == boolean0);
      assertArrayEquals(new float[] {0.0F, 592.0F, 4018.0F}, floatArray0, 0.01F);
      
      int int0 = MathUtils.addAndCheck(592, 1159913472);
      assertEquals(1159914064, int0);
      
      long long0 = MathUtils.indicator((-1973L));
      assertEquals((-1L), long0);
      
      int int1 = MathUtils.sign(1001);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
  }

  @Test(timeout = 4000)
  public void test006()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) (-1382);
      doubleArray0[2] = (double) (-1382);
      doubleArray0[3] = (-2940.17);
      boolean boolean0 = MathUtils.equalsIncludingNaN((double[]) null, doubleArray0);
      assertFalse(boolean0);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {(-1382.0), 0.0, (-1382.0), (-2940.17), 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.NEGATIVE_COMPLEX_MODULE;
      assertEquals(LocalizedFormats.NEGATIVE_COMPLEX_MODULE, localizedFormats0);
      assertEquals("negative complex module {0}", localizedFormats0.getSourceString());
      
      String string0 = "ROUND_HALF_EVEN";
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null, 1680);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test007()  throws Throwable  {
      long long0 = MathUtils.pow(4149L, 4149L);
      assertEquals(3465265825139658085L, long0);
      
      float float0 = MathUtils.round((float) 3465265825139658085L, (-521));
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long1 = MathUtils.gcd(4294967295L, 39916800L);
      assertEquals(15L, long1);
      assertFalse(long1 == long0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 4294967295L;
      floatArray0[1] = Float.NaN;
      floatArray0[2] = (float) 4149L;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertEquals(3, floatArray0.length);
      assertArrayEquals(new float[] {4.2949673E9F, Float.NaN, 4149.0F}, floatArray0, 0.01F);
      
      long long2 = MathUtils.pow(4149L, 2509);
      assertEquals(5451441762177982469L, long2);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      
      double double0 = MathUtils.binomialCoefficientDouble(2509, 193);
      assertEquals(9.449935296536114E293, double0, 0.01);
      
      int[] intArray0 = new int[9];
      intArray0[0] = 66;
      intArray0[1] = 2509;
      intArray0[2] = 2509;
      intArray0[4] = 2509;
      intArray0[5] = 193;
      intArray0[6] = 2509;
      intArray0[7] = (-521);
      intArray0[8] = 193;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(9, intArray1.length);
      assertEquals(9, intArray0.length);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray0, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {66, 2509, 2509, 0, 2509, 193, 2509, (-521), 193}, intArray1);
      assertArrayEquals(new int[] {66, 2509, 2509, 0, 2509, 193, 2509, (-521), 193}, intArray0);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 39916800L;
      MathUtils.checkFinite(doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {3.99168E7}, doubleArray0, 0.01);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 15L);
      assertNotNull(doubleArray1);
      assertEquals(1, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {3.99168E7}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {15.0}, doubleArray1, 0.01);
      
      boolean boolean1 = MathUtils.equals(5840.1875F, Float.NaN, 3.46526593E18F);
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      double[] doubleArray2 = new double[18];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      doubleArray2[0] = (double) 2509;
      doubleArray2[1] = 9.449935296536114E293;
      doubleArray2[2] = (double) 193;
      doubleArray2[1] = (double) 193;
      doubleArray2[4] = (double) 4149L;
      doubleArray2[6] = (double) 2509;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray2, (MathUtils.OrderDirection) null, false, false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test008()  throws Throwable  {
      long long0 = MathUtils.pow(4149L, 4149L);
      assertEquals(3465265825139658085L, long0);
      
      float float0 = MathUtils.round((float) 3465265825139658085L, (-521));
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long1 = MathUtils.gcd(4294967295L, 39916800L);
      assertEquals(15L, long1);
      assertFalse(long1 == long0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 4294967295L;
      floatArray0[1] = Float.NaN;
      floatArray0[2] = (float) 4149L;
      boolean boolean0 = MathUtils.equalsIncludingNaN(Float.NaN, Float.NaN);
      assertTrue(boolean0);
      
      long long2 = MathUtils.pow(4149L, 2509);
      assertEquals(5451441762177982469L, long2);
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      
      double double0 = MathUtils.binomialCoefficientDouble(2509, 193);
      assertEquals(9.449935296536114E293, double0, 0.01);
      
      int[] intArray0 = new int[21];
      intArray0[0] = 66;
      intArray0[1] = 2509;
      intArray0[2] = 2509;
      intArray0[4] = 2509;
      intArray0[5] = 193;
      intArray0[6] = 2509;
      intArray0[7] = (-521);
      intArray0[8] = 193;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(21, intArray1.length);
      assertEquals(21, intArray0.length);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray0, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 39916800L;
      MathUtils.checkFinite(doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {3.99168E7}, doubleArray0, 0.01);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 15L);
      assertNotNull(doubleArray1);
      assertEquals(1, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {3.99168E7}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {15.0}, doubleArray1, 0.01);
      
      boolean boolean1 = MathUtils.equals(5840.1875F, Float.NaN, 3.46526593E18F);
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      double[] doubleArray2 = new double[7];
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 2509;
      doubleArray2[1] = 9.449935296536114E293;
      doubleArray2[2] = (double) 193;
      doubleArray2[3] = (double) 193;
      doubleArray2[4] = (double) 4149L;
      doubleArray2[5] = (double) 4149.0F;
      doubleArray2[6] = (double) 2509;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean2 = MathUtils.checkOrder(doubleArray2, mathUtils_OrderDirection0, false, false);
      assertFalse(boolean2);
      assertEquals(7, doubleArray2.length);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {2509.0, 9.449935296536114E293, 193.0, 193.0, 4149.0, 4149.0, 2509.0}, doubleArray2, 0.01);
  }

  @Test(timeout = 4000)
  public void test009()  throws Throwable  {
      short short0 = (short)2605;
      short short1 = MathUtils.indicator((short)2605);
      assertEquals((short)1, short1);
      assertFalse(short1 == short0);
      
      double[] doubleArray0 = new double[0];
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) (short)1, (float) (short)2605);
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((float) (short)2605, (float) (short)1);
      assertFalse(boolean2);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      
      boolean boolean3 = MathUtils.equalsIncludingNaN((float) (short)1, 257.3111F, (int) (short)1);
      assertFalse(boolean3);
      assertTrue(boolean3 == boolean1);
      assertFalse(boolean3 == boolean0);
      assertTrue(boolean3 == boolean2);
      
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null, (int) (short)2605);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test010()  throws Throwable  {
      int int0 = MathUtils.indicator(67);
      assertEquals(1, int0);
      
      float float0 = MathUtils.round((-180.0806F), 67, 7);
      assertEquals(Float.NaN, float0, 0.01F);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) (-180.0806F), (double) Float.NaN);
      assertFalse(boolean0);
      
      double double0 = MathUtils.sign(3.834E-20);
      assertEquals(1.0, double0, 0.01);
      
      Random.setNextRandom(67);
      int[] intArray0 = new int[9];
      intArray0[0] = 7;
      intArray0[1] = (-1319);
      intArray0[2] = 67;
      intArray0[3] = 1;
      intArray0[4] = 7;
      intArray0[5] = 7;
      intArray0[6] = 601;
      intArray0[7] = 67;
      intArray0[8] = 7;
      int int1 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int1);
      assertEquals(9, intArray0.length);
      assertFalse(int1 == int0);
      assertArrayEquals(new int[] {7, (-1319), 67, 1, 7, 7, 601, 67, 7}, intArray0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 67;
      doubleArray0[1] = (double) 67;
      doubleArray0[2] = (double) 7;
      doubleArray0[3] = 1.2599210498948732;
      doubleArray0[4] = (double) 7;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotNull(doubleArray1);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {67.0, 67.0, 7.0, 1.2599210498948732, 7.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {67.0, 67.0, 7.0, 1.2599210498948732, 7.0}, doubleArray1, 0.01);
  }

  @Test(timeout = 4000)
  public void test011()  throws Throwable  {
      int int0 = MathUtils.indicator(926);
      assertEquals(1, int0);
      
      int[] intArray0 = new int[5];
      intArray0[0] = 926;
      intArray0[1] = 926;
      intArray0[2] = 1;
      intArray0[3] = 926;
      intArray0[4] = 926;
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(5, intArray0.length);
      assertArrayEquals(new int[] {926, 926, 1, 926, 926}, intArray0);
      
      float[] floatArray0 = new float[1];
      floatArray0[0] = 2541.4F;
      float[] floatArray1 = new float[9];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = 2541.4F;
      floatArray1[1] = (float) 926;
      floatArray1[2] = 746.0F;
      floatArray1[3] = (float) 926;
      floatArray1[4] = 2541.4F;
      floatArray1[5] = (float) 1;
      floatArray1[6] = 0.0F;
      floatArray1[7] = (float) 926;
      floatArray1[8] = 2541.4F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertFalse(boolean0);
      assertEquals(1, floatArray0.length);
      assertEquals(9, floatArray1.length);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertArrayEquals(new float[] {2541.4F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {2541.4F, 926.0F, 746.0F, 926.0F, 2541.4F, 1.0F, 0.0F, 926.0F, 2541.4F}, floatArray1, 0.01F);
      
      short short0 = MathUtils.indicator((short)3180);
      assertEquals((short)1, short0);
      
      // Undeclared exception!
      try { 
        MathUtils.safeNorm((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test012()  throws Throwable  {
      LocalizedFormats localizedFormats0 = LocalizedFormats.BINOMIAL_NEGATIVE_PARAMETER;
      assertEquals(LocalizedFormats.BINOMIAL_NEGATIVE_PARAMETER, localizedFormats0);
      assertEquals("must have n >= 0 for binomial coefficient (n, k), got n = {0}", localizedFormats0.getSourceString());
      
      long long0 = MathUtils.indicator(4370L);
      assertEquals(1L, long0);
      
      int int0 = MathUtils.hash((double[]) null);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test013()  throws Throwable  {
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
      
      long long0 = MathUtils.subAndCheck(0L, 0L);
      assertEquals(0L, long0);
      
      double double1 = MathUtils.round((double) 0L, 2524);
      assertEquals(0.0, double1, 0.01);
      assertEquals(double1, double0, 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 2524;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 0.0;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double2 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double2, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      float float0 = MathUtils.round((float) 0L, 315);
      assertEquals(Float.NaN, float0, 0.01F);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      int int0 = MathUtils.gcd(0, 2911);
      assertEquals(2911, int0);
      
      int int1 = MathUtils.sign(2911);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
      
      int int2 = MathUtils.lcm(1676, 2409);
      assertEquals(4037484, int2);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      
      long long1 = MathUtils.gcd(6L, (long) 1);
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      int int3 = MathUtils.pow(2911, 588L);
      assertEquals((-1180489855), int3);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      assertFalse(int3 == int0);
      
      double double3 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double3, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(double3, double0, 0.01);
      assertEquals(double3, double2, 0.01);
      assertEquals(double3, double1, 0.01);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null, 247);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test014()  throws Throwable  {
      double double0 = MathUtils.sign(1066.100182);
      assertEquals(1.0, double0, 0.01);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[1] = 1066.100182;
      double[] doubleArray1 = new double[7];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 1.0;
      doubleArray1[1] = 1066.100182;
      doubleArray1[2] = 1.0;
      doubleArray1[3] = 1066.100182;
      doubleArray1[4] = (-4363.0);
      doubleArray1[5] = 0.0;
      doubleArray1[6] = 1.0;
      double double1 = MathUtils.distance1(doubleArray0, doubleArray1);
      assertEquals(1.0, double1, 0.01);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 1066.100182}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.0, 1066.100182, 1.0, 1066.100182, (-4363.0), 0.0, 1.0}, doubleArray1, 0.01);
      
      double[] doubleArray2 = MathUtils.copyOf(doubleArray0, 394);
      assertNotNull(doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertEquals(394, doubleArray2.length);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertArrayEquals(new double[] {0.0, 1066.100182}, doubleArray0, 0.01);
      
      boolean boolean0 = MathUtils.equals((float) 394, (-1.49642522E9F), (float) 394);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test015()  throws Throwable  {
      double double0 = MathUtils.sign(1066.100182);
      assertEquals(1.0, double0, 0.01);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1.0;
      doubleArray0[1] = 1066.100182;
      double[] doubleArray1 = new double[7];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 1.0;
      doubleArray1[1] = 1066.100182;
      doubleArray1[2] = 1.0;
      doubleArray1[3] = 1066.100182;
      doubleArray1[4] = (-4363.0);
      doubleArray1[5] = 0.0;
      doubleArray1[6] = 1.0;
      double double1 = MathUtils.distance1(doubleArray0, doubleArray1);
      assertEquals(0.0, double1, 0.01);
      assertEquals(2, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertNotEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {1.0, 1066.100182}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.0, 1066.100182, 1.0, 1066.100182, (-4363.0), 0.0, 1.0}, doubleArray1, 0.01);
      
      double[] doubleArray2 = MathUtils.copyOf(doubleArray0, 394);
      assertNotNull(doubleArray2);
      assertEquals(2, doubleArray0.length);
      assertEquals(394, doubleArray2.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertArrayEquals(new double[] {1.0, 1066.100182}, doubleArray0, 0.01);
      
      boolean boolean0 = MathUtils.equals(2330.4312F, (-1.49642522E9F), (float) 394);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((-1.49642522E9F), (-1.49642522E9F), (float) 394);
      assertTrue(boolean1);
      assertFalse(boolean1 == boolean0);
      
      boolean boolean2 = MathUtils.equals((float) 394, 0.5F, (-359.74F));
      assertFalse(boolean2);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      
      byte[] byteArray0 = new byte[3];
      byteArray0[0] = (byte) (-31);
      byteArray0[1] = (byte) (-69);
      byteArray0[2] = (byte)8;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertNotNull(bigInteger0);
      assertEquals(3, byteArray0.length);
      assertArrayEquals(new byte[] {(byte) (-31), (byte) (-69), (byte)8}, byteArray0);
      assertEquals((byte)8, bigInteger0.byteValue());
      assertEquals((short) (-17656), bigInteger0.shortValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, bigInteger0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,983,736)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test016()  throws Throwable  {
      long long0 = MathUtils.lcm((-5148L), (-5148L));
      assertEquals(5148L, long0);
      
      double double0 = MathUtils.normalizeAngle(3.834E-20, 3.834E-20);
      assertEquals(3.834E-20, double0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 5148L, (float) 5148L);
      assertTrue(boolean0);
      
      int int0 = 968;
      int int1 = 3;
      int int2 = MathUtils.lcm(968, 3);
      assertEquals(2904, int2);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      
      double double1 = MathUtils.binomialCoefficientDouble(2606, 697);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 697;
      doubleArray0[1] = (double) 968;
      doubleArray0[2] = 3.834E-20;
      doubleArray0[3] = (double) 2904;
      double[][] doubleArray1 = new double[1][8];
      doubleArray1[0] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, (MathUtils.OrderDirection) null, doubleArray1);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils$1", e);
      }
  }

  @Test(timeout = 4000)
  public void test017()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 1947L);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test018()  throws Throwable  {
      long long0 = MathUtils.pow(0L, 0L);
      assertEquals(1L, long0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 0L;
      doubleArray0[2] = (double) 1L;
      doubleArray0[3] = (double) 0L;
      doubleArray0[4] = (double) 1L;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertNotNull(doubleArray1);
      assertEquals(5, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 0.0, 1.0, 0.0, 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      
      boolean boolean0 = MathUtils.equals(0.0, Double.POSITIVE_INFINITY);
      assertFalse(boolean0);
      
      double double0 = MathUtils.sign(465.2046467442033);
      assertEquals(1.0, double0, 0.01);
      
      byte byte0 = MathUtils.indicator((byte)11);
      assertEquals((byte)1, byte0);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertArrayEquals(new double[] {0.0, 0.0, 1.0, 0.0, 1.0}, doubleArray0, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) 1L, 1.0, 465.2046467442033);
      assertTrue(boolean1);
      assertFalse(boolean1 == boolean0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(0.0, 1.0, (int) (byte)11);
      assertFalse(boolean2);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      
      int int0 = MathUtils.indicator(0);
      assertEquals(1, int0);
      
      boolean boolean3 = MathUtils.equalsIncludingNaN((-2078.69156599906), (-1442.11926567294));
      assertFalse(boolean3);
      assertTrue(boolean3 == boolean0);
      assertTrue(boolean3 == boolean2);
      assertFalse(boolean3 == boolean1);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = bigInteger0.abs();
      assertNotNull(bigInteger1);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short) (-7168), bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, (long) (byte)11);
      assertNotNull(bigInteger3);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short) (-6144), bigInteger3.shortValue());
      assertEquals((byte)0, bigInteger3.byteValue());
      
      long long1 = MathUtils.subAndCheck(519L, 1684L);
      assertEquals((-1165L), long1);
      assertFalse(long1 == long0);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(4295, 285);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test019()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(0, (-1382));
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) (-1382);
      doubleArray0[1] = (double) (-1382);
      doubleArray0[2] = (double) (-1382);
      doubleArray0[3] = (-2940.17);
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = (double) 0;
      doubleArray0[7] = 0.0;
      doubleArray0[8] = 0.0;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {(-1382.0), (-1382.0), (-1382.0), (-2940.17), 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.NEGATIVE_COMPLEX_MODULE;
      assertEquals(LocalizedFormats.NEGATIVE_COMPLEX_MODULE, localizedFormats0);
      assertEquals("negative complex module {0}", localizedFormats0.getSourceString());
      
      LocalizedFormats localizedFormats1 = LocalizedFormats.NULL_NOT_ALLOWED;
      assertEquals(LocalizedFormats.NULL_NOT_ALLOWED, localizedFormats1);
      assertNotSame(localizedFormats1, localizedFormats0);
      assertFalse(localizedFormats1.equals((Object)localizedFormats0));
      assertEquals("null is not allowed", localizedFormats1.getSourceString());
      
      long long0 = MathUtils.gcd((long) 0, (long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test020()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(291.91516F, 291.91516F, 2265);
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 2265;
      doubleArray0[1] = (double) 291.91516F;
      doubleArray0[2] = (-1107.18066);
      doubleArray0[3] = (double) 2265;
      doubleArray0[4] = (double) 2265;
      doubleArray0[5] = (-1501.64260051);
      doubleArray0[6] = (double) 2265;
      doubleArray0[7] = (double) 2265;
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean1);
      assertEquals(8, doubleArray0.length);
      assertTrue(boolean1 == boolean0);
      assertArrayEquals(new double[] {2265.0, 291.9151611328125, (-1107.18066), 2265.0, 2265.0, (-1501.64260051), 2265.0, 2265.0}, doubleArray0, 0.01);
      
      double double0 = MathUtils.binomialCoefficientDouble(2265, 2265);
      assertEquals(1.0, double0, 0.01);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(2265.0, (double) 2265, 276);
      assertTrue(boolean2);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      
      boolean boolean3 = MathUtils.equals((-1309.2169F), (-1309.2169F), (float) 2265);
      assertTrue(boolean3);
      assertTrue(boolean3 == boolean1);
      assertTrue(boolean3 == boolean2);
      assertTrue(boolean3 == boolean0);
      
      int int0 = MathUtils.pow((-723), (long) 2265);
      assertEquals((-404032499), int0);
      
      double[] doubleArray1 = new double[7];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) 276;
      doubleArray1[1] = (-1107.18066);
      doubleArray1[2] = (double) 2265;
      doubleArray1[3] = 1.0;
      doubleArray1[4] = (double) (-1309.2169F);
      doubleArray1[5] = (double) 276;
      doubleArray1[6] = (double) (-404032499);
      double[] doubleArray2 = MathUtils.normalizeArray(doubleArray1, 291.9151611328125);
      assertNotNull(doubleArray2);
      assertEquals(7, doubleArray2.length);
      assertEquals(7, doubleArray1.length);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray1, doubleArray2);
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {(-1.9941134625592503E-4), 7.99943427388129E-4, (-0.0016364735480785153), (-7.225048777388589E-7), 9.459156099562285E-4, (-1.9941134625592503E-4), 291.91545129252063}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {276.0, (-1107.18066), 2265.0, 1.0, (-1309.2169189453125), 276.0, (-4.04032499E8)}, doubleArray1, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(doubleArray0, doubleArray2);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 7
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test021()  throws Throwable  {
      long long0 = 2819561105158720014L;
      long long1 = MathUtils.indicator(2819561105158720014L);
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      float[] floatArray0 = new float[5];
      floatArray0[0] = (float) 2819561105158720014L;
      floatArray0[1] = (float) 1L;
      floatArray0[2] = 1410.6F;
      floatArray0[3] = (float) 2819561105158720014L;
      floatArray0[4] = (float) 1L;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertEquals(5, floatArray0.length);
      assertArrayEquals(new float[] {2.81956123E18F, 1.0F, 1410.6F, 2.81956123E18F, 1.0F}, floatArray0, 0.01F);
      
      int int0 = MathUtils.pow(0, 31L);
      assertEquals(0, int0);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = 0.5;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = (double) 2.81956123E18F;
      doubleArray0[3] = (double) 1410.6F;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {0.5, 0.0, 2.8195612299897078E18, 1410.5999755859375}, doubleArray0, 0.01);
      
      int[] intArray0 = new int[0];
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(0, intArray0.length);
      assertEquals(0, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      
      int int1 = MathUtils.mulAndCheck(9, 0);
      assertEquals(0, int1);
      assertTrue(int1 == int0);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 2918.435358578511);
      assertNotNull(doubleArray1);
      assertEquals(4, doubleArray1.length);
      assertEquals(4, doubleArray0.length);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {5.175336019550039E-16, 0.0, 2918.4353585785093, 1.4600657725652618E-12}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {0.5, 0.0, 2.8195612299897078E18, 1410.5999755859375}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.round((-655.3102792668902), 0, 1030);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test022()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      int int0 = bigInteger0.bitCount();
      assertEquals(0, int0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 121645100408832000L);
      assertNotNull(bigInteger1);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      
      short short0 = MathUtils.sign((short)63);
      assertEquals((short)1, short0);
      
      float float0 = MathUtils.round(-0.0F, (int) (short)63, (int) (short)1);
      assertEquals(Float.NaN, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test023()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(0L, 0L);
      assertEquals(0L, long0);
      
      double double0 = MathUtils.round((double) 0L, 2524);
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 2524;
      doubleArray0[3] = 0.0;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double1 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double1, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      float float0 = MathUtils.round((float) 0L, 315);
      assertEquals(Float.NaN, float0, 0.01F);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      int int0 = MathUtils.gcd(0, 2911);
      assertEquals(2911, int0);
      
      int int1 = MathUtils.sign(2911);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
      
      int int2 = MathUtils.lcm(1676, 2409);
      assertEquals(4037484, int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      
      long long1 = 6L;
      long long2 = MathUtils.gcd(6L, (long) 1);
      assertEquals(1L, long2);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      
      int int3 = MathUtils.pow(2911, 588L);
      assertEquals((-1180489855), int3);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      assertFalse(int3 == int0);
      
      double double2 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double2, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double3 = MathUtils.indicator((-1378.3475617));
      assertEquals((-1.0), double3, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.lcm(315, (-1180489855));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test024()  throws Throwable  {
      long long0 = MathUtils.gcd((-8525404001L), (-8525404001L));
      assertEquals(8525404001L, long0);
      
      int int0 = 8;
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (-8525404001L), 0.0F, 8);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(Double.NaN, (-5065.789429147679));
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      long long1 = MathUtils.sign(472L);
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      long long2 = MathUtils.indicator(472L);
      assertEquals(1L, long2);
      assertFalse(long2 == long0);
      assertTrue(long2 == long1);
      
      long long3 = MathUtils.lcm(3L, (-8525404001L));
      assertEquals(25576212003L, long3);
      assertFalse(long3 == long1);
      assertFalse(long3 == long2);
      assertFalse(long3 == long0);
      
      // Undeclared exception!
      try { 
        MathUtils.round((float) 25576212003L, 8, 8);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 8, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test025()  throws Throwable  {
      long long0 = MathUtils.pow(4149L, 4149L);
      assertEquals(3465265825139658085L, long0);
      
      float float0 = MathUtils.round((float) 3465265825139658085L, (-521));
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long1 = MathUtils.gcd(4294967295L, 39916800L);
      assertEquals(15L, long1);
      assertFalse(long1 == long0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 4294967295L;
      floatArray0[1] = Float.NaN;
      floatArray0[2] = (float) 4149L;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertEquals(3, floatArray0.length);
      assertArrayEquals(new float[] {4.2949673E9F, Float.NaN, 4149.0F}, floatArray0, 0.01F);
      
      long long2 = MathUtils.pow(4149L, 2480);
      assertEquals((-8324367696995459519L), long2);
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      
      double double0 = MathUtils.binomialCoefficientDouble(2480, 193);
      assertEquals(9.150077526253395E292, double0, 0.01);
      
      int[] intArray0 = new int[9];
      intArray0[0] = 66;
      intArray0[1] = 2480;
      intArray0[2] = 2480;
      intArray0[3] = 2480;
      intArray0[4] = 2480;
      intArray0[5] = 193;
      intArray0[6] = 2480;
      intArray0[7] = (-521);
      intArray0[8] = 193;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(9, intArray1.length);
      assertEquals(9, intArray0.length);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray0, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {66, 2480, 2480, 2480, 2480, 193, 2480, (-521), 193}, intArray1);
      assertArrayEquals(new int[] {66, 2480, 2480, 2480, 2480, 193, 2480, (-521), 193}, intArray0);
  }

  @Test(timeout = 4000)
  public void test026()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertNotNull(doubleArray1);
      assertEquals(5, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      
      boolean boolean0 = MathUtils.equals(0.0, Double.POSITIVE_INFINITY);
      assertFalse(boolean0);
      
      double double0 = MathUtils.sign(483.0232045391464);
      assertEquals(1.0, double0, 0.01);
      
      byte byte0 = MathUtils.indicator((byte)11);
      assertEquals((byte)1, byte0);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(0.0, 0.0, (int) (byte)11);
      assertTrue(boolean1);
      assertFalse(boolean1 == boolean0);
      
      int int0 = MathUtils.indicator(0);
      assertEquals(1, int0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((-2078.69156599906), (-1442.11926567294));
      assertFalse(boolean2);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, (long) (byte)11);
      assertNotNull(bigInteger2);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
  }

  @Test(timeout = 4000)
  public void test027()  throws Throwable  {
      byte byte0 = (byte)20;
      byte byte1 = MathUtils.indicator((byte)20);
      assertEquals((byte)1, byte1);
      assertFalse(byte1 == byte0);
      
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(1840700269, (int) (byte)20);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test028()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (-1042.73);
      doubleArray0[1] = 1.304E19;
      doubleArray0[2] = 1872.810240396;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotNull(doubleArray1);
      assertEquals(3, doubleArray0.length);
      assertEquals(3, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {(-1042.73), 1.304E19, 1872.810240396}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-1042.73), 1.304E19, 1872.810240396}, doubleArray1, 0.01);
      
      long long0 = MathUtils.subAndCheck(1785L, 1785L);
      assertEquals(0L, long0);
      
      int int0 = MathUtils.compareTo(0.097, 355.6988644769, 639.6364);
      assertEquals(0, int0);
      
      int int1 = MathUtils.gcd((-312), (-754));
      assertEquals(26, int1);
      assertFalse(int1 == int0);
      
      int[] intArray0 = new int[4];
      intArray0[0] = 0;
      intArray0[1] = (-312);
      intArray0[2] = 26;
      intArray0[3] = (-312);
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(4, intArray0.length);
      assertArrayEquals(new int[] {0, (-312), 26, (-312)}, intArray0);
      
      Random.setNextRandom(14);
      double double1 = MathUtils.binomialCoefficientLog(26, 14);
      assertEquals(16.083266082601433, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      int[] intArray1 = new int[5];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = 26;
      intArray1[1] = (-2130165230);
      intArray1[2] = (-312);
      intArray1[3] = 14;
      intArray1[4] = (-312);
      int[] intArray2 = new int[0];
      assertFalse(intArray2.equals((Object)intArray1));
      assertFalse(intArray2.equals((Object)intArray0));
      
      // Undeclared exception!
      try { 
        MathUtils.distance1(intArray1, intArray2);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test029()  throws Throwable  {
      long long0 = MathUtils.lcm((-5148L), (-5148L));
      assertEquals(5148L, long0);
      
      double double0 = MathUtils.normalizeAngle(3.834E-20, 3.834E-20);
      assertEquals(3.834E-20, double0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 5148L, (float) 5148L);
      assertTrue(boolean0);
      
      int int0 = MathUtils.lcm((-2259), 3);
      assertEquals(2259, int0);
      
      double double1 = MathUtils.binomialCoefficientDouble(2606, 697);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 697;
      doubleArray0[1] = (double) (-2259);
      doubleArray0[2] = 3.834E-20;
      doubleArray0[3] = (double) 2259;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[1][8];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertArrayEquals(new double[] {2259.0, 697.0, 3.834E-20, (-2259.0)}, doubleArray0, 0.01);
      
      float float0 = MathUtils.sign(3.4028235E38F);
      assertEquals(1.0F, float0, 0.01F);
      
      float[] floatArray0 = new float[6];
      floatArray0[0] = 1.0F;
      floatArray0[1] = 3.4028235E38F;
      floatArray0[2] = 1.0F;
      floatArray0[3] = (float) 5148L;
      floatArray0[4] = (float) 3;
      floatArray0[5] = (float) 5148L;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean1);
      assertEquals(6, floatArray0.length);
      assertTrue(boolean1 == boolean0);
      assertArrayEquals(new float[] {1.0F, 3.4028235E38F, 1.0F, 5148.0F, 3.0F, 5148.0F}, floatArray0, 0.01F);
      
      double double2 = MathUtils.safeNorm(doubleArray0);
      assertEquals(3269.857947984897, double2, 0.01);
      assertEquals(4, doubleArray0.length);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertArrayEquals(new double[] {2259.0, 697.0, 3.834E-20, (-2259.0)}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test030()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(0L, 0L);
      assertEquals(0L, long0);
      
      double double0 = MathUtils.round((double) 0L, 2524);
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 2524;
      doubleArray0[3] = 0.0;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double1 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double1, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      float float0 = MathUtils.round((float) 0L, 315);
      assertEquals(Float.NaN, float0, 0.01F);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      int int0 = MathUtils.gcd(0, 2911);
      assertEquals(2911, int0);
      
      int int1 = MathUtils.sign(2911);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
      
      int int2 = MathUtils.lcm(1676, 2409);
      assertEquals(4037484, int2);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      
      long long1 = MathUtils.gcd(6L, (long) 1);
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      int int3 = MathUtils.pow(2911, 588L);
      assertEquals((-1180489855), int3);
      assertFalse(int3 == int0);
      assertFalse(int3 == int1);
      assertFalse(int3 == int2);
      
      double double2 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double2, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.subAndCheck(315, (-2147483642));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // overflow in subtraction: 315 - -2,147,483,642
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test031()  throws Throwable  {
      long long0 = MathUtils.pow(4149L, 4149L);
      assertEquals(3465265825139658085L, long0);
      
      float float0 = MathUtils.round((float) 3465265825139658085L, (-521));
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long1 = MathUtils.gcd(4294967295L, 39916800L);
      assertEquals(15L, long1);
      assertFalse(long1 == long0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 4294967295L;
      floatArray0[1] = Float.NaN;
      floatArray0[2] = (float) 4149L;
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (-521), 4.2949673E9F, (float) 3465265825139658085L);
      assertTrue(boolean0);
      
      long long2 = new Integer((-521));
      assertEquals((-521), long2);
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      
      double double0 = MathUtils.binomialCoefficientDouble(2509, 193);
      assertEquals(9.449935296536114E293, double0, 0.01);
      
      int[] intArray0 = new int[9];
      intArray0[1] = 2509;
      intArray0[2] = 2509;
      intArray0[3] = 66;
      intArray0[4] = 66;
      intArray0[5] = 193;
      intArray0[6] = 2509;
      floatArray0[2] = (float) 193;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(9, intArray1.length);
      assertEquals(9, intArray0.length);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray0, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {0, 2509, 2509, 66, 66, 193, 2509, 0, 0}, intArray1);
      assertArrayEquals(new int[] {0, 2509, 2509, 66, 66, 193, 2509, 0, 0}, intArray0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) Float.NaN;
      doubleArray0[1] = (double) 193;
      doubleArray0[2] = 9.449935296536114E293;
      doubleArray0[3] = (double) 4.2949673E9F;
      doubleArray0[4] = (double) 0;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // value \uFFFD at index 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test032()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      long long0 = bigInteger0.longValueExact();
      assertEquals(10L, long0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      byte byte0 = bigInteger0.byteValueExact();
      assertEquals((byte)10, byte0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      int int0 = bigInteger0.bitCount();
      assertEquals(2, int0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0L);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      int[] intArray0 = new int[2];
      intArray0[0] = 0;
      intArray0[1] = (-746);
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2, intArray0.length);
      assertArrayEquals(new int[] {0, (-746)}, intArray0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (-746), (float) 0, 2174.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test033()  throws Throwable  {
      long long0 = MathUtils.lcm((-5148L), (-5148L));
      assertEquals(5148L, long0);
      
      double double0 = MathUtils.normalizeAngle(3.834E-20, 3.834E-20);
      assertEquals(3.834E-20, double0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 5148L, (float) 5148L);
      assertTrue(boolean0);
      
      int int0 = MathUtils.lcm((-2259), 3);
      assertEquals(2259, int0);
      
      double double1 = MathUtils.binomialCoefficientDouble(2606, 697);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 697;
      doubleArray0[1] = (double) (-2259);
      doubleArray0[2] = 3.834E-20;
      doubleArray0[3] = (double) 2259;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[1][8];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertArrayEquals(new double[] {2259.0, 697.0, 3.834E-20, (-2259.0)}, doubleArray0, 0.01);
      
      float float0 = MathUtils.sign(3.4028235E38F);
      assertEquals(1.0F, float0, 0.01F);
      
      float[] floatArray0 = new float[6];
      floatArray0[0] = 1.0F;
      floatArray0[1] = 3.4028235E38F;
      floatArray0[2] = 1.0F;
      floatArray0[3] = (float) 5148L;
      floatArray0[4] = (float) 3;
      floatArray0[5] = (float) 5148L;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean1);
      assertEquals(6, floatArray0.length);
      assertTrue(boolean1 == boolean0);
      assertArrayEquals(new float[] {1.0F, 3.4028235E38F, 1.0F, 5148.0F, 3.0F, 5148.0F}, floatArray0, 0.01F);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {2259.0, 697.0, 3.834E-20, (-2259.0)}, doubleArray0, 0.01);
      
      double[] doubleArray2 = new double[4];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 2259;
      doubleArray2[1] = 2.2250738585072014E-308;
      doubleArray2[2] = (double) 1.0F;
      doubleArray2[3] = (double) 2259;
      double double2 = MathUtils.distance(doubleArray0, doubleArray2);
      assertEquals(4571.447691924299, double2, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray2.length);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertArrayEquals(new double[] {2259.0, 697.0, 3.834E-20, (-2259.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {2259.0, 2.2250738585072014E-308, 1.0, 2259.0}, doubleArray2, 0.01);
      
      int int1 = MathUtils.subAndCheck((-1021), 3);
      assertEquals((-1024), int1);
      assertFalse(int1 == int0);
  }

  @Test(timeout = 4000)
  public void test034()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck((-3094L), 414L);
      assertEquals((-1280916L), long0);
      
      Random.setNextRandom((-3678));
      Random.setNextRandom((-3678));
      float[] floatArray0 = new float[5];
      floatArray0[0] = (float) (-3678);
      floatArray0[1] = (-222.02F);
      floatArray0[2] = (float) 414L;
      floatArray0[3] = (float) (-1280916L);
      floatArray0[4] = (float) (-3094L);
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertEquals(5, floatArray0.length);
      assertArrayEquals(new float[] {(-3678.0F), (-222.02F), 414.0F, (-1280916.0F), (-3094.0F)}, floatArray0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test035()  throws Throwable  {
      int int0 = 2072;
      int int1 = MathUtils.pow(2072, (long) 2072);
      assertEquals(0, int1);
      assertFalse(int1 == int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(1837.0, (double) 0, (-1601.1089238685297));
      assertFalse(boolean0);
      
      int int2 = 4194304;
      // Undeclared exception!
      try { 
        MathUtils.equals((float) 0, (float) 0, 4194304);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test036()  throws Throwable  {
      long long0 = (-9223372036854775808L);
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((-9223372036854775808L), 1799L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test037()  throws Throwable  {
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
      
      long long0 = MathUtils.subAndCheck(0L, 0L);
      assertEquals(0L, long0);
      
      double double1 = MathUtils.round((double) 0L, 2524);
      assertEquals(0.0, double1, 0.01);
      assertEquals(double1, double0, 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 2524;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 0.0;
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, (double) 0, 0.0);
      assertTrue(boolean0);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      double double2 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double2, 0.01);
      assertEquals(4, doubleArray0.length);
      assertEquals(double2, double0, 0.01);
      assertEquals(double2, double1, 0.01);
      assertArrayEquals(new double[] {0.0, 2524.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      float float0 = MathUtils.round((float) 0L, 315);
      assertEquals(Float.NaN, float0, 0.01F);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      int int0 = MathUtils.gcd(0, 2911);
      assertEquals(2911, int0);
      
      int int1 = MathUtils.sign(2911);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
      
      int int2 = MathUtils.lcm(1676, 2409);
      assertEquals(4037484, int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      
      long long1 = MathUtils.gcd((-37L), (long) 1);
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      double double3 = MathUtils.round((double) Float.NaN, 2524, 0);
      assertEquals(Double.NaN, double3, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test038()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 826.84391;
      doubleArray0[1] = 1706.048468424804;
      doubleArray0[2] = 865.78172;
      doubleArray0[3] = 3.141592653589793;
      doubleArray0[4] = 4.546222882816E12;
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((double[]) null, doubleArray0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test039()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(67, (-2943));
      assertEquals(0.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test040()  throws Throwable  {
      float[] floatArray0 = new float[2];
      floatArray0[0] = (-1817.6644F);
      floatArray0[1] = 2420.1F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertEquals(2, floatArray0.length);
      assertArrayEquals(new float[] {(-1817.6644F), 2420.1F}, floatArray0, 0.01F);
      
      int int0 = 177;
      Random.setNextRandom(177);
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean1);
      assertEquals(2, floatArray0.length);
      assertTrue(boolean1 == boolean0);
      assertArrayEquals(new float[] {(-1817.6644F), 2420.1F}, floatArray0, 0.01F);
      
      long long0 = MathUtils.mulAndCheck((long) 177, (long) 177);
      assertEquals(31329L, long0);
      
      boolean boolean2 = MathUtils.equals(floatArray0, floatArray0);
      assertTrue(boolean2);
      assertEquals(2, floatArray0.length);
      assertTrue(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      assertArrayEquals(new float[] {(-1817.6644F), 2420.1F}, floatArray0, 0.01F);
      
      // Undeclared exception!
      try { 
        MathUtils.distance((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test041()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(1610.0635, 1610.0635, 415);
      assertTrue(boolean0);
      
      long long0 = MathUtils.binomialCoefficient(114, 13);
      assertEquals(43334780015908584L, long0);
      
      long long1 = MathUtils.sign((long) 415);
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      long long2 = MathUtils.pow(64L, 4208363204685324176L);
      assertEquals(0L, long2);
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) 43334780015908584L;
      doubleArray0[1] = (double) 43334780015908584L;
      doubleArray0[2] = (double) 43334780015908584L;
      doubleArray0[3] = (double) 13;
      doubleArray0[4] = (double) 0L;
      doubleArray0[5] = (double) 13;
      doubleArray0[6] = (double) 0L;
      doubleArray0[7] = (double) 415;
      doubleArray0[8] = (double) 64L;
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean1);
      assertEquals(9, doubleArray0.length);
      assertTrue(boolean1 == boolean0);
      assertArrayEquals(new double[] {4.3334780015908584E16, 4.3334780015908584E16, 4.3334780015908584E16, 13.0, 0.0, 13.0, 0.0, 415.0, 64.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test042()  throws Throwable  {
      long long0 = (-1287L);
      long long1 = MathUtils.mulAndCheck((-1287L), (-1287L));
      assertEquals(1656369L, long1);
      assertFalse(long1 == long0);
      
      int int0 = (-708);
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-708), (-708));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -708
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test043()  throws Throwable  {
      int int0 = MathUtils.compareTo(865.78172, 0.0, 865.78172);
      assertEquals(0, int0);
      
      double double0 = MathUtils.log((-1603.44527), 4886.068);
      assertEquals(Double.NaN, double0, 0.01);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 865.78172;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 865.78172;
      doubleArray0[3] = 4886.068;
      doubleArray0[4] = 4886.068;
      doubleArray0[5] = (double) 0;
      doubleArray0[6] = Double.NaN;
      double[][] doubleArray1 = new double[8][4];
      double[] doubleArray2 = new double[3];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 0;
      doubleArray2[1] = (-1603.44527);
      doubleArray2[2] = Double.NaN;
      doubleArray1[0] = doubleArray2;
      doubleArray1[3] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      double[] doubleArray3 = new double[7];
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      
      doubleArray3[0] = (double) 0;
      doubleArray3[1] = (-1603.44527);
      doubleArray3[2] = Double.NaN;
      doubleArray3[3] = (-1603.44527);
      doubleArray3[4] = Double.NaN;
      doubleArray3[5] = 4886.068;
      doubleArray3[6] = (-956.0);
      doubleArray1[4] = doubleArray2;
      doubleArray1[5] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 3 != 7
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test044()  throws Throwable  {
      double double0 = 865.78172;
      double double1 = 0.8;
      int int0 = MathUtils.compareTo(865.78172, 0.8, 865.78172);
      assertEquals(0, int0);
      
      double double2 = MathUtils.log(865.78172, 0.8);
      assertEquals((-0.03299167137065451), double2, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(865.78172, 0.0, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test045()  throws Throwable  {
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, 0.0);
      assertTrue(boolean0);
      
      long long0 = new Integer(0);
      assertEquals(0, long0);
      
      // Undeclared exception!
      try { 
        MathUtils.equals((float) 0, (float) 0, 652893184);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test046()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(4.9E-324, 4.9E-324, 28);
      assertTrue(boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.equals((float) 28, (-273.0969F), 1717986918);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test047()  throws Throwable  {
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, 0.0);
      assertTrue(boolean0);
      
      long long0 = MathUtils.binomialCoefficient(0, 0);
      assertEquals(1L, long0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(Float.NaN, (-1964.3157F));
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      int int0 = MathUtils.gcd((-234), (-2073));
      assertEquals(3, int0);
  }

  @Test(timeout = 4000)
  public void test048()  throws Throwable  {
      double double0 = MathUtils.log((-1600.0), (-279.6566578));
      assertEquals(Double.NaN, double0, 0.01);
      
      byte byte0 = MathUtils.indicator((byte)111);
      assertEquals((byte)1, byte0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = Double.NaN;
      doubleArray0[1] = (-1600.0);
      doubleArray0[2] = (double) (byte)111;
      doubleArray0[3] = (-279.6566578);
      doubleArray0[4] = (-279.6566578);
      doubleArray0[5] = 1749.8551;
      doubleArray0[6] = Double.NaN;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {Double.NaN, (-1600.0), 111.0, (-279.6566578), (-279.6566578), 1749.8551, Double.NaN}, doubleArray0, 0.01);
      
      long long0 = MathUtils.mulAndCheck((long) (byte)1, (-828L));
      assertEquals((-828L), long0);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      short short0 = bigInteger0.shortValueExact();
      assertEquals((short)10, short0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = bigInteger0.shiftRight((byte)1);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((byte)5, bigInteger1.byteValue());
      assertEquals((short)5, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, (int) (byte)1);
      assertNotNull(bigInteger2);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger2.shortValue());
      assertEquals((byte)10, bigInteger2.byteValue());
      
      double double1 = MathUtils.indicator(Double.NaN);
      assertEquals(Double.NaN, double1, 0.01);
      assertEquals(double1, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.round((-1090.9F), 149, 559);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 559, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test049()  throws Throwable  {
      int int0 = MathUtils.sign(93);
      assertEquals(1, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(1.4E-45F, (-1915.0349F), 13);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equals((double) 1, 1.625, 2273.061240043721);
      assertTrue(boolean1);
      assertFalse(boolean1 == boolean0);
      
      int[] intArray0 = new int[3];
      intArray0[0] = 13;
      intArray0[1] = 1;
      intArray0[2] = 93;
      int[] intArray1 = new int[8];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = 93;
      intArray1[1] = 1;
      intArray1[2] = 93;
      intArray1[3] = 599;
      intArray1[4] = 93;
      intArray1[5] = 93;
      intArray1[6] = 1;
      intArray1[7] = 13;
      int int1 = MathUtils.distanceInf(intArray0, intArray1);
      assertEquals(80, int1);
      assertEquals(3, intArray0.length);
      assertEquals(8, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(int1 == int0);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {13, 1, 93}, intArray0);
      assertArrayEquals(new int[] {93, 1, 93, 599, 93, 93, 1, 13}, intArray1);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 93);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      
      boolean boolean2 = MathUtils.equals(541.21335057892, (double) (-1915.0349F), 93);
      assertFalse(boolean2);
      assertTrue(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 93;
      doubleArray0[1] = (double) (-1915.0349F);
      doubleArray0[2] = 2273.061240043721;
      doubleArray0[3] = (double) 13;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test050()  throws Throwable  {
      long long0 = MathUtils.indicator((-321L));
      assertEquals((-1L), long0);
      
      int int0 = MathUtils.compareTo((-1L), (-321L), 1.0);
      assertEquals(1, int0);
      
      long long1 = MathUtils.lcm((-189L), (-1L));
      assertEquals(189L, long1);
      assertFalse(long1 == long0);
      
      int[] intArray0 = new int[5];
      intArray0[0] = 1;
      intArray0[1] = 1;
      intArray0[2] = 1;
      intArray0[3] = 1;
      intArray0[4] = 1;
      int[] intArray1 = MathUtils.copyOf(intArray0, 1);
      assertNotNull(intArray1);
      assertEquals(5, intArray0.length);
      assertEquals(1, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {1, 1, 1, 1, 1}, intArray0);
      assertArrayEquals(new int[] {1}, intArray1);
      
      int int1 = MathUtils.lcm(1, 1);
      assertEquals(1, int1);
      assertTrue(int1 == int0);
      
      int int2 = MathUtils.subAndCheck(67, 1);
      assertEquals(66, int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      
      int int3 = MathUtils.hash(0.0);
      assertEquals(0, int3);
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) (-189L);
      doubleArray0[1] = (double) 1;
      doubleArray0[2] = (double) 1;
      doubleArray0[3] = (double) 66;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {(-189.0), 1.0, 1.0, 66.0}, doubleArray0, 0.01);
      
      int int4 = MathUtils.indicator(17);
      assertEquals(1, int4);
      assertFalse(int4 == int3);
      assertFalse(int4 == int2);
      assertTrue(int4 == int0);
      assertTrue(int4 == int1);
      
      int int5 = MathUtils.hash(2738.2331424026343);
      assertEquals(517308727, int5);
      assertFalse(int5 == int1);
      assertFalse(int5 == int4);
      assertFalse(int5 == int0);
      assertFalse(int5 == int2);
      assertFalse(int5 == int3);
      
      int int6 = MathUtils.hash(903.3);
      assertEquals(652893184, int6);
      assertFalse(int6 == int5);
      assertFalse(int6 == int0);
      assertFalse(int6 == int2);
      assertFalse(int6 == int1);
      assertFalse(int6 == int3);
      assertFalse(int6 == int4);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {(-189.0), 1.0, 1.0, 66.0}, doubleArray0, 0.01);
      
      double[] doubleArray1 = new double[6];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) (-1L);
      doubleArray1[1] = (double) 1;
      doubleArray1[2] = (double) 1;
      doubleArray1[3] = (double) 1;
      doubleArray1[4] = (double) 0;
      doubleArray1[5] = 0.0;
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray1);
      assertFalse(boolean1);
      assertEquals(6, doubleArray1.length);
      assertEquals(4, doubleArray0.length);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray0, doubleArray1);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(boolean1 == boolean0);
      assertArrayEquals(new double[] {(-1.0), 1.0, 1.0, 1.0, 0.0, 0.0}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {(-189.0), 1.0, 1.0, 66.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test051()  throws Throwable  {
      long long0 = MathUtils.pow(4149L, 4149L);
      assertEquals(3465265825139658085L, long0);
      
      float float0 = MathUtils.round((float) 3465265825139658085L, (-521));
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long1 = MathUtils.gcd(4294967295L, 39916800L);
      assertEquals(15L, long1);
      assertFalse(long1 == long0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 4294967295L;
      floatArray0[1] = Float.NaN;
      floatArray0[2] = (float) 4149L;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertEquals(3, floatArray0.length);
      assertArrayEquals(new float[] {4.2949673E9F, Float.NaN, 4149.0F}, floatArray0, 0.01F);
      
      long long2 = MathUtils.pow(4149L, 2509);
      assertEquals(5451441762177982469L, long2);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      
      double double0 = MathUtils.binomialCoefficientDouble(2509, 193);
      assertEquals(9.449935296536114E293, double0, 0.01);
      
      int[] intArray0 = new int[9];
      intArray0[0] = 66;
      intArray0[1] = 2509;
      intArray0[2] = 2509;
      intArray0[3] = 2509;
      intArray0[4] = 2509;
      intArray0[5] = 193;
      intArray0[6] = 2509;
      intArray0[8] = 193;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(9, intArray1.length);
      assertEquals(9, intArray0.length);
      assertNotSame(intArray1, intArray0);
      assertNotSame(intArray0, intArray1);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {66, 2509, 2509, 2509, 2509, 193, 2509, 0, 193}, intArray1);
      assertArrayEquals(new int[] {66, 2509, 2509, 2509, 2509, 193, 2509, 0, 193}, intArray0);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) Float.NaN;
      doubleArray0[1] = (double) 193;
      doubleArray0[2] = 9.449935296536114E293;
      doubleArray0[3] = (double) 4.2949673E9F;
      doubleArray0[4] = (double) 66;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // value \uFFFD at index 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test052()  throws Throwable  {
      long long0 = MathUtils.pow(4149L, 4149L);
      assertEquals(3465265825139658085L, long0);
      
      float float0 = MathUtils.round((float) 3465265825139658085L, (-521));
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long1 = MathUtils.gcd(4294967295L, 39916800L);
      assertEquals(15L, long1);
      assertFalse(long1 == long0);
      
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 4294967295L;
      floatArray0[1] = Float.NaN;
      floatArray0[2] = (float) 4149L;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertEquals(3, floatArray0.length);
      assertArrayEquals(new float[] {4.2949673E9F, Float.NaN, 4149.0F}, floatArray0, 0.01F);
      
      long long2 = MathUtils.pow(4149L, 2509);
      assertEquals(5451441762177982469L, long2);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      
      double double0 = MathUtils.binomialCoefficientDouble(2509, 193);
      assertEquals(9.449935296536114E293, double0, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) Float.NaN;
      doubleArray0[1] = (double) 193;
      doubleArray0[2] = 9.449935296536114E293;
      doubleArray0[3] = (double) 4.2949673E9F;
      doubleArray0[4] = (double) 193;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // value \uFFFD at index 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test053()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(0L, 0L);
      assertEquals(0L, long0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 0L;
      doubleArray0[2] = (double) 0L;
      doubleArray0[3] = (double) 0L;
      doubleArray0[4] = (double) 0L;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertNotNull(doubleArray1);
      assertEquals(5, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      
      boolean boolean0 = MathUtils.equals(0.0, Double.POSITIVE_INFINITY);
      assertFalse(boolean0);
      
      double double0 = MathUtils.sign(465.2046467442033);
      assertEquals(1.0, double0, 0.01);
      
      byte byte0 = MathUtils.indicator((byte)11);
      assertEquals((byte)1, byte0);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) 0L, 0.0, 465.2046467442033);
      assertTrue(boolean1);
      assertFalse(boolean1 == boolean0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(0.0, 0.0, (int) (byte)11);
      assertTrue(boolean2);
      assertFalse(boolean2 == boolean0);
      assertTrue(boolean2 == boolean1);
      
      int int0 = MathUtils.indicator(0);
      assertEquals(1, int0);
      
      boolean boolean3 = MathUtils.equalsIncludingNaN((-2078.69156599906), (-1442.11926567294));
      assertFalse(boolean3);
      assertTrue(boolean3 == boolean0);
      assertFalse(boolean3 == boolean2);
      assertFalse(boolean3 == boolean1);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short) (-7168), bigInteger1.shortValue());
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, (long) (byte)11);
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short) (-6144), bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
  }

  @Test(timeout = 4000)
  public void test054()  throws Throwable  {
      int int0 = MathUtils.compareTo((-1.7976931348623157E308), 0.0, (-1314.8976));
      assertEquals((-1), int0);
      
      double double0 = MathUtils.cosh((-1.7976931348623157E308));
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      long long0 = MathUtils.lcm((long) (-1), (long) (-1));
      assertEquals(1L, long0);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) 1L;
      doubleArray0[1] = Double.POSITIVE_INFINITY;
      doubleArray0[2] = (double) (-1);
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, 2.608E18);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // Array contains an infinite element, \u221E at index 1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test055()  throws Throwable  {
      float float0 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float0, 0.01F);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 0.0F;
      doubleArray0[1] = (double) 0.0F;
      double[][] doubleArray1 = new double[2][9];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertEquals(2, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      
      long long0 = MathUtils.subAndCheck((-2342L), (-2247L));
      assertEquals((-95L), long0);
      
      int int0 = MathUtils.pow((-1698), 0L);
      assertEquals(1, int0);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(2, doubleArray0.length);
      assertEquals(2, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      
      // Undeclared exception!
      MathUtils.factorialDouble(2147469360);
  }

  @Test(timeout = 4000)
  public void test056()  throws Throwable  {
      double double0 = MathUtils.indicator(1657.78195);
      assertEquals(1.0, double0, 0.01);
      
      float float0 = 855.69F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(855.69F, 855.69F);
      assertTrue(boolean0);
      
      long long0 = (-768L);
      long long1 = MathUtils.lcm((-768L), 2147483647L);
      assertEquals(1649267440896L, long1);
      assertFalse(long1 == long0);
      
      double double1 = MathUtils.indicator((double) (-768L));
      assertEquals((-1.0), double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) 855.69F, (double) 1649267440896L);
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      short short0 = MathUtils.indicator((short) (-2025));
      assertEquals((short) (-1), short0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 2147483647L;
      doubleArray0[1] = (double) 1649267440896L;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals((-1014496351), int0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {2.147483647E9, 1.649267440896E12}, doubleArray0, 0.01);
      
      double double2 = MathUtils.safeNorm(doubleArray0);
      assertEquals(1.64926883899674E12, double2, 0.01);
      assertEquals(2, doubleArray0.length);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertArrayEquals(new double[] {2.147483647E9, 1.649267440896E12}, doubleArray0, 0.01);
      
      double[] doubleArray1 = new double[3];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) 2147483647L;
      doubleArray1[1] = (double) (-1014496351);
      double double3 = 616.4;
      doubleArray1[2] = 616.4;
      double[] doubleArray2 = MathUtils.copyOf(doubleArray1);
      assertNotNull(doubleArray2);
      assertEquals(3, doubleArray2.length);
      assertEquals(3, doubleArray1.length);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray1, doubleArray2);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {2.147483647E9, (-1.014496351E9), 616.4}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {2.147483647E9, (-1.014496351E9), 616.4}, doubleArray1, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.lcm((-2644281811660520851L), (-52L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test057()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 8.0E298;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.99;
      doubleArray0[3] = 93.4772862;
      doubleArray0[4] = 3483.0;
      doubleArray0[5] = 1068.662;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[4][4];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(6, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 0.99, 93.4772862, 1068.662, 3483.0, 8.0E298}, doubleArray0, 0.01);
      
      double double0 = MathUtils.round((-1772.012684812), 2847);
      assertEquals((-1772.012684812), double0, 0.01);
      
      long long0 = MathUtils.mulAndCheck((long) 2847, 4L);
      assertEquals(11388L, long0);
      
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.99, 93.4772862, 1068.662, 3483.0, 8.0E298}, doubleArray0, 0.01);
      
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals(1693070687, int0);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.99, 93.4772862, 1068.662, 3483.0, 8.0E298}, doubleArray0, 0.01);
      
      float float0 = MathUtils.indicator((float) 1693070687);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test058()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (-4009.750308);
      doubleArray0[1] = Double.NaN;
      doubleArray0[2] = 1190.2;
      doubleArray0[3] = (-2789.991);
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (-1969.9);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-4009.750308), Double.NaN, 1190.2, (-2789.991), 0.0, (-1969.9)}, doubleArray0, 0.01);
      
      double double1 = MathUtils.indicator((-4009.750308));
      assertEquals((-1.0), double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      long long0 = MathUtils.gcd((-2672L), 9154082963658192752L);
      assertEquals(16L, long0);
      
      double double2 = MathUtils.safeNorm(doubleArray0);
      assertEquals(Double.NaN, double2, 0.01);
      assertEquals(6, doubleArray0.length);
      assertNotEquals(double2, double1, 0.01);
      assertEquals(double2, double0, 0.01);
      assertArrayEquals(new double[] {(-4009.750308), Double.NaN, 1190.2, (-2789.991), 0.0, (-1969.9)}, doubleArray0, 0.01);
      
      boolean boolean0 = MathUtils.equals(Float.NEGATIVE_INFINITY, 0.0F, 6);
      assertFalse(boolean0);
      
      int int0 = MathUtils.hash((double) Float.NEGATIVE_INFINITY);
      assertEquals((-1048576), int0);
      
      long long1 = MathUtils.gcd((-993L), (long) 6);
      assertEquals(3L, long1);
      assertFalse(long1 == long0);
      
      byte byte0 = MathUtils.indicator((byte) (-31));
      assertEquals((byte) (-1), byte0);
      
      double double3 = MathUtils.binomialCoefficientLog(353, (-1048576));
      assertEquals(0.0, double3, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double1, 0.01);
      
      float[] floatArray0 = new float[5];
      floatArray0[0] = (float) 3L;
      floatArray0[1] = (float) (-2672L);
      floatArray0[2] = (float) 9154082963658192752L;
      floatArray0[3] = Float.NaN;
      floatArray0[4] = (float) 6;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean1);
      assertEquals(5, floatArray0.length);
      assertFalse(boolean1 == boolean0);
      assertArrayEquals(new float[] {3.0F, (-2672.0F), 9.154083E18F, Float.NaN, 6.0F}, floatArray0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test059()  throws Throwable  {
      long long0 = MathUtils.indicator((-321L));
      assertEquals((-1L), long0);
      
      int int0 = MathUtils.compareTo((-1L), (-321L), 1.0);
      assertEquals(1, int0);
      
      long long1 = MathUtils.lcm((-189L), (-1L));
      assertEquals(189L, long1);
      assertFalse(long1 == long0);
      
      int[] intArray0 = new int[5];
      intArray0[0] = 1;
      intArray0[1] = 1;
      intArray0[2] = 1;
      intArray0[3] = 1;
      intArray0[4] = 1;
      int[] intArray1 = MathUtils.copyOf(intArray0, 1);
      assertNotNull(intArray1);
      assertEquals(5, intArray0.length);
      assertEquals(1, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {1, 1, 1, 1, 1}, intArray0);
      assertArrayEquals(new int[] {1}, intArray1);
      
      int int1 = MathUtils.lcm(1, 1);
      assertEquals(1, int1);
      assertTrue(int1 == int0);
      
      int int2 = MathUtils.subAndCheck(67, 1);
      assertEquals(66, int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      
      int int3 = MathUtils.hash(0.0);
      assertEquals(0, int3);
      assertFalse(int3 == int0);
      assertFalse(int3 == int1);
      assertFalse(int3 == int2);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) (-189L);
      doubleArray0[1] = (double) 1;
      doubleArray0[2] = (double) 1;
      doubleArray0[3] = (double) 66;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {(-189.0), 1.0, 1.0, 66.0}, doubleArray0, 0.01);
      
      int int4 = MathUtils.indicator(17);
      assertEquals(1, int4);
      assertTrue(int4 == int1);
      assertFalse(int4 == int3);
      assertFalse(int4 == int2);
      assertTrue(int4 == int0);
      
      int int5 = MathUtils.hash(2738.2331424026343);
      assertEquals(517308727, int5);
      assertFalse(int5 == int2);
      assertFalse(int5 == int0);
      assertFalse(int5 == int1);
      assertFalse(int5 == int3);
      assertFalse(int5 == int4);
      
      int int6 = MathUtils.hash(903.3);
      assertEquals(652893184, int6);
      assertFalse(int6 == int1);
      assertFalse(int6 == int2);
      assertFalse(int6 == int4);
      assertFalse(int6 == int3);
      assertFalse(int6 == int5);
      assertFalse(int6 == int0);
      
      double double0 = MathUtils.binomialCoefficientLog(17, 5);
      assertEquals(8.730367211692958, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test060()  throws Throwable  {
      int[] intArray0 = new int[3];
      intArray0[0] = 2998;
      intArray0[1] = 0;
      intArray0[2] = 74;
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(3, intArray0.length);
      assertArrayEquals(new int[] {2998, 0, 74}, intArray0);
      
      boolean boolean0 = MathUtils.equals(3.4028235E38F, 0.0F);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equals((float) 0, (float) 74, 2998);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((double) 2998, (double) 0.0F, (double) 2998);
      assertTrue(boolean2);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      
      int int0 = MathUtils.sign((-2428));
      assertEquals((-1), int0);
      
      boolean boolean3 = MathUtils.equalsIncludingNaN((double) 74, 6.283185307179586, (-1440.93858672));
      assertFalse(boolean3);
      assertTrue(boolean3 == boolean1);
      assertTrue(boolean3 == boolean0);
      assertFalse(boolean3 == boolean2);
      
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(3, intArray0.length);
      assertEquals(3, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {2998, 0, 74}, intArray0);
      assertArrayEquals(new int[] {2998, 0, 74}, intArray1);
      
      double double1 = MathUtils.factorialLog(15);
      assertEquals(27.89927138384089, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      int int1 = MathUtils.pow(2998, 16658445L);
      assertEquals(0, int1);
      assertFalse(int1 == int0);
  }

  @Test(timeout = 4000)
  public void test061()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte) (-6));
      assertEquals((byte) (-1), byte0);
      
      int int0 = MathUtils.lcm((int) (byte) (-6), (int) (byte) (-6));
      assertEquals(6, int0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) (byte) (-6);
      doubleArray0[1] = (double) (byte) (-1);
      doubleArray0[2] = (double) (byte) (-6);
      doubleArray0[3] = (double) (byte) (-1);
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (double) (byte) (-6);
      doubleArray0[6] = (double) (byte) (-1);
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {(-6.0), (-1.0), (-6.0), (-1.0), 0.0, (-6.0), (-1.0)}, doubleArray0, 0.01);
      
      double double0 = MathUtils.sign(Double.POSITIVE_INFINITY);
      assertEquals(1.0, double0, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((-6.0), Double.POSITIVE_INFINITY);
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double1, 0.01);
      assertEquals(7, doubleArray0.length);
      assertNotEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {(-6.0), (-1.0), (-6.0), (-1.0), 0.0, (-6.0), (-1.0)}, doubleArray0, 0.01);
      
      int int1 = MathUtils.hash(0.0);
      assertEquals(0, int1);
      assertFalse(int1 == int0);
  }

  @Test(timeout = 4000)
  public void test062()  throws Throwable  {
      int int0 = 5;
      // Undeclared exception!
      try { 
        MathUtils.round(2.0, 2146867760, 5);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // BigInteger would overflow supported range
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test063()  throws Throwable  {
      int int0 = MathUtils.lcm(0, 0);
      assertEquals(0, int0);
      
      double[] doubleArray0 = new double[10];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, false);
      assertFalse(boolean0);
      assertEquals(10, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      boolean boolean1 = bigInteger0.testBit(0);
      assertTrue(boolean1);
      assertFalse(boolean1 == boolean0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      float float0 = bigInteger0.floatValue();
      assertEquals(1.0F, float0, 0.01F);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = bigInteger0.not();
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((byte) (-2), bigInteger1.byteValue());
      assertEquals((short) (-2), bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger0.pow(0);
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, (long) 0);
      assertNotNull(bigInteger3);
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertSame(bigInteger3, bigInteger2);
      assertSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger3.shortValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      
      int[] intArray0 = new int[1];
      intArray0[0] = 0;
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1, intArray0.length);
      assertArrayEquals(new int[] {0}, intArray0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((double) 0, (-479.0), 0.0);
      assertFalse(boolean2);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      
      int int1 = MathUtils.indicator(0);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
      
      double double1 = MathUtils.factorialLog(0);
      assertEquals(0.0, double1, 0.01);
      assertEquals(double1, double0, 0.01);
      
      boolean boolean3 = bigInteger0.testBit(0);
      assertTrue(boolean3);
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertFalse(boolean3 == boolean0);
      assertTrue(boolean3 == boolean1);
      assertFalse(boolean3 == boolean2);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      double double2 = MathUtils.log(4207.7, Double.NEGATIVE_INFINITY);
      assertEquals(Double.NaN, double2, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.ZERO_NOT_ALLOWED;
      assertEquals(LocalizedFormats.ZERO_NOT_ALLOWED, localizedFormats0);
      assertEquals("zero not allowed here", localizedFormats0.getSourceString());
      
      MathUtils.checkNotNull((Object) bigInteger3, (Localizable) localizedFormats0, (Object[]) mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertSame(bigInteger3, bigInteger2);
      assertSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertEquals("zero not allowed here", localizedFormats0.getSourceString());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger3.shortValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      
      double double3 = MathUtils.cosh(0);
      assertEquals(1.0, double3, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test064()  throws Throwable  {
      int int0 = 67;
      boolean boolean0 = MathUtils.equalsIncludingNaN((-53.67F), (-53.67F), 67);
      assertTrue(boolean0);
      
      int[] intArray0 = new int[5];
      intArray0[0] = 67;
      intArray0[1] = 67;
      intArray0[2] = 67;
      intArray0[3] = 67;
      intArray0[4] = 67;
      int[] intArray1 = MathUtils.copyOf(intArray0, 67);
      assertNotNull(intArray1);
      assertEquals(5, intArray0.length);
      assertEquals(67, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {67, 67, 67, 67, 67}, intArray0);
      
      boolean boolean1 = MathUtils.equals((float[]) null, (float[]) null);
      assertTrue(boolean1);
      assertTrue(boolean1 == boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck((-4267615245585081135L), (-4267615245585081135L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test065()  throws Throwable  {
      int int0 = 633;
      int int1 = MathUtils.addAndCheck(633, 633);
      assertEquals(1266, int1);
      assertFalse(int1 == int0);
      
      float[] floatArray0 = new float[9];
      floatArray0[0] = (float) 1266;
      floatArray0[1] = (float) 1266;
      floatArray0[2] = (float) 633;
      floatArray0[3] = 2343.0F;
      floatArray0[4] = (float) 1266;
      floatArray0[5] = (float) 1266;
      floatArray0[6] = (float) 1266;
      floatArray0[7] = (float) 633;
      floatArray0[8] = (float) 1266;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertEquals(9, floatArray0.length);
      assertArrayEquals(new float[] {1266.0F, 1266.0F, 633.0F, 2343.0F, 1266.0F, 1266.0F, 1266.0F, 633.0F, 1266.0F}, floatArray0, 0.01F);
      
      int[] intArray0 = new int[6];
      intArray0[0] = 1266;
      intArray0[1] = 633;
      intArray0[2] = 633;
      intArray0[3] = 633;
      intArray0[4] = 633;
      intArray0[5] = 633;
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(6, intArray0.length);
      assertArrayEquals(new int[] {1266, 633, 633, 633, 633, 633}, intArray0);
      
      // Undeclared exception!
      try { 
        MathUtils.round((double) 633, (-2146162030));
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // BigInteger would overflow supported range
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test066()  throws Throwable  {
      float[] floatArray0 = new float[5];
      floatArray0[0] = 1.4E-45F;
      floatArray0[1] = Float.NEGATIVE_INFINITY;
      floatArray0[2] = Float.NaN;
      floatArray0[3] = Float.NaN;
      floatArray0[4] = 0.5F;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertFalse(boolean0);
      assertEquals(5, floatArray0.length);
      assertArrayEquals(new float[] {1.4E-45F, Float.NEGATIVE_INFINITY, Float.NaN, Float.NaN, 0.5F}, floatArray0, 0.01F);
      
      double[] doubleArray0 = new double[0];
      MathUtils.checkFinite(doubleArray0);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.equals((double) Float.NaN, (double) Float.NEGATIVE_INFINITY, Integer.MAX_VALUE);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test067()  throws Throwable  {
      long long0 = MathUtils.subAndCheck(4294967295L, (-1783L));
      assertEquals(4294969078L, long0);
      
      long long1 = MathUtils.mulAndCheck((-685L), (-1L));
      assertEquals(685L, long1);
      assertFalse(long1 == long0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(1406, (-4321L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-4,321)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test068()  throws Throwable  {
      int[] intArray0 = new int[4];
      intArray0[0] = 1693;
      intArray0[1] = (-1);
      intArray0[2] = (-2726);
      intArray0[3] = 4194304;
      int[] intArray1 = new int[5];
      assertFalse(intArray1.equals((Object)intArray0));
      
      intArray1[0] = (-1);
      intArray1[1] = (-2726);
      intArray1[2] = 4194304;
      intArray1[3] = 1693;
      intArray1[4] = 4194304;
      double double0 = MathUtils.distance(intArray0, intArray1);
      assertEquals(5932373.733463023, double0, 0.01);
      assertEquals(4, intArray0.length);
      assertEquals(5, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray0.equals((Object)intArray1));
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {1693, (-1), (-2726), 4194304}, intArray0);
      assertArrayEquals(new int[] {(-1), (-2726), 4194304, 1693, 4194304}, intArray1);
  }

  @Test(timeout = 4000)
  public void test069()  throws Throwable  {
      int[] intArray0 = new int[1];
      intArray0[0] = 1148;
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(1, intArray0.length);
      assertArrayEquals(new int[] {1148}, intArray0);
      
      long long0 = MathUtils.pow(2420L, 949L);
      assertEquals(0L, long0);
      
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F);
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 0.0F;
      doubleArray0[1] = (double) 0.0F;
      doubleArray0[2] = (double) 0L;
      doubleArray0[3] = (double) 1148;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, (double[][]) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test070()  throws Throwable  {
      int int0 = MathUtils.hash(0.0);
      assertEquals(0, int0);
      
      int int1 = 0;
      int int2 = MathUtils.gcd((-822), 0);
      assertEquals(822, int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      
      int int3 = MathUtils.mulAndCheck(0, 822);
      assertEquals(0, int3);
      assertFalse(int3 == int2);
      assertTrue(int3 == int0);
      assertTrue(int3 == int1);
      
      boolean boolean0 = MathUtils.equals((double) 822, (double) (-822), (double) 0);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[0];
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test071()  throws Throwable  {
      int int0 = 76;
      double double0 = MathUtils.binomialCoefficientLog(76, 76);
      assertEquals(0.0, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray((double[]) null, 0.0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test072()  throws Throwable  {
      int[] intArray0 = new int[6];
      intArray0[0] = (-425);
      intArray0[1] = (-114);
      int int0 = 61;
      intArray0[2] = 61;
      intArray0[3] = 102;
      intArray0[4] = (-2830);
      int int1 = (-846);
      intArray0[5] = (-846);
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(6, intArray0.length);
      assertArrayEquals(new int[] {(-425), (-114), 61, 102, (-2830), (-846)}, intArray0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 102;
      doubleArray0[1] = (double) 61;
      doubleArray0[2] = (double) (-2830);
      doubleArray0[3] = (double) (-114);
      doubleArray0[4] = (double) 61;
      doubleArray0[5] = 0.0;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {102.0, 61.0, (-2830.0), (-114.0), 61.0, 0.0}, doubleArray0, 0.01);
      
      long long0 = MathUtils.mulAndCheck((long) (-846), 0L);
      assertEquals(0L, long0);
      
      int int2 = 2167;
      int int3 = MathUtils.subAndCheck(131, 2167);
      assertEquals((-2036), int3);
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
      
      long long1 = MathUtils.sign(0L);
      assertEquals(0L, long1);
      assertTrue(long1 == long0);
      
      // Undeclared exception!
      try { 
        MathUtils.equals((-332.8906F), (float) (-2036), (-1271));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test073()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(Double.NaN, Double.NaN);
      assertFalse(boolean0);
      
      double double0 = MathUtils.cosh(603.553);
      assertEquals(6.587299967846383E261, double0, 0.01);
      
      double double1 = MathUtils.cosh(6.587299967846383E261);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      int int0 = (-365);
      byte[] byteArray0 = new byte[1];
      byte byte0 = (byte)110;
      byteArray0[0] = (byte)110;
      BigInteger bigInteger0 = null;
      try {
        bigInteger0 = new BigInteger((-365), byteArray0);
        fail("Expecting exception: NumberFormatException");
      
      } catch(NumberFormatException e) {
         //
         // Invalid signum value
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test074()  throws Throwable  {
      long long0 = MathUtils.indicator((-1L));
      assertEquals((-1L), long0);
      
      int int0 = 0;
      int int1 = (-902);
      long long1 = MathUtils.binomialCoefficient(0, (-902));
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = bigInteger0.nextProbablePrime();
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)2, bigInteger1.byteValue());
      assertEquals((short)2, bigInteger1.shortValue());
      
      // Undeclared exception!
      try { 
        bigInteger0.divideAndRemainder(bigInteger0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // BigInteger divide by zero
         //
         verifyException("java.math.MutableBigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test075()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (-3159.083577368);
      doubleArray0[2] = (-2626.218377);
      doubleArray0[3] = 9.313225746154785E-10;
      doubleArray0[4] = 0.0;
      MathUtils.checkFinite(doubleArray0);
      assertEquals(5, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, (-3159.083577368), (-2626.218377), 9.313225746154785E-10, 0.0}, doubleArray0, 0.01);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-2626.218377));
      assertNotNull(doubleArray1);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, (-3159.083577368), (-2626.218377), 9.313225746154785E-10, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-1434.0553718376143), (-1192.1630051628088), 4.2277075244514326E-10, 0.0}, doubleArray1, 0.01);
      
      double double0 = MathUtils.safeNorm(doubleArray1);
      assertEquals(1864.8773258247143, double0, 0.01);
      assertEquals(5, doubleArray0.length);
      assertEquals(5, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, (-3159.083577368), (-2626.218377), 9.313225746154785E-10, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.0, (-1434.0553718376143), (-1192.1630051628088), 4.2277075244514326E-10, 0.0}, doubleArray1, 0.01);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short) (-7168), bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      int[] intArray0 = new int[5];
      intArray0[0] = (-943);
      intArray0[1] = (-51);
      intArray0[2] = 709;
      intArray0[3] = 0;
      intArray0[4] = 0;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(5, intArray0.length);
      assertArrayEquals(new int[] {(-943), (-51), 709, 0, 0}, intArray0);
  }

  @Test(timeout = 4000)
  public void test076()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotNull(doubleArray1);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      
      double double0 = MathUtils.round(2887.95650298, 166);
      assertEquals(2887.95650298, double0, 0.01);
      
      int int0 = MathUtils.sign(123);
      assertEquals(1, int0);
      
      double double1 = MathUtils.binomialCoefficientDouble(498, (-282));
      assertEquals(1.0, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      int[] intArray0 = new int[6];
      intArray0[0] = 1;
      intArray0[1] = 166;
      intArray0[2] = 123;
      intArray0[3] = 166;
      intArray0[4] = 166;
      intArray0[5] = (-282);
      int int1 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int1);
      assertEquals(6, intArray0.length);
      assertFalse(int1 == int0);
      assertArrayEquals(new int[] {1, 166, 123, 166, 166, (-282)}, intArray0);
      
      double double2 = MathUtils.binomialCoefficientLog(166, 0);
      assertEquals(0.0, double2, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      long long0 = MathUtils.gcd((long) 498, (-2480L));
      assertEquals(2L, long0);
      
      boolean boolean0 = MathUtils.equals((-0.16624882440418567), 0.0);
      assertFalse(boolean0);
      
      double double3 = MathUtils.sign((double) (-282));
      assertEquals((-1.0), double3, 0.01);
      assertNotEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double1, 0.01);
      
      float[] floatArray0 = new float[4];
      floatArray0[0] = (float) (-282);
      floatArray0[1] = (float) 166;
      floatArray0[2] = (float) (-282);
      floatArray0[3] = (float) 123;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean1);
      assertEquals(4, floatArray0.length);
      assertFalse(boolean1 == boolean0);
      assertArrayEquals(new float[] {(-282.0F), 166.0F, (-282.0F), 123.0F}, floatArray0, 0.01F);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(1768.2908F, (float) 0, (-1168.32F));
      assertFalse(boolean2);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      
      byte byte0 = MathUtils.indicator((byte)43);
      assertEquals((byte)1, byte0);
      
      long long1 = MathUtils.addAndCheck(6227020800L, (long) (byte)43);
      assertEquals(6227020843L, long1);
      assertFalse(long1 == long0);
  }

  @Test(timeout = 4000)
  public void test077()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 3.141592653589793;
      doubleArray0[1] = 2963.5833;
      doubleArray0[2] = (-847.315652);
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {3.141592653589793, 2963.5833, (-847.315652)}, doubleArray0, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      int int0 = bigInteger0.intValueExact();
      assertEquals(1, int0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 1741);
      assertNotNull(bigInteger1);
      assertSame(bigInteger0, bigInteger1);
      assertSame(bigInteger1, bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger1.modPow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger2);
      assertSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      assertEquals((short)0, bigInteger2.shortValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger1, 423);
      assertNotNull(bigInteger3);
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger2);
      assertSame(bigInteger1, bigInteger3);
      assertSame(bigInteger1, bigInteger0);
      assertSame(bigInteger3, bigInteger1);
      assertSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((short)1, bigInteger3.shortValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      
      double double0 = MathUtils.binomialCoefficientLog(1741, 721);
      assertEquals(1177.0242487506446, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test078()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (-2714.076597629035);
      doubleArray0[1] = 1504.9882877499;
      doubleArray0[2] = 48.662;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (-1374.771);
      doubleArray0[6] = 1964.0404;
      doubleArray0[7] = 9.220590270857665E-9;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(3921.8642900512423, double0, 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {(-2714.076597629035), 1504.9882877499, 48.662, 0.0, 0.0, (-1374.771), 1964.0404, 9.220590270857665E-9}, doubleArray0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(1.304E19, Double.NEGATIVE_INFINITY);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(1.986821492305628E-8, (-1374.771), 2245);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      double double1 = MathUtils.indicator(Double.NaN);
      assertEquals(Double.NaN, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      float[] floatArray0 = new float[4];
      floatArray0[0] = (float) 2245;
      floatArray0[1] = (float) 2245;
      floatArray0[2] = 0.0F;
      floatArray0[3] = (float) 2245;
      boolean boolean2 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean2);
      assertEquals(4, floatArray0.length);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertArrayEquals(new float[] {2245.0F, 2245.0F, 0.0F, 2245.0F}, floatArray0, 0.01F);
      
      boolean boolean3 = MathUtils.equals((-2004.07), 0.0, 0.0);
      assertFalse(boolean3);
      assertTrue(boolean3 == boolean1);
      assertTrue(boolean3 == boolean0);
      assertFalse(boolean3 == boolean2);
  }

  @Test(timeout = 4000)
  public void test079()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-196.0F), Float.NaN);
      assertFalse(boolean0);
      
      float float0 = MathUtils.sign(Float.NaN);
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long0 = MathUtils.pow(1293L, 1293L);
      assertEquals(8292019799662934781L, long0);
      
      long long1 = MathUtils.gcd(765L, (-4888L));
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      int int0 = MathUtils.hash(0.0);
      assertEquals(0, int0);
      
      long long2 = MathUtils.indicator(1180L);
      assertEquals(1L, long2);
      assertFalse(long2 == long0);
      assertTrue(long2 == long1);
      
      boolean boolean1 = MathUtils.equals(1459.485862047644, 1923.7175);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) 1L;
      doubleArray0[1] = (double) 765L;
      doubleArray0[2] = (double) 0;
      doubleArray0[3] = 1907.56902;
      doubleArray0[4] = (double) (-196.0F);
      doubleArray0[5] = (double) 1293L;
      doubleArray0[6] = (-3255.99201848);
      doubleArray0[7] = (double) (-196.0F);
      doubleArray0[8] = 1459.485862047644;
      int int1 = MathUtils.hash(doubleArray0);
      assertEquals(265435477, int1);
      assertEquals(9, doubleArray0.length);
      assertFalse(int1 == int0);
      assertArrayEquals(new double[] {1.0, 765.0, 0.0, 1907.56902, (-196.0), 1293.0, (-3255.99201848), (-196.0), 1459.485862047644}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test080()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(3187L, 1307674368000L);
      assertEquals(1307674371187L, long0);
      
      long long1 = MathUtils.indicator(1307674371187L);
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      int int0 = 0;
      int int1 = MathUtils.lcm(0, 0);
      assertEquals(0, int1);
      assertTrue(int1 == int0);
      
      double double0 = MathUtils.log(0, 1307674371187L);
      assertEquals(-0.0, double0, 0.01);
      
      double double1 = MathUtils.sign((double) 0);
      assertEquals(0.0, double1, 0.01);
      assertEquals(double1, double0, 0.01);
      
      double double2 = MathUtils.cosh(0);
      assertEquals(1.0, double2, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      long long2 = (-1097L);
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, (-1097L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,097)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test081()  throws Throwable  {
      int int0 = MathUtils.compareTo(0.0, (-5460.0), 0.0);
      assertEquals(1, int0);
      
      boolean boolean0 = MathUtils.equals(0.0, (-371.5098), 5290.857355545);
      assertTrue(boolean0);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-371.5098);
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(371.5098, double0, 0.01);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {(-371.5098)}, doubleArray0, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean1);
      assertEquals(1, doubleArray0.length);
      assertTrue(boolean1 == boolean0);
      assertArrayEquals(new double[] {(-371.5098)}, doubleArray0, 0.01);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN(0.0, 371.5098, (-371.5098));
      assertFalse(boolean2);
      assertFalse(boolean2 == boolean1);
      assertFalse(boolean2 == boolean0);
      
      int int1 = (-1272);
      // Undeclared exception!
      try { 
        MathUtils.pow((long) 1, (-1272));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,272)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test082()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(1.0F, (-3153.3228F));
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equals((-3153.3228F), (-852.5F), 2595);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
  }

  @Test(timeout = 4000)
  public void test083()  throws Throwable  {
      int int0 = 0;
      long long0 = MathUtils.pow(0L, 0);
      assertEquals(1L, long0);
      
      String string0 = "lD/g>y_0U+C}}";
      LocalizedFormats localizedFormats0 = LocalizedFormats.DUPLICATED_ABSCISSA;
      assertEquals(LocalizedFormats.DUPLICATED_ABSCISSA, localizedFormats0);
      assertEquals("Abscissa {0} is duplicated at both indices {1} and {2}", localizedFormats0.getSourceString());
      
      Object[] objectArray0 = new Object[7];
      objectArray0[0] = (Object) "lD/g>y_0U+C}}";
      objectArray0[1] = (Object) localizedFormats0;
      objectArray0[2] = (Object) "lD/g>y_0U+C}}";
      objectArray0[3] = (Object) "lD/g>y_0U+C}}";
      objectArray0[4] = (Object) "lD/g>y_0U+C}}";
      objectArray0[5] = (Object) localizedFormats0;
      Object object0 = new Object();
      assertNotNull(object0);
      
      objectArray0[6] = object0;
      MathUtils.checkNotNull((Object) "lD/g>y_0U+C}}", (Localizable) localizedFormats0, objectArray0);
      assertEquals(7, objectArray0.length);
      assertEquals("Abscissa {0} is duplicated at both indices {1} and {2}", localizedFormats0.getSourceString());
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = BigInteger.ZERO;
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      byte[] byteArray0 = bigInteger1.toByteArray();
      assertNotNull(byteArray0);
      assertEquals(1, byteArray0.length);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertArrayEquals(new byte[] {(byte)0}, byteArray0);
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      int int1 = bigInteger1.intValue();
      assertEquals(0, int1);
      assertNotSame(bigInteger1, bigInteger0);
      assertTrue(int1 == int0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = bigInteger0.multiply(bigInteger1);
      assertNotNull(bigInteger2);
      assertSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertSame(bigInteger1, bigInteger2);
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-2246L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-2,246)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test084()  throws Throwable  {
      BigInteger bigInteger0 = null;
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, (BigInteger) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test085()  throws Throwable  {
      int[] intArray0 = new int[9];
      int int0 = 0;
      intArray0[0] = 0;
      intArray0[1] = (-517);
      intArray0[2] = 186;
      intArray0[3] = 0;
      intArray0[4] = 2874;
      intArray0[5] = (-277);
      intArray0[6] = (-460);
      intArray0[7] = 1186;
      intArray0[8] = 2;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(9, intArray0.length);
      assertEquals(9, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {0, (-517), 186, 0, 2874, (-277), (-460), 1186, 2}, intArray0);
      assertArrayEquals(new int[] {0, (-517), 186, 0, 2874, (-277), (-460), 1186, 2}, intArray1);
      
      float float0 = 1257.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 2, 1257.0F);
      assertFalse(boolean0);
      
      long long0 = MathUtils.sign((long) 0);
      assertEquals(0L, long0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) (-460), (float) 1186);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("{,M#U`Mj^bgQ-Y,");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.{,M#U`Mj^bgQ-Y,
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test086()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      Double double0 = new Double(10.0);
      assertEquals(10.0, (double)double0, 0.01);
      assertNotNull(double0);
      
      double double1 = Double.sum(Double.NEGATIVE_INFINITY, 10.0);
      assertEquals(Double.NEGATIVE_INFINITY, double1, 0.01);
      
      boolean boolean0 = bigInteger0.equals(double0);
      assertFalse(boolean0);
      assertNotEquals((double)double0, (double)double1, 0.01);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = BigInteger.ZERO;
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      int int0 = bigInteger1.intValueExact();
      assertEquals(0, int0);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      int int1 = bigInteger1.bitLength();
      assertEquals(0, int1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertTrue(int1 == int0);
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = bigInteger0.xor(bigInteger1);
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertTrue(bigInteger2.equals((Object)bigInteger0));
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, 16);
      assertNotNull(bigInteger3);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger3, bigInteger2);
      assertSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertTrue(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertTrue(bigInteger3.equals((Object)bigInteger2));
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      assertEquals((short)1, bigInteger3.shortValue());
      
      double double2 = MathUtils.binomialCoefficientDouble(16, 16);
      assertEquals(1.0, double2, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      int int2 = MathUtils.hash((-3101.7));
      assertEquals((-1496425216), int2);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      
      int int3 = MathUtils.gcd(14, 0);
      assertEquals(14, int3);
      assertFalse(int3 == int2);
      assertFalse(int3 == int0);
      assertFalse(int3 == int1);
  }

  @Test(timeout = 4000)
  public void test087()  throws Throwable  {
      int[] intArray0 = new int[9];
      intArray0[0] = 0;
      intArray0[2] = 186;
      intArray0[3] = 0;
      intArray0[4] = 2874;
      intArray0[5] = (-277);
      intArray0[6] = (-460);
      intArray0[7] = 1186;
      intArray0[8] = 2;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(9, intArray0.length);
      assertEquals(9, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {0, 0, 186, 0, 2874, (-277), (-460), 1186, 2}, intArray0);
      assertArrayEquals(new int[] {0, 0, 186, 0, 2874, (-277), (-460), 1186, 2}, intArray1);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 2, 1257.0F);
      assertFalse(boolean0);
      
      long long0 = MathUtils.sign((long) 0);
      assertEquals(0L, long0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) (-460), (float) 1186);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.OrderDirection.valueOf("{,M#U`Mj^bgQ-Y,");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.util.MathUtils.OrderDirection.{,M#U`Mj^bgQ-Y,
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test088()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      Double double0 = new Double(10.0);
      assertEquals(10.0, (double)double0, 0.01);
      assertNotNull(double0);
      
      double double1 = Double.sum(Double.NEGATIVE_INFINITY, 10.0);
      assertEquals(Double.NEGATIVE_INFINITY, double1, 0.01);
      
      boolean boolean0 = bigInteger0.equals(double0);
      assertFalse(boolean0);
      assertNotEquals((double)double0, (double)double1, 0.01);
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = BigInteger.ZERO;
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      int int0 = bigInteger1.intValueExact();
      assertEquals(0, int0);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      int int1 = bigInteger1.bitLength();
      assertEquals(0, int1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertTrue(int1 == int0);
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = bigInteger0.xor(bigInteger1);
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertTrue(bigInteger2.equals((Object)bigInteger0));
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((short)0, bigInteger1.shortValue());
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, 16);
      assertNotNull(bigInteger3);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger0, bigInteger3);
      assertSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertTrue(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertTrue(bigInteger3.equals((Object)bigInteger2));
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger3.shortValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      
      double double2 = MathUtils.binomialCoefficientDouble(16, 16);
      assertEquals(1.0, double2, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      int int2 = MathUtils.hash((double) 16);
      assertEquals(1076887552, int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      
      int int3 = MathUtils.gcd(14, 0);
      assertEquals(14, int3);
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      assertFalse(int3 == int1);
  }

  @Test(timeout = 4000)
  public void test089()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (-4009.750308);
      doubleArray0[1] = Double.NaN;
      doubleArray0[2] = 1190.2;
      doubleArray0[3] = (-2789.991);
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (-1969.9);
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-4009.750308), Double.NaN, 1190.2, (-2789.991), 0.0, (-1969.9)}, doubleArray0, 0.01);
      
      double double1 = MathUtils.safeNorm(doubleArray0);
      assertEquals(Double.NaN, double1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {(-4009.750308), Double.NaN, 1190.2, (-2789.991), 0.0, (-1969.9)}, doubleArray0, 0.01);
      
      double double2 = MathUtils.indicator((-4009.750308));
      assertEquals((-1.0), double2, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      long long0 = (-2672L);
      long long1 = 9154082963658192752L;
      long long2 = MathUtils.gcd((-2672L), 9154082963658192752L);
      assertEquals(16L, long2);
      assertFalse(long2 == long0);
      assertFalse(long2 == long1);
      
      double double3 = MathUtils.safeNorm(doubleArray0);
      assertEquals(Double.NaN, double3, 0.01);
      assertEquals(6, doubleArray0.length);
      assertEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertEquals(double3, double0, 0.01);
      assertArrayEquals(new double[] {(-4009.750308), Double.NaN, 1190.2, (-2789.991), 0.0, (-1969.9)}, doubleArray0, 0.01);
      
      float float0 = Float.NEGATIVE_INFINITY;
      int int0 = 0;
      // Undeclared exception!
      try { 
        MathUtils.equals(Float.NEGATIVE_INFINITY, 0.0F, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test090()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(0L, 0L);
      assertEquals(0L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(662.142F, 0.0F);
      assertFalse(boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.factorial((-3060));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -3,060
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test091()  throws Throwable  {
      int[] intArray0 = new int[0];
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, intArray0.length);
      assertArrayEquals(new int[] {}, intArray0);
      
      boolean boolean0 = MathUtils.equals(2409.95F, 229.68F);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 2409.95F;
      boolean boolean1 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean1);
      assertEquals(1, doubleArray0.length);
      assertFalse(boolean1 == boolean0);
      assertArrayEquals(new double[] {2409.949951171875}, doubleArray0, 0.01);
      
      double double1 = MathUtils.indicator((-294.85681));
      assertEquals((-1.0), double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      double[][] doubleArray1 = new double[4][7];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertEquals(1, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      assertArrayEquals(new double[] {2409.949951171875}, doubleArray0, 0.01);
      
      int int0 = MathUtils.indicator((-403));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test092()  throws Throwable  {
      int int0 = 361;
      int int1 = MathUtils.lcm(361, 361);
      assertEquals(361, int1);
      assertTrue(int1 == int0);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 361;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 361);
      assertNotNull(doubleArray1);
      assertEquals(1, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {361.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {361.0}, doubleArray1, 0.01);
      
      long long0 = MathUtils.addAndCheck((long) 361, 1307674368000L);
      assertEquals(1307674368361L, long0);
      
      long long1 = MathUtils.binomialCoefficient(361, 0);
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      double double0 = MathUtils.indicator(0.0);
      assertEquals(1.0, double0, 0.01);
      
      boolean boolean0 = MathUtils.equals(4376.0986F, (-96.82643F), (-96.82643F));
      assertFalse(boolean0);
      
      double[] doubleArray2 = MathUtils.copyOf(doubleArray0);
      assertNotNull(doubleArray2);
      assertEquals(1, doubleArray0.length);
      assertEquals(1, doubleArray2.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray1);
      assertNotSame(doubleArray2, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {361.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {361.0}, doubleArray2, 0.01);
      
      MathUtils.checkFinite((-599.10927175803));
      int int2 = 1213;
      boolean boolean1 = MathUtils.equals((double) 4376.0986F, 0.0, 1213);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      int int3 = MathUtils.compareTo(1.0, 0.0, (-446.710954));
      assertEquals(1, int3);
      assertFalse(int3 == int2);
      assertFalse(int3 == int0);
      assertFalse(int3 == int1);
      
      int int4 = (-399);
      // Undeclared exception!
      try { 
        MathUtils.equals((float) 361, (float) 1307674368361L, (-399));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test093()  throws Throwable  {
      double[] doubleArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test094()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.distance1((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test095()  throws Throwable  {
      int int0 = MathUtils.compareTo((-1.7976931348623157E308), 0.0, (-1314.8976));
      assertEquals((-1), int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (-1), (float) (-1), (float) (-1));
      assertTrue(boolean0);
      
      int int1 = MathUtils.indicator((-1));
      assertEquals((-1), int1);
      assertTrue(int1 == int0);
  }

  @Test(timeout = 4000)
  public void test096()  throws Throwable  {
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      boolean boolean0 = MathUtils.equals(913.0222F, 913.0222F, (-1.0F));
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(0.0, (double) (-1.0F), (double) (-1.0F));
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      long long0 = MathUtils.binomialCoefficient(23, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test097()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 0.2;
      doubleArray0[1] = (-2065.7404);
      doubleArray0[2] = 40.19140625;
      doubleArray0[3] = (-832.20234527702);
      doubleArray0[4] = 1.1102230246251565E-16;
      doubleArray0[5] = 1.304E19;
      doubleArray0[6] = (-141.91);
      doubleArray0[7] = (-645.2517760847685);
      MathUtils.checkFinite(doubleArray0);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {0.2, (-2065.7404), 40.19140625, (-832.20234527702), 1.1102230246251565E-16, 1.304E19, (-141.91), (-645.2517760847685)}, doubleArray0, 0.01);
      
      long long0 = MathUtils.gcd(2339L, (-1773L));
      assertEquals(1L, long0);
      
      long long1 = MathUtils.pow((-1L), 4095L);
      assertEquals((-1L), long1);
      assertFalse(long1 == long0);
      
      long long2 = MathUtils.pow((-1L), 22L);
      assertEquals(1L, long2);
      assertTrue(long2 == long0);
      assertFalse(long2 == long1);
      
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(6.283185307179586, 0.0, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test098()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (-20.0);
      double[] doubleArray1 = new double[1];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (-20.0);
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray1);
      assertFalse(boolean0);
      assertEquals(3, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 0.0, (-20.0)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-20.0)}, doubleArray1, 0.01);
      
      double[] doubleArray2 = new double[7];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      
      doubleArray2[0] = 0.0;
      doubleArray2[1] = (-20.0);
      doubleArray2[2] = (-20.0);
      doubleArray2[3] = 0.0;
      doubleArray2[4] = 0.0;
      doubleArray2[5] = 0.0;
      doubleArray2[6] = 0.0;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray3 = new double[8][3];
      doubleArray3[0] = doubleArray2;
      double[] doubleArray4 = new double[9];
      assertFalse(doubleArray4.equals((Object)doubleArray1));
      assertFalse(doubleArray4.equals((Object)doubleArray2));
      assertFalse(doubleArray4.equals((Object)doubleArray0));
      
      doubleArray4[0] = (-20.0);
      doubleArray4[1] = (-20.0);
      doubleArray4[2] = Double.NaN;
      doubleArray4[3] = 0.0;
      doubleArray4[4] = (-3555.0728962737385);
      doubleArray4[5] = 0.0;
      doubleArray4[6] = 0.0;
      doubleArray4[7] = 0.0;
      doubleArray4[8] = 0.0;
      doubleArray3[1] = doubleArray4;
      doubleArray3[2] = doubleArray2;
      doubleArray3[3] = doubleArray0;
      doubleArray3[4] = doubleArray2;
      doubleArray3[5] = doubleArray0;
      doubleArray3[6] = doubleArray1;
      doubleArray3[7] = doubleArray1;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray2, mathUtils_OrderDirection0, doubleArray3);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 9 != 7
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test099()  throws Throwable  {
      int int0 = (-1291);
      // Undeclared exception!
      try { 
        MathUtils.factorialDouble((-1291));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1,291
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test100()  throws Throwable  {
      float float0 = 0.0F;
      float float1 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float1, 0.01F);
      assertEquals(float1, float0, 0.01F);
      
      float float2 = MathUtils.sign(0.0F);
      assertEquals(0.0F, float2, 0.01F);
      assertEquals(float2, float1, 0.01F);
      assertEquals(float2, float0, 0.01F);
      
      int int0 = 3954;
      double double0 = MathUtils.factorialDouble(3954);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test101()  throws Throwable  {
      short short0 = MathUtils.indicator((short) (-1632));
      assertEquals((short) (-1), short0);
      
      boolean boolean0 = MathUtils.equals(0.0, (double) (short) (-1632), 35);
      assertFalse(boolean0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      int[] intArray0 = new int[0];
      int[] intArray1 = MathUtils.copyOf(intArray0, 0);
      assertNotNull(intArray1);
      assertEquals(0, intArray0.length);
      assertEquals(0, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
      
      float float0 = MathUtils.sign((float) 35);
      assertEquals(1.0F, float0, 0.01F);
      
      long long0 = MathUtils.indicator((long) (short) (-1632));
      assertEquals((-1L), long0);
      
      int int0 = MathUtils.indicator(0);
      assertEquals(1, int0);
      
      int int1 = MathUtils.hash((-0.16666666666666666));
      assertEquals((-359661568), int1);
      assertFalse(int1 == int0);
      
      float float1 = MathUtils.round((float) 1, (int) (short) (-1));
      assertEquals(0.0F, float1, 0.01F);
      assertNotEquals(float1, float0, 0.01F);
      
      boolean boolean1 = MathUtils.equals((float) 1, (float) 0, 1.4E-45F);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      double double0 = MathUtils.indicator(0.0);
      assertEquals(1.0, double0, 0.01);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 1.0F;
      double double1 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double1, 0.01);
      assertEquals(1, doubleArray0.length);
      assertNotEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {1.0}, doubleArray0, 0.01);
      
      float[] floatArray0 = new float[7];
      floatArray0[0] = (float) 35;
      floatArray0[1] = (float) 0;
      floatArray0[2] = 0.0F;
      floatArray0[3] = (float) (-359661568);
      floatArray0[4] = (float) (short) (-1632);
      floatArray0[5] = 1.4E-45F;
      floatArray0[6] = (float) (-1L);
      boolean boolean2 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean2);
      assertEquals(7, floatArray0.length);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      assertArrayEquals(new float[] {35.0F, 0.0F, 0.0F, (-3.59661568E8F), (-1632.0F), 1.4E-45F, (-1.0F)}, floatArray0, 0.01F);
      
      boolean boolean3 = MathUtils.equals((-435.1858), (double) (-1L), 35);
      assertFalse(boolean3);
      assertTrue(boolean3 == boolean1);
      assertTrue(boolean3 == boolean0);
      assertFalse(boolean3 == boolean2);
      
      double double2 = MathUtils.sign((double) 0.0F);
      assertEquals(0.0, double2, 0.01);
      assertEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test102()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, (-1593.28197643994));
      assertFalse(boolean0);
      
      long long0 = MathUtils.lcm(9193070505571053912L, 9193070505571053912L);
      assertEquals(9193070505571053912L, long0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (double) 9193070505571053912L;
      doubleArray0[2] = (-1593.28197643994);
      doubleArray0[3] = (double) 9193070505571053912L;
      doubleArray0[4] = (double) 9193070505571053912L;
      doubleArray0[5] = (double) 9193070505571053912L;
      doubleArray0[6] = (double) 9193070505571053912L;
      doubleArray0[7] = (double) 9193070505571053912L;
      doubleArray0[8] = 0.0;
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, (-1593.28197643994));
      assertNotNull(doubleArray1);
      assertEquals(9, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 9.1930705055710536E18, (-1593.28197643994), 9.1930705055710536E18, 9.1930705055710536E18, 9.1930705055710536E18, 9.1930705055710536E18, 9.1930705055710536E18, 0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {-0.0, (-265.5469960733234), 4.6022843236652865E-14, (-265.5469960733234), (-265.5469960733234), (-265.5469960733234), (-265.5469960733234), (-265.5469960733234), -0.0}, doubleArray1, 0.01);
      
      double double0 = MathUtils.sign(0.0);
      assertEquals(0.0, double0, 0.01);
      
      int int0 = MathUtils.lcm(2004, 1722);
      assertEquals(575148, int0);
      
      double double1 = MathUtils.cosh(1410.9);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test103()  throws Throwable  {
      int int0 = 0;
      int int1 = MathUtils.gcd(0, 0);
      assertEquals(0, int1);
      assertTrue(int1 == int0);
      
      int int2 = (-1564);
      int int3 = MathUtils.mulAndCheck(0, (-1564));
      assertEquals(0, int3);
      assertTrue(int3 == int1);
      assertTrue(int3 == int0);
      assertFalse(int3 == int2);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) (-1564), (double) 0, 2147.6);
      assertTrue(boolean0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      // Undeclared exception!
      try { 
        MathUtils.equals(2147.6, (-2534.041), 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test104()  throws Throwable  {
      int[] intArray0 = new int[3];
      intArray0[0] = 2998;
      intArray0[1] = 0;
      int int0 = 74;
      intArray0[2] = 74;
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(3, intArray0.length);
      assertArrayEquals(new int[] {2998, 0, 74}, intArray0);
      
      boolean boolean0 = MathUtils.equals(3.4028235E38F, 0.0F);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 3.4028235E38F;
      doubleArray0[1] = (double) 2998;
      doubleArray0[2] = (double) 3.4028235E38F;
      doubleArray0[3] = (double) 2998;
      doubleArray0[4] = (double) 0;
      doubleArray0[5] = (double) 3.4028235E38F;
      doubleArray0[6] = (double) 2998;
      doubleArray0[7] = 1001.019;
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double1, 0.01);
      assertEquals(8, doubleArray0.length);
      assertEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {3.4028234663852886E38, 2998.0, 3.4028234663852886E38, 2998.0, 0.0, 3.4028234663852886E38, 2998.0, 1001.019}, doubleArray0, 0.01);
      
      boolean boolean1 = MathUtils.equals((float) 0, (float) 74, 2998);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      boolean boolean2 = MathUtils.equalsIncludingNaN((double) 2998, 0.0, 3.4028234663852886E38);
      assertTrue(boolean2);
      assertFalse(boolean2 == boolean0);
      assertFalse(boolean2 == boolean1);
      
      int int1 = MathUtils.sign((-2431));
      assertEquals((-1), int1);
      assertFalse(int1 == int0);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not increasing (340,282,346,638,528,860,000,000,000,000,000,000,000 > 2,998)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test105()  throws Throwable  {
      int int0 = 2072;
      int int1 = MathUtils.pow(2072, (long) 2072);
      assertEquals(0, int1);
      assertFalse(int1 == int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) 2072, (double) 0, (double) 2072);
      assertTrue(boolean0);
      
      int int2 = 0;
      // Undeclared exception!
      try { 
        MathUtils.equals((float) 0, (float) 0, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test106()  throws Throwable  {
      byte byte0 = (byte)0;
      byte byte1 = MathUtils.sign((byte)0);
      assertEquals((byte)0, byte1);
      assertTrue(byte1 == byte0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = bigInteger0.max(bigInteger1);
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger2.shortValue());
      assertEquals((byte)1, bigInteger2.byteValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, (long) (byte)0);
      assertNotNull(bigInteger3);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertSame(bigInteger3, bigInteger1);
      assertSame(bigInteger3, bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      assertEquals((short)1, bigInteger3.shortValue());
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) (byte)0;
      double double0 = 520.48840259791;
      doubleArray0[1] = 520.48840259791;
      doubleArray0[2] = (double) (byte)0;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      BigInteger bigInteger4 = bigInteger0.shiftLeft((byte)0);
      assertNotNull(bigInteger4);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger4, bigInteger2);
      assertNotSame(bigInteger4, bigInteger1);
      assertNotSame(bigInteger4, bigInteger3);
      assertSame(bigInteger4, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger4.equals((Object)bigInteger2));
      assertFalse(bigInteger4.equals((Object)bigInteger1));
      assertFalse(bigInteger4.equals((Object)bigInteger3));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)0, bigInteger4.byteValue());
      assertEquals((short)0, bigInteger4.shortValue());
      
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not increasing (520.488 > 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test107()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test108()  throws Throwable  {
      short short0 = (short)0;
      short short1 = MathUtils.sign((short)0);
      assertEquals((short)0, short1);
      assertTrue(short1 == short0);
      
      double double0 = MathUtils.cosh((-2855.5168755237));
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      double double1 = MathUtils.factorialLog(961);
      assertEquals(5643.4764194733725, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      double double2 = MathUtils.binomialCoefficientDouble(961, 961);
      assertEquals(1.0, double2, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      
      long long0 = MathUtils.factorial(5);
      assertEquals(120L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (short)0, (-2292.8406F), (float) 961);
      assertFalse(boolean0);
      
      BigInteger bigInteger0 = MathUtils.pow((BigInteger) null, (long) (short)0);
      assertNotNull(bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.factorial(961);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test109()  throws Throwable  {
      LocalizedFormats localizedFormats0 = LocalizedFormats.NOT_POSITIVE_PERMUTATION;
      assertEquals(LocalizedFormats.NOT_POSITIVE_PERMUTATION, localizedFormats0);
      assertEquals("permutation k ({0}) must be positive", localizedFormats0.getSourceString());
      
      Object[] objectArray0 = new Object[9];
      objectArray0[0] = (Object) localizedFormats0;
      Object object0 = new Object();
      assertNotNull(object0);
      
      objectArray0[1] = object0;
      objectArray0[2] = (Object) localizedFormats0;
      objectArray0[3] = (Object) "(";
      objectArray0[4] = (Object) localizedFormats0;
      objectArray0[5] = (Object) "(";
      objectArray0[6] = (Object) "(";
      Object object1 = new Object();
      assertNotNull(object1);
      assertFalse(object1.equals((Object)object0));
      
      objectArray0[7] = object1;
      objectArray0[8] = (Object) "(";
      MathUtils.checkNotNull((Object) "(", (Localizable) localizedFormats0, objectArray0);
      assertEquals(9, objectArray0.length);
      assertEquals("permutation k ({0}) must be positive", localizedFormats0.getSourceString());
  }

  @Test(timeout = 4000)
  public void test110()  throws Throwable  {
      double double0 = MathUtils.log((-86.0), (-86.0));
      assertEquals(Double.NaN, double0, 0.01);
      
      int int0 = MathUtils.hash(Double.NaN);
      assertEquals(2146959360, int0);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.BETA;
      assertEquals(LocalizedFormats.BETA, localizedFormats0);
      assertEquals("beta", localizedFormats0.getSourceString());
      
      Object[] objectArray0 = new Object[4];
      objectArray0[0] = (Object) localizedFormats0;
      objectArray0[1] = (Object) localizedFormats0;
      objectArray0[2] = null;
      objectArray0[3] = (Object) localizedFormats0;
      // Undeclared exception!
      try { 
        MathUtils.checkNotNull((Object) null, (Localizable) localizedFormats0, objectArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // beta
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test111()  throws Throwable  {
      int int0 = MathUtils.gcd((-2106), (-2106));
      assertEquals(2106, int0);
      
      byte byte0 = MathUtils.indicator((byte)0);
      assertEquals((byte)1, byte0);
      
      boolean boolean0 = MathUtils.equals(1493.5F, Float.NEGATIVE_INFINITY);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test112()  throws Throwable  {
      int int0 = MathUtils.indicator(45);
      assertEquals(1, int0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 45;
      doubleArray0[1] = (double) 45;
      doubleArray0[2] = (double) 1;
      doubleArray0[3] = (double) 1;
      doubleArray0[4] = (double) 1;
      doubleArray0[5] = (double) 45;
      doubleArray0[6] = (double) 45;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertFalse(boolean0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {45.0, 45.0, 1.0, 1.0, 1.0, 45.0, 45.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test113()  throws Throwable  {
      int int0 = 2072;
      int int1 = MathUtils.pow(2072, (long) 2072);
      assertEquals(0, int1);
      assertFalse(int1 == int0);
      
      int int2 = MathUtils.compareTo(1837.0, 0, 0);
      assertEquals(1, int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(1837.0, (double) 0, (-1601.496371633268));
      assertFalse(boolean0);
      
      float float0 = 4146.1F;
      int int3 = 0;
      // Undeclared exception!
      try { 
        MathUtils.equals((float) 0, 4146.1F, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test114()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double double0 = 0.0;
      doubleArray0[0] = 0.0;
      MathUtils.checkOrder(doubleArray0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      
      int int0 = 634;
      long long0 = MathUtils.pow((-1079L), 634);
      assertEquals(5727837889623568017L, long0);
      
      long long1 = MathUtils.binomialCoefficient(634, (-425));
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      float float0 = (-1.0F);
      boolean boolean0 = MathUtils.equals((-1.0F), 1.0F);
      assertFalse(boolean0);
      
      double double1 = MathUtils.round((double) (-1.0F), (-1142), 0);
      assertEquals(Double.NEGATIVE_INFINITY, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      double[] doubleArray1 = new double[7];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) (-1.0F);
      doubleArray1[1] = (double) 0;
      doubleArray1[2] = (double) 634;
      doubleArray1[3] = (double) (-1.0F);
      doubleArray1[4] = Double.NEGATIVE_INFINITY;
      doubleArray1[5] = (double) (-1.0F);
      doubleArray1[6] = (double) (-1142);
      double double2 = MathUtils.distanceInf(doubleArray1, doubleArray1);
      assertEquals(Double.NaN, double2, 0.01);
      assertEquals(7, doubleArray1.length);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {(-1.0), 0.0, 634.0, (-1.0), Double.NEGATIVE_INFINITY, (-1.0), (-1142.0)}, doubleArray1, 0.01);
      
      double double3 = MathUtils.round(0.7249995199969751, (-2604));
      assertEquals(0.0, double3, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertEquals(double3, double0, 0.01);
      assertNotEquals(double3, double2, 0.01);
      
      double double4 = MathUtils.distanceInf(doubleArray0, doubleArray1);
      assertEquals(1.0, double4, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(7, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertNotEquals(double4, double1, 0.01);
      assertNotEquals(double4, double2, 0.01);
      assertNotEquals(double4, double3, 0.01);
      assertNotEquals(double4, double0, 0.01);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-1.0), 0.0, 634.0, (-1.0), Double.NEGATIVE_INFINITY, (-1.0), (-1142.0)}, doubleArray1, 0.01);
      
      int int1 = MathUtils.addAndCheck(0, (-2604));
      assertEquals((-2604), int1);
      assertFalse(int1 == int0);
      
      MockRandom mockRandom0 = new MockRandom();
      assertNotNull(mockRandom0);
      
      IntStream intStream0 = mockRandom0.ints();
      assertNotNull(intStream0);
      
      // Undeclared exception!
      try { 
        BigInteger.probablePrime(0, mockRandom0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // bitLength < 2
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test115()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (-1650.7044800785);
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {(-1650.7044800785)}, doubleArray0, 0.01);
      
      double double0 = MathUtils.normalizeAngle((-1650.7044800785), 1.0);
      assertEquals(1.773255709731302, double0, 0.01);
      
      int int0 = MathUtils.mulAndCheck(0, 0);
      assertEquals(0, int0);
      
      double[][] doubleArray1 = new double[0][7];
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertEquals(1, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertArrayEquals(new double[] {(-1650.7044800785)}, doubleArray0, 0.01);
      
      float float0 = MathUtils.indicator((-3572.891F));
      assertEquals((-1.0F), float0, 0.01F);
      
      double[] doubleArray2 = new double[2];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (-1812.67506085538);
      doubleArray2[1] = (double) (-1.0F);
      double[] doubleArray3 = MathUtils.copyOf(doubleArray2);
      assertNotNull(doubleArray3);
      assertEquals(2, doubleArray2.length);
      assertEquals(2, doubleArray3.length);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray3);
      assertNotSame(doubleArray3, doubleArray2);
      assertNotSame(doubleArray3, doubleArray0);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray3.equals((Object)doubleArray2));
      assertFalse(doubleArray3.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {(-1812.67506085538), (-1.0)}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {(-1812.67506085538), (-1.0)}, doubleArray3, 0.01);
      
      int[] intArray0 = new int[8];
      intArray0[0] = 0;
      intArray0[1] = 0;
      intArray0[2] = 0;
      intArray0[3] = 0;
      intArray0[4] = 0;
      intArray0[5] = 0;
      intArray0[6] = 0;
      intArray0[7] = 0;
      int int1 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int1);
      assertEquals(8, intArray0.length);
      assertTrue(int1 == int0);
      assertArrayEquals(new int[] {0, 0, 0, 0, 0, 0, 0, 0}, intArray0);
      
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray2);
      assertEquals(161.97058077688007, double1, 0.01);
      assertEquals(1, doubleArray0.length);
      assertEquals(2, doubleArray2.length);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray0, doubleArray3);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray3);
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray0.equals((Object)doubleArray3));
      assertNotEquals(double1, double0, 0.01);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray3));
      assertArrayEquals(new double[] {(-1650.7044800785)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-1812.67506085538), (-1.0)}, doubleArray2, 0.01);
  }

  @Test(timeout = 4000)
  public void test116()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 873.804566643;
      doubleArray0[1] = 873.804566643;
      doubleArray0[2] = 1526.702;
      doubleArray0[3] = 0.7249995199969751;
      doubleArray0[4] = 0.0;
      doubleArray0[6] = 0.0;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, false);
      assertFalse(boolean0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {873.804566643, 873.804566643, 1526.702, 0.7249995199969751, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      long long0 = MathUtils.gcd(78L, 2715L);
      assertEquals(3L, long0);
      
      int int0 = MathUtils.gcd(3187, 3187);
      assertEquals(3187, int0);
  }

  @Test(timeout = 4000)
  public void test117()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(0L, 0L);
      assertEquals(0L, long0);
      
      byte byte0 = MathUtils.indicator((byte)56);
      assertEquals((byte)1, byte0);
      
      int[] intArray0 = new int[2];
      intArray0[0] = (int) (byte)56;
      intArray0[1] = (int) (byte)56;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(2, intArray0.length);
      assertArrayEquals(new int[] {56, 56}, intArray0);
      
      long long1 = MathUtils.subAndCheck(0L, (-1366L));
      assertEquals(1366L, long1);
      assertFalse(long1 == long0);
      
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2, intArray0.length);
      assertArrayEquals(new int[] {56, 56}, intArray0);
      
      long long2 = MathUtils.gcd(0L, (long) 56);
      assertEquals(56L, long2);
      assertFalse(long2 == long1);
      assertFalse(long2 == long0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      boolean boolean0 = MathUtils.equals(1288.158257678958, (double) 0);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (double) 56;
      doubleArray0[4] = (double) 1366L;
      doubleArray0[5] = (double) 56L;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[5][7];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(5, doubleArray1.length);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {1366.0, 56.0, 56.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      float float0 = MathUtils.round(3011.0F, 0);
      assertEquals(3011.0F, float0, 0.01F);
      
      double[] doubleArray2 = MathUtils.copyOf(doubleArray0, 56);
      assertNotNull(doubleArray2);
      assertEquals(56, doubleArray2.length);
      assertEquals(6, doubleArray0.length);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {1366.0, 56.0, 56.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) (-1366L), 2121.317168935);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN((-197.02613657847), 0.0, (-2518));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test118()  throws Throwable  {
      byte byte0 = MathUtils.indicator((byte)16);
      assertEquals((byte)1, byte0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) (byte)16;
      doubleArray0[1] = 2048.0233794;
      doubleArray0[2] = (double) (byte)16;
      doubleArray0[3] = (double) (byte)16;
      doubleArray0[4] = (double) (byte)16;
      doubleArray0[5] = (double) (byte)1;
      doubleArray0[6] = (double) (byte)16;
      doubleArray0[7] = (double) (byte)16;
      doubleArray0[8] = (double) (byte)1;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {16.0, 2048.0233794, 16.0, 16.0, 16.0, 1.0, 16.0, 16.0, 1.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test119()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 391.5617704;
      doubleArray0[1] = (-1.0);
      doubleArray0[2] = 95.755956176;
      doubleArray0[3] = 2277.35;
      doubleArray0[4] = (-109.8895838);
      doubleArray0[5] = 0.5;
      doubleArray0[6] = (-680.7458);
      double[] doubleArray1 = new double[5];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 95.755956176;
      doubleArray1[1] = 95.755956176;
      doubleArray1[2] = (-1.0);
      doubleArray1[3] = (-109.8895838);
      doubleArray1[4] = 391.5617704;
      // Undeclared exception!
      try { 
        MathUtils.distance1(doubleArray0, doubleArray1);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 5
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test120()  throws Throwable  {
      int int0 = 610;
      double double0 = MathUtils.binomialCoefficientDouble(610, 610);
      assertEquals(1.0, double0, 0.01);
      
      long long0 = MathUtils.gcd(0L, (long) 610);
      assertEquals(610L, long0);
      
      byte byte0 = MathUtils.sign((byte)60);
      assertEquals((byte)1, byte0);
      
      int int1 = (-1351);
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(226.6133F, (float) (byte)1, (-1351));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test121()  throws Throwable  {
      int int0 = 250;
      // Undeclared exception!
      try { 
        MathUtils.factorial(250);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test122()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, 0.0F, (-232.7F));
      assertTrue(boolean0);
      
      double double0 = MathUtils.cosh(0.0F);
      assertEquals(1.0, double0, 0.01);
      
      int[] intArray0 = new int[1];
      intArray0[0] = 0;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(1, intArray0.length);
      assertArrayEquals(new int[] {0}, intArray0);
      
      long long0 = MathUtils.gcd((-612L), (long) 0);
      assertEquals(612L, long0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) 0, -0.0F);
      assertTrue(boolean1);
      assertTrue(boolean1 == boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.factorialLog((-2355));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -2,355
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test123()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(0, 0);
      assertEquals(0, int0);
      
      int int1 = MathUtils.addAndCheck(0, 0);
      assertEquals(0, int1);
      assertTrue(int1 == int0);
      
      boolean boolean0 = MathUtils.equals(3.0, 3.0, Double.NaN);
      assertTrue(boolean0);
      
      int int2 = MathUtils.hash(0.0);
      assertEquals(0, int2);
      assertTrue(int2 == int0);
      assertTrue(int2 == int1);
      
      double double0 = MathUtils.round(0.0, (-1566), 0);
      assertEquals(0.0, double0, 0.01);
      
      float float0 = MathUtils.sign((float) 0);
      assertEquals(0.0F, float0, 0.01F);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 0;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean1 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false, true);
      assertTrue(boolean1);
      assertEquals(1, doubleArray0.length);
      assertTrue(boolean1 == boolean0);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      
      boolean boolean2 = MathUtils.equals(0.0, (double) (-1566), 3238.96);
      assertTrue(boolean2);
      assertTrue(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.copyOf(doubleArray0, (-1566));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test124()  throws Throwable  {
      short short0 = MathUtils.indicator((short)0);
      assertEquals((short)1, short0);
      
      int int0 = MathUtils.indicator((int) (short)0);
      assertEquals(1, int0);
      
      double double0 = MathUtils.sign((-1641.895));
      assertEquals((-1.0), double0, 0.01);
      
      MathUtils.checkFinite((-1.0));
      double double1 = MathUtils.log((-2425.1597617), (-1.0));
      assertEquals(Double.NaN, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      float float0 = MathUtils.round(2087.0F, 7);
      assertEquals(2087.0F, float0, 0.01F);
      
      boolean boolean0 = MathUtils.equals((float) 1, (float) 7, (float) 7);
      assertTrue(boolean0);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger1.not();
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((short) (-2), bigInteger2.shortValue());
      assertEquals((byte) (-2), bigInteger2.byteValue());
      
      BigInteger bigInteger3 = bigInteger0.divide(bigInteger1);
      assertNotNull(bigInteger3);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertNotSame(bigInteger3, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertTrue(bigInteger3.equals((Object)bigInteger0));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)10, bigInteger3.byteValue());
      assertEquals((short)10, bigInteger3.shortValue());
      
      int int1 = bigInteger0.compareTo(bigInteger1);
      assertEquals(1, int1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger1, bigInteger3);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertTrue(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger3));
      assertFalse(bigInteger1.equals((Object)bigInteger2));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertTrue(int1 == int0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      BigInteger bigInteger4 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger4);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger4, bigInteger1);
      assertNotSame(bigInteger4, bigInteger0);
      assertNotSame(bigInteger4, bigInteger3);
      assertNotSame(bigInteger4, bigInteger2);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertTrue(bigInteger0.equals((Object)bigInteger3));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger4.equals((Object)bigInteger1));
      assertFalse(bigInteger4.equals((Object)bigInteger0));
      assertFalse(bigInteger4.equals((Object)bigInteger3));
      assertFalse(bigInteger4.equals((Object)bigInteger2));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger4.byteValue());
      assertEquals((short) (-7168), bigInteger4.shortValue());
      
      boolean boolean1 = MathUtils.equals((float) (short)0, (-3469.1619F));
      assertFalse(boolean1);
      assertFalse(boolean1 == boolean0);
      
      float[] floatArray0 = new float[0];
      boolean boolean2 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean2);
      assertEquals(0, floatArray0.length);
      assertFalse(boolean2 == boolean1);
      assertTrue(boolean2 == boolean0);
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      
      // Undeclared exception!
      try { 
        MathUtils.pow((-3958705157555305932L), (-3902L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-3,902)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test125()  throws Throwable  {
      int[] intArray0 = new int[6];
      intArray0[0] = 978;
      intArray0[1] = 1228;
      intArray0[2] = 645;
      intArray0[3] = 1177;
      int int0 = 1;
      intArray0[4] = 1;
      intArray0[5] = 0;
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(6, intArray0.length);
      assertArrayEquals(new int[] {978, 1228, 645, 1177, 1, 0}, intArray0);
      
      long long0 = MathUtils.binomialCoefficient(16, 1);
      assertEquals(16L, long0);
      
      double double1 = MathUtils.round(0.0036, 4050);
      assertEquals(0.0036, double1, 0.01);
      assertEquals(double1, double0, 0.01);
      
      boolean boolean0 = MathUtils.equals((float) 978, (float) 1, 0.5F);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) 1228, 1037.81F);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null, mathUtils_OrderDirection0, false);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test126()  throws Throwable  {
      float float0 = MathUtils.round(0.0F, 0, 0);
      assertEquals(1.0F, float0, 0.01F);
      
      long long0 = MathUtils.indicator((-488L));
      assertEquals((-1L), long0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(1, intArray0.length);
      assertEquals(1, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {0}, intArray0);
      assertArrayEquals(new int[] {0}, intArray1);
      
      MockRandom mockRandom0 = new MockRandom();
      assertNotNull(mockRandom0);
      
      long long1 = mockRandom0.nextLong();
      assertEquals(0L, long1);
      assertFalse(long1 == long0);
      
      BigInteger bigInteger0 = null;
      try {
        bigInteger0 = new BigInteger(0, 0, mockRandom0);
        fail("Expecting exception: ArithmeticException");
      
      } catch(ArithmeticException e) {
         //
         // bitLength < 2
         //
         verifyException("java.math.BigInteger", e);
      }
  }

  @Test(timeout = 4000)
  public void test127()  throws Throwable  {
      double double0 = MathUtils.round(0.0, 14, 0);
      assertEquals(0.0, double0, 0.01);
      
      double double1 = MathUtils.indicator(0.0);
      assertEquals(1.0, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      double double2 = MathUtils.normalizeAngle(0.0, (-5922.46));
      assertEquals((-5925.04374467035), double2, 0.01);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      
      int int0 = MathUtils.lcm(3, 63);
      assertEquals(63, int0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(24L, (-5930L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-5,930)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test128()  throws Throwable  {
      long long0 = MathUtils.subAndCheck((-221L), (-221L));
      assertEquals(0L, long0);
      
      long long1 = MathUtils.indicator(1825L);
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) (-221L);
      doubleArray0[2] = (double) 1L;
      doubleArray0[3] = (double) 0L;
      doubleArray0[4] = (double) 1825L;
      doubleArray0[5] = (double) (-221L);
      doubleArray0[6] = (double) 1L;
      doubleArray0[7] = (double) 1L;
      doubleArray0[8] = (double) 0L;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[1][4];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(9, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertArrayEquals(new double[] {1825.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, (-221.0), (-221.0)}, doubleArray0, 0.01);
      
      boolean boolean0 = MathUtils.equals(1825.0, 0.0, 588.4649);
      assertFalse(boolean0);
      
      int int0 = MathUtils.lcm(0, 0);
      assertEquals(0, int0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test129()  throws Throwable  {
      double double0 = MathUtils.log(1.304E19, 1863.0);
      assertEquals(0.17107849149772322, double0, 0.01);
      
      MathUtils.checkFinite(0.17107849149772322);
      int int0 = MathUtils.addAndCheck(0, 136);
      assertEquals(136, int0);
      
      byte[] byteArray0 = new byte[2];
      byteArray0[0] = (byte) (-120);
      byteArray0[1] = (byte)78;
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertNotNull(bigInteger0);
      assertEquals(2, byteArray0.length);
      assertArrayEquals(new byte[] {(byte) (-120), (byte)78}, byteArray0);
      assertEquals((short) (-30642), bigInteger0.shortValue());
      assertEquals((byte)78, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = BigInteger.TEN;
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)10, bigInteger1.shortValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = bigInteger0.gcd(bigInteger1);
      assertNotNull(bigInteger2);
      assertEquals(2, byteArray0.length);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertArrayEquals(new byte[] {(byte) (-120), (byte)78}, byteArray0);
      assertEquals((short) (-30642), bigInteger0.shortValue());
      assertEquals((byte)78, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((byte)2, bigInteger2.byteValue());
      assertEquals((short)2, bigInteger2.shortValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, 0);
      assertNotNull(bigInteger3);
      assertEquals(2, byteArray0.length);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertArrayEquals(new byte[] {(byte) (-120), (byte)78}, byteArray0);
      assertEquals((short) (-30642), bigInteger0.shortValue());
      assertEquals((byte)78, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger3.shortValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(1863.0, 0.0);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) 136, (-2565.2132), (double) 136);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
  }

  @Test(timeout = 4000)
  public void test130()  throws Throwable  {
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = 2287.30124963;
      doubleArray0[1] = 2183.0;
      doubleArray0[2] = (-244.76960986764);
      doubleArray0[3] = 31.25;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 1151.29465929;
      doubleArray0[6] = (-1985.7520236);
      doubleArray0[7] = 0.0;
      doubleArray0[8] = Double.NEGATIVE_INFINITY;
      // Undeclared exception!
      try { 
        MathUtils.checkFinite(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // value -\u221E at index 8
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test131()  throws Throwable  {
      float[] floatArray0 = new float[0];
      float[] floatArray1 = new float[4];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = 0.0F;
      floatArray1[1] = (-1191.927F);
      floatArray1[2] = (-3.4028235E38F);
      floatArray1[3] = 0.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertFalse(boolean0);
      assertEquals(0, floatArray0.length);
      assertEquals(4, floatArray1.length);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {0.0F, (-1191.927F), (-3.4028235E38F), 0.0F}, floatArray1, 0.01F);
      
      int int0 = 5022;
      int int1 = MathUtils.gcd(5022, 5022);
      assertEquals(5022, int1);
      assertTrue(int1 == int0);
      
      MathUtils.checkFinite(88.64406130616);
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 0.0F;
      doubleArray0[1] = (double) (-3.4028235E38F);
      doubleArray0[2] = (double) (-1191.927F);
      doubleArray0[3] = (double) (-1191.927F);
      doubleArray0[4] = (double) 0.0F;
      doubleArray0[5] = (double) 5022;
      doubleArray0[6] = (double) 5022;
      int int2 = MathUtils.hash(doubleArray0);
      assertEquals((-1550247520), int2);
      assertEquals(7, doubleArray0.length);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertArrayEquals(new double[] {0.0, (-3.4028234663852886E38), (-1191.927001953125), (-1191.927001953125), 0.0, 5022.0, 5022.0}, doubleArray0, 0.01);
      
      int int3 = MathUtils.mulAndCheck((-3734), (-3734));
      assertEquals(13942756, int3);
      assertFalse(int3 == int1);
      assertFalse(int3 == int2);
      assertFalse(int3 == int0);
      
      int int4 = MathUtils.compareTo(1.1102230246251565E-16, 3485.811951147, 0.0);
      assertEquals((-1), int4);
      assertFalse(int4 == int2);
      assertFalse(int4 == int1);
      assertFalse(int4 == int3);
      assertFalse(int4 == int0);
      
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(3.4028234663852886E38, double0, 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, (-3.4028234663852886E38), (-1191.927001953125), (-1191.927001953125), 0.0, 5022.0, 5022.0}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.factorialLog((-1));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test132()  throws Throwable  {
      float[] floatArray0 = new float[0];
      float[] floatArray1 = new float[4];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = 0.0F;
      floatArray1[1] = (-1191.927F);
      floatArray1[2] = (-3.4028235E38F);
      floatArray1[3] = 0.0F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray1);
      assertFalse(boolean0);
      assertEquals(0, floatArray0.length);
      assertEquals(4, floatArray1.length);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertArrayEquals(new float[] {}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {0.0F, (-1191.927F), (-3.4028235E38F), 0.0F}, floatArray1, 0.01F);
      
      int int0 = 5022;
      int int1 = MathUtils.gcd(5022, 5022);
      assertEquals(5022, int1);
      assertTrue(int1 == int0);
      
      MathUtils.checkFinite(88.64406130616);
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 0.0F;
      doubleArray0[2] = (double) (-1191.927F);
      doubleArray0[3] = (double) (-1191.927F);
      doubleArray0[4] = (double) 0.0F;
      doubleArray0[5] = (double) 5022;
      doubleArray0[6] = (double) 5022;
      int int2 = MathUtils.hash(doubleArray0);
      assertEquals(1463677503, int2);
      assertEquals(7, doubleArray0.length);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      assertArrayEquals(new double[] {0.0, 0.0, (-1191.927001953125), (-1191.927001953125), 0.0, 5022.0, 5022.0}, doubleArray0, 0.01);
      
      int int3 = MathUtils.mulAndCheck((-3734), (-3734));
      assertEquals(13942756, int3);
      assertFalse(int3 == int1);
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      
      int int4 = MathUtils.compareTo(1.1102230246251565E-16, 3485.811951147, 0.0);
      assertEquals((-1), int4);
      assertFalse(int4 == int0);
      assertFalse(int4 == int1);
      assertFalse(int4 == int2);
      assertFalse(int4 == int3);
      
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(7299.47586857919, double0, 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-1191.927001953125), (-1191.927001953125), 0.0, 5022.0, 5022.0}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.factorialLog((-1));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for n!, got n = -1
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test133()  throws Throwable  {
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 873.804566643;
      doubleArray0[1] = (-754.80195947822);
      doubleArray0[2] = 1526.702;
      doubleArray0[3] = 0.7249995199969751;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 0.5;
      doubleArray0[6] = 0.0;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, false);
      assertFalse(boolean0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {873.804566643, (-754.80195947822), 1526.702, 0.7249995199969751, 0.0, 0.5, 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test134()  throws Throwable  {
      int int0 = 0;
      int int1 = MathUtils.lcm(0, 0);
      assertEquals(0, int1);
      assertTrue(int1 == int0);
      
      double[] doubleArray0 = new double[0];
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, false);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test135()  throws Throwable  {
      float float0 = MathUtils.indicator(0.0F);
      assertEquals(1.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test136()  throws Throwable  {
      int int0 = 0;
      int int1 = MathUtils.mulAndCheck(0, 0);
      assertEquals(0, int1);
      assertTrue(int1 == int0);
      
      BigInteger bigInteger0 = null;
      BigInteger bigInteger1 = BigInteger.ZERO;
      assertNotNull(bigInteger1);
      assertEquals((byte)0, bigInteger1.byteValue());
      assertEquals((short)0, bigInteger1.shortValue());
      
      double double0 = MathUtils.sinh(0);
      assertEquals(0.0, double0, 0.01);
      
      MockRandom mockRandom0 = new MockRandom();
      assertNotNull(mockRandom0);
      
      // Undeclared exception!
      try { 
        mockRandom0.ints((long) 0, 191, 0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // bound must be greater than origin
         //
         verifyException("java.util.Random", e);
      }
  }

  @Test(timeout = 4000)
  public void test137()  throws Throwable  {
      byte[] byteArray0 = new byte[5];
      byteArray0[0] = (byte)5;
      byteArray0[1] = (byte)11;
      byteArray0[2] = (byte)13;
      byteArray0[3] = (byte)0;
      byteArray0[4] = (byte) (-69);
      BigInteger bigInteger0 = new BigInteger(byteArray0);
      assertNotNull(bigInteger0);
      assertEquals(5, byteArray0.length);
      assertArrayEquals(new byte[] {(byte)5, (byte)11, (byte)13, (byte)0, (byte) (-69)}, byteArray0);
      assertEquals((short)187, bigInteger0.shortValue());
      assertEquals((byte) (-69), bigInteger0.byteValue());
      
      BigInteger bigInteger1 = bigInteger0.negate();
      assertNotNull(bigInteger1);
      assertEquals(5, byteArray0.length);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertArrayEquals(new byte[] {(byte)5, (byte)11, (byte)13, (byte)0, (byte) (-69)}, byteArray0);
      assertEquals((short)187, bigInteger0.shortValue());
      assertEquals((byte) (-69), bigInteger0.byteValue());
      assertEquals((byte)69, bigInteger1.byteValue());
      assertEquals((short) (-187), bigInteger1.shortValue());
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, 2426);
      assertNotNull(bigInteger2);
      assertEquals(5, byteArray0.length);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertArrayEquals(new byte[] {(byte)5, (byte)11, (byte)13, (byte)0, (byte) (-69)}, byteArray0);
      assertEquals((short)187, bigInteger0.shortValue());
      assertEquals((byte) (-69), bigInteger0.byteValue());
      assertEquals((short) (-15943), bigInteger2.shortValue());
      assertEquals((byte) (-71), bigInteger2.byteValue());
  }

  @Test(timeout = 4000)
  public void test138()  throws Throwable  {
      float float0 = MathUtils.sign(758.95F);
      assertEquals(1.0F, float0, 0.01F);
      
      int[] intArray0 = new int[8];
      intArray0[0] = 1799;
      intArray0[1] = 112;
      intArray0[2] = 0;
      intArray0[3] = 13;
      intArray0[4] = (-1072);
      intArray0[5] = (-1065);
      intArray0[6] = (-449);
      intArray0[7] = (-1123);
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(8, intArray0.length);
      assertArrayEquals(new int[] {1799, 112, 0, 13, (-1072), (-1065), (-449), (-1123)}, intArray0);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 112;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(112.0, double0, 0.01);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {112.0}, doubleArray0, 0.01);
      
      boolean boolean0 = MathUtils.equals(1.304E19, (double) 112, 0.04168701738764507);
      assertFalse(boolean0);
      
      long long0 = MathUtils.binomialCoefficient(112, (-1123));
      assertEquals(1L, long0);
      
      int int1 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int1);
      assertEquals(8, intArray0.length);
      assertTrue(int1 == int0);
      assertArrayEquals(new int[] {1799, 112, 0, 13, (-1072), (-1065), (-449), (-1123)}, intArray0);
      
      short short0 = MathUtils.sign((short) (-3650));
      assertEquals((short) (-1), short0);
      
      long long1 = MathUtils.sign((long) 1799);
      assertEquals(1L, long1);
      assertTrue(long1 == long0);
  }

  @Test(timeout = 4000)
  public void test139()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = 294.092850461416;
      doubleArray0[1] = (-690.8481);
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (-2008.5);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, false);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not increasing (294.093 > -690.848)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test140()  throws Throwable  {
      int int0 = MathUtils.gcd(0, 634);
      assertEquals(634, int0);
      
      boolean boolean0 = MathUtils.equals((-1.0F), (float) 0);
      assertFalse(boolean0);
      
      long long0 = MathUtils.addAndCheck(0L, 0L);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test141()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 0.0;
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0}, doubleArray0, 0.01);
      
      boolean boolean0 = MathUtils.equals((-432.3413), 488.510044458141);
      assertFalse(boolean0);
      
      int int0 = 2264;
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-587), 2264);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 2,264, n = -587
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test142()  throws Throwable  {
      float[] floatArray0 = new float[7];
      floatArray0[0] = (-1.0F);
      floatArray0[1] = 218.06178F;
      floatArray0[2] = 3.4028235E38F;
      floatArray0[3] = 2505.0F;
      floatArray0[4] = (-1977.2F);
      floatArray0[5] = 1.0F;
      floatArray0[6] = (-1839.3F);
      float[] floatArray1 = new float[0];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray1);
      assertFalse(boolean0);
      assertEquals(7, floatArray0.length);
      assertEquals(0, floatArray1.length);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertArrayEquals(new float[] {(-1.0F), 218.06178F, 3.4028235E38F, 2505.0F, (-1977.2F), 1.0F, (-1839.3F)}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {}, floatArray1, 0.01F);
  }

  @Test(timeout = 4000)
  public void test143()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = Double.POSITIVE_INFINITY;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (-1138.75);
      doubleArray0[3] = 1809.37;
      doubleArray0[4] = 641.8635005296035;
      doubleArray0[5] = Double.NaN;
      doubleArray0[6] = Double.POSITIVE_INFINITY;
      doubleArray0[7] = 522.75778666847;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly increasing (\u221E >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test144()  throws Throwable  {
      double[] doubleArray0 = new double[23];
      doubleArray0[0] = 1580.001483671846;
      doubleArray0[1] = 0.5;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 836.3863546902;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (-2207.83869002);
      doubleArray0[6] = 0.0;
      doubleArray0[7] = (-1186.7215623);
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(23, doubleArray0.length);
      
      Integer integer0 = new Integer((-1289));
      assertEquals((-1289), (int)integer0);
      assertNotNull(integer0);
      
      Pair<Integer, Object> pair0 = new Pair<Integer, Object>(integer0, integer0);
      assertNotNull(pair0);
      
      MathUtils.checkNotNull((Object) pair0);
      long long0 = MathUtils.sign((long) (-1289));
      assertEquals((-1L), long0);
      
      int[] intArray0 = new int[3];
      intArray0[0] = (-1289);
      intArray0[1] = 232;
      intArray0[2] = (-1289);
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(3, intArray0.length);
      assertArrayEquals(new int[] {(-1289), 232, (-1289)}, intArray0);
  }

  @Test(timeout = 4000)
  public void test145()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle(0.0, 1.0);
      assertEquals(0.0, double0, 0.01);
      
      long long0 = 24L;
      long long1 = MathUtils.gcd(1154L, 24L);
      assertEquals(2L, long1);
      assertFalse(long1 == long0);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 1.0;
      doubleArray0[2] = (double) 2L;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = (double) 24L;
      doubleArray0[5] = (double) 1154L;
      doubleArray0[6] = (double) 24L;
      doubleArray0[7] = 1.0;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 2 and 3 are not strictly increasing (2 >= 0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test146()  throws Throwable  {
      long long0 = MathUtils.lcm(935L, 935L);
      assertEquals(935L, long0);
      
      long long1 = MathUtils.addAndCheck(935L, (-1390L));
      assertEquals((-455L), long1);
      assertFalse(long1 == long0);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) 935L;
      doubleArray0[1] = (double) 935L;
      doubleArray0[2] = (double) (-1390L);
      MathUtils.checkFinite(doubleArray0);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {935.0, 935.0, (-1390.0)}, doubleArray0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) (-1390L), (float) 935L, (float) (-455L));
      assertFalse(boolean0);
      
      double double0 = MathUtils.sign(1077.68909080239);
      assertEquals(1.0, double0, 0.01);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      int int0 = (-1032);
      int int1 = MathUtils.lcm((-1032), 9);
      assertEquals(3096, int1);
      assertFalse(int1 == int0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow(2571, (-1032));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,032)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test147()  throws Throwable  {
      int int0 = (-1609);
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(0.0, 0.0, (-1609));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test148()  throws Throwable  {
      double double0 = 0.08371849358081818;
      double double1 = MathUtils.log(0.08371849358081818, 0.08371849358081818);
      assertEquals(1.0, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      int int0 = 0;
      int int1 = MathUtils.pow(0, (long) 0);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
      
      int int2 = 0;
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(0, 1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 1, n = 0
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test149()  throws Throwable  {
      int int0 = (-3568);
      int int1 = MathUtils.sign((-3568));
      assertEquals((-1), int1);
      assertFalse(int1 == int0);
      
      int int2 = (-770);
      // Undeclared exception!
      try { 
        MathUtils.equals((double) (-1), 1364.62254, (-770));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test150()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(1.625, 0.5039018405998233, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test151()  throws Throwable  {
      long long0 = MathUtils.indicator(1307674368000L);
      assertEquals(1L, long0);
      
      long long1 = MathUtils.lcm((-33L), (-3247L));
      assertEquals(107151L, long1);
      assertFalse(long1 == long0);
  }

  @Test(timeout = 4000)
  public void test152()  throws Throwable  {
      double double0 = 0.0;
      int int0 = MathUtils.hash(0.0);
      assertEquals(0, int0);
      
      double[] doubleArray0 = new double[0];
      double double1 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double1, 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      MathUtils.checkFinite((-1327.74026962));
      int[] intArray0 = new int[0];
      int int1 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int1);
      assertEquals(0, intArray0.length);
      assertTrue(int1 == int0);
      assertArrayEquals(new int[] {}, intArray0);
      
      int int2 = MathUtils.hash((-1327.74026962));
      assertEquals((-911606133), int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      
      long long0 = MathUtils.lcm(9218868437227405312L, (long) 0);
      assertEquals(0L, long0);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientLog((-1688), 0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 0, n = -1,688
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test153()  throws Throwable  {
      int int0 = 0;
      int int1 = MathUtils.pow(0, 0);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
      
      int int2 = (-236);
      int int3 = MathUtils.gcd((-236), 1);
      assertEquals(1, int3);
      assertTrue(int3 == int1);
      assertFalse(int3 == int0);
      assertFalse(int3 == int2);
      
      double[] doubleArray0 = null;
      // Undeclared exception!
      try { 
        MathUtils.distance1((double[]) null, (double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test154()  throws Throwable  {
      int int0 = (-2883);
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient((-1318), (-2883));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -1,318
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test155()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(3962.5479, 3962.5479, 254);
      assertTrue(boolean0);
      
      int int0 = MathUtils.gcd(254, 254);
      assertEquals(254, int0);
      
      int int1 = MathUtils.lcm((-1), 254);
      assertEquals(254, int1);
      assertTrue(int1 == int0);
      
      long long0 = MathUtils.addAndCheck((long) 254, (long) 254);
      assertEquals(508L, long0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 3962.5479;
      doubleArray0[1] = (double) 508L;
      doubleArray0[2] = (double) 254;
      doubleArray0[3] = (double) (-1);
      doubleArray0[4] = Double.NaN;
      doubleArray0[5] = 2848.63;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {3962.5479, 508.0, 254.0, (-1.0), Double.NaN, 2848.63}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test156()  throws Throwable  {
      double double0 = MathUtils.indicator(0.0);
      assertEquals(1.0, double0, 0.01);
      
      int int0 = 1098;
      int int1 = MathUtils.pow(1098, 1098);
      assertEquals(0, int1);
      assertFalse(int1 == int0);
      
      int int2 = 3;
      float float0 = MathUtils.round((float) 0, 1098, 3);
      assertEquals(Float.NaN, float0, 0.01F);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 3;
      doubleArray0[1] = (double) 1098;
      MathUtils.checkOrder(doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {3.0, 1098.0}, doubleArray0, 0.01);
      
      int[] intArray0 = new int[3];
      intArray0[0] = 3;
      intArray0[1] = 3;
      intArray0[2] = (-419);
      int int3 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int3);
      assertEquals(3, intArray0.length);
      assertFalse(int3 == int2);
      assertFalse(int3 == int0);
      assertTrue(int3 == int1);
      assertArrayEquals(new int[] {3, 3, (-419)}, intArray0);
      
      double[][] doubleArray1 = new double[1][9];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertEquals(2, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertArrayEquals(new double[] {3.0, 1098.0}, doubleArray0, 0.01);
      
      int int4 = (-2263);
      int int5 = MathUtils.mulAndCheck(3, (-2263));
      assertEquals((-6789), int5);
      assertFalse(int5 == int4);
      assertFalse(int5 == int0);
      assertFalse(int5 == int2);
      assertFalse(int5 == int1);
      assertFalse(int5 == int3);
      
      double[] doubleArray2 = MathUtils.copyOf(doubleArray0);
      assertNotNull(doubleArray2);
      assertEquals(2, doubleArray2.length);
      assertEquals(2, doubleArray0.length);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray0, doubleArray2);
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {3.0, 1098.0}, doubleArray2, 0.01);
      assertArrayEquals(new double[] {3.0, 1098.0}, doubleArray0, 0.01);
      
      short short0 = MathUtils.sign((short)12);
      assertEquals((short)1, short0);
      
      double double1 = MathUtils.sign(0.0);
      assertEquals(0.0, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      Object object0 = new Object();
      assertNotNull(object0);
      
      LocalizedFormats localizedFormats0 = LocalizedFormats.ASSYMETRIC_EIGEN_NOT_SUPPORTED;
      assertEquals(LocalizedFormats.ASSYMETRIC_EIGEN_NOT_SUPPORTED, localizedFormats0);
      assertEquals("eigen decomposition of assymetric matrices not supported yet", localizedFormats0.getSourceString());
      
      Locale locale0 = Locale.US;
      assertNotNull(locale0);
      assertEquals("en", locale0.getLanguage());
      assertEquals("eng", locale0.getISO3Language());
      assertEquals("US", locale0.getCountry());
      assertEquals("", locale0.getVariant());
      assertEquals("en_US", locale0.toString());
      assertEquals("USA", locale0.getISO3Country());
      
      String string0 = "";
      // Undeclared exception!
      try { 
        LocalizedFormats.valueOf("");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.exception.util.LocalizedFormats.
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test157()  throws Throwable  {
      int int0 = MathUtils.pow(913, 913);
      assertEquals((-1994457967), int0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) (-1994457967);
      doubleArray0[1] = (double) (-1994457967);
      doubleArray0[2] = (double) (-1994457967);
      doubleArray0[3] = (double) (-1994457967);
      doubleArray0[4] = (double) 913;
      doubleArray0[5] = (double) (-1994457967);
      doubleArray0[6] = (double) 913;
      doubleArray0[7] = (double) (-1994457967);
      doubleArray0[8] = (double) (-1994457967);
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {(-1.994457967E9), (-1.994457967E9), (-1.994457967E9), (-1.994457967E9), 913.0, (-1.994457967E9), 913.0, (-1.994457967E9), (-1.994457967E9)}, doubleArray0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((-715.8371F), 1.0F, 913);
      assertFalse(boolean0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      // Undeclared exception!
      try { 
        MathUtils.lcm(3311, 2131337154);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test158()  throws Throwable  {
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = 0.19999998807907104;
      doubleArray0[1] = 7.0;
      doubleArray0[2] = 1.1430250008909141E-8;
      doubleArray0[3] = (-7.800414592973399E-9);
      doubleArray0[4] = 1933.66908141717;
      doubleArray0[5] = 185.1333421119;
      doubleArray0[6] = 0.0;
      doubleArray0[7] = (-284.137342);
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0);
      assertNotNull(doubleArray1);
      assertEquals(8, doubleArray0.length);
      assertEquals(8, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.19999998807907104, 7.0, 1.1430250008909141E-8, (-7.800414592973399E-9), 1933.66908141717, 185.1333421119, 0.0, (-284.137342)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {0.19999998807907104, 7.0, 1.1430250008909141E-8, (-7.800414592973399E-9), 1933.66908141717, 185.1333421119, 0.0, (-284.137342)}, doubleArray1, 0.01);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray1, mathUtils_OrderDirection0, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly decreasing (0.2 <= 7)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test159()  throws Throwable  {
      int int0 = MathUtils.hash(Double.NEGATIVE_INFINITY);
      assertEquals((-1048576), int0);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = Double.NEGATIVE_INFINITY;
      doubleArray0[1] = (double) (-1048576);
      doubleArray0[2] = (double) (-1048576);
      doubleArray0[3] = (-3041.7);
      doubleArray0[4] = (double) (-1048576);
      doubleArray0[5] = Double.NEGATIVE_INFINITY;
      doubleArray0[6] = (double) (-1048576);
      doubleArray0[7] = Double.NEGATIVE_INFINITY;
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {Double.NEGATIVE_INFINITY, (-1048576.0), (-1048576.0), (-3041.7), (-1048576.0), Double.NEGATIVE_INFINITY, (-1048576.0), Double.NEGATIVE_INFINITY}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-1048576), (-1048576));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -1,048,576
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test160()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.99, 0.0);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equals(0.0F, 0.0F);
      assertTrue(boolean1);
      assertFalse(boolean1 == boolean0);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      int int0 = 0;
      BigInteger bigInteger1 = bigInteger0.clearBit(0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger0.negate();
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertTrue(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short) (-10), bigInteger2.shortValue());
      assertEquals((byte) (-10), bigInteger2.byteValue());
      
      MockRandom mockRandom0 = new MockRandom(61);
      assertNotNull(mockRandom0);
      
      BigInteger bigInteger3 = BigInteger.probablePrime(61, mockRandom0);
      assertNotNull(bigInteger3);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotSame(bigInteger3, bigInteger0);
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertEquals((byte)17, bigInteger3.byteValue());
      assertEquals((short)17, bigInteger3.shortValue());
      
      BigInteger bigInteger4 = MathUtils.pow(bigInteger0, 0L);
      assertNotNull(bigInteger4);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger4);
      assertNotSame(bigInteger4, bigInteger2);
      assertNotSame(bigInteger4, bigInteger3);
      assertNotSame(bigInteger4, bigInteger0);
      assertNotSame(bigInteger4, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger0.equals((Object)bigInteger3));
      assertTrue(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger4.equals((Object)bigInteger2));
      assertFalse(bigInteger4.equals((Object)bigInteger3));
      assertFalse(bigInteger4.equals((Object)bigInteger0));
      assertFalse(bigInteger4.equals((Object)bigInteger1));
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger4.shortValue());
      assertEquals((byte)1, bigInteger4.byteValue());
      
      double double0 = MathUtils.cosh((-1680.5434862536977));
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      long long0 = MathUtils.indicator(0L);
      assertEquals(1L, long0);
      
      double[][] doubleArray0 = new double[6][9];
      doubleArray0[0] = null;
      doubleArray0[1] = null;
      doubleArray0[2] = null;
      doubleArray0[3] = null;
      doubleArray0[4] = null;
      doubleArray0[5] = null;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test161()  throws Throwable  {
      long long0 = MathUtils.addAndCheck(533L, 533L);
      assertEquals(1066L, long0);
      
      int int0 = MathUtils.hash((double) 1066L);
      assertEquals(1083222016, int0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 533L;
      doubleArray0[1] = (double) 1066L;
      double[][] doubleArray1 = new double[6][0];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[9];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 1066L;
      doubleArray2[1] = (double) 1083222016;
      doubleArray2[2] = (double) 533L;
      doubleArray2[3] = (double) 1066L;
      doubleArray2[4] = 890.6205742273676;
      doubleArray2[5] = (double) 533L;
      doubleArray2[6] = (double) 1083222016;
      doubleArray2[7] = (double) 1066L;
      doubleArray2[8] = 0.0;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      doubleArray1[5] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 9 != 2
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test162()  throws Throwable  {
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = 693.29;
      doubleArray0[1] = (-797.70073775);
      doubleArray0[2] = (-853.66134212293);
      doubleArray0[3] = (-1196.78308);
      doubleArray0[4] = 154.791088864;
      int int0 = 61;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 61);
      assertNotNull(doubleArray1);
      assertEquals(5, doubleArray0.length);
      assertEquals(61, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {693.29, (-797.70073775), (-853.66134212293), (-1196.78308), 154.791088864}, doubleArray0, 0.01);
      
      int int1 = MathUtils.addAndCheck(61, 61);
      assertEquals(122, int1);
      assertFalse(int1 == int0);
      
      int int2 = 170;
      // Undeclared exception!
      try { 
        MathUtils.round((float) 61, 2263, 170);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method 170, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test163()  throws Throwable  {
      double double0 = MathUtils.factorialLog(0);
      assertEquals(0.0, double0, 0.01);
      
      long long0 = MathUtils.pow((long) 0, 0);
      assertEquals(1L, long0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = (-2742.7715430337266);
      doubleArray0[3] = 0.0;
      doubleArray0[4] = (double) 0;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = (double) 0;
      double double1 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double1, 0.01);
      assertEquals(7, doubleArray0.length);
      assertEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {0.0, 0.0, (-2742.7715430337266), 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      long long1 = MathUtils.subAndCheck((long) 0, 113236205062349959L);
      assertEquals((-113236205062349959L), long1);
      assertFalse(long1 == long0);
      
      boolean boolean0 = MathUtils.equals(doubleArray0, (double[]) null);
      assertFalse(boolean0);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, (-2742.7715430337266), 0.0, 0.0, 0.0, 0.0}, doubleArray0, 0.01);
      
      int int0 = MathUtils.hash((-2742.7715430337266));
      assertEquals((-954912981), int0);
      
      short short0 = MathUtils.sign((short)44);
      assertEquals((short)1, short0);
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf((int[]) null, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test164()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 6.283185307179586;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = (-2222.3861548);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 0 and 1 are not strictly decreasing (0 <= 6.283)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test165()  throws Throwable  {
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      int int0 = (-4649);
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble((-4649), (-4649));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= 0 for binomial coefficient (n, k), got n = -4,649
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test166()  throws Throwable  {
      int int0 = (-363);
      long long0 = 2974L;
      int int1 = MathUtils.pow((-363), 2974L);
      assertEquals((-25316599), int1);
      assertFalse(int1 == int0);
      
      // Undeclared exception!
      try { 
        MathUtils.pow((-363), (-25316599));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-25,316,599)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test167()  throws Throwable  {
      int[] intArray0 = new int[2];
      intArray0[0] = 0;
      intArray0[1] = 0;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(2, intArray0.length);
      assertEquals(2, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {0, 0}, intArray0);
      assertArrayEquals(new int[] {0, 0}, intArray1);
      
      long long0 = MathUtils.indicator((long) 0);
      assertEquals(1L, long0);
      
      float float0 = Float.NaN;
      int int0 = (-2315);
      // Undeclared exception!
      try { 
        MathUtils.round(Float.NaN, 0, (-2315));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // invalid rounding method -2,315, valid methods: ROUND_CEILING (2), ROUND_DOWN (1), ROUND_FLOOR (3), ROUND_HALF_DOWN (5), ROUND_HALF_EVEN (6), ROUND_HALF_UP (4), ROUND_UNNECESSARY (7), ROUND_UP (0)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test168()  throws Throwable  {
      float float0 = MathUtils.indicator((-1623.8916F));
      assertEquals((-1.0F), float0, 0.01F);
      
      float float1 = MathUtils.sign((-1.0F));
      assertEquals((-1.0F), float1, 0.01F);
      assertEquals(float1, float0, 0.01F);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) (-1.0F);
      doubleArray0[1] = (double) (-1.0F);
      doubleArray0[2] = (double) (-1.0F);
      doubleArray0[3] = (double) (-1.0F);
      doubleArray0[4] = (double) (-1.0F);
      doubleArray0[5] = (double) (-1.0F);
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-1.0), (-1.0), (-1.0), (-1.0), (-1.0), (-1.0)}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test169()  throws Throwable  {
      short short0 = MathUtils.indicator((short)4540);
      assertEquals((short)1, short0);
      
      int int0 = MathUtils.subAndCheck((-3086), (-3086));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test170()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(3216, 3216);
      assertEquals(10342656, int0);
      
      long long0 = MathUtils.indicator((long) 3216);
      assertEquals(1L, long0);
      
      double[] doubleArray0 = new double[8];
      doubleArray0[0] = (double) 3216;
      doubleArray0[1] = (double) 10342656;
      doubleArray0[2] = (double) 1L;
      doubleArray0[3] = (double) 10342656;
      doubleArray0[4] = (-1224.945633851);
      doubleArray0[5] = (double) 1L;
      doubleArray0[6] = 0.0;
      doubleArray0[7] = (double) 1L;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(8, doubleArray0.length);
      assertArrayEquals(new double[] {3216.0, 1.0342656E7, 1.0, 1.0342656E7, (-1224.945633851), 1.0, 0.0, 1.0}, doubleArray0, 0.01);
      
      float float0 = MathUtils.sign((float) 1L);
      assertEquals(1.0F, float0, 0.01F);
      
      int int1 = 1380;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 1380);
      assertNotNull(doubleArray1);
      assertEquals(8, doubleArray0.length);
      assertEquals(1380, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {3216.0, 1.0342656E7, 1.0, 1.0342656E7, (-1224.945633851), 1.0, 0.0, 1.0}, doubleArray0, 0.01);
      
      int int2 = 0;
      long long1 = MathUtils.binomialCoefficient(0, 0);
      assertEquals(1L, long1);
      assertTrue(long1 == long0);
      
      boolean boolean0 = MathUtils.equals((float) 1L, (-1120.75F));
      assertFalse(boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.distance(doubleArray1, doubleArray0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 8
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test171()  throws Throwable  {
      int[] intArray0 = new int[3];
      intArray0[0] = (-2953);
      intArray0[1] = 0;
      intArray0[2] = 2076;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(3, intArray0.length);
      assertArrayEquals(new int[] {(-2953), 0, 2076}, intArray0);
      
      double double0 = MathUtils.binomialCoefficientLog(1278, 0);
      assertEquals(0.0, double0, 0.01);
      
      double double1 = MathUtils.factorialLog(69);
      assertEquals(226.1905483237276, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      boolean boolean0 = MathUtils.equals((float) 0, 2316.3047F, (float) (-2953));
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) (-2953);
      doubleArray0[1] = (double) 2316.3047F;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 226.1905483237276;
      doubleArray0[4] = 226.1905483237276;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 2076);
      assertNotNull(doubleArray1);
      assertEquals(5, doubleArray0.length);
      assertEquals(2076, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {(-2953.0), 2316.3046875, 0.0, 226.1905483237276, 226.1905483237276}, doubleArray0, 0.01);
      
      boolean boolean1 = MathUtils.equals(3335.01, 0.0, 0.0);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.equalsIncludingNaN(1.0F, 0.0F, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test172()  throws Throwable  {
      int int0 = 2;
      // Undeclared exception!
      try { 
        MathUtils.pow((BigInteger) null, 2);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test173()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 248.58348006360137;
      boolean boolean0 = MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {248.58348006360137}, doubleArray0, 0.01);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(2330.77F, 2330.77F);
      assertTrue(boolean1);
      assertTrue(boolean1 == boolean0);
      
      int int0 = MathUtils.pow((-866), 0L);
      assertEquals(1, int0);
      
      double double0 = MathUtils.round(1.0, 0);
      assertEquals(1.0, double0, 0.01);
      
      int int1 = MathUtils.pow(0, (long) 0);
      assertEquals(1, int1);
      assertTrue(int1 == int0);
      
      // Undeclared exception!
      try { 
        MathUtils.equals(0.0F, 2330.77F, (-500));
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test174()  throws Throwable  {
      float float0 = MathUtils.sign((-363.342F));
      assertEquals((-1.0F), float0, 0.01F);
      
      int int0 = MathUtils.compareTo((-1.0F), (-1.0F), (-8.891659658688631));
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test175()  throws Throwable  {
      double[] doubleArray0 = null;
      int int0 = 0;
      double double0 = MathUtils.binomialCoefficientLog(1, 0);
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray1 = new double[6];
      doubleArray1[0] = 1.7976931348623157E308;
      doubleArray1[1] = 0.0;
      doubleArray1[2] = (double) 0;
      doubleArray1[3] = (double) 1;
      doubleArray1[4] = (double) 1;
      doubleArray1[5] = (double) 0;
      double[] doubleArray2 = MathUtils.normalizeArray(doubleArray1, (-738.41591782));
      assertNotNull(doubleArray2);
      assertEquals(6, doubleArray1.length);
      assertEquals(6, doubleArray2.length);
      assertNotSame(doubleArray1, doubleArray2);
      assertNotSame(doubleArray2, doubleArray1);
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertArrayEquals(new double[] {1.7976931348623157E308, 0.0, 0.0, 1.0, 1.0, 0.0}, doubleArray1, 0.01);
      assertArrayEquals(new double[] {Double.NEGATIVE_INFINITY, -0.0, -0.0, (-4.1075748886172105E-306), (-4.1075748886172105E-306), -0.0}, doubleArray2, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.copyOf((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test176()  throws Throwable  {
      short short0 = MathUtils.indicator((short)18);
      assertEquals((short)1, short0);
  }

  @Test(timeout = 4000)
  public void test177()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(13, 13);
      assertEquals(0.0, double0, 0.01);
      
      int int0 = 0;
      int int1 = MathUtils.pow(0, 877);
      assertEquals(0, int1);
      assertTrue(int1 == int0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 13;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[1][7];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(2, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertArrayEquals(new double[] {0.0, 13.0}, doubleArray0, 0.01);
      
      int int2 = MathUtils.hash(doubleArray0);
      assertEquals(1076495297, int2);
      assertEquals(2, doubleArray0.length);
      assertFalse(int2 == int0);
      assertFalse(int2 == int1);
      assertArrayEquals(new double[] {0.0, 13.0}, doubleArray0, 0.01);
      
      int[] intArray0 = new int[4];
      intArray0[0] = 1076495297;
      intArray0[1] = 0;
      intArray0[2] = 1076495297;
      int int3 = (-2302);
      intArray0[3] = (-2302);
      int int4 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int4);
      assertEquals(4, intArray0.length);
      assertTrue(int4 == int1);
      assertTrue(int4 == int0);
      assertFalse(int4 == int2);
      assertFalse(int4 == int3);
      assertArrayEquals(new int[] {1076495297, 0, 1076495297, (-2302)}, intArray0);
      
      int int5 = MathUtils.compareTo(1.997844754509471E-9, 877, 0.5);
      assertEquals((-1), int5);
      assertFalse(int5 == int0);
      assertFalse(int5 == int3);
      assertFalse(int5 == int4);
      assertFalse(int5 == int1);
      assertFalse(int5 == int2);
      
      long long0 = MathUtils.subAndCheck((long) 0, (long) 13);
      assertEquals((-13L), long0);
      
      long long1 = MathUtils.mulAndCheck((long) 1076495297, (long) (-1));
      assertEquals((-1076495297L), long1);
      assertFalse(long1 == long0);
      
      // Undeclared exception!
      try { 
        MathUtils.distanceInf(intArray0, (int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test178()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0, (-0.49999999999999994), 125);
      assertFalse(boolean0);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = BigInteger.ONE;
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      BigInteger bigInteger2 = bigInteger0.divide(bigInteger1);
      assertNotNull(bigInteger2);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertSame(bigInteger2, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      assertEquals((short)0, bigInteger2.shortValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger3);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger0);
      assertSame(bigInteger3, bigInteger1);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)1, bigInteger3.byteValue());
      assertEquals((short)1, bigInteger3.shortValue());
      
      int[] intArray0 = new int[9];
      intArray0[0] = 125;
      intArray0[1] = 125;
      intArray0[2] = 125;
      intArray0[3] = 125;
      intArray0[4] = 125;
      intArray0[5] = 144;
      intArray0[6] = 125;
      intArray0[7] = 125;
      intArray0[8] = 125;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(9, intArray0.length);
      assertArrayEquals(new int[] {125, 125, 125, 125, 125, 144, 125, 125, 125}, intArray0);
  }

  @Test(timeout = 4000)
  public void test179()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(1681, 1681);
      assertEquals(0.0, double0, 0.01);
      
      double double1 = MathUtils.factorialDouble(1681);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      int int0 = 694;
      int int1 = MathUtils.pow(694, 0L);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
      
      long long0 = MathUtils.indicator((-4267615245585081135L));
      assertEquals((-1L), long0);
      
      MathUtils.checkFinite(918.055004150292);
      int[] intArray0 = new int[8];
      intArray0[0] = 1681;
      intArray0[1] = 1;
      intArray0[2] = 694;
      intArray0[3] = 694;
      intArray0[4] = 1681;
      intArray0[5] = 1;
      intArray0[6] = 1681;
      intArray0[7] = 694;
      double double2 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double2, 0.01);
      assertEquals(8, intArray0.length);
      assertEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertArrayEquals(new int[] {1681, 1, 694, 694, 1681, 1, 1681, 694}, intArray0);
      
      double double3 = MathUtils.sign(918.055004150292);
      assertEquals(1.0, double3, 0.01);
      assertNotEquals(double3, double1, 0.01);
      assertNotEquals(double3, double2, 0.01);
      assertNotEquals(double3, double0, 0.01);
      
      double double4 = 1859.1;
      double double5 = MathUtils.round(1859.1, 1681);
      assertEquals(1859.1, double5, 0.01);
      assertNotEquals(double5, double1, 0.01);
      assertEquals(double5, double4, 0.01);
      assertNotEquals(double5, double2, 0.01);
      assertNotEquals(double5, double0, 0.01);
      assertNotEquals(double5, double3, 0.01);
      
      int int2 = MathUtils.addAndCheck((-326), 694);
      assertEquals(368, int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
      
      int int3 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int3);
      assertEquals(8, intArray0.length);
      assertFalse(int3 == int1);
      assertFalse(int3 == int2);
      assertFalse(int3 == int0);
      assertArrayEquals(new int[] {1681, 1, 694, 694, 1681, 1, 1681, 694}, intArray0);
      
      int int4 = (-3698);
      // Undeclared exception!
      try { 
        MathUtils.copyOf(intArray0, (-3698));
        fail("Expecting exception: NegativeArraySizeException");
      
      } catch(NegativeArraySizeException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test180()  throws Throwable  {
      long long0 = MathUtils.indicator((-2736L));
      assertEquals((-1L), long0);
  }

  @Test(timeout = 4000)
  public void test181()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-2526.1), 1008.7645, (-2526.1));
      assertFalse(boolean0);
      
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      byte byte0 = bigInteger0.byteValueExact();
      assertEquals((byte)10, byte0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      int int0 = bigInteger0.bitCount();
      assertEquals(2, int0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0L);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      
      int[] intArray0 = new int[2];
      intArray0[0] = 0;
      intArray0[1] = (-746);
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2, intArray0.length);
      assertArrayEquals(new int[] {0, (-746)}, intArray0);
  }

  @Test(timeout = 4000)
  public void test182()  throws Throwable  {
      long long0 = MathUtils.factorial(0);
      assertEquals(1L, long0);
      
      int int0 = MathUtils.sign(0);
      assertEquals(0, int0);
      
      int int1 = MathUtils.subAndCheck((-1712), 0);
      assertEquals((-1712), int1);
      assertFalse(int1 == int0);
      
      double double0 = MathUtils.round((-3886.634892), (-1712));
      assertEquals(0.0, double0, 0.01);
      
      int int2 = MathUtils.sign((-1712));
      assertEquals((-1), int2);
      assertFalse(int2 == int1);
      assertFalse(int2 == int0);
  }

  @Test(timeout = 4000)
  public void test183()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 0.1111111111111111;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.1111111111111111}, doubleArray0, 0.01);
      
      MathUtils.checkNotNull((Object) "j6=KJL4E");
      double double0 = MathUtils.factorialDouble(2900);
      assertEquals(Double.POSITIVE_INFINITY, double0, 0.01);
      
      short short0 = MathUtils.sign((short) (-740));
      assertEquals((short) (-1), short0);
      
      int[] intArray0 = new int[6];
      intArray0[0] = (int) (short) (-740);
      intArray0[1] = (int) (short) (-740);
      intArray0[2] = (int) (short) (-1);
      intArray0[3] = 2900;
      intArray0[4] = 2900;
      intArray0[5] = (int) (short) (-1);
      int[] intArray1 = MathUtils.copyOf(intArray0, 0);
      assertNotNull(intArray1);
      assertEquals(6, intArray0.length);
      assertEquals(0, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {(-740), (-740), (-1), 2900, 2900, (-1)}, intArray0);
      assertArrayEquals(new int[] {}, intArray1);
  }

  @Test(timeout = 4000)
  public void test184()  throws Throwable  {
      double double0 = MathUtils.binomialCoefficientLog(0, 0);
      assertEquals(0.0, double0, 0.01);
      
      int int0 = MathUtils.pow(781, 0L);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test185()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 308.028799553;
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {308.028799553}, doubleArray0, 0.01);
      
      double double1 = MathUtils.indicator(308.028799553);
      assertEquals(1.0, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      double[] doubleArray1 = MathUtils.normalizeArray(doubleArray0, 1.0);
      assertNotNull(doubleArray1);
      assertEquals(1, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {308.028799553}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.0}, doubleArray1, 0.01);
      
      float[] floatArray0 = new float[1];
      floatArray0[0] = 0.0F;
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertEquals(1, floatArray0.length);
      assertArrayEquals(new float[] {0.0F}, floatArray0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test186()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(389L, 389L);
      assertEquals(151321L, long0);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = (double) 151321L;
      doubleArray0[1] = (double) 389L;
      doubleArray0[2] = (double) 151321L;
      doubleArray0[3] = (double) 389L;
      doubleArray0[4] = (double) 151321L;
      doubleArray0[5] = 1.304E19;
      doubleArray0[6] = (double) 151321L;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(1.304E19, double0, 0.01);
      assertEquals(7, doubleArray0.length);
      assertArrayEquals(new double[] {151321.0, 389.0, 151321.0, 389.0, 151321.0, 1.304E19, 151321.0}, doubleArray0, 0.01);
      
      double double1 = MathUtils.sinh(929.5214626932);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      long long1 = MathUtils.addAndCheck(151321L, (-755L));
      assertEquals(150566L, long1);
      assertFalse(long1 == long0);
  }

  @Test(timeout = 4000)
  public void test187()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.mulAndCheck(4577762542105553359L, 9223372036854775807L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test188()  throws Throwable  {
      int int0 = MathUtils.mulAndCheck(0, 0);
      assertEquals(0, int0);
      
      double double0 = MathUtils.log(6.123233995736766E-17, (-1867.06));
      assertEquals(Double.NaN, double0, 0.01);
      
      long long0 = MathUtils.binomialCoefficient(0, 0);
      assertEquals(1L, long0);
      
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F, 1928);
      assertTrue(boolean0);
      
      MathUtils.checkFinite((double) 1928);
      float float0 = MathUtils.sign((float) 0);
      assertEquals(0.0F, float0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test189()  throws Throwable  {
      byte byte0 = (byte) (-45);
      byte byte1 = MathUtils.indicator((byte) (-45));
      assertEquals((byte) (-1), byte1);
      assertFalse(byte1 == byte0);
      
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test190()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.pow((-1977), (-1977));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,977)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test191()  throws Throwable  {
      int int0 = 3149;
      int int1 = MathUtils.pow(3149, 3149);
      assertEquals(1388408381, int1);
      assertFalse(int1 == int0);
      
      double[] doubleArray0 = new double[0];
      double[][] doubleArray1 = new double[1][8];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, doubleArray1);
      assertEquals(0, doubleArray0.length);
      assertEquals(1, doubleArray1.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(0, doubleArray0.length);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      
      double double1 = MathUtils.sign(0.0);
      assertEquals(0.0, double1, 0.01);
      assertEquals(double1, double0, 0.01);
      
      long long0 = MathUtils.indicator((long) 1388408381);
      assertEquals(1L, long0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0F, (float) 3149);
      assertFalse(boolean0);
      
      double double2 = MathUtils.indicator(0.11113807559013367);
      assertEquals(1.0, double2, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, (-1117.377988));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test192()  throws Throwable  {
      int[] intArray0 = new int[0];
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(0, intArray0.length);
      assertArrayEquals(new int[] {}, intArray0);
      
      byte byte0 = MathUtils.indicator((byte) (-111));
      assertEquals((byte) (-1), byte0);
      
      // Undeclared exception!
      try { 
        MathUtils.round((-2227.799636), (-1180), 141);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test193()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 1596.3885198192627;
      doubleArray0[2] = 6.0;
      doubleArray0[3] = (-0.0013888888689039883);
      doubleArray0[4] = 1.304E19;
      doubleArray0[5] = 469.8293502;
      double[] doubleArray1 = new double[6];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 1.304E19;
      doubleArray1[1] = 6.0;
      doubleArray1[2] = 469.8293502;
      doubleArray1[3] = 469.8293502;
      doubleArray1[4] = 0.0;
      doubleArray1[5] = 6.0;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertFalse(boolean0);
      assertEquals(6, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 1596.3885198192627, 6.0, (-0.0013888888689039883), 1.304E19, 469.8293502}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.304E19, 6.0, 469.8293502, 469.8293502, 0.0, 6.0}, doubleArray1, 0.01);
  }

  @Test(timeout = 4000)
  public void test194()  throws Throwable  {
      double[] doubleArray0 = new double[0];
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertNotNull(doubleArray1);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      
      double[] doubleArray2 = MathUtils.copyOf(doubleArray0);
      assertNotNull(doubleArray2);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray2.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray2, doubleArray0);
      assertNotSame(doubleArray2, doubleArray1);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      assertFalse(doubleArray2.equals((Object)doubleArray1));
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray2, 0.01);
      
      boolean boolean0 = MathUtils.equals((double) 0, 0.0);
      assertTrue(boolean0);
      
      double double0 = MathUtils.indicator((-1064.9463885644));
      assertEquals((-1.0), double0, 0.01);
      
      long long0 = MathUtils.mulAndCheck(0L, 0L);
      assertEquals(0L, long0);
      
      int int0 = MathUtils.hash((-1064.9463885644));
      assertEquals((-628763031), int0);
      
      int int1 = MathUtils.mulAndCheck(85, 5089);
      assertEquals(432565, int1);
      assertFalse(int1 == int0);
      
      double double1 = MathUtils.safeNorm(doubleArray1);
      assertEquals(0.0, double1, 0.01);
      assertEquals(0, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray0, doubleArray2);
      assertNotSame(doubleArray1, doubleArray0);
      assertNotSame(doubleArray1, doubleArray2);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray0.equals((Object)doubleArray2));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertFalse(doubleArray1.equals((Object)doubleArray2));
      assertNotEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
  }

  @Test(timeout = 4000)
  public void test195()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(259, 142);
      assertEquals(117, int0);
      
      double double0 = MathUtils.sinh(259);
      assertEquals(1.5178918086083622E112, double0, 0.01);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) 142;
      doubleArray0[1] = 1.5178918086083622E112;
      doubleArray0[2] = 1.5178918086083622E112;
      doubleArray0[3] = (double) 117;
      doubleArray0[4] = (double) 259;
      doubleArray0[5] = (double) 117;
      double double1 = MathUtils.safeNorm(doubleArray0);
      assertEquals(2.1466231819489723E112, double1, 0.01);
      assertEquals(6, doubleArray0.length);
      assertNotEquals(double1, double0, 0.01);
      assertArrayEquals(new double[] {142.0, 1.5178918086083622E112, 1.5178918086083622E112, 117.0, 259.0, 117.0}, doubleArray0, 0.01);
      
      int int1 = MathUtils.pow(9, 259);
      assertEquals((-1226986791), int1);
      assertFalse(int1 == int0);
      
      double double2 = MathUtils.cosh(1.5178918086083622E112);
      assertEquals(Double.POSITIVE_INFINITY, double2, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertNotEquals(double2, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test196()  throws Throwable  {
      int int0 = (-1308);
      // Undeclared exception!
      try { 
        MathUtils.pow((-1308), (-1308));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-1,308)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test197()  throws Throwable  {
      double[] doubleArray0 = new double[1];
      double double0 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {0.0}, doubleArray0, 0.01);
      
      double double1 = MathUtils.indicator(308.028799553);
      assertEquals(1.0, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.normalizeArray(doubleArray0, 1.0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // array sums to zero
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test198()  throws Throwable  {
      MathUtils.checkFinite((-6970.3754));
      Integer integer0 = new Integer(3071);
      assertEquals(3071, (int)integer0);
      assertNotNull(integer0);
      
      MathUtils.checkNotNull((Object) integer0);
      boolean boolean0 = MathUtils.equals(10.0, 2319.025879461, 2.0);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test199()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-0.1428571423679182), (-0.1428571423679182), 393);
      assertTrue(boolean0);
      
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      float[] floatArray0 = new float[8];
      floatArray0[0] = 3340.9F;
      floatArray0[1] = (float) 393;
      floatArray0[2] = (-1.0F);
      floatArray0[3] = (float) 393;
      floatArray0[4] = (float) 393;
      floatArray0[5] = (float) 393;
      floatArray0[6] = Float.NaN;
      floatArray0[7] = (float) 393;
      boolean boolean1 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean1);
      assertEquals(8, floatArray0.length);
      assertTrue(boolean1 == boolean0);
      assertArrayEquals(new float[] {3340.9F, 393.0F, (-1.0F), 393.0F, 393.0F, 393.0F, Float.NaN, 393.0F}, floatArray0, 0.01F);
  }

  @Test(timeout = 4000)
  public void test200()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-255.3), 1.5, 782);
      assertFalse(boolean0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-255.3);
      doubleArray0[1] = (double) 782;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(822.6190430569912, double0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {(-255.3), 782.0}, doubleArray0, 0.01);
      
      short short0 = MathUtils.indicator((short) (-1233));
      assertEquals((short) (-1), short0);
      
      boolean boolean1 = MathUtils.equals((float) 782, (-1862.3F), 3666.5571F);
      assertTrue(boolean1);
      assertFalse(boolean1 == boolean0);
  }

  @Test(timeout = 4000)
  public void test201()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN(0.0, (-462.12736));
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test202()  throws Throwable  {
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((short)10, bigInteger0.shortValue());
      assertEquals((byte)10, bigInteger0.byteValue());
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 23, 1.0F);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test203()  throws Throwable  {
      double double0 = MathUtils.normalizeAngle(0.0, (-108.166059732));
      assertEquals((-106.81415022205297), double0, 0.01);
      
      Integer integer0 = new Integer(0);
      assertEquals(0, (int)integer0);
      assertNotNull(integer0);
      
      MathUtils.checkNotNull((Object) integer0);
      int int0 = MathUtils.gcd(0, 4018);
      assertEquals(4018, int0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 0, (float) 4018, (-1369.8F));
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(0.0, (double) 0, 88.0923145);
      assertTrue(boolean1);
      assertFalse(boolean1 == boolean0);
      
      int[] intArray0 = new int[2];
      intArray0[0] = 2959;
      intArray0[1] = 4018;
      int[] intArray1 = MathUtils.copyOf(intArray0);
      assertNotNull(intArray1);
      assertEquals(2, intArray0.length);
      assertEquals(2, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {2959, 4018}, intArray0);
      assertArrayEquals(new int[] {2959, 4018}, intArray1);
  }

  @Test(timeout = 4000)
  public void test204()  throws Throwable  {
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      boolean boolean0 = MathUtils.equals(0.0, 0.0, 0.0);
      assertTrue(boolean0);
      
      long long0 = MathUtils.indicator(466L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test205()  throws Throwable  {
      boolean boolean0 = MathUtils.equalsIncludingNaN((float[]) null, (float[]) null);
      assertTrue(boolean0);
      
      MathUtils.checkFinite(1557.019);
      boolean boolean1 = MathUtils.equals(1557.019, 1557.019, 1751);
      assertTrue(boolean1);
      assertTrue(boolean1 == boolean0);
  }

  @Test(timeout = 4000)
  public void test206()  throws Throwable  {
      double double0 = MathUtils.indicator(0.0);
      assertEquals(1.0, double0, 0.01);
      
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 1.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = 1.0;
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 0);
      assertNotNull(doubleArray1);
      assertEquals(7, doubleArray0.length);
      assertEquals(0, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {}, doubleArray1, 0.01);
      
      int int0 = MathUtils.compareTo(1.0, 9.140260083262505E-9, 1.0);
      assertEquals(0, int0);
  }

  @Test(timeout = 4000)
  public void test207()  throws Throwable  {
      float[] floatArray0 = new float[1];
      floatArray0[0] = (-3662.0225F);
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      assertEquals(1, floatArray0.length);
      assertArrayEquals(new float[] {(-3662.0225F)}, floatArray0, 0.01F);
      
      byte byte0 = MathUtils.sign((byte) (-88));
      assertEquals((byte) (-1), byte0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = (double) (byte) (-88);
      doubleArray0[1] = (double) (-3662.0225F);
      doubleArray0[2] = (double) (byte) (-88);
      doubleArray0[3] = (double) (byte) (-1);
      doubleArray0[4] = (double) (-3662.0225F);
      double[] doubleArray1 = new double[9];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = (double) (-3662.0225F);
      doubleArray1[1] = (double) (-3662.0225F);
      doubleArray1[2] = (double) (byte) (-88);
      doubleArray1[3] = (double) (byte) (-1);
      doubleArray1[4] = (-1425.895928749);
      doubleArray1[5] = (double) (byte) (-1);
      doubleArray1[6] = (double) (byte) (-88);
      doubleArray1[7] = (double) (-3662.0225F);
      doubleArray1[8] = (double) (-3662.0225F);
      boolean boolean1 = MathUtils.equals(doubleArray0, doubleArray1);
      assertFalse(boolean1);
      assertEquals(5, doubleArray0.length);
      assertEquals(9, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(boolean1 == boolean0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {(-88.0), (-3662.0224609375), (-88.0), (-1.0), (-3662.0224609375)}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {(-3662.0224609375), (-3662.0224609375), (-88.0), (-1.0), (-1425.895928749), (-1.0), (-88.0), (-3662.0224609375), (-3662.0224609375)}, doubleArray1, 0.01);
  }

  @Test(timeout = 4000)
  public void test208()  throws Throwable  {
      boolean boolean0 = MathUtils.equals(0.0F, 0.0F);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test209()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((float[]) null, (float[]) null);
      assertTrue(boolean0);
      
      int int0 = MathUtils.subAndCheck((-488), (-488));
      assertEquals(0, int0);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (double) (-488);
      doubleArray0[1] = (double) 0;
      doubleArray0[2] = (double) (-488);
      doubleArray0[3] = (double) 0;
      doubleArray0[4] = (double) 0;
      doubleArray0[5] = (double) (-488);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, true);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly increasing (0 >= -488)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test210()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = 1476.8791602311048;
      doubleArray0[1] = (-20.0);
      doubleArray0[2] = (-1129.0);
      doubleArray0[3] = 0.0;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (-6.663542893624021E-14);
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {1476.8791602311048, (-20.0), (-1129.0), 0.0, 0.0, (-6.663542893624021E-14)}, doubleArray0, 0.01);
      
      MathUtils.checkFinite(doubleArray0);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {1476.8791602311048, (-20.0), (-1129.0), 0.0, 0.0, (-6.663542893624021E-14)}, doubleArray0, 0.01);
      
      BigInteger bigInteger0 = BigInteger.valueOf((-2147483648L));
      assertNotNull(bigInteger0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      String string0 = bigInteger0.toString();
      assertEquals("-2147483648", string0);
      assertNotNull(string0);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      Double double1 = new Double((-1129.0));
      assertEquals((-1129.0), (double)double1, 0.01);
      assertNotNull(double1);
      assertNotEquals((double)double1, (double)double0, 0.01);
      
      boolean boolean0 = bigInteger0.equals(double1);
      assertFalse(boolean0);
      assertNotEquals((double)double1, (double)double0, 0.01);
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = BigInteger.TEN;
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = bigInteger0.gcd(bigInteger1);
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger2, bigInteger1);
      assertNotSame(bigInteger2, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertFalse(bigInteger2.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((byte)10, bigInteger1.byteValue());
      assertEquals((short)10, bigInteger1.shortValue());
      assertEquals((short)2, bigInteger2.shortValue());
      assertEquals((byte)2, bigInteger2.byteValue());
      
      BigInteger bigInteger3 = MathUtils.pow(bigInteger0, 2684);
      assertNotNull(bigInteger3);
      assertNotSame(bigInteger0, bigInteger3);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger3, bigInteger2);
      assertNotSame(bigInteger3, bigInteger1);
      assertNotSame(bigInteger3, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger0.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger2));
      assertFalse(bigInteger3.equals((Object)bigInteger1));
      assertFalse(bigInteger3.equals((Object)bigInteger0));
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger3.shortValue());
      assertEquals((byte)0, bigInteger3.byteValue());
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) (-2147483648L), (float) 2684, 2684);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
      
      int int0 = MathUtils.lcm((-118), 0);
      assertEquals(0, int0);
      
      long long0 = MathUtils.lcm((long) 0, (long) 0);
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test211()  throws Throwable  {
      byte byte0 = MathUtils.sign((byte) (-119));
      assertEquals((byte) (-1), byte0);
  }

  @Test(timeout = 4000)
  public void test212()  throws Throwable  {
      double double0 = MathUtils.log(0.0, 0.0);
      assertEquals(Double.NaN, double0, 0.01);
      
      long long0 = MathUtils.subAndCheck(4398046511103L, 2602L);
      assertEquals(4398046508501L, long0);
      
      double[] doubleArray0 = new double[5];
      doubleArray0[0] = Double.NaN;
      doubleArray0[1] = (double) 4398046511103L;
      doubleArray0[2] = (double) 4398046511103L;
      doubleArray0[3] = (double) 2602L;
      doubleArray0[4] = (double) 4398046508501L;
      // Undeclared exception!
      try { 
        MathUtils.checkOrder(doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // points 1 and 2 are not strictly increasing (4,398,046,511,103 >= 4,398,046,511,103)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test213()  throws Throwable  {
      int int0 = MathUtils.gcd((-1912), 551);
      assertEquals(1, int0);
      
      float float0 = MathUtils.round((float) 551, 1);
      assertEquals(551.0F, float0, 0.01F);
      
      long long0 = MathUtils.mulAndCheck((long) 551, 0L);
      assertEquals(0L, long0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (-1060.88);
      doubleArray0[1] = (double) 0L;
      MathUtils.checkOrder(doubleArray0);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {(-1060.88), 0.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test214()  throws Throwable  {
      int int0 = MathUtils.subAndCheck(12, 12);
      assertEquals(0, int0);
      
      MathUtils.checkFinite((-601.4306876));
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 12;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      boolean boolean0 = MathUtils.checkOrder(doubleArray0, mathUtils_OrderDirection0, true, false);
      assertTrue(boolean0);
      assertEquals(1, doubleArray0.length);
      assertArrayEquals(new double[] {12.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test215()  throws Throwable  {
      long long0 = MathUtils.pow(9218868437227405312L, 9218868437227405312L);
      assertEquals(0L, long0);
      
      int int0 = (-114);
      int int1 = MathUtils.pow((-114), 0L);
      assertEquals(1, int1);
      assertFalse(int1 == int0);
      
      // Undeclared exception!
      try { 
        MathUtils.checkOrder((double[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test216()  throws Throwable  {
      boolean boolean0 = MathUtils.equals((-1.0F), (-1.0F));
      assertTrue(boolean0);
      
      float float0 = MathUtils.indicator((-654.6607F));
      assertEquals((-1.0F), float0, 0.01F);
      
      double double0 = MathUtils.sinh((-1.0F));
      assertEquals((-1.1752011936438014), double0, 0.01);
      
      double double1 = MathUtils.cosh(0.0);
      assertEquals(1.0, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = 1.0;
      double double2 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double2, 0.01);
      assertEquals(1, doubleArray0.length);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertArrayEquals(new double[] {1.0}, doubleArray0, 0.01);
      
      MathUtils.checkFinite(591.352);
      BigInteger bigInteger0 = BigInteger.TEN;
      assertNotNull(bigInteger0);
      assertEquals((byte)10, bigInteger0.byteValue());
      assertEquals((short)10, bigInteger0.shortValue());
      
      // Undeclared exception!
      try { 
        MathUtils.pow(bigInteger0, (-120));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-120)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test217()  throws Throwable  {
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1862.5741323590214;
      doubleArray0[1] = (-538.0);
      double double0 = MathUtils.distance1(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(2, doubleArray0.length);
      assertArrayEquals(new double[] {1862.5741323590214, (-538.0)}, doubleArray0, 0.01);
      
      int int0 = MathUtils.compareTo(0.0, (-538.0), 1862.5741323590214);
      assertEquals(0, int0);
      
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 11);
      assertNotNull(doubleArray1);
      assertEquals(2, doubleArray0.length);
      assertEquals(11, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {1862.5741323590214, (-538.0)}, doubleArray0, 0.01);
      
      float float0 = MathUtils.round(1.0F, 11);
      assertEquals(1.0F, float0, 0.01F);
      
      int int1 = MathUtils.lcm(0, 11);
      assertEquals(0, int1);
      assertTrue(int1 == int0);
      
      MathUtils.checkFinite((double) 1.0F);
      short short0 = MathUtils.sign((short) (-910));
      assertEquals((short) (-1), short0);
      
      int int2 = MathUtils.hash((double) 0);
      assertEquals(0, int2);
      assertTrue(int2 == int1);
      assertTrue(int2 == int0);
      
      long long0 = MathUtils.pow((long) 0, 0L);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test218()  throws Throwable  {
      long long0 = MathUtils.mulAndCheck(0L, 2047L);
      assertEquals(0L, long0);
      
      short short0 = MathUtils.sign((short) (-5212));
      assertEquals((short) (-1), short0);
      
      double double0 = MathUtils.indicator(40.19140625);
      assertEquals(1.0, double0, 0.01);
  }

  @Test(timeout = 4000)
  public void test219()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 3.145894820876798E-6;
      doubleArray0[1] = 1.304E19;
      doubleArray0[2] = 2181.005;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals((-1100514055), int0);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {3.145894820876798E-6, 1.304E19, 2181.005}, doubleArray0, 0.01);
      
      double[] doubleArray1 = new double[6];
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      
      doubleArray1[0] = 1.304E19;
      doubleArray1[1] = 3.145894820876798E-6;
      doubleArray1[2] = 3.145894820876798E-6;
      doubleArray1[3] = (double) (-1100514055);
      doubleArray1[4] = (double) (-1100514055);
      doubleArray1[5] = 3.145894820876798E-6;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray1);
      assertFalse(boolean0);
      assertEquals(3, doubleArray0.length);
      assertEquals(6, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray0.equals((Object)doubleArray1));
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {3.145894820876798E-6, 1.304E19, 2181.005}, doubleArray0, 0.01);
      assertArrayEquals(new double[] {1.304E19, 3.145894820876798E-6, 3.145894820876798E-6, (-1.100514055E9), (-1.100514055E9), 3.145894820876798E-6}, doubleArray1, 0.01);
  }

  @Test(timeout = 4000)
  public void test220()  throws Throwable  {
      int[] intArray0 = new int[8];
      intArray0[0] = 501;
      intArray0[1] = 3839;
      intArray0[2] = 1906;
      intArray0[3] = 102;
      intArray0[4] = 0;
      intArray0[5] = (-336);
      intArray0[6] = (-2788);
      intArray0[7] = 14;
      int[] intArray1 = MathUtils.copyOf(intArray0, 14);
      assertNotNull(intArray1);
      assertEquals(8, intArray0.length);
      assertEquals(14, intArray1.length);
      assertNotSame(intArray0, intArray1);
      assertNotSame(intArray1, intArray0);
      assertFalse(intArray1.equals((Object)intArray0));
      assertArrayEquals(new int[] {501, 3839, 1906, 102, 0, (-336), (-2788), 14}, intArray0);
  }

  @Test(timeout = 4000)
  public void test221()  throws Throwable  {
      float float0 = 1261.8824F;
      boolean boolean0 = MathUtils.equalsIncludingNaN(1261.8824F, 1261.8824F);
      assertTrue(boolean0);
      
      // Undeclared exception!
      try { 
        MathUtils.copyOf((int[]) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test222()  throws Throwable  {
      long long0 = MathUtils.lcm((-1073741824L), (-1073741824L));
      assertEquals(1073741824L, long0);
      
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) 1073741824L;
      doubleArray0[1] = (double) 1073741824L;
      doubleArray0[2] = (double) 1073741824L;
      doubleArray0[3] = (double) 1073741824L;
      doubleArray0[4] = (double) 1073741824L;
      doubleArray0[5] = (double) (-1073741824L);
      doubleArray0[6] = (double) 1073741824L;
      doubleArray0[7] = (double) (-1073741824L);
      doubleArray0[8] = (double) 1073741824L;
      double double0 = MathUtils.distanceInf(doubleArray0, doubleArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(9, doubleArray0.length);
      assertArrayEquals(new double[] {1.073741824E9, 1.073741824E9, 1.073741824E9, 1.073741824E9, 1.073741824E9, (-1.073741824E9), 1.073741824E9, (-1.073741824E9), 1.073741824E9}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test223()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = 265.05141646;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 416.9;
      boolean boolean0 = MathUtils.equals(doubleArray0, doubleArray0);
      assertTrue(boolean0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {265.05141646, 0.0, 0.0, 416.9}, doubleArray0, 0.01);
      
      int int0 = MathUtils.indicator(10);
      assertEquals(1, int0);
      
      float float0 = MathUtils.round(0.0F, 605);
      assertEquals(Float.NaN, float0, 0.01F);
      
      long long0 = MathUtils.pow(24L, 67);
      assertEquals(0L, long0);
      
      byte byte0 = MathUtils.sign((byte)9);
      assertEquals((byte)1, byte0);
  }

  @Test(timeout = 4000)
  public void test224()  throws Throwable  {
      int int0 = MathUtils.compareTo(384.6952725757, (-1376.72575359733), 2.2250738585072014E-308);
      assertEquals(1, int0);
  }

  @Test(timeout = 4000)
  public void test225()  throws Throwable  {
      MathUtils.checkFinite(0.0);
      long long0 = MathUtils.subAndCheck((-379L), 2147483648L);
      assertEquals((-2147484027L), long0);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 591.335013757798;
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (double) 2147483648L;
      int int0 = MathUtils.hash(doubleArray0);
      assertEquals((-1668022960), int0);
      assertEquals(3, doubleArray0.length);
      assertArrayEquals(new double[] {591.335013757798, 0.0, 2.147483648E9}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test226()  throws Throwable  {
      double double0 = MathUtils.log((-3.141592653589793), (-3.141592653589793));
      assertEquals(Double.NaN, double0, 0.01);
      
      long long0 = MathUtils.subAndCheck(731L, 1828L);
      assertEquals((-1097L), long0);
      
      boolean boolean0 = MathUtils.equals((double) 731L, 3217.267912942);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test227()  throws Throwable  {
      int int0 = MathUtils.hash(1341.387077757189);
      assertEquals(512255239, int0);
      
      long long0 = MathUtils.addAndCheck(1423L, 1423L);
      assertEquals(2846L, long0);
      
      byte byte0 = MathUtils.indicator((byte)0);
      assertEquals((byte)1, byte0);
      
      BigInteger bigInteger0 = BigInteger.ONE;
      assertNotNull(bigInteger0);
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, bigInteger0);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertTrue(bigInteger1.equals((Object)bigInteger0));
      assertEquals((short)1, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger0.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
  }

  @Test(timeout = 4000)
  public void test228()  throws Throwable  {
      int int0 = MathUtils.hash(3.834E-20);
      assertEquals((-750837265), int0);
      
      int[] intArray0 = new int[1];
      intArray0[0] = (-750837265);
      int int1 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int1);
      assertEquals(1, intArray0.length);
      assertFalse(int1 == int0);
      assertArrayEquals(new int[] {(-750837265)}, intArray0);
      
      boolean boolean0 = MathUtils.equals(1925.772F, 146.51F, 0.5F);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((double) 0.5F, (double) 0.5F, (double) 0.5F);
      assertTrue(boolean1);
      assertFalse(boolean1 == boolean0);
  }

  @Test(timeout = 4000)
  public void test229()  throws Throwable  {
      long long0 = MathUtils.gcd(843L, 843L);
      assertEquals(843L, long0);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 843L;
      doubleArray0[1] = (double) 843L;
      doubleArray0[2] = (double) 843L;
      doubleArray0[3] = (double) 843L;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[9][7];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      double[] doubleArray2 = new double[9];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = (double) 843L;
      doubleArray2[1] = (double) 843L;
      doubleArray2[2] = (double) 843L;
      doubleArray2[3] = 416.28;
      doubleArray2[4] = (double) 843L;
      doubleArray2[5] = (double) 843L;
      doubleArray2[6] = 1203.0;
      doubleArray2[7] = (double) 843L;
      doubleArray2[8] = (double) 843L;
      doubleArray1[5] = doubleArray2;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      doubleArray1[8] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 9 != 4
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test230()  throws Throwable  {
      // Undeclared exception!
      try { 
        MathUtils.round(2985.498872102479, (-2485), (-2485));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Invalid rounding mode
         //
         verifyException("java.math.BigDecimal", e);
      }
  }

  @Test(timeout = 4000)
  public void test231()  throws Throwable  {
      int int0 = MathUtils.gcd(150, 150);
      assertEquals(150, int0);
      
      long long0 = MathUtils.pow((long) 150, (long) 150);
      assertEquals(0L, long0);
      
      long long1 = MathUtils.binomialCoefficient(150, 150);
      assertEquals(1L, long1);
      assertFalse(long1 == long0);
      
      Double double0 = new Double(3.141592653589793);
      assertEquals(3.141592653589793, (double)double0, 0.01);
      assertNotNull(double0);
      
      MathUtils.checkNotNull((Object) double0);
      int int1 = MathUtils.lcm(3183, 48);
      assertEquals(50928, int1);
      assertFalse(int1 == int0);
      
      double double1 = MathUtils.binomialCoefficientDouble(50928, 1814);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test232()  throws Throwable  {
      int int0 = MathUtils.sign(17);
      assertEquals(1, int0);
      
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficientDouble(17, 31);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // must have n >= k for binomial coefficient (n, k), got k = 31, n = 17
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test233()  throws Throwable  {
      Object object0 = new Object();
      assertNotNull(object0);
      
      MathUtils.checkNotNull(object0);
      boolean boolean0 = MathUtils.equals(913.0222F, 913.0222F, (-1.0F));
      assertTrue(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN(0.0, 0.0);
      assertTrue(boolean1);
      assertTrue(boolean1 == boolean0);
      
      long long0 = MathUtils.binomialCoefficient(0, 0);
      assertEquals(1L, long0);
  }

  @Test(timeout = 4000)
  public void test234()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = 2.0;
      doubleArray0[1] = 0.7249995199969751;
      doubleArray0[2] = 1.0;
      double[][] doubleArray1 = new double[5][3];
      doubleArray1[0] = doubleArray0;
      double[] doubleArray2 = new double[2];
      assertFalse(doubleArray2.equals((Object)doubleArray0));
      
      doubleArray2[0] = 1.0;
      doubleArray2[1] = 1.0;
      doubleArray1[1] = doubleArray2;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      doubleArray1[4] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 2 != 3
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test235()  throws Throwable  {
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = 0.0;
      doubleArray0[1] = 5755.38322;
      doubleArray0[2] = (-1025.928857883);
      doubleArray0[3] = (-839.4799886166328);
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.INCREASING;
      double[][] doubleArray1 = new double[4][5];
      doubleArray1[0] = doubleArray0;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      assertEquals(4, doubleArray0.length);
      assertEquals(4, doubleArray1.length);
      assertArrayEquals(new double[] {(-1025.928857883), (-839.4799886166328), 0.0, 5755.38322}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test236()  throws Throwable  {
      long long0 = MathUtils.gcd(2709L, 2709L);
      assertEquals(2709L, long0);
  }

  @Test(timeout = 4000)
  public void test237()  throws Throwable  {
      double double0 = MathUtils.factorialDouble(0);
      assertEquals(1.0, double0, 0.01);
      
      BigInteger bigInteger0 = BigInteger.ZERO;
      assertNotNull(bigInteger0);
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      
      BigInteger bigInteger1 = MathUtils.pow(bigInteger0, 0L);
      assertNotNull(bigInteger1);
      assertNotSame(bigInteger0, bigInteger1);
      assertNotSame(bigInteger1, bigInteger0);
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      
      BigInteger bigInteger2 = MathUtils.pow(bigInteger0, bigInteger1);
      assertNotNull(bigInteger2);
      assertNotSame(bigInteger0, bigInteger1);
      assertSame(bigInteger0, bigInteger2);
      assertNotSame(bigInteger1, bigInteger2);
      assertNotSame(bigInteger1, bigInteger0);
      assertNotSame(bigInteger2, bigInteger1);
      assertSame(bigInteger2, bigInteger0);
      assertFalse(bigInteger0.equals((Object)bigInteger1));
      assertFalse(bigInteger1.equals((Object)bigInteger0));
      assertFalse(bigInteger2.equals((Object)bigInteger1));
      assertEquals((byte)0, bigInteger0.byteValue());
      assertEquals((short)0, bigInteger0.shortValue());
      assertEquals((byte)1, bigInteger1.byteValue());
      assertEquals((short)1, bigInteger1.shortValue());
      assertEquals((short)0, bigInteger2.shortValue());
      assertEquals((byte)0, bigInteger2.byteValue());
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) 0L;
      doubleArray0[2] = (double) 0L;
      doubleArray0[3] = 1.0;
      MathUtils.checkFinite(doubleArray0);
      assertEquals(4, doubleArray0.length);
      assertArrayEquals(new double[] {0.0, 0.0, 0.0, 1.0}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test238()  throws Throwable  {
      int[] intArray0 = new int[4];
      intArray0[0] = Integer.MIN_VALUE;
      intArray0[1] = 67;
      intArray0[2] = 0;
      intArray0[3] = 587;
      int int0 = MathUtils.distance1(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(4, intArray0.length);
      assertArrayEquals(new int[] {Integer.MIN_VALUE, 67, 0, 587}, intArray0);
  }

  @Test(timeout = 4000)
  public void test239()  throws Throwable  {
      float float0 = MathUtils.round((-2831.0F), (-1));
      assertEquals((-2830.0F), float0, 0.01F);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double) (-2831.0F), 5.0, 2531);
      assertFalse(boolean0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((-2371.145F), (float) 2531, 1);
      assertFalse(boolean1);
      assertTrue(boolean1 == boolean0);
  }

  @Test(timeout = 4000)
  public void test240()  throws Throwable  {
      double double0 = MathUtils.round(2505.3464998815825, 0, 0);
      assertEquals(2506.0, double0, 0.01);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((double[]) null, (double[]) null);
      assertTrue(boolean0);
      
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray0 = new double[9][0];
      doubleArray0[0] = null;
      doubleArray0[1] = null;
      doubleArray0[2] = null;
      doubleArray0[3] = null;
      doubleArray0[4] = null;
      doubleArray0[5] = null;
      doubleArray0[6] = null;
      doubleArray0[7] = null;
      doubleArray0[8] = null;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace((double[]) null, mathUtils_OrderDirection0, doubleArray0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test241()  throws Throwable  {
      int int0 = 1985142784;
      // Undeclared exception!
      MathUtils.factorialLog(1985142784);
  }

  @Test(timeout = 4000)
  public void test242()  throws Throwable  {
      double double0 = MathUtils.sign(2457.3);
      assertEquals(1.0, double0, 0.01);
      
      double double1 = MathUtils.binomialCoefficientLog(2070, 1762);
      assertEquals(866.9557258703777, double1, 0.01);
      assertNotEquals(double1, double0, 0.01);
      
      short short0 = MathUtils.sign((short)943);
      assertEquals((short)1, short0);
      
      int[] intArray0 = new int[3];
      intArray0[0] = (int) (short)943;
      intArray0[1] = 1762;
      intArray0[2] = 1762;
      int int0 = MathUtils.distanceInf(intArray0, intArray0);
      assertEquals(0, int0);
      assertEquals(3, intArray0.length);
      assertArrayEquals(new int[] {943, 1762, 1762}, intArray0);
      
      boolean boolean0 = MathUtils.equalsIncludingNaN((float) 1762, 1.0F, 127);
      assertFalse(boolean0);
      
      Random.setNextRandom((short)943);
      int int1 = MathUtils.pow(1762, 1984);
      assertEquals(0, int1);
      assertTrue(int1 == int0);
      
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = (double) 127;
      doubleArray0[1] = (double) 0;
      double double2 = MathUtils.distance(doubleArray0, doubleArray0);
      assertEquals(0.0, double2, 0.01);
      assertEquals(2, doubleArray0.length);
      assertNotEquals(double2, double0, 0.01);
      assertNotEquals(double2, double1, 0.01);
      assertArrayEquals(new double[] {127.0, 0.0}, doubleArray0, 0.01);
      
      // Undeclared exception!
      try { 
        MathUtils.pow((int) (short)1, (-4262701917L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // exponent (-4,262,701,917)
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test243()  throws Throwable  {
      MathUtils.OrderDirection[] mathUtils_OrderDirectionArray0 = MathUtils.OrderDirection.values();
      assertNotNull(mathUtils_OrderDirectionArray0);
      assertEquals(2, mathUtils_OrderDirectionArray0.length);
      
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (-2868.0);
      doubleArray0[1] = Double.POSITIVE_INFINITY;
      doubleArray0[2] = Double.NEGATIVE_INFINITY;
      doubleArray0[3] = 1173.0413419308522;
      doubleArray0[4] = 1066.100182;
      doubleArray0[5] = 2309.025879461;
      double double0 = MathUtils.safeNorm(doubleArray0);
      assertEquals(Double.NaN, double0, 0.01);
      assertEquals(6, doubleArray0.length);
      assertArrayEquals(new double[] {(-2868.0), Double.POSITIVE_INFINITY, Double.NEGATIVE_INFINITY, 1173.0413419308522, 1066.100182, 2309.025879461}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test244()  throws Throwable  {
      int[] intArray0 = new int[6];
      intArray0[0] = 2139095040;
      intArray0[1] = (-385);
      intArray0[2] = 13;
      intArray0[3] = 159;
      intArray0[4] = (-2112846813);
      intArray0[5] = 16;
      double double0 = MathUtils.distance(intArray0, intArray0);
      assertEquals(0.0, double0, 0.01);
      assertEquals(6, intArray0.length);
      assertArrayEquals(new int[] {2139095040, (-385), 13, 159, (-2112846813), 16}, intArray0);
      
      float float0 = MathUtils.sign((float) 2139095040);
      assertEquals(1.0F, float0, 0.01F);
      
      short short0 = MathUtils.sign((short) (-1703));
      assertEquals((short) (-1), short0);
      
      long long0 = MathUtils.sign((long) 13);
      assertEquals(1L, long0);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (-2526.0);
      doubleArray0[1] = (double) 13;
      doubleArray0[2] = (double) (short) (-1);
      double[] doubleArray1 = MathUtils.copyOf(doubleArray0, 13);
      assertNotNull(doubleArray1);
      assertEquals(3, doubleArray0.length);
      assertEquals(13, doubleArray1.length);
      assertNotSame(doubleArray0, doubleArray1);
      assertNotSame(doubleArray1, doubleArray0);
      assertFalse(doubleArray1.equals((Object)doubleArray0));
      assertArrayEquals(new double[] {(-2526.0), 13.0, (-1.0)}, doubleArray0, 0.01);
      
      float[] floatArray0 = new float[6];
      floatArray0[0] = (float) 2139095040;
      floatArray0[1] = (float) 159;
      floatArray0[2] = (float) 13;
      floatArray0[3] = (float) 16;
      floatArray0[4] = (-3988.59F);
      floatArray0[5] = (float) 159;
      float[] floatArray1 = new float[7];
      assertFalse(floatArray1.equals((Object)floatArray0));
      
      floatArray1[0] = 439.8656F;
      floatArray1[1] = (float) 1L;
      floatArray1[2] = (float) 2139095040;
      floatArray1[3] = (float) (-2112846813);
      floatArray1[4] = (float) (-385);
      floatArray1[5] = (float) 13;
      floatArray1[6] = (float) (-2112846813);
      boolean boolean0 = MathUtils.equals(floatArray0, floatArray1);
      assertFalse(boolean0);
      assertEquals(6, floatArray0.length);
      assertEquals(7, floatArray1.length);
      assertNotSame(floatArray0, floatArray1);
      assertNotSame(floatArray1, floatArray0);
      assertFalse(floatArray0.equals((Object)floatArray1));
      assertFalse(floatArray1.equals((Object)floatArray0));
      assertArrayEquals(new float[] {2.13909504E9F, 159.0F, 13.0F, 16.0F, (-3988.59F), 159.0F}, floatArray0, 0.01F);
      assertArrayEquals(new float[] {439.8656F, 1.0F, 2.13909504E9F, (-2.11284685E9F), (-385.0F), 13.0F, (-2.11284685E9F)}, floatArray1, 0.01F);
  }

  @Test(timeout = 4000)
  public void test245()  throws Throwable  {
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (-1042.73);
      doubleArray0[1] = 1.304E19;
      doubleArray0[2] = 1872.810240396;
      MathUtils.copyOf(doubleArray0);
      MathUtils.subAndCheck(1785L, 1785L);
      MathUtils.compareTo(0.097, 355.6988644769, 639.6364);
      MathUtils.gcd((-312), (-754));
      int[] intArray0 = new int[4];
      intArray0[0] = 0;
      intArray0[1] = (-312);
      intArray0[2] = 26;
      intArray0[3] = (-312);
      MathUtils.distance(intArray0, intArray0);
      Random.setNextRandom(14);
      MathUtils.binomialCoefficientLog(26, 14);
      int[] intArray1 = new int[5];
      intArray1[0] = 26;
      intArray1[1] = (-2130165230);
      intArray1[2] = (-312);
      intArray1[3] = 14;
      intArray1[4] = (-312);
      // Undeclared exception!
      try { 
        MathUtils.distance1(intArray1, intArray0);
        fail("Expecting exception: ArrayIndexOutOfBoundsException");
      
      } catch(ArrayIndexOutOfBoundsException e) {
         //
         // 4
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test246()  throws Throwable  {
      MathUtils.sign(1066.100182);
      double[] doubleArray0 = new double[2];
      doubleArray0[0] = 1.0;
      doubleArray0[1] = 1066.100182;
      double[] doubleArray1 = new double[7];
      doubleArray1[0] = 1.0;
      doubleArray1[1] = 1066.100182;
      doubleArray1[2] = 1.0;
      doubleArray1[3] = 1066.100182;
      doubleArray1[4] = (-4363.0);
      doubleArray1[5] = 0.0;
      doubleArray1[6] = 1.0;
      double double0 = MathUtils.distance1(doubleArray0, doubleArray1);
      assertEquals(0.0, double0, 0.01);
      
      double[] doubleArray2 = MathUtils.copyOf(doubleArray0, 376);
      assertEquals(376, doubleArray2.length);
      
      boolean boolean0 = MathUtils.equals(2330.4312F, (-1.49642522E9F), (float) 376);
      assertFalse(boolean0);
      
      double[] doubleArray3 = MathUtils.normalizeArray(doubleArray1, 5.4514417621779825E18);
      assertArrayEquals(new double[] {(-2.447007205713531E15), (-2.608754827366507E18), (-2.447007205713531E15), (-2.608754827366507E18), 1.0676292438528137E19, -0.0, (-2.447007205713531E15)}, doubleArray3, 0.01);
      
      long long0 = MathUtils.gcd(1684L, 1684L);
      assertEquals(1684L, long0);
  }

  @Test(timeout = 4000)
  public void test247()  throws Throwable  {
      MathUtils.factorialLog(0);
      MathUtils.subAndCheck(0L, 0L);
      MathUtils.round((double) 0L, 2524);
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 0L;
      doubleArray0[1] = (double) 2524;
      doubleArray0[2] = 0.0;
      doubleArray0[3] = 0.0;
      MathUtils.equals(doubleArray0, doubleArray0);
      MathUtils.checkFinite(doubleArray0);
      MathUtils.distance(doubleArray0, doubleArray0);
      MathUtils.round((float) 0L, 315);
      Object object0 = new Object();
      MathUtils.checkNotNull(object0);
      MathUtils.gcd(0, 2911);
      MathUtils.sign(2911);
      MathUtils.lcm(1676, 2911);
      MathUtils.gcd(6L, (long) 1);
      MathUtils.pow(2911, 588L);
      MathUtils.distance(doubleArray0, doubleArray0);
      // Undeclared exception!
      try { 
        MathUtils.binomialCoefficient(2911, 315);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // arithmetic exception
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test248()  throws Throwable  {
      double[] doubleArray0 = new double[6];
      doubleArray0[0] = (-4009.750308);
      doubleArray0[1] = Double.NaN;
      doubleArray0[3] = 1190.2;
      doubleArray0[4] = 0.0;
      doubleArray0[5] = (-1969.9);
      MathUtils.distanceInf(doubleArray0, doubleArray0);
      MathUtils.safeNorm(doubleArray0);
      MathUtils.indicator((-4009.750308));
      MathUtils.gcd((-2672L), 9154082963658192752L);
      MathUtils.safeNorm(doubleArray0);
      // Undeclared exception!
      try { 
        MathUtils.equals(Float.NEGATIVE_INFINITY, 0.0F, 0);
        fail("Expecting exception: AssertionError");
      
      } catch(AssertionError e) {
         //
         // no message in exception (getMessage() returned null)
         //
      }
  }

  @Test(timeout = 4000)
  public void test249()  throws Throwable  {
      long long0 = MathUtils.lcm((-5148L), (-5148L));
      assertEquals(5148L, long0);
      
      double double0 = MathUtils.normalizeAngle(25.092084683470652, 25.092084683470652);
      assertEquals(25.092084683470652, double0, 0.01);
      
      MathUtils.equalsIncludingNaN((float) 5148L, (float) 5148L);
      int int0 = MathUtils.lcm((-2259), 3);
      assertEquals(2259, int0);
      
      double double1 = MathUtils.binomialCoefficientDouble(2606, 697);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
      
      double[] doubleArray0 = new double[4];
      doubleArray0[0] = (double) 697;
      doubleArray0[1] = (double) (-2259);
      doubleArray0[2] = 25.092084683470652;
      doubleArray0[3] = (double) 2259;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      double[][] doubleArray1 = new double[1][8];
      doubleArray1[0] = doubleArray0;
      MathUtils.sortInPlace(doubleArray0, mathUtils_OrderDirection0, doubleArray1);
      Integer integer0 = new Integer(2606);
      float[] floatArray0 = new float[6];
      floatArray0[0] = (float) (int)integer0;
      floatArray0[1] = 3.4028235E38F;
      floatArray0[2] = (float) (int)integer0;
      floatArray0[3] = (float) 5148L;
      floatArray0[4] = (float) 3;
      floatArray0[5] = (float) 5148L;
      boolean boolean0 = MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      assertTrue(boolean0);
      
      MathUtils.checkFinite(doubleArray0);
      assertArrayEquals(new double[] {2259.0, 697.0, 25.092084683470652, (-2259.0)}, doubleArray0, 0.01);
  }

  @Test(timeout = 4000)
  public void test250()  throws Throwable  {
      long long0 = MathUtils.factorial(3);
      assertEquals(6L, long0);
      
      boolean boolean0 = MathUtils.equals((float) 6L, 2585.6F, 2618);
      int int0 = MathUtils.addAndCheck((-3094), 0);
      assertEquals((-3094), int0);
      
      boolean boolean1 = MathUtils.equalsIncludingNaN((float) (-3094), Float.NEGATIVE_INFINITY, (-1890.5284F));
      assertFalse(boolean1);
      
      double[] doubleArray0 = new double[3];
      doubleArray0[0] = (double) 0;
      doubleArray0[1] = (double) (-1890.5284F);
      doubleArray0[2] = (double) 0;
      MathUtils.hash(doubleArray0);
      double double0 = MathUtils.sign((double) 0);
      assertEquals(0.0, double0, 0.01);
      
      boolean boolean2 = MathUtils.equals((float) (-3094), 943.0F);
      assertTrue(boolean2 == boolean0);
      
      MathUtils.copyOf(doubleArray0, 0);
      int int1 = MathUtils.subAndCheck((-277), (-277));
      assertEquals(0, int1);
      
      double double1 = MathUtils.factorialDouble(189);
      assertEquals(Double.POSITIVE_INFINITY, double1, 0.01);
  }

  @Test(timeout = 4000)
  public void test251()  throws Throwable  {
      int int0 = MathUtils.compareTo((-1.7976931348623157E308), (-7.060773487315205), (-7.060773487315205));
      assertEquals((-1), int0);
      
      long long0 = MathUtils.mulAndCheck(3998L, (long) (-1));
      assertEquals((-3998L), long0);
  }

  @Test(timeout = 4000)
  public void test252()  throws Throwable  {
      MathUtils.binomialCoefficientLog(0, (-1382));
      double[] doubleArray0 = new double[9];
      doubleArray0[0] = (double) (-1382);
      doubleArray0[1] = 0.0;
      doubleArray0[2] = (double) (-1382);
      doubleArray0[3] = (-2940.17);
      doubleArray0[4] = 0.0;
      doubleArray0[5] = 0.0;
      doubleArray0[6] = (double) 0;
      doubleArray0[7] = 0.0;
      doubleArray0[8] = 0.0;
      MathUtils.equalsIncludingNaN(doubleArray0, doubleArray0);
      LocalizedFormats localizedFormats0 = LocalizedFormats.NEGATIVE_COMPLEX_MODULE;
      // Undeclared exception!
      try { 
        LocalizedFormats.valueOf("ROUND_HALF_EVEN");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // No enum constant org.apache.commons.math.exception.util.LocalizedFormats.ROUND_HALF_EVEN
         //
         verifyException("java.lang.Enum", e);
      }
  }

  @Test(timeout = 4000)
  public void test253()  throws Throwable  {
      MathUtils.compareTo(865.78172, 865.78172, 865.78172);
      MathUtils.log((-1603.44527), 4884.142816669608);
      MathUtils.equalsIncludingNaN((-1603.44527), 4884.142816669608, 0.0);
      double[] doubleArray0 = new double[7];
      doubleArray0[0] = 865.78172;
      doubleArray0[1] = 865.78172;
      doubleArray0[2] = 865.78172;
      doubleArray0[3] = 4884.142816669608;
      doubleArray0[4] = 4884.142816669608;
      doubleArray0[5] = (double) 0;
      doubleArray0[6] = Double.NaN;
      double[][] doubleArray1 = new double[8][4];
      double[] doubleArray2 = new double[3];
      doubleArray2[0] = (double) 0;
      doubleArray2[1] = (-1603.44527);
      doubleArray2[2] = Double.NaN;
      doubleArray1[0] = doubleArray2;
      doubleArray1[1] = doubleArray0;
      doubleArray1[2] = doubleArray0;
      doubleArray1[3] = doubleArray0;
      double[] doubleArray3 = new double[7];
      doubleArray3[0] = (double) 0;
      doubleArray3[1] = (-1603.44527);
      doubleArray3[2] = Double.NaN;
      doubleArray3[3] = (-1603.44527);
      doubleArray3[4] = Double.NaN;
      doubleArray3[5] = 4884.142816669608;
      doubleArray3[6] = (-956.0);
      doubleArray1[4] = doubleArray3;
      doubleArray1[5] = doubleArray0;
      doubleArray1[6] = doubleArray0;
      doubleArray1[7] = doubleArray0;
      // Undeclared exception!
      try { 
        MathUtils.sortInPlace(doubleArray0, doubleArray1);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // 3 != 7
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test254()  throws Throwable  {
      MathUtils.pow(4149L, 4149L);
      MathUtils.round((float) 3465265825139658085L, (-521));
      MathUtils.gcd(4294967295L, 39916800L);
      float[] floatArray0 = new float[3];
      floatArray0[0] = (float) 4294967295L;
      floatArray0[1] = Float.NaN;
      floatArray0[2] = (float) 4149L;
      MathUtils.equalsIncludingNaN(floatArray0, floatArray0);
      MathUtils.pow(4149L, 2509);
      MathUtils.binomialCoefficientDouble(2509, 193);
      int[] intArray0 = new int[9];
      intArray0[0] = 66;
      intArray0[1] = 2509;
      intArray0[2] = 2509;
      intArray0[4] = 2509;
      intArray0[5] = 193;
      intArray0[6] = 2509;
      intArray0[7] = (-521);
      intArray0[8] = 193;
      MathUtils.copyOf(intArray0);
      double[] doubleArray0 = new double[1];
      doubleArray0[0] = (double) 39916800L;
      MathUtils.checkFinite(doubleArray0);
      MathUtils.normalizeArray(doubleArray0, 15L);
      MathUtils.equals(5840.1875F, Float.NaN, 3.46526593E18F);
      double[] doubleArray1 = new double[7];
      doubleArray1[0] = (double) 2509;
      doubleArray1[1] = 9.449935296536114E293;
      doubleArray1[2] = (double) 193;
      doubleArray1[3] = (double) 193;
      doubleArray1[4] = (double) 4149L;
      doubleArray1[5] = (double) 4149.0F;
      doubleArray1[6] = (double) 2509;
      MathUtils.OrderDirection mathUtils_OrderDirection0 = MathUtils.OrderDirection.DECREASING;
      MathUtils.checkOrder(doubleArray1, mathUtils_OrderDirection0, false, false);
  }
}
