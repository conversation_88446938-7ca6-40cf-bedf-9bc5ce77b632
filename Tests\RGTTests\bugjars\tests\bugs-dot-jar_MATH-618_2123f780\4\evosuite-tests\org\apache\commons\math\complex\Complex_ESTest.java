/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 12:32:27 GMT 2019
 */

package org.apache.commons.math.complex;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.List;
import org.apache.commons.math.complex.Complex;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class Complex_ESTest extends Complex_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Complex complex0 = new Complex(0.0, (-134.22));
      Complex complex1 = complex0.I.cosh();
      Complex complex2 = complex0.exp();
      complex0.equals(complex1);
      complex2.toString();
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Complex complex0 = new Complex(1507.0, 1507.0);
      complex0.I.readResolve();
      complex0.ZERO.getField();
      Complex complex1 = complex0.INF.multiply((double) 801);
      Complex complex2 = complex1.INF.add(complex0);
      Complex complex3 = complex2.ONE.sqrt();
      complex3.NaN.tanh();
      Complex complex4 = complex1.ZERO.multiply(complex0);
      complex1.tanh();
      complex0.INF.getField();
      complex0.nthRoot(801);
      complex0.abs();
      Complex complex5 = complex1.sqrt();
      complex5.I.abs();
      Complex complex6 = complex0.log();
      complex0.abs();
      complex6.conjugate();
      complex1.acos();
      Complex complex7 = complex6.pow(complex0);
      complex6.INF.sin();
      complex7.ZERO.tan();
      complex4.tanh();
      complex7.negate();
      complex5.tanh();
      complex2.cosh();
      complex0.equals("Array contains an infinite element, {0} at index {1}");
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.acos();
      Complex complex2 = complex0.tan();
      Complex complex3 = complex2.ONE.divide(complex0);
      complex0.I.nthRoot(114);
      complex3.sqrt1z();
      Object object0 = complex0.readResolve();
      complex1.acos();
      complex0.equals(object0);
      Complex complex4 = complex0.sqrt();
      complex1.equals((Object) null);
      Complex complex5 = complex4.atan();
      complex1.ONE.getArgument();
      Complex complex6 = complex5.INF.createComplex(10.0, (-1168.0017));
      complex3.atan();
      Complex complex7 = new Complex((-698.108211246), 114);
      Complex complex8 = complex4.subtract(complex7);
      Complex complex9 = complex7.pow(complex4);
      Complex complex10 = complex7.tanh();
      complex10.sqrt();
      Complex complex11 = complex7.conjugate();
      Complex complex12 = complex5.sqrt1z();
      Complex complex13 = complex8.conjugate();
      complex13.multiply((double) 114);
      Complex complex14 = complex1.pow(complex3);
      complex14.nthRoot(114);
      complex5.INF.multiply(complex10);
      complex6.conjugate();
      Complex complex15 = complex10.add(complex3);
      complex12.getImaginary();
      complex9.getField();
      complex2.getReal();
      complex11.acos();
      complex15.getField();
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.I.getArgument();
      Complex complex1 = complex0.acos();
      Complex complex2 = complex1.acos();
      Complex complex3 = complex1.ONE.subtract(complex2);
      complex2.NaN.acos();
      Complex complex4 = complex3.ZERO.log();
      complex2.I.nthRoot(855);
      Complex complex5 = complex1.multiply(complex0);
      complex3.NaN.sinh();
      complex1.I.sqrt1z();
      complex3.ZERO.getArgument();
      Complex complex6 = complex5.NaN.sinh();
      complex6.ZERO.multiply(complex4);
      complex5.toString();
      complex5.sqrt();
      complex5.exp();
      complex4.divide(complex3);
      complex2.getField();
      complex6.readResolve();
      Complex complex7 = complex3.add(complex2);
      complex7.subtract(complex2);
      int int0 = (-1604);
      try { 
        complex4.nthRoot((-1604));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: -1,604
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Complex complex0 = Complex.I;
      try { 
        complex0.multiply((Complex) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      complex0.ZERO.hashCode();
      Complex complex1 = complex0.sin();
      // Undeclared exception!
      complex1.ONE.nthRoot(Integer.MAX_VALUE);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.negate();
      Complex complex2 = complex1.add(complex0);
      complex2.INF.toString();
      complex1.hashCode();
      Complex complex3 = complex1.tan();
      Complex complex4 = complex1.divide(complex0);
      Complex complex5 = complex4.NaN.sin();
      complex1.hashCode();
      complex3.isNaN();
      complex0.getArgument();
      complex0.readResolve();
      Complex complex6 = complex1.atan();
      complex3.isNaN();
      complex5.tan();
      Complex complex7 = complex2.sqrt1z();
      complex6.divide(complex7);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Complex complex0 = new Complex(5656.41498, 5656.41498);
      complex0.I.abs();
      Complex complex1 = Complex.NaN;
      Complex complex2 = complex0.ZERO.multiply(complex1);
      Complex complex3 = complex2.INF.negate();
      Complex complex4 = complex0.ONE.cosh();
      Complex complex5 = complex4.ONE.tan();
      Complex complex6 = complex3.pow(complex1);
      Complex complex7 = Complex.I;
      complex3.INF.pow(complex6);
      complex0.ONE.readResolve();
      Complex complex8 = complex3.ONE.pow(complex7);
      Complex complex9 = complex6.ZERO.subtract(complex7);
      Complex complex10 = complex0.negate();
      Complex complex11 = complex10.subtract(complex3);
      complex0.isNaN();
      Complex complex12 = complex9.subtract(complex11);
      Complex complex13 = complex12.INF.cosh();
      Complex complex14 = complex13.INF.pow(complex7);
      complex12.add(complex10);
      complex0.exp();
      complex9.sqrt();
      complex5.cosh();
      complex14.sqrt();
      Complex complex15 = complex10.NaN.tan();
      complex15.NaN.multiply(complex3);
      complex13.getArgument();
      complex2.getField();
      complex2.log();
      Complex complex16 = complex14.negate();
      complex8.subtract(complex16);
      complex6.getImaginary();
      complex13.abs();
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Complex complex0 = new Complex(1863.0, 1863.0);
      Complex complex1 = complex0.ONE.conjugate();
      Complex complex2 = complex0.sinh();
      Complex complex3 = complex1.exp();
      Complex complex4 = complex3.I.conjugate();
      complex4.readResolve();
      complex4.I.toString();
      Complex complex5 = complex0.sqrt1z();
      complex4.ONE.getField();
      complex2.NaN.pow(complex1);
      complex2.readResolve();
      Complex complex6 = complex0.add(complex4);
      Complex complex7 = complex2.INF.log();
      complex6.ZERO.createComplex(0.030589580535888672, 0.0875862700108075);
      complex0.tanh();
      complex2.abs();
      complex5.abs();
      complex7.isNaN();
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.createComplex((-1706.3), (-1706.3));
      Complex complex2 = complex1.multiply(complex0);
      complex2.INF.acos();
      complex1.INF.atan();
      Complex complex3 = complex0.log();
      Complex complex4 = complex3.ONE.asin();
      Complex complex5 = complex4.I.atan();
      Complex complex6 = complex5.atan();
      complex3.ZERO.nthRoot(2002);
      complex4.ONE.sin();
      complex3.isInfinite();
      complex2.NaN.abs();
      complex3.getArgument();
      Complex complex7 = complex2.log();
      complex7.NaN.sqrt1z();
      complex6.isNaN();
      complex0.isInfinite();
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Complex complex0 = new Complex((-3342.142679749035), (-3342.142679749035));
      complex0.toString();
      Complex complex1 = complex0.cos();
      Complex complex2 = complex1.ONE.pow(complex0);
      Complex complex3 = complex2.ONE.subtract(complex0);
      complex3.ONE.getArgument();
      complex0.getField();
      Complex complex4 = complex0.pow(complex1);
      complex3.atan();
      complex0.cosh();
      Complex complex5 = complex4.createComplex(0.0, 0.0);
      complex5.subtract(complex4);
      complex0.sin();
      complex4.hashCode();
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Complex complex0 = new Complex(1670.4, 1670.4);
      Complex complex1 = complex0.atan();
      complex1.INF.hashCode();
      Complex complex2 = complex1.sqrt1z();
      Complex complex3 = complex2.ZERO.sqrt();
      complex3.I.sqrt();
      complex2.tan();
      complex0.sin();
      complex0.toString();
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Complex complex0 = new Complex(338.9362833455, (-1668.44984));
      complex0.getArgument();
      Complex complex1 = complex0.tanh();
      Complex complex2 = complex1.ONE.sinh();
      complex2.abs();
      Complex complex3 = complex2.sinh();
      complex1.toString();
      Complex complex4 = complex0.subtract(complex2);
      Complex complex5 = complex4.INF.asin();
      Complex complex6 = complex4.NaN.createComplex(0.08713622391223907, 0.0);
      complex6.ONE.cosh();
      complex5.subtract(complex2);
      complex5.ONE.toString();
      complex1.getArgument();
      Complex complex7 = complex3.acos();
      complex7.sinh();
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Complex complex0 = new Complex(357.533, 0.0);
      Complex complex1 = complex0.I.log();
      Object object0 = complex1.readResolve();
      Complex complex2 = complex0.ONE.sqrt1z();
      complex2.ONE.toString();
      Complex complex3 = complex0.ONE.exp();
      complex3.I.getField();
      Complex complex4 = complex3.NaN.createComplex((-1589.246633434), 0.0);
      Complex complex5 = complex0.conjugate();
      Complex complex6 = complex5.INF.sqrt();
      complex6.NaN.abs();
      complex0.ONE.multiply((-2481.865));
      complex5.NaN.getArgument();
      complex5.abs();
      complex0.abs();
      Complex complex7 = complex5.cos();
      complex0.pow(complex2);
      complex6.getField();
      Complex complex8 = complex2.conjugate();
      complex8.equals(object0);
      complex7.getImaginary();
      complex4.tanh();
      complex8.getReal();
      Complex complex9 = complex3.multiply(357.533);
      Complex complex10 = complex7.createComplex(0.6299605249474366, 357.533);
      complex8.readResolve();
      complex5.exp();
      complex3.divide(complex10);
      complex9.atan();
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.exp();
      Complex complex2 = null;
      try { 
        complex1.I.subtract((Complex) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      complex0.abs();
      Complex complex1 = new Complex(Double.NaN, 0.0);
      Complex complex2 = complex0.pow(complex1);
      Complex complex3 = complex0.acos();
      complex2.isInfinite();
      Complex complex4 = complex2.multiply(complex0);
      complex3.I.atan();
      complex2.NaN.toString();
      Complex complex5 = complex2.ZERO.conjugate();
      complex5.NaN.asin();
      Complex complex6 = complex4.ONE.sqrt();
      Complex complex7 = complex3.acos();
      complex7.NaN.sqrt();
      List<Complex> list0 = complex7.I.nthRoot(722);
      Complex complex8 = complex7.I.multiply((double) 722);
      complex4.isInfinite();
      complex7.equals("(NaN, NaN)");
      Complex complex9 = complex2.divide(complex7);
      Complex complex10 = complex9.INF.negate();
      Complex complex11 = complex2.acos();
      Complex complex12 = complex2.ZERO.tan();
      complex4.equals(list0);
      complex6.isNaN();
      complex10.I.getArgument();
      Complex complex13 = complex7.sqrt1z();
      Complex complex14 = complex12.acos();
      complex14.ZERO.tanh();
      Complex complex15 = complex10.subtract(complex6);
      complex6.ZERO.createComplex(Double.NaN, 1.5707963267948966);
      complex15.NaN.readResolve();
      complex6.isNaN();
      complex11.getReal();
      complex9.asin();
      complex11.exp();
      complex8.getImaginary();
      complex13.asin();
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      complex0.ZERO.createComplex(Double.POSITIVE_INFINITY, 2412.5996616038);
      complex0.hashCode();
      Complex complex1 = complex0.conjugate();
      Complex complex2 = complex1.createComplex(0.0, (-24.4));
      Complex complex3 = complex2.NaN.tanh();
      Complex complex4 = complex3.ONE.tan();
      Complex complex5 = complex3.INF.subtract(complex2);
      complex5.ZERO.multiply(0.0);
      complex5.ONE.multiply(complex4);
      complex4.ONE.multiply((-1681.28329988094));
      complex0.isNaN();
      complex2.isNaN();
      complex0.pow(complex4);
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = Complex.INF;
      complex1.NaN.getArgument();
      complex0.getArgument();
      Complex complex2 = complex0.multiply(complex1);
      complex2.I.multiply(complex1);
      complex2.ZERO.sqrt1z();
      Complex complex3 = complex1.log();
      complex3.INF.abs();
      complex3.isInfinite();
      complex3.sqrt();
      Complex complex4 = complex0.multiply(0.0);
      complex4.I.log();
      complex4.log();
      Complex complex5 = complex1.negate();
      complex5.abs();
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Complex complex0 = new Complex(0.0, 0.0);
      complex0.NaN.getArgument();
      Complex complex1 = new Complex((-316.10088), Double.NaN);
      Complex complex2 = complex1.ONE.tan();
      Complex complex3 = complex2.ONE.sqrt1z();
      Complex complex4 = complex0.I.add(complex1);
      complex4.readResolve();
      Complex complex5 = complex0.ZERO.negate();
      complex4.ONE.sinh();
      complex0.readResolve();
      Complex complex6 = complex0.sin();
      Complex complex7 = complex6.ZERO.cos();
      Complex complex8 = Complex.ZERO;
      complex7.ONE.negate();
      complex7.ZERO.subtract(complex2);
      complex1.ZERO.hashCode();
      complex5.subtract(complex8);
      Complex complex9 = complex7.I.sinh();
      complex6.createComplex(0.0, Double.NaN);
      complex0.getField();
      complex5.getArgument();
      complex3.sqrt1z();
      complex4.getReal();
      complex4.atan();
      complex6.sqrt1z();
      complex9.getImaginary();
      complex1.getField();
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.I.negate();
      Complex complex2 = complex1.ONE.conjugate();
      complex1.ZERO.divide(complex0);
      complex1.INF.multiply(620.7);
      complex2.NaN.asin();
      complex2.ONE.sin();
      complex0.nthRoot(17);
      complex0.toString();
      complex2.divide(complex1);
      complex1.getImaginary();
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = Complex.ONE;
      Complex complex2 = complex1.INF.conjugate();
      Complex complex3 = complex2.INF.log();
      complex3.INF.sqrt1z();
      Complex complex4 = complex2.ONE.negate();
      Complex complex5 = complex0.NaN.divide(complex1);
      complex0.readResolve();
      complex0.equals(complex5);
      Complex complex6 = complex0.tan();
      Complex complex7 = Complex.I;
      complex1.I.subtract(complex7);
      Complex complex8 = complex6.subtract(complex2);
      complex5.multiply(complex4);
      Complex complex9 = complex4.asin();
      Complex complex10 = complex9.atan();
      complex10.tanh();
      complex9.INF.asin();
      complex5.toString();
      Complex complex11 = complex4.subtract(complex10);
      complex11.ZERO.abs();
      complex8.conjugate();
      complex8.sqrt1z();
      Complex complex12 = Complex.INF;
      complex11.equals(complex12);
      complex8.isInfinite();
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = Complex.ONE;
      Complex complex2 = complex1.INF.exp();
      Complex complex3 = complex0.INF.multiply(complex1);
      Complex complex4 = complex3.ZERO.cos();
      Complex complex5 = complex0.INF.createComplex((-3205.399), (-3205.399));
      Object object0 = new Object();
      Complex complex6 = complex0.ONE.divide(complex5);
      complex0.toString();
      complex0.equals(object0);
      complex0.abs();
      Complex complex7 = complex6.I.multiply(complex0);
      complex7.I.multiply((-1.0));
      complex0.exp();
      Complex complex8 = complex4.tan();
      Complex complex9 = complex3.multiply(complex6);
      complex9.ZERO.divide(complex2);
      Complex complex10 = complex6.add(complex8);
      complex10.I.divide(complex0);
      complex0.getArgument();
      complex2.isInfinite();
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Complex complex0 = new Complex((-812.7279588697263), 0.11764700710773468);
      complex0.INF.getArgument();
      Complex complex1 = complex0.I.negate();
      Complex complex2 = complex0.atan();
      complex2.ONE.abs();
      Complex complex3 = complex1.NaN.subtract(complex2);
      complex3.nthRoot(202);
      complex1.NaN.acos();
      Complex complex4 = complex0.NaN.tanh();
      complex2.ONE.pow(complex1);
      Complex complex5 = complex4.I.negate();
      Complex complex6 = complex2.divide(complex0);
      Complex complex7 = complex0.exp();
      Complex complex8 = complex4.asin();
      Complex complex9 = complex7.atan();
      complex9.ONE.subtract(complex5);
      Complex complex10 = complex5.ZERO.tan();
      Complex complex11 = complex8.I.negate();
      complex6.ONE.abs();
      Complex complex12 = complex5.ONE.add(complex7);
      complex11.hashCode();
      complex0.I.asin();
      complex8.isInfinite();
      complex7.getReal();
      complex5.getReal();
      complex4.add(complex0);
      Complex complex13 = complex6.add(complex8);
      complex10.multiply(0.0);
      complex8.readResolve();
      complex13.subtract(complex12);
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      complex0.readResolve();
      Complex complex1 = complex0.NaN.createComplex(5883.648027812495, 5883.648027812495);
      Complex complex2 = complex1.NaN.subtract(complex0);
      complex0.conjugate();
      complex0.subtract(complex1);
      complex1.hashCode();
      complex2.toString();
      complex2.tanh();
      complex0.getImaginary();
      complex1.multiply(225.0911489);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Complex complex0 = new Complex(782.11330469, 782.11330469);
      Complex complex1 = complex0.cosh();
      complex1.tan();
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.conjugate();
      complex0.acos();
      complex0.INF.hashCode();
      Complex complex2 = complex1.sqrt();
      Complex complex3 = complex2.ZERO.sin();
      Complex complex4 = Complex.NaN;
      Complex complex5 = complex3.NaN.divide(complex4);
      Complex complex6 = complex5.INF.tanh();
      Complex complex7 = complex0.sqrt1z();
      complex1.I.sinh();
      complex7.I.readResolve();
      Complex complex8 = complex7.NaN.log();
      complex8.NaN.tan();
      complex2.isNaN();
      Complex complex9 = complex5.sin();
      complex9.INF.negate();
      Complex complex10 = complex6.createComplex((-1306.942548), 2484.58);
      complex10.ONE.sin();
      complex1.cos();
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Complex complex0 = Complex.INF;
      try { 
        complex0.INF.divide((Complex) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      complex0.NaN.asin();
      complex0.toString();
      complex0.getReal();
      complex0.log();
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.toString();
      complex0.equals(complex0);
      complex0.sqrt();
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.sinh();
      complex0.ONE.acos();
      Complex complex2 = complex1.I.pow(complex0);
      complex2.ZERO.tanh();
      complex2.readResolve();
      complex2.NaN.conjugate();
      complex1.isInfinite();
      complex0.acos();
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = Complex.ONE;
      Complex complex2 = complex0.add(complex1);
      complex0.ZERO.pow(complex1);
      complex2.toString();
      complex2.getReal();
      complex1.equals(complex0);
      complex1.readResolve();
      complex2.getArgument();
      complex0.tan();
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.nthRoot(238);
      Complex complex1 = Complex.ONE;
      complex0.divide(complex1);
      complex0.log();
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.INF.tan();
      complex1.multiply(1328.87994349469);
      complex0.abs();
      complex0.readResolve();
      complex0.getArgument();
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Complex complex0 = new Complex(0.0, Double.POSITIVE_INFINITY);
      complex0.ONE.tanh();
      Complex complex1 = complex0.INF.exp();
      Complex complex2 = complex1.ONE.sinh();
      complex0.getField();
      complex0.conjugate();
      complex0.abs();
      Object object0 = new Object();
      complex0.equals(object0);
      complex2.negate();
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Complex complex0 = new Complex((-1020.711), (-1020.711));
      Complex complex1 = complex0.negate();
      complex1.cos();
      complex1.toString();
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.I.toString();
      complex0.readResolve();
      complex0.NaN.exp();
      Complex complex1 = complex0.conjugate();
      Complex complex2 = complex1.ONE.divide(complex0);
      Complex complex3 = complex1.asin();
      Complex complex4 = complex2.INF.sqrt1z();
      complex4.I.toString();
      complex0.getField();
      Complex complex5 = complex2.negate();
      Complex complex6 = complex3.sin();
      complex6.toString();
      complex5.getField();
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.sin();
      Complex complex2 = complex0.pow(complex1);
      Complex complex3 = complex0.subtract(complex2);
      Complex complex4 = complex3.NaN.log();
      Complex complex5 = complex4.ZERO.atan();
      complex5.ZERO.pow(complex0);
      complex3.getReal();
      complex1.log();
      complex1.getField();
      complex1.sqrt();
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.I.sin();
      Complex complex2 = complex0.acos();
      complex2.atan();
      complex2.tanh();
      complex1.toString();
      complex0.getArgument();
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.exp();
      complex1.NaN.cosh();
      complex0.toString();
      complex1.multiply(complex0);
      complex1.sqrt1z();
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Complex complex0 = new Complex(0.0, 0.0);
      complex0.conjugate();
      complex0.INF.exp();
      Complex complex1 = complex0.log();
      complex1.hashCode();
      complex0.subtract(complex1);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.getArgument();
      complex0.getReal();
      complex0.getField();
      Complex complex1 = complex0.log();
      complex1.ZERO.add(complex0);
      complex1.getField();
      complex0.multiply(1656.74);
      complex0.getField();
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.NaN.cosh();
      Complex complex2 = complex1.ZERO.multiply((-914.22140791302));
      Complex complex3 = complex2.createComplex((-1360.985116608), 3710.3361915);
      complex0.ZERO.sin();
      complex3.ONE.readResolve();
      Complex complex4 = Complex.INF;
      Complex complex5 = complex2.ZERO.multiply(complex4);
      complex5.ONE.add(complex0);
      complex0.getArgument();
      complex2.getField();
      complex3.getReal();
      Complex complex6 = complex3.sqrt();
      complex6.I.multiply((-1360.985116608));
      complex5.sqrt();
      complex1.atan();
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.I.atan();
      complex1.NaN.tan();
      Complex complex2 = complex0.atan();
      Complex complex3 = complex2.subtract(complex0);
      Complex complex4 = complex3.add(complex0);
      complex4.INF.negate();
      complex1.isInfinite();
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.I.negate();
      Complex complex2 = complex0.INF.tan();
      complex0.getField();
      complex1.getArgument();
      Complex complex3 = complex2.acos();
      complex3.I.log();
      complex0.isNaN();
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = Complex.ONE;
      complex0.divide(complex1);
      complex1.sqrt();
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      complex0.NaN.acos();
      Complex complex1 = complex0.exp();
      complex1.ZERO.sinh();
      complex1.I.acos();
      complex0.multiply(complex1);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.ONE.readResolve();
      Complex complex1 = complex0.negate();
      complex0.log();
      complex1.hashCode();
      complex1.getReal();
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      Complex complex0 = new Complex(0.0, 0.0);
      complex0.isNaN();
      complex0.nthRoot(1804);
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.ONE.sin();
      complex1.NaN.nthRoot(17);
      complex1.ZERO.sqrt();
      complex0.log();
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.multiply((-475.0));
      complex1.I.abs();
      complex0.getField();
      Complex complex2 = complex0.tan();
      complex2.add(complex1);
      complex0.acos();
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      complex0.NaN.hashCode();
      Complex complex1 = complex0.I.sinh();
      complex1.ONE.readResolve();
      complex0.createComplex(0.0, 0.0);
      Complex complex2 = complex0.sqrt1z();
      complex2.getArgument();
      Complex complex3 = complex0.divide(complex1);
      Complex complex4 = Complex.ONE;
      Complex complex5 = complex3.divide(complex4);
      Complex complex6 = complex5.ZERO.sqrt1z();
      complex6.I.conjugate();
      Complex complex7 = complex5.atan();
      complex7.I.sinh();
      Complex complex8 = complex5.tan();
      complex8.ZERO.sqrt1z();
      complex3.add(complex1);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      complex0.ONE.tan();
      complex0.sin();
      complex0.conjugate();
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.ZERO.createComplex((-6.032174644509064E-23), (-6.032174644509064E-23));
      complex0.cosh();
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.sqrt();
      complex0.I.nthRoot(778);
      Complex complex2 = complex1.exp();
      Complex complex3 = complex0.pow(complex2);
      complex3.NaN.getField();
      Complex complex4 = complex3.atan();
      Complex complex5 = complex4.sin();
      complex2.getImaginary();
      complex5.acos();
      complex5.tan();
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.NaN.cos();
      Complex complex2 = complex0.exp();
      Complex complex3 = complex2.multiply((-434.26157));
      complex1.INF.subtract(complex0);
      Complex complex4 = complex3.NaN.negate();
      complex4.ZERO.sin();
      Complex complex5 = complex3.I.sinh();
      Complex complex6 = complex3.conjugate();
      Complex complex7 = complex0.asin();
      complex7.INF.acos();
      complex2.atan();
      Complex complex8 = complex1.atan();
      complex8.ZERO.divide(complex6);
      complex5.hashCode();
      complex5.isInfinite();
      try { 
        complex7.nthRoot(0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: 0
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = Complex.I;
      complex0.NaN.multiply(complex1);
      Complex complex2 = complex0.ZERO.sinh();
      Complex complex3 = complex2.tan();
      complex3.sinh();
      complex3.I.pow(complex0);
      complex3.INF.hashCode();
      complex2.INF.hashCode();
      Complex complex4 = null;
      try { 
        complex0.ONE.add((Complex) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      Complex complex0 = new Complex(1734.295, 0.0);
      Complex complex1 = complex0.INF.tan();
      complex1.toString();
      complex1.ZERO.abs();
      Complex complex2 = complex1.I.sinh();
      Complex complex3 = complex2.ONE.cos();
      Complex complex4 = complex3.I.tan();
      complex0.ZERO.toString();
      Complex complex5 = complex4.INF.multiply(complex3);
      Complex complex6 = Complex.ONE;
      complex1.ZERO.divide(complex6);
      Complex complex7 = complex2.NaN.atan();
      Complex complex8 = complex7.I.sqrt1z();
      complex8.ONE.sin();
      complex0.equals((Object) null);
      Complex complex9 = complex2.ONE.multiply((-2419.97));
      Complex complex10 = complex0.cosh();
      Complex complex11 = complex2.ZERO.add(complex10);
      complex11.INF.createComplex(1.0, 0.0);
      complex0.log();
      Complex complex12 = complex1.pow(complex2);
      Complex complex13 = complex9.ZERO.subtract(complex2);
      complex13.I.cos();
      complex13.NaN.log();
      complex1.getField();
      complex4.sin();
      complex9.sqrt();
      complex12.toString();
      complex7.getImaginary();
      Complex complex14 = Complex.I;
      complex5.multiply(complex14);
      complex9.log();
      try { 
        complex7.pow((Complex) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      complex0.ONE.acos();
      complex0.I.sinh();
      complex0.tanh();
      complex0.negate();
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.isNaN();
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.sqrt1z();
      Complex complex2 = complex1.negate();
      Complex complex3 = complex2.ZERO.sin();
      Complex complex4 = complex3.NaN.sqrt();
      complex3.I.sqrt1z();
      Complex complex5 = complex2.INF.negate();
      Complex complex6 = complex0.divide(complex1);
      Complex complex7 = complex6.divide(complex2);
      complex7.ZERO.abs();
      complex7.INF.cos();
      complex6.ZERO.conjugate();
      complex7.I.readResolve();
      Complex complex8 = complex1.conjugate();
      complex8.ZERO.divide(complex0);
      complex6.getImaginary();
      complex1.hashCode();
      Complex complex9 = complex1.sqrt1z();
      complex2.divide(complex0);
      Complex complex10 = complex9.asin();
      complex10.NaN.sinh();
      complex4.log();
      complex5.log();
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      complex0.getArgument();
      complex0.INF.sinh();
      complex0.NaN.nthRoot(3694);
      complex0.NaN.atan();
      Complex complex1 = complex0.conjugate();
      complex1.hashCode();
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.sin();
      Complex complex2 = complex1.ZERO.conjugate();
      complex2.I.createComplex(4680.08214, 4680.08214);
      Complex complex3 = complex0.sinh();
      complex3.ONE.cos();
      Complex complex4 = complex3.cosh();
      complex3.createComplex(4680.08214, Double.POSITIVE_INFINITY);
      complex4.ZERO.sinh();
      Complex complex5 = complex4.cosh();
      Complex complex6 = complex5.NaN.conjugate();
      Complex complex7 = complex6.multiply((-188.32));
      complex7.ONE.conjugate();
      complex0.INF.hashCode();
      complex2.exp();
      complex4.abs();
      try { 
        complex1.nthRoot((-269));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: -269
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.INF.conjugate();
      complex0.INF.getArgument();
      Complex complex2 = complex0.sinh();
      Complex complex3 = complex2.INF.sinh();
      Complex complex4 = complex3.I.asin();
      Complex complex5 = complex4.I.cos();
      Complex complex6 = complex3.INF.atan();
      Complex complex7 = complex2.multiply(complex0);
      Complex complex8 = complex7.NaN.subtract(complex0);
      complex8.INF.nthRoot(499);
      Complex complex9 = complex1.I.acos();
      Complex complex10 = complex7.negate();
      complex10.NaN.multiply(complex6);
      Complex complex11 = complex1.sqrt1z();
      Complex complex12 = complex9.ONE.log();
      complex12.INF.getField();
      complex0.toString();
      complex10.log();
      complex7.ONE.toString();
      Complex complex13 = complex7.asin();
      Complex complex14 = complex13.NaN.conjugate();
      Complex complex15 = complex9.exp();
      Complex complex16 = complex13.add(complex8);
      Complex complex17 = complex7.sqrt();
      Complex complex18 = Complex.INF;
      complex17.exp();
      complex16.add(complex14);
      Complex complex19 = complex14.tan();
      complex11.cosh();
      complex15.exp();
      complex19.asin();
      complex5.getField();
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.tanh();
      Complex complex2 = complex1.I.pow(complex0);
      Complex complex3 = complex2.ZERO.cosh();
      Complex complex4 = complex3.ZERO.subtract(complex0);
      Complex complex5 = Complex.I;
      Complex complex6 = complex4.ZERO.pow(complex5);
      Complex complex7 = complex4.NaN.log();
      complex7.INF.sin();
      complex1.multiply((-1679.4119111419));
      complex1.equals(complex0);
      Complex complex8 = complex4.cos();
      complex3.getField();
      Complex complex9 = complex0.tan();
      Complex complex10 = complex8.createComplex((-1679.4119111419), (-1679.4119111419));
      Complex complex11 = complex10.INF.sqrt1z();
      complex10.INF.hashCode();
      Complex complex12 = complex8.sinh();
      Complex complex13 = complex12.subtract(complex9);
      complex12.INF.sin();
      complex11.createComplex(0.38109784554181547, 951.408479);
      complex6.exp();
      complex13.sqrt1z();
      complex12.sin();
      complex8.abs();
      complex12.sqrt();
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.sin();
      Complex complex2 = complex1.I.cosh();
      Complex complex3 = complex2.ONE.multiply(2823.20310732);
      complex0.tan();
      Complex complex4 = complex0.multiply((-3573.88389));
      Complex complex5 = complex4.NaN.subtract(complex2);
      Complex complex6 = complex0.asin();
      Complex complex7 = complex6.ZERO.add(complex1);
      complex4.toString();
      Complex complex8 = complex7.sqrt1z();
      complex8.nthRoot(182);
      Complex complex9 = complex0.multiply((-3573.88389));
      complex4.abs();
      Complex complex10 = complex2.sinh();
      Complex complex11 = complex10.ZERO.cosh();
      complex0.I.log();
      complex10.exp();
      complex2.pow(complex6);
      Complex complex12 = complex3.asin();
      complex12.tanh();
      Complex complex13 = complex1.multiply(5.0);
      complex11.sinh();
      complex1.exp();
      complex3.cosh();
      complex7.multiply((-195.3));
      complex5.tanh();
      complex7.createComplex((-1556.6), (-3573.88389));
      complex13.getField();
      Complex complex14 = complex9.sinh();
      complex14.divide(complex13);
  }

  @Test(timeout = 4000)
  public void test65()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = Complex.NaN;
      complex1.NaN.readResolve();
      Complex complex2 = complex0.pow(complex1);
      Complex complex3 = complex2.sqrt1z();
      Complex complex4 = complex0.I.negate();
      complex1.ONE.sqrt();
      complex4.NaN.asin();
      complex1.ZERO.nthRoot(3405);
      Complex complex5 = complex3.ONE.tanh();
      Complex complex6 = complex5.ONE.sinh();
      Complex complex7 = complex5.ZERO.multiply(93.972);
      Complex complex8 = complex3.add(complex2);
      Complex complex9 = complex8.NaN.divide(complex6);
      complex9.ZERO.atan();
      complex8.INF.nthRoot(1696);
      Complex complex10 = complex0.sqrt();
      Complex complex11 = complex2.multiply(complex0);
      complex11.ONE.abs();
      complex4.nthRoot(934);
      complex8.ONE.abs();
      complex1.multiply(complex4);
      Complex complex12 = complex1.tan();
      complex0.equals((Object) null);
      complex10.toString();
      complex11.cosh();
      complex7.readResolve();
      Complex complex13 = complex8.atan();
      Complex complex14 = complex13.ZERO.exp();
      complex14.cosh();
      complex13.sqrt1z();
      complex3.asin();
      complex12.nthRoot(934);
  }

  @Test(timeout = 4000)
  public void test66()  throws Throwable  {
      Complex complex0 = new Complex(1137.9208, 1137.9208);
      Complex complex1 = complex0.asin();
      Complex complex2 = complex1.NaN.cosh();
      Complex complex3 = complex2.NaN.conjugate();
      complex0.toString();
      Complex complex4 = complex1.sin();
      Complex complex5 = complex4.sqrt();
      Complex complex6 = complex3.INF.sqrt();
      Complex complex7 = complex1.INF.tan();
      complex4.toString();
      complex5.INF.toString();
      Complex complex8 = complex5.I.pow(complex4);
      complex8.I.tanh();
      complex4.isInfinite();
      Complex complex9 = complex2.pow(complex1);
      complex9.NaN.add(complex3);
      complex3.abs();
      complex3.ONE.abs();
      Complex complex10 = complex2.cosh();
      complex6.getField();
      Complex complex11 = complex6.log();
      complex9.subtract(complex6);
      complex1.NaN.createComplex(1137.9208, 957.53144808599);
      complex7.add(complex2);
      complex1.multiply(complex11);
      complex11.getReal();
      Complex complex12 = complex0.ONE.sinh();
      complex11.negate();
      complex1.sqrt1z();
      complex12.sqrt1z();
      complex10.sinh();
  }

  @Test(timeout = 4000)
  public void test67()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.NaN.negate();
      Complex complex2 = complex1.ONE.tanh();
      Complex complex3 = complex2.ONE.tan();
      Complex complex4 = complex3.ZERO.exp();
      complex0.toString();
      complex1.I.add(complex2);
      Complex complex5 = complex0.sqrt1z();
      complex5.ZERO.multiply(complex0);
      Complex complex6 = complex2.createComplex(1463.9, 990.0071074976);
      complex1.cosh();
      Complex complex7 = complex3.conjugate();
      Complex complex8 = complex7.pow(complex4);
      complex3.sinh();
      complex5.cosh();
      complex6.sqrt1z();
      complex1.equals(complex2);
      complex8.readResolve();
      complex1.cos();
  }

  @Test(timeout = 4000)
  public void test68()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.NaN.abs();
      Complex complex1 = complex0.negate();
      Complex complex2 = complex1.I.add(complex0);
      complex0.ZERO.subtract(complex2);
      Complex complex3 = complex0.exp();
      complex0.isInfinite();
      Complex complex4 = complex2.multiply(Double.NaN);
      Complex complex5 = complex4.I.log();
      Complex complex6 = complex5.NaN.createComplex(791.651255, Double.NaN);
      complex6.I.abs();
      complex5.I.hashCode();
      complex4.pow(complex2);
      Complex complex7 = complex0.NaN.acos();
      Complex complex8 = complex7.ZERO.sin();
      complex4.hashCode();
      Complex complex9 = complex4.INF.acos();
      complex3.acos();
      complex2.getArgument();
      Complex complex10 = complex0.sinh();
      complex10.hashCode();
      Complex complex11 = complex4.log();
      complex7.INF.tanh();
      Complex complex12 = complex11.ZERO.conjugate();
      Complex complex13 = complex12.INF.multiply(785.960375021457);
      complex13.ZERO.asin();
      Complex complex14 = complex3.cosh();
      Complex complex15 = complex14.createComplex(Double.NaN, Double.NaN);
      complex15.ZERO.getField();
      Complex complex16 = complex15.ZERO.negate();
      complex16.ONE.divide(complex8);
      Complex complex17 = complex10.subtract(complex8);
      complex17.toString();
      complex5.getReal();
      complex2.sqrt1z();
      complex15.multiply(complex2);
      complex9.abs();
  }

  @Test(timeout = 4000)
  public void test69()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.acos();
      Complex complex2 = complex0.tan();
      Complex complex3 = complex2.ONE.divide(complex0);
      complex0.I.nthRoot(114);
      complex3.sqrt1z();
      Object object0 = complex0.readResolve();
      complex1.acos();
      complex0.equals(object0);
      Complex complex4 = complex0.sqrt();
      complex1.equals((Object) null);
      Complex complex5 = complex4.atan();
      complex1.ONE.getArgument();
      Complex complex6 = complex5.INF.createComplex(10.0, (-1168.0017));
      Complex complex7 = new Complex((-698.108211246), 114);
      Complex complex8 = complex4.subtract(complex7);
      Complex complex9 = complex7.pow(complex4);
      Complex complex10 = complex7.tanh();
      complex10.sqrt();
      Complex complex11 = complex7.conjugate();
      Complex complex12 = complex5.sqrt1z();
      Complex complex13 = complex8.conjugate();
      complex13.multiply((double) 114);
      Complex complex14 = complex1.pow(complex3);
      complex14.nthRoot(114);
      complex5.INF.multiply(complex10);
      complex6.conjugate();
      Complex complex15 = complex10.add(complex3);
      complex12.getImaginary();
      complex9.getField();
      complex2.getReal();
      complex11.acos();
      complex15.getField();
  }

  @Test(timeout = 4000)
  public void test70()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.ONE.sqrt1z();
      Complex complex2 = complex1.INF.conjugate();
      complex2.negate();
      Complex complex3 = complex2.ZERO.multiply(complex1);
      complex3.cos();
      complex2.NaN.toString();
      try { 
        complex0.nthRoot((-850));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: -850
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test71()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.INF.createComplex(538.9289, 10.0);
      Complex complex2 = (Complex)complex1.INF.readResolve();
      Complex complex3 = complex0.sinh();
      complex3.ONE.readResolve();
      Complex complex4 = complex1.NaN.divide(complex3);
      complex4.I.getArgument();
      Complex complex5 = complex1.ONE.pow(complex4);
      complex3.getImaginary();
      complex1.abs();
      Complex complex6 = complex0.NaN.add(complex1);
      Complex complex7 = complex6.ZERO.sqrt1z();
      assertSame(complex6, complex5);
      assertEquals(1.0, complex7.getReal(), 0.01);
      assertEquals(0.0, complex7.getImaginary(), 0.01);
      
      Complex complex8 = complex3.NaN.log();
      Complex complex9 = complex8.ZERO.log();
      complex0.nthRoot(3827);
      complex3.getReal();
      Complex complex10 = complex3.negate();
      Complex complex11 = complex0.I.sin();
      complex10.ZERO.sinh();
      double double0 = complex3.abs();
      assertEquals(0.8414709848078965, double0, 0.01);
      
      complex0.ZERO.divide(complex2);
      Complex complex12 = complex11.ZERO.negate();
      assertFalse(complex12.equals((Object)complex9));
      
      complex0.getArgument();
      Complex complex13 = complex10.log();
      complex0.getImaginary();
      Complex complex14 = complex0.tan();
      complex14.NaN.getArgument();
      assertEquals(0.761594155955765, complex14.getImaginary(), 0.01);
      
      complex10.isNaN();
      complex13.equals(complex11);
      assertEquals(-0.0, complex10.getReal(), 0.01);
  }

  @Test(timeout = 4000)
  public void test72()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.NaN.cosh();
      Complex complex2 = complex0.NaN.pow(complex1);
      Complex complex3 = complex0.acos();
      Complex complex4 = complex3.I.sqrt();
      Complex complex5 = complex0.negate();
      Complex complex6 = complex5.I.asin();
      assertEquals(0.8813735870195428, complex6.getImaginary(), 0.01);
      
      complex0.INF.subtract(complex3);
      Complex complex7 = complex5.ONE.sinh();
      complex7.NaN.getArgument();
      assertEquals(0.0, complex7.getImaginary(), 0.01);
      assertEquals(1.1752011936438014, complex7.getReal(), 0.01);
      
      complex5.INF.getArgument();
      complex3.createComplex(0.0, 0.0);
      Complex complex8 = complex3.tan();
      Complex complex9 = complex4.negate();
      Complex complex10 = complex8.divide(complex2);
      complex4.abs();
      Complex complex11 = complex8.log();
      Complex complex12 = complex11.multiply(0.7853981633974483);
      assertEquals((-1.2337005501361697), complex12.getImaginary(), 0.01);
      assertEquals(0.2721982612879502, complex12.getReal(), 0.01);
      
      complex11.NaN.pow(complex9);
      complex11.NaN.getArgument();
      complex1.multiply(0.0);
      Complex complex13 = complex3.subtract(complex1);
      Complex complex14 = complex9.acos();
      complex8.isInfinite();
      assertEquals(6.123233995736767E-17, complex8.getReal(), 0.01);
      
      complex14.isInfinite();
      assertEquals(2.142655196996107, complex14.getReal(), 0.01);
      
      Complex complex15 = complex1.pow(complex0);
      Complex complex16 = Complex.ZERO;
      Complex complex17 = complex15.add(complex16);
      assertSame(complex17, complex13);
      assertSame(complex15, complex10);
  }

  @Test(timeout = 4000)
  public void test73()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = new Complex(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);
      complex0.I.getField();
      complex0.INF.abs();
      Complex complex2 = complex0.I.multiply(complex1);
      Complex complex3 = complex2.ONE.multiply(Double.POSITIVE_INFINITY);
      Complex complex4 = complex3.sinh();
      complex4.asin();
      Complex complex5 = complex3.INF.tanh();
      complex1.ZERO.toString();
      complex0.getImaginary();
      Complex complex6 = complex1.tan();
      complex6.NaN.getArgument();
      Complex complex7 = complex0.ONE.pow(complex1);
      complex2.ZERO.nthRoot(1);
      Complex complex8 = complex2.tan();
      Complex complex9 = complex1.cos();
      complex6.pow(complex9);
      complex6.getArgument();
      complex9.toString();
      complex8.ONE.createComplex(Double.NaN, 1304.723);
      complex9.add(complex8);
      Complex complex10 = complex7.ONE.pow(complex0);
      Complex complex11 = complex8.pow(complex9);
      complex11.I.hashCode();
      Complex complex12 = Complex.NaN;
      Complex complex13 = complex9.subtract(complex12);
      Complex complex14 = complex5.sinh();
      Complex complex15 = complex7.sin();
      complex15.acos();
      Complex complex16 = Complex.NaN;
      complex14.equals(complex8);
      complex1.negate();
      complex13.sin();
      complex10.nthRoot(1);
  }
}
