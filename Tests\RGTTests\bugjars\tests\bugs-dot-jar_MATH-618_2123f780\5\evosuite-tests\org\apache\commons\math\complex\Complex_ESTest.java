/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 12:37:25 GMT 2019
 */

package org.apache.commons.math.complex;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.commons.math.complex.Complex;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class Complex_ESTest extends Complex_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.I.abs();
      Complex complex1 = complex0.ZERO.atan();
      Complex complex2 = complex1.ZERO.tanh();
      Complex complex3 = complex0.log();
      Complex complex4 = complex3.NaN.acos();
      complex4.ONE.nthRoot(4943);
      Complex complex5 = complex3.INF.sqrt();
      Complex complex6 = complex0.sinh();
      complex4.pow(complex0);
      Complex complex7 = complex6.ZERO.createComplex((-796.6212383370555), (-5828.0));
      Complex complex8 = complex7.ONE.sin();
      complex1.I.atan();
      Complex complex9 = complex6.INF.divide(complex4);
      complex9.ZERO.add(complex3);
      complex9.ONE.tanh();
      complex5.NaN.getArgument();
      complex7.INF.sqrt1z();
      complex0.cosh();
      complex3.negate();
      complex1.getImaginary();
      complex4.equals(complex5);
      complex5.abs();
      complex6.toString();
      Complex complex10 = complex0.sin();
      complex10.toString();
      Complex complex11 = complex0.multiply(complex2);
      complex11.ZERO.hashCode();
      complex5.toString();
      Complex complex12 = complex5.tanh();
      Complex complex13 = complex8.tanh();
      complex12.subtract(complex13);
      complex3.exp();
      complex1.readResolve();
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Complex complex0 = new Complex((-0.4430227241169225), 1.4770329287303994);
      complex0.INF.getField();
      Complex complex1 = complex0.tan();
      Complex complex2 = complex1.sin();
      complex2.INF.getField();
      complex2.NaN.nthRoot(114);
      complex2.I.sqrt1z();
      Complex complex3 = complex0.negate();
      Complex complex4 = complex1.ZERO.cosh();
      complex4.atan();
      complex4.tan();
      Complex complex5 = complex3.NaN.pow(complex1);
      complex5.cos();
      Complex complex6 = complex0.ZERO.createComplex(1.4770329287303994, 1.3917211653329172E12);
      Complex complex7 = complex3.INF.subtract(complex1);
      complex7.I.createComplex(Double.POSITIVE_INFINITY, 1834.0719898394952);
      complex7.NaN.toString();
      Complex complex8 = complex1.atan();
      complex2.add(complex8);
      complex6.asin();
      complex4.cos();
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.abs();
      Complex complex1 = Complex.I;
      Complex complex2 = complex0.I.multiply(complex1);
      Complex complex3 = complex2.I.conjugate();
      Complex complex4 = complex1.multiply(complex0);
      Complex complex5 = complex4.NaN.log();
      complex4.ONE.divide(complex0);
      complex1.NaN.toString();
      Complex complex6 = complex1.ZERO.multiply((-370.039));
      complex0.NaN.sqrt();
      Complex complex7 = complex4.NaN.multiply(complex5);
      complex0.ZERO.atan();
      Complex complex8 = complex7.ONE.acos();
      complex8.INF.sqrt();
      Complex complex9 = complex0.multiply(complex1);
      complex6.createComplex((-370.039), (-370.039));
      complex5.ONE.conjugate();
      Complex complex10 = complex9.multiply((-370.039));
      complex10.I.log();
      Complex complex11 = complex9.exp();
      complex0.atan();
      complex0.atan();
      complex6.nthRoot(382);
      complex6.asin();
      complex5.exp();
      complex9.getImaginary();
      Complex complex12 = complex7.log();
      complex12.ZERO.getArgument();
      Complex complex13 = new Complex(Double.POSITIVE_INFINITY, 0.0);
      complex11.getField();
      Complex complex14 = complex1.ZERO.cosh();
      complex1.hashCode();
      complex6.tanh();
      complex3.getImaginary();
      complex14.createComplex(1.5, (-1.0));
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.I.multiply((-2434.39903));
      Complex complex2 = complex0.cosh();
      Complex complex3 = complex2.I.cosh();
      Complex complex4 = complex2.cosh();
      complex3.I.subtract(complex2);
      Complex complex5 = complex3.sinh();
      Complex complex6 = complex5.tan();
      Complex complex7 = complex0.I.sinh();
      complex5.NaN.conjugate();
      Complex complex8 = complex7.acos();
      Complex complex9 = complex8.ONE.asin();
      Complex complex10 = complex6.sqrt1z();
      complex10.ZERO.asin();
      complex10.NaN.sinh();
      complex2.INF.sinh();
      Complex complex11 = complex2.atan();
      complex11.I.nthRoot(242);
      complex3.abs();
      complex0.I.sin();
      complex0.toString();
      Complex complex12 = complex0.multiply((-2434.39903));
      Complex complex13 = complex12.I.tanh();
      complex13.INF.conjugate();
      complex12.cos();
      complex12.add(complex4);
      complex1.sqrt();
      complex1.nthRoot(242);
      complex1.sinh();
      complex9.cos();
      complex8.negate();
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.abs();
      Complex complex1 = Complex.I;
      Complex complex2 = complex0.I.multiply(complex1);
      complex2.I.conjugate();
      Complex complex3 = complex1.multiply(complex0);
      Complex complex4 = complex3.NaN.log();
      complex3.ONE.divide(complex1);
      complex1.NaN.toString();
      Complex complex5 = complex1.ZERO.multiply(Double.POSITIVE_INFINITY);
      complex0.NaN.sqrt();
      Complex complex6 = complex3.NaN.multiply(complex4);
      complex0.ZERO.atan();
      Complex complex7 = complex6.ONE.acos();
      Complex complex8 = complex7.INF.sqrt();
      Complex complex9 = complex0.multiply(complex1);
      complex5.createComplex(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);
      complex4.ONE.conjugate();
      Complex complex10 = complex9.multiply(Double.POSITIVE_INFINITY);
      complex10.I.log();
      complex9.exp();
      complex0.atan();
      complex5.nthRoot(382);
      complex5.asin();
      Complex complex11 = complex4.exp();
      Complex complex12 = complex11.I.atan();
      complex9.getImaginary();
      Complex complex13 = complex6.log();
      complex13.ZERO.getArgument();
      complex12.readResolve();
      complex8.getField();
      Complex complex14 = complex1.ZERO.cosh();
      complex1.hashCode();
      complex5.tanh();
      complex12.getImaginary();
      complex14.createComplex(1.5, Double.NaN);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.ZERO.asin();
      Complex complex2 = complex0.sin();
      Complex complex3 = complex2.INF.asin();
      complex1.abs();
      complex0.abs();
      Complex complex4 = complex1.multiply(Double.POSITIVE_INFINITY);
      complex4.getField();
      complex4.getField();
      complex3.abs();
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.NaN.atan();
      complex0.hashCode();
      Complex complex2 = new Complex((-4207.24), 0.0);
      Complex complex3 = complex0.multiply(complex2);
      Complex complex4 = complex3.log();
      Complex complex5 = complex1.multiply(0.0);
      Complex complex6 = complex3.I.sin();
      Complex complex7 = complex5.log();
      complex6.ONE.conjugate();
      Complex complex8 = complex7.INF.asin();
      Complex complex9 = complex2.ONE.exp();
      complex7.I.acos();
      Complex complex10 = complex0.ZERO.divide(complex6);
      complex3.isNaN();
      Complex complex11 = complex0.createComplex((-4207.24), 0.0);
      complex7.atan();
      Complex complex12 = complex8.log();
      complex6.readResolve();
      Complex complex13 = complex12.sqrt1z();
      complex4.multiply((-881.4929014385175));
      Complex complex14 = complex13.exp();
      complex14.ONE.abs();
      Complex complex15 = complex1.tanh();
      complex15.isInfinite();
      complex0.abs();
      complex5.tanh();
      complex9.cos();
      complex3.toString();
      complex11.sqrt1z();
      complex13.getReal();
      Complex complex16 = complex12.negate();
      complex16.getArgument();
      complex1.conjugate();
      complex3.cos();
      // Undeclared exception!
      complex10.nthRoot(1431655765);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.ONE.cosh();
      Complex complex2 = complex0.INF.tan();
      Complex complex3 = complex2.I.divide(complex0);
      complex2.ZERO.getField();
      Complex complex4 = complex1.INF.subtract(complex0);
      complex0.I.readResolve();
      complex1.NaN.abs();
      complex0.toString();
      Complex complex5 = complex0.ZERO.negate();
      complex0.getReal();
      complex2.NaN.atan();
      Complex complex6 = complex1.sinh();
      complex6.exp();
      complex6.NaN.multiply(complex4);
      complex2.hashCode();
      complex4.ZERO.getField();
      complex6.getField();
      complex3.pow(complex4);
      complex3.getImaginary();
      Complex complex7 = complex1.sqrt1z();
      complex7.INF.sqrt1z();
      complex7.abs();
      Complex complex8 = complex0.cosh();
      Complex complex9 = complex4.sin();
      Complex complex10 = complex8.tanh();
      complex10.INF.asin();
      complex8.isInfinite();
      Complex complex11 = complex3.tanh();
      complex4.add(complex1);
      complex10.negate();
      complex6.cosh();
      complex9.getArgument();
      complex11.multiply(0.0);
      complex7.equals(complex5);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.ONE.sqrt();
      Complex complex2 = complex1.I.negate();
      Complex complex3 = complex0.I.exp();
      complex0.isInfinite();
      complex3.NaN.getArgument();
      Complex complex4 = complex0.negate();
      complex3.add(complex2);
      complex0.abs();
      complex3.asin();
      Complex complex5 = complex4.sin();
      int int0 = 0;
      try { 
        complex5.I.nthRoot(0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: 0
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Complex complex0 = new Complex(1.2675934823758863E-8, (-57.49601041046));
      complex0.I.tanh();
      complex0.readResolve();
      Complex complex1 = complex0.ONE.cosh();
      complex1.sqrt();
      complex1.ONE.getArgument();
      Complex complex2 = complex0.exp();
      complex2.isInfinite();
      complex2.sqrt();
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Complex complex0 = new Complex((-1978.6), (-1978.6));
      Complex complex1 = complex0.NaN.log();
      Complex complex2 = complex1.NaN.divide(complex0);
      Complex complex3 = complex1.tanh();
      Complex complex4 = Complex.INF;
      Complex complex5 = complex0.ZERO.subtract(complex4);
      complex2.INF.tan();
      Complex complex6 = complex3.I.tan();
      complex0.equals("d@p");
      Complex complex7 = complex0.negate();
      Complex complex8 = complex7.multiply((-616.0));
      complex8.INF.hashCode();
      Complex complex9 = complex0.cosh();
      complex9.ONE.abs();
      complex4.nthRoot(1);
      complex5.ZERO.nthRoot(2846);
      Complex complex10 = complex5.add(complex3);
      Complex complex11 = complex10.tan();
      complex11.I.negate();
      complex2.nthRoot(1498);
      complex0.isNaN();
      complex5.toString();
      Complex complex12 = complex5.atan();
      complex12.ONE.toString();
      complex2.asin();
      complex5.getField();
      Complex complex13 = complex8.sinh();
      complex3.multiply(1077.103);
      complex13.hashCode();
      complex11.tan();
      complex6.abs();
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Complex complex0 = new Complex((-1.0), 508.1440842559997);
      complex0.INF.getField();
      Complex complex1 = complex0.INF.createComplex(1493.43590845, 508.1440842559997);
      Complex complex2 = complex1.I.subtract(complex0);
      complex2.I.sin();
      complex2.cosh();
      complex0.INF.readResolve();
      complex0.getArgument();
      complex0.nthRoot(1);
      complex0.getField();
      complex1.ONE.sinh();
      Complex complex3 = complex1.sqrt();
      Complex complex4 = complex3.asin();
      Complex complex5 = complex3.ZERO.pow(complex0);
      complex1.I.tanh();
      complex4.NaN.getArgument();
      Complex complex6 = complex4.INF.divide(complex3);
      complex6.NaN.getField();
      complex0.ZERO.negate();
      complex0.I.getField();
      Complex complex7 = complex1.tanh();
      complex0.abs();
      complex7.isNaN();
      complex1.getImaginary();
      Complex complex8 = complex1.tan();
      complex5.getField();
      complex8.isNaN();
      complex7.pow(complex3);
      Complex complex9 = Complex.NaN;
      complex4.subtract(complex9);
      complex0.getArgument();
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Complex complex0 = new Complex(6.283185307179586, Double.NaN);
      Complex complex1 = new Complex(6.283185307179586, Double.NaN);
      complex0.I.asin();
      Complex complex2 = complex0.multiply(complex1);
      Complex complex3 = complex1.negate();
      complex2.sinh();
      Complex complex4 = complex1.NaN.cosh();
      complex2.ZERO.tanh();
      complex4.INF.subtract(complex3);
      Complex complex5 = complex0.asin();
      complex3.getField();
      Complex complex6 = complex2.sqrt();
      complex6.INF.acos();
      complex5.getField();
      Complex complex7 = complex2.sinh();
      complex7.INF.cosh();
      complex5.tan();
      complex7.hashCode();
      complex3.getArgument();
      Complex complex8 = complex2.exp();
      complex8.asin();
      complex6.sqrt1z();
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Complex complex0 = new Complex(4109.149, (-668.573));
      Complex complex1 = complex0.sin();
      Complex complex2 = complex0.sqrt1z();
      complex2.ONE.hashCode();
      complex2.ONE.log();
      Complex complex3 = complex1.conjugate();
      Complex complex4 = complex3.multiply(complex0);
      complex4.I.conjugate();
      complex4.I.acos();
      complex4.INF.multiply((-657.28626023));
      complex1.ONE.sin();
      Complex complex5 = complex4.sin();
      complex5.I.sqrt1z();
      complex0.equals(complex5);
      complex5.sqrt1z();
      complex1.exp();
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.NaN.sqrt();
      Complex complex2 = complex0.I.cos();
      complex0.isInfinite();
      Complex complex3 = complex0.sin();
      complex3.hashCode();
      Complex complex4 = complex0.asin();
      Complex complex5 = complex4.ONE.pow(complex3);
      complex3.INF.sinh();
      Complex complex6 = complex5.INF.multiply(0.0);
      Complex complex7 = complex4.INF.sqrt1z();
      Complex complex8 = complex7.INF.cos();
      complex6.ZERO.acos();
      complex1.nthRoot(2246);
      Complex complex9 = complex0.cos();
      Complex complex10 = complex2.asin();
      complex6.nthRoot(2246);
      complex5.atan();
      Complex complex11 = complex0.NaN.sqrt();
      Complex complex12 = complex1.tanh();
      complex9.atan();
      complex10.cosh();
      complex12.isInfinite();
      complex12.acos();
      complex2.abs();
      complex11.toString();
      complex1.log();
      Complex complex13 = complex9.log();
      complex11.tanh();
      complex6.sinh();
      complex13.multiply(complex9);
      complex8.pow(complex9);
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.createComplex((-1054.886769), 837.421277);
      Complex complex2 = complex0.NaN.atan();
      Complex complex3 = complex0.NaN.multiply(complex1);
      complex1.INF.toString();
      Complex complex4 = complex0.acos();
      complex0.readResolve();
      Complex complex5 = complex2.I.pow(complex1);
      Complex complex6 = complex4.sqrt1z();
      Complex complex7 = complex1.multiply((-1054.886769));
      Complex complex8 = complex6.createComplex((-1054.886769), (-1054.886769));
      Complex complex9 = complex8.I.multiply(complex2);
      Complex complex10 = complex7.acos();
      Complex complex11 = complex8.subtract(complex4);
      complex3.ONE.divide(complex5);
      complex8.INF.createComplex(0.0, 1581.9205032967118);
      complex11.subtract(complex8);
      complex5.acos();
      complex9.pow(complex10);
      complex3.abs();
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      complex0.getArgument();
      Complex complex1 = complex0.log();
      Complex complex2 = complex1.NaN.subtract(complex0);
      Complex complex3 = complex2.ONE.conjugate();
      Complex complex4 = complex1.I.conjugate();
      complex1.ONE.readResolve();
      Complex complex5 = complex1.ONE.cos();
      Complex complex6 = complex5.ZERO.atan();
      Complex complex7 = complex6.ZERO.acos();
      complex7.ONE.hashCode();
      Complex complex8 = complex5.ONE.pow(complex2);
      complex8.I.sqrt1z();
      complex1.equals(complex0);
      Complex complex9 = complex4.acos();
      complex9.exp();
      complex2.ZERO.hashCode();
      Complex complex10 = complex4.conjugate();
      Object object0 = complex10.readResolve();
      complex10.ONE.exp();
      complex10.ZERO.tan();
      complex4.equals(object0);
      complex0.add(complex10);
      complex3.isInfinite();
      complex9.sinh();
      complex9.sinh();
      complex7.getField();
      complex9.tan();
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.I.subtract(complex0);
      Complex complex2 = complex0.createComplex(0.0, 2519.1088576414763);
      Complex complex3 = complex2.NaN.conjugate();
      complex3.NaN.sin();
      complex0.getImaginary();
      complex1.equals(complex0);
      complex1.conjugate();
      complex3.getField();
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.NaN.sinh();
      complex1.ONE.tan();
      Complex complex2 = complex1.INF.divide(complex0);
      complex2.NaN.hashCode();
      int int0 = (-1);
      complex1.ZERO.conjugate();
      try { 
        complex1.INF.nthRoot((-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: -1
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.log();
      Complex complex2 = complex1.sqrt1z();
      Complex complex3 = complex0.sinh();
      complex3.ZERO.getField();
      Complex complex4 = complex2.I.atan();
      complex0.I.getArgument();
      Complex complex5 = complex4.I.subtract(complex1);
      Complex complex6 = complex4.ONE.tan();
      complex6.ZERO.exp();
      complex4.NaN.conjugate();
      complex1.getField();
      complex4.ONE.multiply(1.5707963267948966);
      complex1.ZERO.add(complex5);
      complex4.getArgument();
      Complex complex7 = complex5.sinh();
      complex7.atan();
      Complex complex8 = complex4.conjugate();
      Complex complex9 = complex8.I.atan();
      complex9.acos();
      complex8.I.readResolve();
      complex2.cos();
      complex4.abs();
      complex4.cos();
      Complex complex10 = Complex.I;
      complex8.add(complex10);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.ZERO.createComplex(1432.94937821008, 1432.94937821008);
      Complex complex2 = complex1.NaN.subtract(complex0);
      Complex complex3 = complex2.INF.tan();
      Complex complex4 = complex0.ONE.negate();
      Complex complex5 = Complex.NaN;
      Complex complex6 = complex0.multiply(complex5);
      complex5.I.getArgument();
      Complex complex7 = complex6.tan();
      complex7.sqrt1z();
      Complex complex8 = complex6.cosh();
      Complex complex9 = complex8.ONE.cos();
      complex2.add(complex1);
      complex7.I.multiply(complex0);
      complex6.abs();
      complex7.hashCode();
      complex0.sqrt();
      complex2.multiply(complex4);
      Complex complex10 = complex2.tan();
      Complex complex11 = Complex.NaN;
      complex10.INF.pow(complex11);
      complex7.cos();
      complex3.getReal();
      complex1.cos();
      complex2.readResolve();
      complex10.getImaginary();
      complex9.nthRoot(282);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Complex complex0 = new Complex(1741.387, 1741.387);
      Complex complex1 = complex0.I.tanh();
      Complex complex2 = new Complex(389.78020614, 1741.387);
      complex1.INF.subtract(complex2);
      Complex complex3 = complex1.NaN.sqrt();
      Complex complex4 = Complex.ZERO;
      Complex complex5 = complex0.subtract(complex4);
      complex4.INF.acos();
      complex0.NaN.multiply(1.0);
      Complex complex6 = complex5.createComplex(1741.387, 0.19999999999923582);
      Complex complex7 = complex0.pow(complex6);
      Complex complex8 = complex7.I.acos();
      complex8.NaN.negate();
      Complex complex9 = complex6.tan();
      complex9.ZERO.tan();
      Complex complex10 = complex0.sqrt();
      complex5.getField();
      Complex complex11 = complex3.exp();
      complex11.INF.divide(complex3);
      complex11.sqrt1z();
      complex7.sqrt1z();
      Complex complex12 = complex0.multiply((-1279.5067));
      complex10.sin();
      complex12.tan();
      complex8.getField();
      complex10.exp();
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.abs();
      Complex complex1 = null;
      try { 
        complex0.I.pow((Complex) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.I.multiply(1014.2425903);
      Complex complex1 = complex0.I.sqrt();
      complex0.getField();
      Complex complex2 = complex1.multiply(1014.2425903);
      complex2.tanh();
      complex0.sinh();
      Complex complex3 = complex1.sqrt();
      complex3.cos();
      complex1.createComplex(1014.2425903, 1014.2425903);
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      complex0.getImaginary();
      Complex complex1 = complex0.NaN.cosh();
      Complex complex2 = new Complex(Double.NaN, Double.NaN);
      complex2.NaN.cos();
      Complex complex3 = complex2.NaN.asin();
      Complex complex4 = complex0.pow(complex2);
      complex4.ONE.getField();
      Complex complex5 = complex4.multiply(Double.NaN);
      Complex complex6 = complex2.I.sinh();
      complex6.NaN.abs();
      complex1.abs();
      complex5.equals(complex4);
      complex4.ONE.divide(complex3);
      Complex complex7 = complex3.createComplex(0.0, 0.0);
      complex7.ZERO.getField();
      Complex complex8 = complex4.atan();
      complex8.multiply(complex2);
      Complex complex9 = complex4.exp();
      complex1.getField();
      Complex complex10 = complex9.sqrt();
      complex10.isNaN();
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.getField();
      Complex complex1 = complex0.cos();
      Complex complex2 = complex1.NaN.add(complex0);
      Complex complex3 = complex2.INF.exp();
      complex3.INF.tan();
      Complex complex4 = complex0.NaN.cosh();
      complex4.ZERO.divide(complex2);
      complex0.INF.log();
      complex0.getImaginary();
      Complex complex5 = complex0.ONE.cos();
      complex5.NaN.createComplex(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);
      Complex complex6 = complex1.ZERO.subtract(complex5);
      complex1.equals(complex0);
      complex1.toString();
      Complex complex7 = complex1.tan();
      complex7.ONE.getArgument();
      complex6.atan();
      complex7.getField();
      complex7.atan();
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.tanh();
      complex1.I.log();
      complex0.readResolve();
      complex0.ZERO.getArgument();
      Complex complex2 = complex0.sqrt();
      complex0.INF.log();
      Complex complex3 = complex2.createComplex(0.0, 0.0);
      Complex complex4 = Complex.ONE;
      Complex complex5 = complex3.add(complex4);
      complex5.I.nthRoot(17);
      complex5.INF.sqrt();
      complex5.createComplex((-0.010714690733195933), 40.19140625);
      complex1.cos();
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = null;
      try { 
        complex0.add((Complex) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Complex complex0 = new Complex(131.85618333, 131.85618333);
      Complex complex1 = Complex.I;
      Complex complex2 = complex0.NaN.add(complex1);
      Complex complex3 = Complex.I;
      Complex complex4 = complex0.subtract(complex3);
      Complex complex5 = complex4.ZERO.subtract(complex0);
      Complex complex6 = complex3.NaN.conjugate();
      Complex complex7 = complex6.NaN.asin();
      complex4.multiply(0.125);
      Complex complex8 = complex3.INF.negate();
      complex3.I.divide(complex8);
      Complex complex9 = complex3.tanh();
      Complex complex10 = complex9.ONE.sqrt1z();
      complex10.NaN.hashCode();
      complex9.ONE.multiply(131.85618333);
      complex5.readResolve();
      Complex complex11 = complex5.log();
      complex4.divide(complex1);
      complex2.readResolve();
      complex9.isInfinite();
      complex11.abs();
      complex11.getImaginary();
      complex5.negate();
      complex5.divide(complex8);
      complex7.divide(complex8);
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.createComplex(1.0, (-227.66361608453795));
      complex1.INF.getArgument();
      complex0.negate();
      Complex complex2 = complex1.asin();
      Complex complex3 = complex0.ZERO.cos();
      Complex complex4 = complex0.exp();
      Complex complex5 = complex4.INF.exp();
      Complex complex6 = complex1.cos();
      complex6.sin();
      Complex complex7 = complex6.log();
      Complex complex8 = Complex.I;
      complex2.equals(complex8);
      complex0.isInfinite();
      Complex complex9 = complex8.INF.subtract(complex7);
      Complex complex10 = complex6.createComplex(1.0, 1.0);
      complex1.tanh();
      Complex complex11 = complex8.asin();
      Complex complex12 = complex5.cosh();
      complex9.getImaginary();
      complex12.toString();
      Complex complex13 = complex10.pow(complex3);
      complex3.subtract(complex13);
      complex11.isInfinite();
      Complex complex14 = complex5.multiply(0.7853981633974483);
      complex5.cos();
      complex14.divide(complex8);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Complex complex0 = new Complex(0.0, 1973.68);
      Complex complex1 = complex0.acos();
      Complex complex2 = complex0.I.sqrt1z();
      complex1.getArgument();
      complex0.equals(complex1);
      complex0.hashCode();
      complex2.sqrt1z();
      complex2.createComplex(1973.68, (-1.383332322110721));
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = Complex.NaN;
      Complex complex2 = complex1.ONE.tan();
      Complex complex3 = complex2.ZERO.divide(complex0);
      Complex complex4 = complex0.INF.multiply(complex1);
      Complex complex5 = complex0.conjugate();
      complex4.negate();
      complex0.cosh();
      complex1.getReal();
      complex0.multiply(complex1);
      complex3.sqrt();
      complex2.pow(complex4);
      complex0.hashCode();
      Complex complex6 = complex4.createComplex(Double.NaN, Double.NaN);
      complex6.I.sqrt1z();
      complex5.sin();
      complex5.add(complex3);
      complex1.hashCode();
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.NaN.getArgument();
      Complex complex1 = complex0.I.exp();
      complex0.isNaN();
      Complex complex2 = complex1.negate();
      complex0.nthRoot(50);
      complex1.getField();
      complex1.getArgument();
      complex0.abs();
      Complex complex3 = complex2.sinh();
      complex3.NaN.getField();
      complex2.tan();
      complex1.getImaginary();
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = Complex.INF;
      Complex complex2 = complex0.atan();
      complex2.ZERO.log();
      complex0.add(complex1);
      complex0.sinh();
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      complex0.I.toString();
      Complex complex1 = complex0.ZERO.log();
      complex0.toString();
      Complex complex2 = complex1.sqrt();
      Complex complex3 = complex2.atan();
      complex1.nthRoot(288);
      Complex complex4 = complex0.tan();
      Complex complex5 = complex4.ONE.divide(complex3);
      complex4.ZERO.sin();
      complex1.getArgument();
      Complex complex6 = complex4.sqrt1z();
      complex6.NaN.tan();
      complex3.createComplex((-184.89680014425204), (-195.2888413642));
      Complex complex7 = complex0.tanh();
      complex7.log();
      complex1.sqrt1z();
      complex5.pow(complex1);
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      double double0 = 85.6563789;
      double double1 = (-96.7423);
      Complex complex0 = new Complex(85.6563789, (-96.7423));
      try { 
        complex0.divide((Complex) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.ONE.multiply(Double.NaN);
      complex0.readResolve();
      Complex complex2 = complex1.NaN.sin();
      complex2.ONE.sinh();
      Complex complex3 = complex0.atan();
      complex0.conjugate();
      complex3.cos();
      complex0.sinh();
      complex1.cos();
      Complex complex4 = complex1.negate();
      Complex complex5 = complex4.NaN.createComplex((-34.968789588), Double.NaN);
      Object object0 = complex0.readResolve();
      complex4.equals(object0);
      complex5.sinh();
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = Complex.ONE;
      complex0.I.subtract(complex1);
      Complex complex2 = complex0.atan();
      complex2.cosh();
      complex0.cosh();
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = null;
      try { 
        complex0.ONE.subtract((Complex) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = Complex.NaN;
      complex0.subtract(complex1);
      complex1.abs();
      Complex complex2 = Complex.ONE;
      complex0.multiply(complex2);
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.sqrt1z();
      complex1.ZERO.sqrt();
      complex1.pow(complex0);
      complex0.ZERO.abs();
      complex1.add(complex0);
      complex1.getField();
      complex1.isInfinite();
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.ZERO.hashCode();
      complex0.NaN.sqrt1z();
      complex0.multiply(2472.30289223326);
      complex0.isInfinite();
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.createComplex(Double.POSITIVE_INFINITY, Double.POSITIVE_INFINITY);
      complex0.INF.hashCode();
      Complex complex1 = complex0.sqrt();
      complex0.add(complex1);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.ZERO.negate();
      complex1.ONE.getArgument();
      complex0.ONE.abs();
      Complex complex2 = complex0.sin();
      Complex complex3 = complex0.tanh();
      complex0.ZERO.subtract(complex1);
      Complex complex4 = complex3.acos();
      complex4.ONE.log();
      complex2.INF.multiply(0.0);
      Complex complex5 = complex3.cos();
      complex5.ZERO.getArgument();
      complex2.pow(complex1);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.I.conjugate();
      complex1.subtract(complex0);
      Complex complex2 = complex1.ONE.sqrt();
      Complex complex3 = complex0.multiply((-2565.481991339));
      Complex complex4 = complex3.cosh();
      complex4.atan();
      complex4.INF.sqrt();
      complex3.INF.readResolve();
      Complex complex5 = complex4.NaN.divide(complex3);
      Complex complex6 = complex5.ONE.tanh();
      Complex complex7 = complex6.INF.atan();
      complex7.INF.asin();
      complex0.sqrt();
      complex6.hashCode();
      complex2.isNaN();
      Complex complex8 = Complex.ONE;
      complex3.divide(complex8);
      complex5.tanh();
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      complex0.getReal();
      Complex complex1 = complex0.sqrt();
      Complex complex2 = complex1.negate();
      Complex complex3 = complex2.negate();
      Complex complex4 = complex2.NaN.createComplex(Double.NaN, 2.0);
      complex4.subtract(complex3);
      complex2.getImaginary();
      Complex complex5 = complex3.atan();
      complex5.I.add(complex3);
      complex2.exp();
      Complex complex6 = new Complex(Double.NaN, Double.NaN);
      complex2.multiply(complex6);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.I.sinh();
      complex0.ZERO.abs();
      Complex complex2 = complex1.ONE.exp();
      complex2.I.readResolve();
      try { 
        complex0.multiply((Complex) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null is not allowed
         //
         verifyException("org.apache.commons.math.util.MathUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      Complex complex0 = new Complex((-0.3058015757857271), (-0.3058015757857271));
      Complex complex1 = complex0.NaN.cos();
      Complex complex2 = complex1.INF.multiply((-0.3058015757857271));
      Complex complex3 = complex2.sqrt1z();
      complex3.NaN.multiply(complex2);
      complex3.toString();
      complex0.I.hashCode();
      complex2.ZERO.sin();
      complex0.getReal();
      complex0.readResolve();
      complex2.isInfinite();
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.ONE.exp();
      Complex complex2 = Complex.INF;
      complex0.ONE.hashCode();
      Complex complex3 = complex2.divide(complex0);
      complex3.ONE.subtract(complex1);
      complex0.divide(complex2);
      try { 
        complex0.INF.nthRoot((-2172));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: -2,172
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      Complex complex0 = new Complex(1.0E-6, Double.POSITIVE_INFINITY);
      complex0.ONE.cosh();
      Complex complex1 = complex0.sin();
      Complex complex2 = complex1.negate();
      complex2.acos();
      complex1.subtract(complex0);
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.multiply(0.0);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      Complex complex0 = new Complex((-713.329829138), (-713.329829138));
      Complex complex1 = Complex.NaN;
      complex1.INF.cos();
      Complex complex2 = complex1.ZERO.acos();
      Complex complex3 = complex0.I.add(complex1);
      complex3.NaN.tan();
      complex3.readResolve();
      Complex complex4 = complex0.sinh();
      Complex complex5 = complex4.INF.subtract(complex0);
      complex4.I.hashCode();
      complex5.ONE.getArgument();
      Complex complex6 = complex0.asin();
      Complex complex7 = complex6.INF.conjugate();
      Complex complex8 = complex6.ONE.sinh();
      complex8.tanh();
      complex8.ONE.subtract(complex3);
      complex0.getImaginary();
      complex0.NaN.negate();
      complex7.sinh();
      complex2.cos();
      complex5.getArgument();
      complex0.cos();
      complex3.cosh();
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.conjugate();
      Complex complex2 = complex1.exp();
      complex2.getReal();
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.ONE.tan();
      Complex complex2 = Complex.ZERO;
      complex0.divide(complex2);
      complex0.ONE.hashCode();
      Complex complex3 = complex1.log();
      complex3.I.tanh();
      Complex complex4 = complex0.exp();
      complex1.acos();
      complex4.conjugate();
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.NaN.createComplex(0.0, 0.0);
      Complex complex2 = complex0.negate();
      complex2.INF.subtract(complex1);
      Complex complex3 = complex0.asin();
      Complex complex4 = complex3.ONE.sqrt();
      complex3.divide(complex0);
      complex4.abs();
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.cosh();
      complex0.INF.log();
      Complex complex2 = complex0.I.sin();
      complex2.ZERO.hashCode();
      complex0.INF.hashCode();
      complex2.NaN.log();
      complex0.atan();
      Complex complex3 = complex1.tanh();
      complex3.ONE.asin();
      complex1.multiply(0.0);
      complex3.exp();
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = Complex.INF;
      complex0.ONE.multiply(complex1);
      complex0.readResolve();
      complex0.log();
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      complex0.sin();
      Complex complex1 = new Complex((-233.95), (-233.95));
      complex1.I.sin();
      complex0.multiply(complex1);
      complex1.atan();
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      complex0.I.exp();
      Complex complex1 = Complex.ONE;
      Complex complex2 = complex0.pow(complex1);
      complex1.I.hashCode();
      complex1.sinh();
      complex1.getArgument();
      complex0.isNaN();
      complex2.toString();
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      Complex complex0 = Complex.I;
      Object object0 = complex0.readResolve();
      complex0.equals(object0);
      complex0.getImaginary();
      complex0.NaN.getField();
      Complex complex1 = Complex.I;
      complex1.abs();
      complex0.divide(complex1);
      complex1.getImaginary();
      complex0.negate();
      complex0.tan();
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = Complex.I;
      Complex complex2 = complex1.ONE.divide(complex0);
      Complex complex3 = complex0.multiply(complex1);
      complex3.I.sin();
      complex3.I.divide(complex1);
      complex1.multiply((-1.0));
      complex2.toString();
  }

  @Test(timeout = 4000)
  public void test61()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.exp();
  }

  @Test(timeout = 4000)
  public void test62()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.NaN.acos();
      Complex complex2 = Complex.ZERO;
      complex2.I.cos();
      Complex complex3 = complex0.multiply(complex2);
      complex0.getField();
      Complex complex4 = complex2.subtract(complex1);
      complex0.exp();
      complex4.tanh();
      complex3.getField();
  }

  @Test(timeout = 4000)
  public void test63()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      complex0.NaN.toString();
      complex0.isNaN();
      complex0.equals("(NaN, NaN)");
      Complex complex1 = complex0.tanh();
      complex1.sqrt();
      complex0.divide(complex1);
      complex1.isNaN();
      complex1.tan();
  }

  @Test(timeout = 4000)
  public void test64()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      complex0.INF.nthRoot(289);
      Complex complex1 = complex0.createComplex((-1.0), (-1.0));
      Complex complex2 = complex0.multiply(complex1);
      complex2.negate();
      complex1.readResolve();
      Complex complex3 = complex1.acos();
      complex0.isInfinite();
      complex3.nthRoot(1);
  }

  @Test(timeout = 4000)
  public void test65()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.I.conjugate();
      Complex complex2 = complex1.I.multiply(complex0);
      complex2.divide(complex1);
      Complex complex3 = complex0.cos();
      complex3.multiply(2.0);
      complex3.hashCode();
  }

  @Test(timeout = 4000)
  public void test66()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.conjugate();
      complex0.abs();
      Complex complex2 = complex0.tan();
      complex2.ONE.createComplex(1.0, 0.0);
      complex1.isNaN();
  }

  @Test(timeout = 4000)
  public void test67()  throws Throwable  {
      Complex complex0 = new Complex(2264.9703, 2.0);
      complex0.sqrt1z();
  }

  @Test(timeout = 4000)
  public void test68()  throws Throwable  {
      Complex complex0 = new Complex((-3098.57109711973), (-3098.57109711973));
      Complex complex1 = Complex.I;
      complex0.pow(complex1);
  }

  @Test(timeout = 4000)
  public void test69()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = Complex.ZERO;
      complex1.ONE.cosh();
      Complex complex2 = complex0.sin();
      complex2.INF.tan();
      complex2.I.toString();
      complex2.INF.getArgument();
      complex2.negate();
      Complex complex3 = complex2.ZERO.subtract(complex1);
      try { 
        complex3.ZERO.nthRoot((-1877));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: -1,877
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test70()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.NaN.nthRoot(233);
      complex0.INF.toString();
      complex0.readResolve();
      Complex complex1 = Complex.I;
      Complex complex2 = complex1.I.exp();
      complex2.ZERO.asin();
      complex2.ZERO.cos();
      complex0.add(complex1);
  }

  @Test(timeout = 4000)
  public void test71()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.negate();
      Complex complex2 = complex1.NaN.exp();
      complex2.ONE.cos();
      Complex complex3 = complex1.sin();
      Complex complex4 = complex3.sinh();
      try { 
        complex4.nthRoot(0);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: 0
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test72()  throws Throwable  {
      Complex complex0 = Complex.I;
      Complex complex1 = complex0.tan();
      complex0.cosh();
      complex0.getReal();
      try { 
        complex1.nthRoot((-711));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: -711
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test73()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = Complex.INF;
      complex0.atan();
      complex0.add(complex1);
      complex0.acos();
  }

  @Test(timeout = 4000)
  public void test74()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.ONE.sqrt();
      Complex complex2 = complex0.conjugate();
      complex2.isNaN();
      complex1.I.getField();
      complex2.sqrt1z();
      complex1.isInfinite();
  }

  @Test(timeout = 4000)
  public void test75()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      complex0.isNaN();
  }

  @Test(timeout = 4000)
  public void test76()  throws Throwable  {
      Complex complex0 = new Complex(Double.NaN, Double.NaN);
      complex0.abs();
      Complex complex1 = complex0.conjugate();
      Complex complex2 = complex0.cos();
      Complex complex3 = complex2.INF.createComplex(Double.NaN, Double.NaN);
      Complex complex4 = complex3.ONE.sin();
      Complex complex5 = complex2.sqrt1z();
      Complex complex6 = complex2.tanh();
      Complex complex7 = complex6.ZERO.createComplex(Double.NaN, Double.NaN);
      Complex complex8 = complex5.multiply(726.3411474880684);
      complex2.tan();
      Complex complex9 = complex8.ZERO.conjugate();
      complex8.ZERO.getField();
      Complex complex10 = complex5.multiply(complex2);
      complex10.ZERO.toString();
      Complex complex11 = complex4.multiply(Double.NaN);
      complex4.cos();
      Complex complex12 = complex7.subtract(complex11);
      complex12.ONE.log();
      Complex complex13 = complex9.tan();
      complex13.INF.getField();
      complex11.I.pow(complex1);
      Complex complex14 = complex7.acos();
      Complex complex15 = complex14.subtract(complex13);
      complex4.add(complex11);
      complex15.abs();
      complex3.subtract(complex14);
      complex0.multiply(1581.6747138953488);
      complex1.createComplex(726.3411474880684, Double.NaN);
  }

  @Test(timeout = 4000)
  public void test77()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = complex0.NaN.sinh();
      complex1.ONE.tan();
      complex1.INF.divide(complex0);
      complex0.hashCode();
      int int0 = (-1);
      complex1.ZERO.conjugate();
      try { 
        complex1.INF.nthRoot((-1));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // cannot compute nth root for null or negative n: -1
         //
         verifyException("org.apache.commons.math.MathRuntimeException", e);
      }
  }

  @Test(timeout = 4000)
  public void test78()  throws Throwable  {
      Complex complex0 = Complex.I;
      complex0.getField();
      Complex complex1 = Complex.ZERO;
      Complex complex2 = complex0.INF.cosh();
      complex2.ZERO.getArgument();
      Complex complex3 = complex0.ZERO.cosh();
      Complex complex4 = complex3.INF.asin();
      Complex complex5 = complex0.add(complex1);
      complex5.conjugate();
      complex0.cosh();
      Complex complex6 = complex2.exp();
      Complex complex7 = complex5.conjugate();
      complex3.ONE.sqrt1z();
      Complex complex8 = complex6.multiply(0.970207946773875);
      complex2.acos();
      complex6.INF.acos();
      Complex complex9 = complex6.atan();
      complex9.ZERO.sin();
      Complex complex10 = complex9.INF.acos();
      complex10.INF.cosh();
      complex1.ZERO.acos();
      complex1.nthRoot(2644);
      complex3.toString();
      Complex complex11 = complex4.sin();
      complex7.nthRoot(7);
      Complex complex12 = complex8.multiply(complex0);
      complex12.I.cosh();
      complex6.sinh();
      complex4.atan();
      complex6.cos();
      complex8.abs();
      Complex complex13 = complex11.negate();
      complex13.multiply(0.0);
      complex0.toString();
      complex2.multiply(0.0);
      complex13.exp();
  }

  @Test(timeout = 4000)
  public void test79()  throws Throwable  {
      Complex complex0 = Complex.ONE;
      Complex complex1 = complex0.tan();
      complex1.ONE.asin();
      complex1.I.sin();
      complex0.nthRoot(3389);
      Complex complex2 = complex1.log();
      Complex complex3 = complex0.multiply(470.9517266533487);
      Complex complex4 = complex3.ONE.atan();
      complex4.ONE.asin();
      Complex complex5 = complex3.conjugate();
      Complex complex6 = complex3.createComplex(470.9517266533487, (-3222.360428646267));
      Complex complex7 = complex6.pow(complex2);
      Complex complex8 = complex7.I.log();
      Complex complex9 = complex1.multiply((-3222.360428646267));
      Complex complex10 = complex2.divide(complex0);
      complex10.INF.abs();
      complex10.ONE.sin();
      complex8.ONE.abs();
      complex5.nthRoot(7);
      complex9.sqrt();
      complex0.cos();
      complex7.getField();
      complex8.getField();
      Complex complex11 = new Complex(7, Double.POSITIVE_INFINITY);
      complex10.atan();
      Complex complex12 = complex7.cosh();
      complex9.conjugate();
      complex2.divide(complex12);
  }

  @Test(timeout = 4000)
  public void test80()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.ONE.sinh();
      Complex complex2 = complex0.INF.sin();
      Complex complex3 = complex2.INF.negate();
      Complex complex4 = complex3.INF.log();
      Complex complex5 = complex4.I.tan();
      Complex complex6 = complex0.ONE.atan();
      complex0.nthRoot(1134);
      complex2.readResolve();
      Complex complex7 = complex2.exp();
      complex1.isInfinite();
      complex7.I.nthRoot(1134);
      Complex complex8 = complex0.cosh();
      Complex complex9 = complex7.cosh();
      complex9.I.hashCode();
      Complex complex10 = complex8.add(complex0);
      complex10.INF.readResolve();
      complex8.nthRoot(1);
      complex8.tanh();
      complex1.getField();
      complex9.INF.tan();
      Complex complex11 = complex8.createComplex(1, 1520.788375796797);
      Complex complex12 = complex6.asin();
      complex12.ZERO.add(complex6);
      Object object0 = new Object();
      complex5.equals(object0);
      Complex complex13 = complex10.sqrt();
      Complex complex14 = complex9.pow(complex8);
      complex1.subtract(complex2);
      complex11.tanh();
      complex13.equals(complex10);
      complex5.createComplex(1134, 1134);
      complex5.sin();
      complex14.cosh();
  }

  @Test(timeout = 4000)
  public void test81()  throws Throwable  {
      Complex complex0 = Complex.ZERO;
      Complex complex1 = complex0.ZERO.acos();
      complex0.INF.toString();
      Complex complex2 = complex0.I.subtract(complex1);
      complex0.readResolve();
      Complex complex3 = complex0.createComplex(0.0, 2519.1088576414763);
      Complex complex4 = complex3.NaN.conjugate();
      Complex complex5 = complex0.I.negate();
      complex0.isInfinite();
      complex5.NaN.getField();
      complex0.getImaginary();
      complex5.equals(complex1);
      complex2.conjugate();
      complex4.getField();
  }

  @Test(timeout = 4000)
  public void test82()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.negate();
      complex0.abs();
      Complex complex2 = complex0.multiply(Double.NaN);
      Complex complex3 = complex0.multiply(complex2);
      Complex complex4 = complex0.multiply(Double.NaN);
      complex3.log();
      Complex complex5 = complex0.cosh();
      Complex complex6 = complex4.add(complex1);
      Complex complex7 = complex0.multiply(complex1);
      Complex complex8 = complex7.multiply(complex1);
      complex8.NaN.divide(complex4);
      Complex complex9 = complex4.INF.createComplex(0.0, (-223.32796005178));
      complex9.I.pow(complex7);
      complex9.INF.multiply(0.0);
      complex7.INF.getArgument();
      Complex complex10 = complex6.divide(complex1);
      Complex complex11 = complex10.sqrt1z();
      complex11.INF.negate();
      complex11.hashCode();
      Complex complex12 = complex5.sin();
      complex11.multiply(complex12);
  }

  @Test(timeout = 4000)
  public void test83()  throws Throwable  {
      Complex complex0 = Complex.NaN;
      Complex complex1 = complex0.atan();
      Complex complex2 = complex1.sin();
      Complex complex3 = complex2.INF.sinh();
      Complex complex4 = complex0.INF.exp();
      complex4.getArgument();
      Complex complex5 = complex4.I.sqrt();
      complex3.INF.sin();
      Complex complex6 = Complex.NaN;
      complex3.ONE.multiply(complex6);
      complex5.INF.atan();
      complex2.I.subtract(complex0);
      Complex complex7 = complex2.multiply(Double.POSITIVE_INFINITY);
      Complex complex8 = complex7.ZERO.sqrt1z();
      complex5.abs();
      complex8.ZERO.sqrt1z();
      complex4.tan();
      Complex complex9 = complex8.cosh();
      complex3.cosh();
      Object object0 = new Object();
      complex9.equals(object0);
      complex4.tanh();
  }

  @Test(timeout = 4000)
  public void test84()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = Complex.ZERO;
      Complex complex2 = Complex.NaN;
      Complex complex3 = complex1.tanh();
      Complex complex4 = complex1.NaN.asin();
      complex0.abs();
      Complex complex5 = complex1.INF.asin();
      Complex complex6 = complex5.I.cosh();
      Complex complex7 = complex0.ONE.add(complex3);
      complex3.ZERO.toString();
      Complex complex8 = complex1.pow(complex2);
      Complex complex9 = complex4.ZERO.acos();
      complex3.INF.pow(complex7);
      Complex complex10 = complex8.ONE.cosh();
      complex3.tanh();
      complex8.nthRoot(557);
      complex3.getReal();
      complex4.getImaginary();
      complex3.pow(complex0);
      Complex complex11 = complex6.acos();
      complex11.I.tanh();
      complex6.readResolve();
      complex8.log();
      complex4.toString();
      complex10.isNaN();
      complex0.getImaginary();
      complex5.conjugate();
      complex4.toString();
      complex9.conjugate();
      complex6.readResolve();
  }

  @Test(timeout = 4000)
  public void test85()  throws Throwable  {
      Complex complex0 = Complex.INF;
      Complex complex1 = Complex.ZERO;
      Complex complex2 = complex0.INF.divide(complex1);
      Complex complex3 = new Complex(0.0, 0.3999999761581421);
      Complex complex4 = complex3.ONE.sinh();
      Complex complex5 = complex3.ZERO.sinh();
      complex5.ONE.getArgument();
      Complex complex6 = complex5.exp();
      Complex complex7 = complex3.NaN.tan();
      Complex complex8 = complex7.ONE.cosh();
      assertEquals(0.0, complex8.getImaginary(), 0.01);
      
      Complex complex9 = complex4.NaN.divide(complex6);
      Complex complex10 = complex2.NaN.divide(complex3);
      Complex complex11 = new Complex(1431.902346, 1431.902346);
      complex11.I.abs();
      Complex complex12 = complex0.add(complex11);
      complex4.ZERO.negate();
      Complex complex13 = complex10.INF.asin();
      Complex complex14 = complex13.I.tan();
      Complex complex15 = complex2.sqrt1z();
      complex11.hashCode();
      Complex complex16 = complex5.NaN.subtract(complex11);
      complex4.getArgument();
      complex15.isNaN();
      complex4.getField();
      complex10.ONE.getArgument();
      Complex complex17 = complex11.divide(complex12);
      Complex complex18 = complex3.acos();
      Complex complex19 = complex10.asin();
      assertSame(complex19, complex15);
      
      complex5.toString();
      complex10.readResolve();
      complex5.isInfinite();
      double double0 = complex11.abs();
      assertEquals(2025.0157177070523, double0, 0.01);
      
      Complex complex20 = complex16.multiply(complex6);
      complex20.multiply(complex18);
      assertEquals(1.5707963267948966, complex18.getReal(), 0.01);
      assertEquals((-0.3900352976341058), complex18.getImaginary(), 0.01);
      
      complex9.acos();
      assertEquals(0.0, complex6.getImaginary(), 0.01);
      assertTrue(complex5.equals((Object)complex17));
      
      complex14.multiply(complex12);
      assertSame(complex10, complex7);
      assertEquals(0.761594155955765, complex14.getImaginary(), 0.01);
      assertFalse(complex1.equals((Object)complex6));
      assertEquals(0.0, complex14.getReal(), 0.01);
      assertSame(complex2, complex13);
  }

  @Test(timeout = 4000)
  public void test86()  throws Throwable  {
      Complex complex0 = Complex.INF;
      complex0.I.abs();
      Complex complex1 = complex0.ZERO.atan();
      Complex complex2 = complex0.log();
      Complex complex3 = complex2.NaN.acos();
      complex3.ONE.nthRoot(4943);
      Complex complex4 = complex2.INF.sqrt();
      Complex complex5 = complex0.sinh();
      Complex complex6 = complex5.ZERO.sinh();
      complex3.pow(complex0);
      Complex complex7 = complex5.ZERO.createComplex((-796.6212383370555), (-5828.0));
      Complex complex8 = complex7.ONE.sin();
      complex1.I.atan();
      Complex complex9 = complex5.INF.divide(complex3);
      complex9.ZERO.add(complex2);
      complex9.ONE.tanh();
      complex4.NaN.getArgument();
      Complex complex10 = complex3.exp();
      complex10.INF.sqrt1z();
      complex0.cosh();
      complex2.negate();
      complex1.getImaginary();
      complex3.equals(complex4);
      complex4.abs();
      complex5.toString();
      Complex complex11 = complex0.sin();
      complex11.toString();
      complex0.multiply(complex10);
      complex4.toString();
      Complex complex12 = complex6.tanh();
      Complex complex13 = complex8.tanh();
      complex12.subtract(complex13);
      complex6.exp();
      complex1.readResolve();
  }
}
