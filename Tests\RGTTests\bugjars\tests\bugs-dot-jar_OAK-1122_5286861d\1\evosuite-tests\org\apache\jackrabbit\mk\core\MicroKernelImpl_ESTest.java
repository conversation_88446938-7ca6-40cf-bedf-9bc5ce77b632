/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 13:40:57 GMT 2019
 */

package org.apache.jackrabbit.mk.core;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.ByteArrayInputStream;
import org.apache.jackrabbit.mk.blobs.BlobStore;
import org.apache.jackrabbit.mk.blobs.DbBlobStore;
import org.apache.jackrabbit.mk.core.MicroKernelImpl;
import org.apache.jackrabbit.mk.core.Repository;
import org.apache.jackrabbit.mk.json.JsopBuilder;
import org.apache.jackrabbit.mk.model.Id;
import org.apache.jackrabbit.mk.model.StoredCommit;
import org.apache.jackrabbit.mk.model.StoredNode;
import org.apache.jackrabbit.mk.store.RevisionProvider;
import org.apache.jackrabbit.mk.store.RevisionStore;
import org.apache.jackrabbit.mk.util.NodeFilter;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.testdata.EvoSuiteFile;
import org.evosuite.runtime.testdata.FileSystemHandling;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MicroKernelImpl_ESTest extends MicroKernelImpl_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Id.fromLong(1901L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((Id) null).when(revisionStore0).getHeadCommitId();
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Id id0 = Id.fromString("");
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.waitForCommit((String) null, (-532L));
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getHeadRevision();
      assertEquals("0000000000000000", string0);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Id id0 = new Id(byteArray0);
      Id id1 = Id.fromLong(0L);
      StoredCommit storedCommit0 = new StoredCommit(id1, id0, 0L, id1, (String) null, (String) null, (Id) null);
      RevisionStore.PutToken revisionStore_PutToken0 = mock(RevisionStore.PutToken.class, new ViolatedAssumptionAnswer());
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      doReturn(revisionStore_PutToken0).when(revisionStore0).createPutToken();
      doReturn(id1).when(revisionStore0).putCommit(any(org.apache.jackrabbit.mk.store.RevisionStore.PutToken.class) , any(org.apache.jackrabbit.mk.model.MutableCommit.class));
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.branch((String) null);
      assertEquals("0000000000000000", string0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      byte[] byteArray0 = new byte[6];
      ByteArrayInputStream byteArrayInputStream0 = new ByteArrayInputStream(byteArray0, (-1948), (byte)93);
      try { 
        microKernelImpl0.write(byteArrayInputStream0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.merge("0000000000000000", "wG$eC(CzLNXvst-tu\"");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.init((String) null);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Id id0 = new Id(byteArray0);
      String string0 = null;
      FileSystemHandling.shouldAllThrowIOExceptions();
      Id id1 = Id.fromString("");
      FileSystemHandling.shouldThrowIOException((EvoSuiteFile) null);
      StoredCommit storedCommit0 = new StoredCommit(id0, id1, 2680L, id0, "1,X)We|@ppW(*8Xz'[", "", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0, (StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string1 = microKernelImpl0.getRevisionHistory(1716L, (-1), "");
      assertEquals("[{\"id\":\"\",\"ts\":2680,\"msg\":\"1,X)We|@ppW(*8Xz'[\"},{\"id\":\"\",\"ts\":2680,\"msg\":\"1,X)We|@ppW(*8Xz'[\"}]", string1);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Id id0 = new Id(byteArray0);
      String string0 = null;
      FileSystemHandling.shouldAllThrowIOExceptions();
      Id id1 = Id.fromString("");
      StoredCommit storedCommit0 = new StoredCommit(id0, id1, 2680L, id0, "1,X)We|@ppW(*8Xz'[", "", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0, (StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.getRevisionHistory(1716L, 9986, "");
      String string1 = "A61Us~nP!RJZ*ip";
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal(string0, string1, string0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      repository0.shutDown();
      try { 
        microKernelImpl0.getHeadRevision();
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: not initialized
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getChildNodeCount("\"#K", "db");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Repository repository0 = new Repository((String) null);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: not initialized
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      FileSystemHandling.shouldAllThrowIOExceptions();
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl("");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.io.IOException: Simulated IOException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("\"#K", "sc", "", 42);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // \"#K
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      JsopBuilder jsopBuilder0 = new JsopBuilder();
      RevisionProvider revisionProvider0 = mock(RevisionProvider.class, new ViolatedAssumptionAnswer());
      StoredNode storedNode0 = new StoredNode((Id) null, revisionProvider0);
      microKernelImpl0.toJson(jsopBuilder0, storedNode0, 43, 43, 43, true, (NodeFilter) null);
      assertEquals("\":childNodeCount\":0", jsopBuilder0.toString());
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.getLength((String) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.branch("1,X)We|@ppW(*8Xz'[");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 1,X)We|@ppW(*8Xz'[
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 2680L, id0, "1,X)We|@ppW(*8Xz'[", "", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.branch((String) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.commit("0000000000000000", "0000000000000000", "1N.?w", "org.apache.jackrabbit.mk.core.MicroKernelImpl");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.commit("", (String) null, "", (String) null);
      assertEquals("", string0);
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getNodes("Eu),C4j'Pi7ji", (String) null, 3248, (-65L), 3248, (String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Eu),C4j'Pi7ji
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.getChildNodeCount("\"#K", "sc");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getChildNodeCount("\"#K", "sc");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // sc
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists("\"o8mb.EXIlo(sfoy", "1N~-{5Q~r8*tL9h0iE");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 1N~-{5Q~r8*tL9h0iE
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("ALaW:JKQ%", (String) null, "ALaW:JKQ%", 42);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // ALaW:JKQ%
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff((String) null, "sc", "", 42);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // sc
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal("0000000000000000", "changes", ":conflict");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // changes
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal("0000000000000000", (String) null, "K\"4).");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      byte[] byteArray0 = new byte[0];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.getRevisionHistory(1716L, (-1), "");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.waitForCommit((String) null, (-532L));
      assertEquals("0000000000000000", string0);
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl("");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl();
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // com/google/common/cache/Weigher
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.rebase("", "GC started.");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // GC started.
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getHeadRevision();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }
}
