/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 13:47:32 GMT 2019
 */

package org.apache.jackrabbit.mk.core;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.jackrabbit.mk.blobs.DbBlobStore;
import org.apache.jackrabbit.mk.blobs.FileBlobStore;
import org.apache.jackrabbit.mk.blobs.MemoryBlobStore;
import org.apache.jackrabbit.mk.core.MicroKernelImpl;
import org.apache.jackrabbit.mk.core.Repository;
import org.apache.jackrabbit.mk.model.Id;
import org.apache.jackrabbit.mk.model.StoredCommit;
import org.apache.jackrabbit.mk.model.StoredNode;
import org.apache.jackrabbit.mk.store.RevisionStore;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.testdata.FileSystemHandling;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MicroKernelImpl_ESTest extends MicroKernelImpl_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      byteArray0[0] = (byte) (-20);
      byteArray0[1] = (byte)62;
      byteArray0[2] = (byte)95;
      byteArray0[3] = (byte)3;
      byteArray0[4] = (byte) (-68);
      byteArray0[5] = (byte)16;
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (byte) (-68), id0, "Q[a=f_Nq_", "Q[a=f_Nq_", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getHeadRevision();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      byteArray0[0] = (byte) (-46);
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 0L, id0, "", "", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((Id) null).when(revisionStore0).getHeadCommitId();
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((Id) null).when(revisionStore0).getHeadCommitId();
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Repository repository0 = new Repository("KNcTj5HE");
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: not initialized
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      FileSystemHandling.shouldAllThrowIOExceptions();
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(":childNodeCount");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.io.IOException: Simulated IOException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Id id0 = Id.fromLong(559L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.merge("", "F69`CZFF");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (byte)95, id0, "2a(e=H", "2a(e=H", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      doReturn((StoredNode) null).when(revisionStore0).getNode(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn((StoredNode) null).when(revisionStore0).getRootNode(any(org.apache.jackrabbit.mk.model.Id.class));
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getRevisionHistory((-1L), 1282, "2a(e=H");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.model.DiffBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (byte) (-68), id0, "Q[a=f_Nq_", "Q[a=f_Nq_", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getRevisionHistory((-1L), 1272, "2a(e=H");
      assertEquals("[]", string0);
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      byte[] byteArray0 = new byte[1];
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 0L, id0, "", "", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((Id) null).when(revisionStore0).getHeadCommitId();
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl("");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl();
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // com/google/common/cache/Weigher
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }
}
