/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 13:49:38 GMT 2019
 */

package org.apache.jackrabbit.mk.core;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.ByteArrayInputStream;
import java.io.FileDescriptor;
import org.apache.jackrabbit.mk.blobs.BlobStore;
import org.apache.jackrabbit.mk.blobs.FileBlobStore;
import org.apache.jackrabbit.mk.blobs.MemoryBlobStore;
import org.apache.jackrabbit.mk.core.MicroKernelImpl;
import org.apache.jackrabbit.mk.core.Repository;
import org.apache.jackrabbit.mk.json.JsopBuilder;
import org.apache.jackrabbit.mk.model.Id;
import org.apache.jackrabbit.mk.model.MutableNode;
import org.apache.jackrabbit.mk.model.StoredCommit;
import org.apache.jackrabbit.mk.model.StoredNode;
import org.apache.jackrabbit.mk.store.RevisionProvider;
import org.apache.jackrabbit.mk.store.RevisionStore;
import org.apache.jackrabbit.mk.util.NodeFilter;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.mock.java.io.MockFileInputStream;
import org.evosuite.runtime.testdata.FileSystemHandling;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MicroKernelImpl_ESTest extends MicroKernelImpl_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getNodes("o^P\\(", "?m]5T Ac>r$T", 971, (-1695L), 127, "q~Gr)");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // o^P\\(
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("", "3]A_N|(i}ai", "6e,v85", (byte)94);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // 3]A_N|(i}ai
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 645L, id0, "org.apache.jackrabbit.mk.store.IdFactory$1", "org.apache.jackrabbit.mk.store.IdFactory$1", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      doReturn((StoredNode) null).when(revisionStore0).getNode(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn((StoredNode) null).when(revisionStore0).getRootNode(any(org.apache.jackrabbit.mk.model.Id.class));
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getRevisionHistory(645L, (-1182), "org.apache.jackrabbit.mk.store.IdFactory$1");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.model.DiffBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getRevisionHistory((byte)23, (byte)0, "@g_SBPY:UvB\t@");
      assertEquals("[]", string0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getHeadCommit();
      FileBlobStore fileBlobStore0 = new FileBlobStore("org.apache.jackrabbit.mk.model.StagedNodeTree");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      repository0.getHeadCommit();
      RevisionStore revisionStore1 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore1).getHeadCommitId();
      Repository repository1 = new Repository(revisionStore1, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository1);
      repository1.init();
      // Undeclared exception!
      try { 
        microKernelImpl0.getHeadRevision();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byteArray0[0] = (byte)23;
      byteArray0[1] = (byte)23;
      byteArray0[2] = (byte)23;
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.checkpoint(945L);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      JsopBuilder jsopBuilder0 = new JsopBuilder();
      try { 
        microKernelImpl0.toJson(jsopBuilder0, (StoredNode) null, 2177, 127, 185, true, (NodeFilter) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      Id id0 = Id.fromLong(656L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      repository0.shutDown();
      // Undeclared exception!
      try { 
        microKernelImpl0.rebase("", "");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: not initialized
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists("}\"+VQP7VmD", "");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      byteArray0[1] = (byte)98;
      byteArray0[2] = (byte)98;
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (byte)98, id0, "msg", "X;", id0);
      RevisionProvider revisionProvider0 = mock(RevisionProvider.class, new ViolatedAssumptionAnswer());
      StoredNode storedNode0 = new StoredNode(id0, revisionProvider0);
      RevisionProvider revisionProvider1 = mock(RevisionProvider.class, new ViolatedAssumptionAnswer());
      MutableNode mutableNode0 = new MutableNode(storedNode0, revisionProvider1);
      RevisionProvider revisionProvider2 = mock(RevisionProvider.class, new ViolatedAssumptionAnswer());
      StoredNode storedNode1 = new StoredNode(id0, mutableNode0, revisionProvider2);
      RevisionStore.PutToken revisionStore_PutToken0 = mock(RevisionStore.PutToken.class, new ViolatedAssumptionAnswer());
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      doReturn(storedNode1).when(revisionStore0).getNode(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(storedNode0, storedNode0, (StoredNode) null).when(revisionStore0).getRootNode(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(revisionStore_PutToken0).when(revisionStore0).createPutToken();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      storedCommit0.toString();
      microKernelImpl0.getLength("");
      try { 
        microKernelImpl0.merge("", "`G]Q-jw_A");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.merge("o^P\\(", "7U");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // o^P\\(
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      byteArray0[1] = (byte)94;
      byteArray0[2] = (byte)0;
      byteArray0[3] = (byte) (-122);
      byteArray0[5] = (byte)94;
      byteArray0[6] = (byte)3;
      byteArray0[7] = (byte) (-43);
      ByteArrayInputStream byteArrayInputStream0 = new ByteArrayInputStream(byteArray0, (-951), (byte) (-122));
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (-1503L), id0, "", "+*Ws^", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0, (StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getRevisionHistory((-1512L), (byte) (-43), "");
      assertEquals("[{\"id\":\"005e0086005e03d5\",\"ts\":-1503,\"msg\":\"\"},{\"id\":\"005e0086005e03d5\",\"ts\":-1503,\"msg\":\"\"}]", string0);
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getNodes((String) null, (String) null, (-2523), (-3470L), 351, (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.getLength(":conflict");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      repository0.shutDown();
      try { 
        microKernelImpl0.getHeadRevision();
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: not initialized
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Id id0 = Id.fromLong(3600000L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getChildNodeCount("\"#@", "?W*^~*; _h1n");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // ?W*^~*; _h1n
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.branch("");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl((Repository) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      FileSystemHandling.shouldAllThrowIOExceptions();
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl((String) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.io.IOException: Simulated IOException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      JsopBuilder jsopBuilder0 = new JsopBuilder();
      RevisionProvider revisionProvider0 = mock(RevisionProvider.class, new ViolatedAssumptionAnswer());
      StoredNode storedNode0 = new StoredNode(id0, revisionProvider0);
      microKernelImpl0.toJson(jsopBuilder0, storedNode0, 1, 59, 1471, true, (NodeFilter) null);
      assertEquals("\":childNodeCount\":0", jsopBuilder0.toString());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      byte[] byteArray0 = new byte[9];
      ByteArrayInputStream byteArrayInputStream0 = new ByteArrayInputStream(byteArray0, (byte) (-32), (byte) (-32));
      // Undeclared exception!
      try { 
        microKernelImpl0.write(byteArrayInputStream0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      FileDescriptor fileDescriptor0 = new FileDescriptor();
      MockFileInputStream mockFileInputStream0 = new MockFileInputStream(fileDescriptor0);
      try { 
        microKernelImpl0.write(mockFileInputStream0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.read("YKa<k", (-1723L), byteArray0, (byte)23, 5356);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: YKa<k
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.merge("", "");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Id id0 = Id.fromLong(660L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.branch("?W*^~*; _h1n");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // ?W*^~*; _h1n
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.commit("", "", "B')", "B')");
      assertEquals("B')", string0);
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.commit("changeDeletedNode", "9Sb<16zNjhLTXE", "B')", "B')");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.commit("changeDeletedNode", "9Sb<16zNjhLTXE", "B')", "B')");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.commit("", "9Sb<16zNjhLTXE", "B')", "B')");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // B')
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getNodes("/o^P\\(", "N3Y", (-3547), (-1723L), (-3547), "N3Y");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // N3Y
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getNodes("", "null", 127, (-1735L), (byte)23, "");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // null
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getChildNodeCount("", "");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists("The minimum size must be bigger than a content hash itself; limit = 48", "}\"+VQP7VmD");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // }\"+VQP7VmD
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.diff("", "", "", 1471);
      assertEquals("", string0);
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff((String) null, "", "", (-957));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // depth
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("@&5#v6*^4^b5(O", "@&5#v6*^4^b5(O", (String) null, (byte)23);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // @&5#v6*^4^b5(O
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal("}uj", "}\"+VQP7VmD", "tWc & ?F+7nG");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // }uj
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Id id0 = Id.fromLong(656L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal("%_=}4", "", (String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // %_=}4
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Id id0 = Id.fromLong(656L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal("%_=}4", "", "%_=}4");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Id id0 = Id.fromLong(656L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.getJournal("", "", "");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 645L, id0, "org.apache.jackrabbit.mk.store.IdFactory$1", "org.apache.jackrabbit.mk.store.IdFactory$1", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getRevisionHistory(1471, 127, "}\"+VQP7VmD");
      assertEquals("[]", string0);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists("The minimum size must be bigger than a content hash itself; limit = 48", "}\"+VQP7VmD");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl("K#AmmG");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Id id0 = Id.fromLong(645L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, (BlobStore) null);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.init("3REjHBh_K30N(j;");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl();
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // com/google/common/cache/Weigher
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.rebase("", "%_=}4");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // %_=}4
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getChildNodeCount("", (String) null);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }
}
