/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 13:53:51 GMT 2019
 */

package org.apache.jackrabbit.mk.core;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.BufferedInputStream;
import java.io.InputStream;
import org.apache.jackrabbit.mk.blobs.FileBlobStore;
import org.apache.jackrabbit.mk.blobs.MemoryBlobStore;
import org.apache.jackrabbit.mk.core.MicroKernelImpl;
import org.apache.jackrabbit.mk.core.Repository;
import org.apache.jackrabbit.mk.model.Id;
import org.apache.jackrabbit.mk.model.MutableNode;
import org.apache.jackrabbit.mk.model.StoredCommit;
import org.apache.jackrabbit.mk.model.StoredNode;
import org.apache.jackrabbit.mk.store.RevisionProvider;
import org.apache.jackrabbit.mk.store.RevisionStore;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.testdata.EvoSuiteFile;
import org.evosuite.runtime.testdata.FileSystemHandling;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MicroKernelImpl_ESTest extends MicroKernelImpl_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      Id id0 = Id.fromLong((-1122L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.commit("$5k6w?\"kEm", ".dat", " not found in revision ", "`fg{~]4Fm'B}rEZ");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      Id id0 = Id.fromLong((-1122L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists(".dat", "");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 0L, id0, (String) null, (String) null, id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      doReturn((StoredNode) null).when(revisionStore0).getNode(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn((StoredNode) null).when(revisionStore0).getRootNode(any(org.apache.jackrabbit.mk.model.Id.class));
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getRevisionHistory(0L, (-22), "Rt");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.model.DiffBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      byte[] byteArray0 = new byte[2];
      int int0 = microKernelImpl0.read("", 288L, byteArray0, 4, (-1391));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      long long0 = microKernelImpl0.getLength("");
      assertEquals(0L, long0);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.rebase("fffffffffffff9e8", ", ");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // , 
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      Id id0 = Id.fromLong((-1566L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.merge((String) null, "UTF-8");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      FileSystemHandling.shouldThrowIOException((EvoSuiteFile) null);
      Id id0 = Id.fromLong((-1122L));
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (-1266L), id0, "uEYORUS2JP0+}3", ".dat", id0);
      RevisionProvider revisionProvider0 = mock(RevisionProvider.class, new ViolatedAssumptionAnswer());
      MutableNode mutableNode0 = new MutableNode(revisionProvider0);
      RevisionProvider revisionProvider1 = mock(RevisionProvider.class, new ViolatedAssumptionAnswer());
      StoredNode storedNode0 = new StoredNode(id0, mutableNode0, revisionProvider1);
      RevisionStore.PutToken revisionStore_PutToken0 = mock(RevisionStore.PutToken.class, new ViolatedAssumptionAnswer());
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(storedCommit0, storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      doReturn(storedNode0).when(revisionStore0).getNode(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(storedNode0, storedNode0, (StoredNode) null).when(revisionStore0).getRootNode(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(revisionStore_PutToken0).when(revisionStore0).createPutToken();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.getRevisionHistory((-786L), (-2013265917), "^{L*R2'#|");
      microKernelImpl0.getRevisionHistory((-1122L), 0, "uEYORUS2JP0+}3");
      try { 
        microKernelImpl0.merge("", "^{L*R2'#|");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.merge(":childNodeCount", ":childNodeCount");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // :childNodeCount
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.init(":childNodeCount");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      Id id0 = Id.fromLong((-1122L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      repository0.shutDown();
      try { 
        microKernelImpl0.getRevisionHistory((-786L), (-2013265917), "^{L*R2'#|");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: not initialized
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      Id id0 = Id.fromLong((-1122L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getNodes((String) null, "Y|P3g50/0X9", 59, 0L, 59, (String) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal((String) null, "V1ky-1D-2J0[Y2v\"ri0", "m&-Y,8cP7");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      Id id0 = Id.fromLong((-1122L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getChildNodeCount(":conflict", "");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      Id id0 = Id.fromLong((-1122L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.commit((String) null, (String) null, (String) null, "");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      repository0.shutDown();
      try { 
        microKernelImpl0.checkpoint((-1560L));
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: not initialized
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.checkpoint((-1560L));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl((Repository) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      FileSystemHandling.shouldAllThrowIOExceptions();
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl("ts");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.io.IOException: Simulated IOException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      Id id0 = Id.fromLong((-1546L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      byte[] byteArray0 = new byte[2];
      try { 
        microKernelImpl0.read("m&-:Y,8P7", (-1L), byteArray0, (byte)0, (byte)0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: m&-:Y,8P7
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":c#uldNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.branch("+)FnXi_P|pM1uH|]");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // +)FnXi_P|pM1uH|]
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      Id id0 = Id.fromLong((-1546L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getHeadRevision();
      assertEquals("fffffffffffff9f6", string0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      BufferedInputStream bufferedInputStream0 = new BufferedInputStream((InputStream) null);
      try { 
        microKernelImpl0.write(bufferedInputStream0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.io.IOException: Stream closed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      byte[] byteArray0 = new byte[2];
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.read("m&-:Y,8P7", (-1560L), byteArray0, (byte)0, (byte)0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      Id id0 = Id.fromLong((-1122L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.getLength(".dat");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.getLength(":conflict");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: :conflict
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getNodes("0000000000000000", "Rt", 0, (-1199L), 0, (String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Rt
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.getNodes(":childNodeCount", "p_H", (-2040109463), 654L, 42, ".dat");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getChildNodeCount(":childNodeCount", ":childNodeCount");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // :childNodeCount
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists((String) null, (String) null);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists((String) null, "Rt");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // Rt
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (-1560L), id0, "", (String) null, id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.diff("fffffffffffff9ee", (String) null, "", 22);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("fffffffffffff9e8", ":childNodeCount", "", (-6));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // depth
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("fffffffffffff9ee", (String) null, "O", 22);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore("");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.diff("fffffffffffff9e8", (String) null, "", 22);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("fffffffffffff9e8", ":childNodeCount", (String) null, 22);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // :childNodeCount
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal(":childNodeCount", "m&-:Y,8P7", (String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // :childNodeCount
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal(":childNodeCount", "m&-:Y,8P7", (String) null);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal("fffffffffffff9e8", ":childNodeCount", "fffffffffffff9e8");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // :childNodeCount
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      Id id0 = Id.fromLong((-1122L));
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 0L, id0, "u/EYORUS2lJP0+}3", ".dat", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      doReturn((StoredNode) null).when(revisionStore0).getNode(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn((StoredNode) null).when(revisionStore0).getRootNode(any(org.apache.jackrabbit.mk.model.Id.class));
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getRevisionHistory((-786L), (-2013265917), "^{L*R2'#|");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.model.DiffBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      Id id0 = Id.fromLong(0L);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 0L, id0, (String) null, (String) null, id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getRevisionHistory((-4394L), 58, (String) null);
      assertEquals("[{\"id\":\"0000000000000000\",\"ts\":0,\"msg\":null}]", string0);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      Id id0 = Id.fromLong((-1122L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.getRevisionHistory((-1122L), 0, ".dat");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.checkpoint((-1560L));
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":c!ild&deCo=t");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      microKernelImpl0.dispose();
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.waitForCommit("m&-:Y,8P7", (-1753L));
      assertEquals("fffffffffffff9e8", string0);
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl("");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl();
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // com/google/common/cache/Weigher
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.rebase("fffffffffffff9e8", "fffffffffffff9e8");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      Id id0 = Id.fromLong((-1560L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      FileBlobStore fileBlobStore0 = new FileBlobStore(":childNodeCount");
      Repository repository0 = new Repository(revisionStore0, fileBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.checkpoint((-1560L));
      assertEquals("fffffffffffff9e8", string0);
  }
}
