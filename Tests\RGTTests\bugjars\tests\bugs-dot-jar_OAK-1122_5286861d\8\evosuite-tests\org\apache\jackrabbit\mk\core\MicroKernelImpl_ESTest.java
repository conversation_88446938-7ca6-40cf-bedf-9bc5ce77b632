/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 13:55:52 GMT 2019
 */

package org.apache.jackrabbit.mk.core;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import org.apache.jackrabbit.mk.blobs.DbBlobStore;
import org.apache.jackrabbit.mk.blobs.MemoryBlobStore;
import org.apache.jackrabbit.mk.core.MicroKernelImpl;
import org.apache.jackrabbit.mk.core.Repository;
import org.apache.jackrabbit.mk.model.Id;
import org.apache.jackrabbit.mk.model.StoredCommit;
import org.apache.jackrabbit.mk.store.RevisionStore;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.testdata.FileSystemHandling;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MicroKernelImpl_ESTest extends MicroKernelImpl_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test0()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((Id) null).when(revisionStore0).getHeadCommitId();
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test1()  throws Throwable  {
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository((RevisionStore) null, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test2()  throws Throwable  {
      FileSystemHandling.shouldAllThrowIOExceptions();
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl("");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.io.IOException: Simulated IOException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test3()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      Id id0 = new Id(byteArray0);
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists(" onto ", "R:O,m?D6n1=L2&W%|");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // R:O,m?D6n1=L2&W%|
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test4()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (byte) (-104), id0, "pKf&f.:", "b8y2m4p", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((Id) null).when(revisionStore0).getHeadCommitId();
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test5()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (-1097L), id0, "R:O,m?D6n1=L2&W%|", "R:O,m?D6n1=L2&W%|", id0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((Id) null).when(revisionStore0).getHeadCommitId();
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test6()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      Id id0 = new Id(byteArray0);
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.getRevisionHistory((byte) (-68), (-829), (String) null);
  }

  @Test(timeout = 4000)
  public void test7()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl("\u0004 .D'SVux$)M");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test8()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl();
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // com/google/common/cache/Weigher
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }
}
