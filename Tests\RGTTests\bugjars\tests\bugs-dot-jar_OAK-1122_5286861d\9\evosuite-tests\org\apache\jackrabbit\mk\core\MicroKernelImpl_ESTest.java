/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 13:58:04 GMT 2019
 */

package org.apache.jackrabbit.mk.core;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.io.ByteArrayInputStream;
import java.io.PipedInputStream;
import java.io.PushbackInputStream;
import java.io.SequenceInputStream;
import org.apache.jackrabbit.mk.blobs.DbBlobStore;
import org.apache.jackrabbit.mk.blobs.MemoryBlobStore;
import org.apache.jackrabbit.mk.core.MicroKernelImpl;
import org.apache.jackrabbit.mk.core.Repository;
import org.apache.jackrabbit.mk.json.JsopBuilder;
import org.apache.jackrabbit.mk.model.Id;
import org.apache.jackrabbit.mk.model.StoredCommit;
import org.apache.jackrabbit.mk.model.StoredNode;
import org.apache.jackrabbit.mk.store.RevisionProvider;
import org.apache.jackrabbit.mk.store.RevisionStore;
import org.apache.jackrabbit.mk.util.NodeFilter;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.evosuite.runtime.testdata.FileSystemHandling;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class MicroKernelImpl_ESTest extends MicroKernelImpl_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 1L, id0, "n=bYNAeVl.", "R", id0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      doReturn((StoredNode) null).when(revisionStore0).getNode(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn((StoredNode) null).when(revisionStore0).getRootNode(any(org.apache.jackrabbit.mk.model.Id.class));
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getRevisionHistory(1L, 42, " not found");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.model.DiffBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      PipedInputStream pipedInputStream0 = new PipedInputStream((byte)62);
      SequenceInputStream sequenceInputStream0 = new SequenceInputStream(pipedInputStream0, pipedInputStream0);
      PushbackInputStream pushbackInputStream0 = new PushbackInputStream(sequenceInputStream0, (byte)123);
      try { 
        microKernelImpl0.write(pushbackInputStream0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.io.IOException: Pipe not connected
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.rebase((String) null, "(>|WwI=;'s");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.read("^.Ky(F", (byte)118, byteArray0, (byte)118, (byte)118);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: ^.Ky(F
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists("n=bYNAeVl.", "R4wmX`V92w8&S9}");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // R4wmX`V92w8&S9}
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.merge("", "R4wmX`V92w8&S9}");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.merge((String) null, "changeDeletedProperty");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      FileSystemHandling.shouldAllThrowIOExceptions();
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.init("n=bYNAeVl.");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.io.IOException: Simulated IOException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.init("bf{%y!aiPnS");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      Id.fromLong((-214L));
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((Id) null).when(revisionStore0).getHeadCommitId();
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal((String) null, "R4wmX`V92w8&S9}", "\b");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getHeadRevision();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getChildNodeCount("n=bY]AXVl.", "3e7651");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.commit((String) null, ":id", "l$I]]NG", ">CABjn1Rvb");
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      repository0.shutDown();
      try { 
        microKernelImpl0.checkpoint(338L);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: not initialized
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.checkpoint((-749L));
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      Repository repository0 = new Repository("Z0@Hf8h");
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: not initialized
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      FileSystemHandling.shouldAllThrowIOExceptions();
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl("k)");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.io.IOException: Simulated IOException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getHeadRevision();
      assertEquals("000000", string0);
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionProvider revisionProvider0 = mock(RevisionProvider.class, new ViolatedAssumptionAnswer());
      StoredNode storedNode0 = new StoredNode(id0, revisionProvider0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      JsopBuilder jsopBuilder0 = new JsopBuilder();
      microKernelImpl0.toJson(jsopBuilder0, storedNode0, (byte)81, (byte)118, (byte)118, false, (NodeFilter) null);
      assertEquals("", jsopBuilder0.toString());
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      ByteArrayInputStream byteArrayInputStream0 = new ByteArrayInputStream(byteArray0);
      String string0 = microKernelImpl0.write(byteArrayInputStream0);
      assertEquals("0003000000", string0);
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      int int0 = microKernelImpl0.read("", 0L, byteArray0, (byte)118, (-2997));
      assertEquals((-1), int0);
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      Id.fromString("");
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((Id) null).when(revisionStore0).getHeadCommitId();
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.getLength("R4wmX`V92w8&S9}");
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: R4wmX`V92w8&S9}
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.merge("n=bYNAeVl.", "changeDeletedProperty");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // n=bYNAeVl.
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      try { 
        microKernelImpl0.branch((String) null);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      byte[] byteArray0 = new byte[9];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((Id) null).when(revisionStore0).getHeadCommitId();
      DbBlobStore dbBlobStore0 = new DbBlobStore();
      Repository repository0 = new Repository(revisionStore0, dbBlobStore0);
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl(repository0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // java.lang.NullPointerException
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.branch("offset > 0 with child node filter");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // offset > 0 with child node filter
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.commit("n=bYNAeVl.", "R4wmX`V92w83[&S9}", (String) null, "R4wmX`V92w83[&S9}");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.commit("n=bYNAeVl.", "R4wmX`V92w8&S9}", (String) null, "R4wmX`V92w8&S9}");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getNodes("007651", "create sequence if not exists DATASTORE_ID", (byte)81, 761L, 1024, ";r8}uU+DiG6&^OQ;b");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // create sequence if not exists DATASTORE_ID
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists("", "");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists("", "");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.nodeExists("R4wmX`V92w8&S9}", (String) null);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, (Id) null).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("q?oP9Jk", (String) null, " not found", (byte)118);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // q?oP9Jk
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("n=bYNAeVl.", (String) null, "n=bYNAeVl.", (-1431655763));
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // depth
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("R4wmX`V92w8&S9}", " not found", "v_XlM]69A?_BCH61:`", 43);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // R4wmX`V92w8&S9}
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.diff("R4wmX`V92w8&S9}", ",", (String) null, (byte)62);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // R4wmX`V92w8&S9}
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal("/R4wmX`V92w8&S9}", (String) null, (String) null);
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // /R4wmX`V92w8&S9}
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.getJournal("<}/DaS5E:[O", "(>|WwI=;'s", "3e7651");
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (byte)62, id0, "inconsistent range specified: fromRevision denotes a private branch while toRevision denotes a head or another private branch", "inconsistent range specified: fromRevision denotes a private branch while toRevision denotes a head or another private branch", id0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      StoredNode storedNode0 = new StoredNode(id0, (RevisionProvider) null);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      doReturn((StoredNode) null).when(revisionStore0).getNode(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn((StoredNode) null).when(revisionStore0).getRootNode(any(org.apache.jackrabbit.mk.model.Id.class));
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.getRevisionHistory(1L, 42, " not found");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/apache/jackrabbit/oak/commons/PathUtils
         //
         verifyException("org.apache.jackrabbit.mk.model.DiffBuilder", e);
      }
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getRevisionHistory((byte)118, (-1421), "n=bYNAeVl.");
      assertEquals("[]", string0);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, (byte)62, id0, "inconsistent range specified: fromRevision denotes a private branch while toRevision denotes a head or another private branch", "inconsistent range specified: fromRevision denotes a private branch while toRevision denotes a head or another private branch", id0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0, storedCommit0, storedCommit0, storedCommit0, storedCommit0).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getRevisionHistory((-1463L), 42, (String) null);
      assertNotNull(string0);
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 1L, id0, "K+t[%w =", "n=bYNAeVl.", id0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(storedCommit0).when(revisionStore0).getHeadCommit();
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.getRevisionHistory((byte)62, 42, " not found");
      assertEquals("[]", string0);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      microKernelImpl0.dispose();
      // Undeclared exception!
      try { 
        microKernelImpl0.checkpoint((-749L));
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // this instance has already been disposed
         //
         verifyException("org.apache.jackrabbit.mk.core.MicroKernelImpl", e);
      }
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.waitForCommit("3e7651", (byte)81);
      assertEquals("000000", string0);
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl("ckS");
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      MicroKernelImpl microKernelImpl0 = null;
      try {
        microKernelImpl0 = new MicroKernelImpl();
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // com/google/common/cache/Weigher
         //
         verifyException("org.apache.jackrabbit.mk.core.Repository", e);
      }
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      byte[] byteArray0 = new byte[6];
      Id id0 = new Id(byteArray0);
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn((StoredCommit) null).when(revisionStore0).getCommit(any(org.apache.jackrabbit.mk.model.Id.class));
      doReturn(id0).when(revisionStore0).getHeadCommitId();
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      // Undeclared exception!
      try { 
        microKernelImpl0.rebase("", "n=bYNAeVl.");
        fail("Expecting exception: IllegalArgumentException");
      
      } catch(IllegalArgumentException e) {
         //
         // n=bYNAeVl.
         //
         verifyException("org.apache.jackrabbit.mk.util.StringUtils", e);
      }
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      MemoryBlobStore memoryBlobStore0 = new MemoryBlobStore();
      RevisionStore revisionStore0 = mock(RevisionStore.class, new ViolatedAssumptionAnswer());
      doReturn(id0, id0).when(revisionStore0).getHeadCommitId();
      Repository repository0 = new Repository(revisionStore0, memoryBlobStore0);
      MicroKernelImpl microKernelImpl0 = new MicroKernelImpl(repository0);
      String string0 = microKernelImpl0.checkpoint((-749L));
      assertEquals("000000", string0);
  }
}
