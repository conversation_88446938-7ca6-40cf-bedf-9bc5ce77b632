/**
 * Scaffolding file used to store all the setups needed to run 
 * tests automatically generated by EvoSuite
 * Sat Dec 28 13:58:04 GMT 2019
 */

package org.apache.jackrabbit.mk.core;

import org.evosuite.runtime.annotation.EvoSuiteClassExclude;
import org.junit.BeforeClass;
import org.junit.Before;
import org.junit.After;
import org.junit.AfterClass;
import org.evosuite.runtime.sandbox.Sandbox;
import org.evosuite.runtime.sandbox.Sandbox.SandboxMode;

import static org.evosuite.shaded.org.mockito.Mockito.*;
@EvoSuiteClassExclude
public class MicroKernelImpl_ESTest_scaffolding {

  @org.junit.Rule 
  public org.evosuite.runtime.vnet.NonFunctionalRequirementRule nfr = new org.evosuite.runtime.vnet.NonFunctionalRequirementRule();

  private static final java.util.Properties defaultProperties = (java.util.Properties) java.lang.System.getProperties().clone(); 

  private org.evosuite.runtime.thread.ThreadStopper threadStopper =  new org.evosuite.runtime.thread.ThreadStopper (org.evosuite.runtime.thread.KillSwitchHandler.getInstance(), 3000);


  @BeforeClass 
  public static void initEvoSuiteFramework() { 
    org.evosuite.runtime.RuntimeSettings.className = "org.apache.jackrabbit.mk.core.MicroKernelImpl"; 
    org.evosuite.runtime.GuiSupport.initialize(); 
    org.evosuite.runtime.RuntimeSettings.maxNumberOfThreads = 100; 
    org.evosuite.runtime.RuntimeSettings.maxNumberOfIterationsPerLoop = 10000; 
    org.evosuite.runtime.RuntimeSettings.mockSystemIn = true; 
    org.evosuite.runtime.RuntimeSettings.sandboxMode = org.evosuite.runtime.sandbox.Sandbox.SandboxMode.RECOMMENDED; 
    org.evosuite.runtime.sandbox.Sandbox.initializeSecurityManagerForSUT(); 
    org.evosuite.runtime.classhandling.JDKClassResetter.init();
    setSystemProperties();
    initializeClasses();
    org.evosuite.runtime.Runtime.getInstance().resetRuntime(); 
    try { initMocksToAvoidTimeoutsInTheTests(); } catch(ClassNotFoundException e) {} 
  } 

  @AfterClass 
  public static void clearEvoSuiteFramework(){ 
    Sandbox.resetDefaultSecurityManager(); 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
  } 

  @Before 
  public void initTestCase(){ 
    threadStopper.storeCurrentThreads();
    threadStopper.startRecordingTime();
    org.evosuite.runtime.jvm.ShutdownHookHandler.getInstance().initHandler(); 
    org.evosuite.runtime.sandbox.Sandbox.goingToExecuteSUTCode(); 
    setSystemProperties(); 
    org.evosuite.runtime.GuiSupport.setHeadless(); 
    org.evosuite.runtime.Runtime.getInstance().resetRuntime(); 
    org.evosuite.runtime.agent.InstrumentingAgent.activate(); 
  } 

  @After 
  public void doneWithTestCase(){ 
    threadStopper.killAndJoinClientThreads();
    org.evosuite.runtime.jvm.ShutdownHookHandler.getInstance().safeExecuteAddedHooks(); 
    org.evosuite.runtime.classhandling.JDKClassResetter.reset(); 
    resetClasses(); 
    org.evosuite.runtime.sandbox.Sandbox.doneWithExecutingSUTCode(); 
    org.evosuite.runtime.agent.InstrumentingAgent.deactivate(); 
    org.evosuite.runtime.GuiSupport.restoreHeadlessMode(); 
  } 

  public static void setSystemProperties() {
 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
    java.lang.System.setProperty("file.encoding", "UTF-8"); 
    java.lang.System.setProperty("java.awt.headless", "true"); 
    java.lang.System.setProperty("java.io.tmpdir", "/tmp"); 
    java.lang.System.setProperty("user.country", "US"); 
    java.lang.System.setProperty("user.dir", "/home/<USER>/Desktop/research/evosuite-bugjar/tests/bugs-dot-jar_OAK-1122_5286861d/9"); 
    java.lang.System.setProperty("user.home", "/home/<USER>"); 
    java.lang.System.setProperty("user.language", "en"); 
    java.lang.System.setProperty("user.name", "wasp"); 
    java.lang.System.setProperty("user.timezone", "America/Los_Angeles"); 
  }

  private static void initializeClasses() {
    org.evosuite.runtime.classhandling.ClassStateSupport.initializeClasses(MicroKernelImpl_ESTest_scaffolding.class.getClassLoader() ,
      "org.apache.jackrabbit.mk.model.CommitBuilder$RemoveNode",
      "org.apache.jackrabbit.mk.model.StagedNodeTree$StagedNode",
      "org.apache.jackrabbit.mk.blobs.DbBlobStore",
      "org.apache.jackrabbit.mk.api.MicroKernel",
      "org.apache.jackrabbit.mk.model.MutableCommit",
      "org.apache.jackrabbit.mk.model.Id",
      "org.apache.jackrabbit.mk.model.CommitBuilder",
      "org.apache.jackrabbit.mk.util.CommitGate",
      "org.apache.jackrabbit.mk.json.JsopStream",
      "org.apache.jackrabbit.mk.model.CommitBuilder$CopyNode",
      "org.apache.jackrabbit.mk.model.StoredCommit",
      "org.apache.jackrabbit.mk.json.JsopTokenizer",
      "org.apache.jackrabbit.mk.model.MutableNode",
      "org.apache.jackrabbit.mk.json.JsopReader",
      "org.apache.jackrabbit.mk.core.MicroKernelImpl",
      "org.apache.jackrabbit.mk.store.Binding$StringEntryIterator",
      "org.apache.jackrabbit.mk.core.Repository",
      "org.apache.jackrabbit.mk.model.CommitBuilder$MoveNode",
      "org.apache.jackrabbit.mk.model.TraversingNodeDiffHandler",
      "org.apache.jackrabbit.mk.store.NotFoundException",
      "org.apache.jackrabbit.mk.persistence.Persistence",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$PutTokenImpl",
      "org.h2.Driver",
      "org.apache.jackrabbit.mk.model.CommitBuilder$AddNode",
      "org.apache.jackrabbit.mk.model.DiffBuilder",
      "org.apache.jackrabbit.mk.json.JsonObject",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap",
      "org.apache.jackrabbit.mk.util.IOUtils",
      "org.apache.jackrabbit.mk.model.DiffBuilder$1",
      "org.apache.jackrabbit.mk.model.CommitBuilder$Change",
      "org.apache.jackrabbit.mk.persistence.GCPersistence",
      "org.apache.jackrabbit.mk.store.Binding$BytesEntryIterator",
      "org.apache.jackrabbit.mk.blobs.FileBlobStore",
      "org.apache.jackrabbit.mk.store.Binding",
      "org.apache.jackrabbit.mk.blobs.AbstractBlobStore",
      "org.apache.jackrabbit.mk.store.RevisionStore$PutToken",
      "org.apache.jackrabbit.mk.persistence.H2Persistence",
      "org.apache.jackrabbit.mk.model.NodeDiffHandler",
      "org.apache.jackrabbit.mk.model.Commit",
      "org.apache.jackrabbit.mk.blobs.BlobStore",
      "org.apache.jackrabbit.mk.util.Cache$Backend",
      "org.apache.jackrabbit.mk.store.BinaryBinding",
      "org.apache.jackrabbit.mk.model.StoredNode",
      "org.apache.jackrabbit.mk.util.NodeFilter",
      "org.apache.jackrabbit.mk.util.UnmodifiableIterator",
      "org.apache.jackrabbit.mk.json.JsopWriter",
      "org.apache.jackrabbit.mk.store.RevisionProvider",
      "org.apache.jackrabbit.mk.model.ChildNodeEntry",
      "org.apache.jackrabbit.mk.model.CommitBuilder$SetProperty",
      "org.apache.jackrabbit.mk.util.NameFilter",
      "org.apache.jackrabbit.mk.store.IdFactory$1",
      "org.apache.jackrabbit.mk.model.StagedNodeTree",
      "org.apache.jackrabbit.mk.json.JsopBuilder",
      "org.apache.jackrabbit.mk.store.CacheObject",
      "org.apache.jackrabbit.mk.model.ChildNodeEntries",
      "org.apache.jackrabbit.mk.model.NodeDelta",
      "org.apache.jackrabbit.mk.util.Cache$1",
      "org.apache.jackrabbit.mk.api.MicroKernelException",
      "org.apache.jackrabbit.mk.blobs.AbstractBlobStore$Data",
      "org.apache.jackrabbit.mk.model.AbstractNode",
      "org.apache.jackrabbit.mk.store.RevisionStore",
      "org.apache.jackrabbit.mk.model.Node",
      "org.apache.jackrabbit.mk.util.Cache",
      "org.apache.jackrabbit.mk.util.StringUtils",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore",
      "org.apache.jackrabbit.mk.model.AbstractCommit",
      "org.apache.jackrabbit.mk.store.PersistHook",
      "org.apache.jackrabbit.mk.blobs.AbstractBlobStore$BlockId",
      "org.apache.jackrabbit.mk.blobs.MemoryBlobStore",
      "org.apache.jackrabbit.mk.store.IdFactory",
      "org.apache.jackrabbit.mk.util.Cache$Value"
    );
  } 
  private static void initMocksToAvoidTimeoutsInTheTests() throws ClassNotFoundException { 
    mock(Class.forName("org.apache.jackrabbit.mk.store.RevisionProvider", false, MicroKernelImpl_ESTest_scaffolding.class.getClassLoader()));
    mock(Class.forName("org.apache.jackrabbit.mk.store.RevisionStore", false, MicroKernelImpl_ESTest_scaffolding.class.getClassLoader()));
  }

  private static void resetClasses() {
    org.evosuite.runtime.classhandling.ClassResetter.getInstance().setClassLoader(MicroKernelImpl_ESTest_scaffolding.class.getClassLoader()); 

    org.evosuite.runtime.classhandling.ClassStateSupport.resetClasses(
      "org.apache.jackrabbit.mk.core.MicroKernelImpl",
      "org.apache.jackrabbit.mk.util.CommitGate",
      "org.apache.jackrabbit.mk.core.Repository",
      "org.apache.jackrabbit.mk.store.NotFoundException",
      "org.apache.jackrabbit.mk.persistence.H2Persistence",
      "org.apache.jackrabbit.mk.store.IdFactory",
      "org.apache.jackrabbit.mk.store.IdFactory$1",
      "org.h2.Driver",
      "org.apache.jackrabbit.mk.api.MicroKernelException",
      "org.apache.jackrabbit.mk.blobs.AbstractBlobStore",
      "org.apache.jackrabbit.mk.blobs.MemoryBlobStore",
      "org.apache.jackrabbit.mk.util.Cache",
      "org.apache.jackrabbit.mk.util.Cache$1",
      "org.apache.jackrabbit.mk.blobs.DbBlobStore",
      "org.apache.jackrabbit.mk.model.Id",
      "org.apache.jackrabbit.mk.blobs.FileBlobStore",
      "org.apache.jackrabbit.mk.model.AbstractCommit",
      "org.apache.jackrabbit.mk.model.MutableCommit",
      "org.apache.jackrabbit.mk.util.StringUtils",
      "org.apache.jackrabbit.mk.model.CommitBuilder",
      "org.apache.jackrabbit.mk.model.StagedNodeTree",
      "org.apache.jackrabbit.mk.blobs.AbstractBlobStore$BlockId",
      "org.apache.jackrabbit.mk.json.JsopBuilder",
      "org.apache.jackrabbit.mk.model.AbstractNode",
      "org.apache.jackrabbit.mk.model.StoredNode",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap",
      "org.apache.jackrabbit.mk.util.NodeFilter",
      "org.apache.jackrabbit.mk.json.JsopTokenizer",
      "org.apache.jackrabbit.mk.store.BinaryBinding",
      "org.apache.jackrabbit.mk.model.StoredCommit",
      "org.apache.jackrabbit.mk.util.IOUtils",
      "org.apache.jackrabbit.mk.model.DiffBuilder",
      "org.apache.jackrabbit.mk.json.JsopStream",
      "org.apache.jackrabbit.mk.model.MutableNode",
      "org.apache.jackrabbit.mk.util.UnmodifiableIterator",
      "org.apache.jackrabbit.mk.store.RevisionStore$PutToken",
      "org.apache.jackrabbit.mk.model.NodeDelta"
    );
  }
}
