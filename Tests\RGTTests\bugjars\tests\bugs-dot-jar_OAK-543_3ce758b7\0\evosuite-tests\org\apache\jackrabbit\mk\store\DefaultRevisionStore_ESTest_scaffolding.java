/**
 * Scaffolding file used to store all the setups needed to run 
 * tests automatically generated by EvoSuite
 * Sat Dec 28 16:50:29 GMT 2019
 */

package org.apache.jackrabbit.mk.store;

import org.evosuite.runtime.annotation.EvoSuiteClassExclude;
import org.junit.BeforeClass;
import org.junit.Before;
import org.junit.After;
import org.junit.AfterClass;
import org.evosuite.runtime.sandbox.Sandbox;
import org.evosuite.runtime.sandbox.Sandbox.SandboxMode;

import static org.evosuite.shaded.org.mockito.Mockito.*;
@EvoSuiteClassExclude
public class DefaultRevisionStore_ESTest_scaffolding {

  @org.junit.Rule 
  public org.evosuite.runtime.vnet.NonFunctionalRequirementRule nfr = new org.evosuite.runtime.vnet.NonFunctionalRequirementRule();

  private static final java.util.Properties defaultProperties = (java.util.Properties) java.lang.System.getProperties().clone(); 

  private org.evosuite.runtime.thread.ThreadStopper threadStopper =  new org.evosuite.runtime.thread.ThreadStopper (org.evosuite.runtime.thread.KillSwitchHandler.getInstance(), 3000);


  @BeforeClass 
  public static void initEvoSuiteFramework() { 
    org.evosuite.runtime.RuntimeSettings.className = "org.apache.jackrabbit.mk.store.DefaultRevisionStore"; 
    org.evosuite.runtime.GuiSupport.initialize(); 
    org.evosuite.runtime.RuntimeSettings.maxNumberOfThreads = 100; 
    org.evosuite.runtime.RuntimeSettings.maxNumberOfIterationsPerLoop = 10000; 
    org.evosuite.runtime.RuntimeSettings.mockSystemIn = true; 
    org.evosuite.runtime.RuntimeSettings.sandboxMode = org.evosuite.runtime.sandbox.Sandbox.SandboxMode.RECOMMENDED; 
    org.evosuite.runtime.sandbox.Sandbox.initializeSecurityManagerForSUT(); 
    org.evosuite.runtime.classhandling.JDKClassResetter.init();
    setSystemProperties();
    initializeClasses();
    org.evosuite.runtime.Runtime.getInstance().resetRuntime(); 
    try { initMocksToAvoidTimeoutsInTheTests(); } catch(ClassNotFoundException e) {} 
  } 

  @AfterClass 
  public static void clearEvoSuiteFramework(){ 
    Sandbox.resetDefaultSecurityManager(); 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
  } 

  @Before 
  public void initTestCase(){ 
    threadStopper.storeCurrentThreads();
    threadStopper.startRecordingTime();
    org.evosuite.runtime.jvm.ShutdownHookHandler.getInstance().initHandler(); 
    org.evosuite.runtime.sandbox.Sandbox.goingToExecuteSUTCode(); 
    setSystemProperties(); 
    org.evosuite.runtime.GuiSupport.setHeadless(); 
    org.evosuite.runtime.Runtime.getInstance().resetRuntime(); 
    org.evosuite.runtime.agent.InstrumentingAgent.activate(); 
  } 

  @After 
  public void doneWithTestCase(){ 
    threadStopper.killAndJoinClientThreads();
    org.evosuite.runtime.jvm.ShutdownHookHandler.getInstance().safeExecuteAddedHooks(); 
    org.evosuite.runtime.classhandling.JDKClassResetter.reset(); 
    resetClasses(); 
    org.evosuite.runtime.sandbox.Sandbox.doneWithExecutingSUTCode(); 
    org.evosuite.runtime.agent.InstrumentingAgent.deactivate(); 
    org.evosuite.runtime.GuiSupport.restoreHeadlessMode(); 
  } 

  public static void setSystemProperties() {
 
    java.lang.System.setProperties((java.util.Properties) defaultProperties.clone()); 
    java.lang.System.setProperty("file.encoding", "UTF-8"); 
    java.lang.System.setProperty("java.awt.headless", "true"); 
    java.lang.System.setProperty("java.io.tmpdir", "/tmp"); 
    java.lang.System.setProperty("user.country", "US"); 
    java.lang.System.setProperty("user.dir", "/home/<USER>/Desktop/research/evosuite-bugjar/tests/bugs-dot-jar_OAK-543_3ce758b7/0"); 
    java.lang.System.setProperty("user.home", "/home/<USER>"); 
    java.lang.System.setProperty("user.language", "en"); 
    java.lang.System.setProperty("user.name", "wasp"); 
    java.lang.System.setProperty("user.timezone", "America/Los_Angeles"); 
  }

  private static void initializeClasses() {
    org.evosuite.runtime.classhandling.ClassStateSupport.initializeClasses(DefaultRevisionStore_ESTest_scaffolding.class.getClassLoader() ,
      "com.google.common.base.Suppliers$SupplierOfInstance",
      "com.google.common.collect.ImmutableMultimap$Itr",
      "org.apache.jackrabbit.mk.model.tree.NodeStateDiff",
      "com.google.common.cache.AbstractCache$StatsCounter",
      "org.apache.jackrabbit.mk.model.tree.AbstractNodeState",
      "com.google.common.collect.MapMaker$NullComputingConcurrentMap",
      "org.apache.jackrabbit.mk.store.StoredNodeAsState",
      "com.google.common.collect.ComputingConcurrentHashMap",
      "com.google.common.collect.PeekingIterator",
      "org.apache.jackrabbit.mk.model.AbstractNode$1",
      "com.google.common.base.Splitter$MapSplitter",
      "com.google.common.cache.LocalCache$WriteThroughEntry",
      "com.google.common.collect.ImmutableCollection$ArrayBasedBuilder",
      "com.google.common.cache.Weigher",
      "com.google.common.collect.Platform",
      "com.google.common.collect.NullsLastOrdering",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap$4",
      "com.google.common.collect.RegularImmutableMultiset",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap$2",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap$3",
      "com.google.common.collect.RegularImmutableMap",
      "com.google.common.collect.RegularImmutableBiMap",
      "org.apache.jackrabbit.mk.store.NotFoundException",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap$1",
      "com.google.common.collect.ImmutableMultimap$Values",
      "com.google.common.cache.LocalCache$EntryFactory$6",
      "com.google.common.cache.LocalCache$EntryFactory$7",
      "com.google.common.collect.ByFunctionOrdering",
      "com.google.common.cache.LocalCache$EntryFactory$8",
      "com.google.common.cache.LocalCache$EntryFactory$2",
      "com.google.common.cache.LocalCache$EntryFactory$3",
      "com.google.common.cache.LocalCache$StrongAccessWriteEntry",
      "com.google.common.collect.AbstractMapEntry",
      "com.google.common.cache.LocalCache$EntryFactory$4",
      "com.google.common.collect.Iterators$12",
      "com.google.common.cache.LocalCache$LocalManualCache",
      "com.google.common.cache.LocalCache$EntryFactory$5",
      "com.google.common.collect.Iterators$11",
      "com.google.common.collect.EmptyImmutableBiMap",
      "com.google.common.collect.ImmutableBiMap$Builder",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap",
      "com.google.common.base.Predicate",
      "com.google.common.cache.LocalCache$EntryFactory$1",
      "com.google.common.collect.ImmutableListMultimap$Builder",
      "com.google.common.collect.ImmutableMapEntry$TerminalEntry",
      "com.google.common.cache.CacheBuilderSpec",
      "org.apache.jackrabbit.mk.store.BinaryBinding$2",
      "com.google.common.cache.LocalCache$WeakWriteEntry",
      "com.google.common.cache.LocalCache$KeyIterator",
      "com.google.common.collect.NullsFirstOrdering",
      "org.apache.jackrabbit.mk.model.NodeDiffHandler",
      "org.apache.jackrabbit.mk.store.BinaryBinding$1",
      "com.google.common.cache.CacheBuilderSpec$RefreshDurationParser",
      "com.google.common.cache.CacheBuilderSpec$LongParser",
      "org.apache.jackrabbit.mk.model.Commit",
      "com.google.common.util.concurrent.ExecutionError",
      "com.google.common.cache.LocalCache$ValueReference",
      "com.google.common.collect.Iterators$13",
      "com.google.common.cache.LocalCache",
      "org.apache.jackrabbit.mk.htree.HashDirectory",
      "org.apache.jackrabbit.mk.store.RevisionProvider",
      "com.google.common.collect.UnmodifiableListIterator",
      "com.google.common.cache.LocalCache$StrongAccessEntry",
      "com.google.common.collect.ImmutableMultimap",
      "com.google.common.collect.ImmutableSortedSet",
      "com.google.common.cache.LocalCache$LocalManualCache$1",
      "com.google.common.cache.LocalCache$KeySet",
      "com.google.common.collect.MapMakerInternalMap$Strength",
      "com.google.common.cache.LocalCache$StrongValueReference",
      "com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets$1$1",
      "com.google.common.collect.MapMakerInternalMap$Segment",
      "com.google.common.base.CharMatcher",
      "org.apache.jackrabbit.mk.store.StoredNodeAsState$2",
      "org.apache.jackrabbit.mk.store.StoredNodeAsState$1",
      "com.google.common.collect.ImmutableSortedSet$Builder",
      "org.apache.jackrabbit.mk.model.Node",
      "com.google.common.base.Equivalence$Equals",
      "com.google.common.base.CharMatcher$5",
      "org.apache.jackrabbit.mk.util.StringUtils",
      "com.google.common.base.CharMatcher$4",
      "org.apache.jackrabbit.mk.persistence.InMemPersistence",
      "com.google.common.base.CharMatcher$3",
      "org.apache.jackrabbit.mk.model.AbstractCommit",
      "com.google.common.cache.ForwardingCache",
      "com.google.common.base.CharMatcher$2",
      "org.apache.jackrabbit.mk.store.StoredNodeAsState$1$1",
      "com.google.common.base.CharMatcher$9",
      "org.apache.jackrabbit.mk.store.AbstractRevisionStore",
      "com.google.common.base.Equivalence$Wrapper",
      "com.google.common.base.CharMatcher$8",
      "com.google.common.base.CharMatcher$7",
      "com.google.common.base.CharMatcher$6",
      "org.apache.jackrabbit.mk.htree.HashDirectory$ContainerEntry",
      "com.google.common.collect.ComputingConcurrentHashMap$ComputingSegment",
      "com.google.common.base.Preconditions",
      "com.google.common.collect.ImmutableSortedSetFauxverideShim",
      "com.google.common.base.CharMatcher$1",
      "com.google.common.collect.MapMaker",
      "com.google.common.collect.ImmutableMapValues",
      "com.google.common.collect.MapMaker$ComputingMapAdapter",
      "com.google.common.base.CharMatcher$FastMatcher",
      "com.google.common.collect.ImmutableEntry",
      "com.google.common.collect.MapMakerInternalMap$ValueReference",
      "com.google.common.collect.EmptyImmutableSetMultimap",
      "org.apache.jackrabbit.mk.htree.HashDirectory$DirectoryEntry",
      "com.google.common.collect.ImmutableCollection",
      "com.google.common.cache.LocalCache$ManualSerializationProxy",
      "com.google.common.util.concurrent.SettableFuture",
      "com.google.common.collect.ImmutableMultiset$EntrySet$1",
      "com.google.common.cache.LocalCache$AccessQueue",
      "com.google.common.cache.CacheBuilderSpec$MaximumWeightParser",
      "com.google.common.base.CharMatcher$BitSetMatcher",
      "com.google.common.cache.LocalCache$WeightedWeakValueReference",
      "org.apache.jackrabbit.mk.model.MutableNode",
      "com.google.common.util.concurrent.ExecutionList",
      "org.apache.jackrabbit.mk.htree.HashDirectory$IndexEntry",
      "com.google.common.collect.ImmutableCollection$Builder",
      "com.google.common.collect.ImmutableSetMultimap",
      "com.google.common.collect.Iterators$6",
      "com.google.common.cache.LocalCache$EntrySet",
      "com.google.common.collect.Iterators$7",
      "com.google.common.collect.BiMap",
      "com.google.common.collect.ImmutableSet",
      "com.google.common.collect.ImmutableMapEntry",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$PutTokenImpl",
      "com.google.common.collect.ImmutableSortedAsList",
      "com.google.common.util.concurrent.AbstractFuture$Sync",
      "com.google.common.collect.Iterators$1",
      "com.google.common.collect.Iterators$2",
      "com.google.common.collect.ImmutableMapValues$1",
      "com.google.common.collect.Iterators$3",
      "com.google.common.cache.CacheLoader",
      "com.google.common.collect.ImmutableMultimap$EntryCollection",
      "org.apache.jackrabbit.mk.model.tree.PropertyState",
      "org.apache.jackrabbit.mk.util.IOUtils",
      "org.apache.jackrabbit.mk.store.StoredNodeAsState$2$1",
      "com.google.common.base.Ticker$1",
      "com.google.common.cache.LocalCache$SoftValueReference",
      "com.google.common.cache.CacheLoader$FunctionToCacheLoader",
      "com.google.common.base.Splitter$Strategy",
      "com.google.common.collect.RegularImmutableBiMap$Inverse",
      "com.google.common.collect.RegularImmutableMap$NonTerminalMapEntry",
      "com.google.common.collect.ImmutableMultimap$Builder",
      "com.google.common.cache.LocalCache$WeightedSoftValueReference",
      "com.google.common.base.Equivalence$Identity",
      "org.apache.jackrabbit.mk.store.RevisionStore$PutToken",
      "com.google.common.util.concurrent.ListenableFuture",
      "com.google.common.collect.Multiset",
      "com.google.common.collect.ImmutableSetMultimap$EntrySet",
      "com.google.common.collect.AbstractMultimap",
      "com.google.common.base.Supplier",
      "com.google.common.cache.LocalCache$WeakEntry",
      "com.google.common.collect.EmptyImmutableListMultimap",
      "org.apache.jackrabbit.mk.store.BinaryBinding",
      "com.google.common.collect.AbstractSequentialIterator",
      "com.google.common.collect.EmptyImmutableSet",
      "org.apache.jackrabbit.mk.model.StoredNode",
      "org.apache.jackrabbit.mk.util.UnmodifiableIterator",
      "com.google.common.cache.LocalCache$WeakAccessWriteEntry",
      "com.google.common.collect.ComputingConcurrentHashMap$ComputingValueReference",
      "com.google.common.collect.ImmutableList",
      "com.google.common.collect.ReverseOrdering",
      "com.google.common.util.concurrent.UncheckedExecutionException",
      "org.apache.jackrabbit.mk.model.ChildNodeEntry",
      "com.google.common.cache.LocalCache$2",
      "com.google.common.cache.CacheBuilderSpec$MaximumSizeParser",
      "com.google.common.cache.CacheBuilder$NullListener",
      "com.google.common.cache.CacheBuilderSpec$KeyStrengthParser",
      "com.google.common.cache.LocalCache$1",
      "com.google.common.cache.LocalCache$StrongEntry",
      "org.apache.jackrabbit.mk.model.tree.NodeState",
      "com.google.common.cache.LocalCache$Strength$3",
      "com.google.common.cache.LocalCache$Strength$2",
      "com.google.common.collect.MapMakerInternalMap$ReferenceEntry",
      "com.google.common.cache.LocalCache$Strength$1",
      "org.apache.jackrabbit.mk.model.AbstractNode",
      "com.google.common.collect.ImmutableMap$Builder",
      "com.google.common.collect.MapMaker$RemovalListener",
      "com.google.common.cache.LocalCache$Segment",
      "com.google.common.cache.LocalCache$ReferenceEntry",
      "org.apache.jackrabbit.mk.store.PersistHook",
      "com.google.common.collect.Maps$EntryTransformer",
      "com.google.common.collect.Ordering",
      "com.google.common.collect.NaturalOrdering",
      "com.google.common.collect.MapMakerInternalMap",
      "com.google.common.cache.Cache",
      "org.apache.jackrabbit.mk.store.IdFactory",
      "com.google.common.cache.LocalCache$EntryIterator",
      "com.google.common.cache.CacheBuilderSpec$ConcurrencyLevelParser",
      "com.google.common.collect.AllEqualOrdering",
      "org.apache.jackrabbit.mk.model.tree.ChildNode",
      "com.google.common.cache.LocalCache$WeightedStrongValueReference",
      "com.google.common.cache.CacheBuilderSpec$WriteDurationParser",
      "com.google.common.collect.Hashing",
      "com.google.common.cache.LoadingCache",
      "com.google.common.collect.ImmutableList$SubList",
      "com.google.common.base.FunctionalEquivalence",
      "com.google.common.collect.ListMultimap",
      "com.google.common.cache.CacheBuilderSpec$ValueStrengthParser",
      "com.google.common.base.Suppliers$SupplierFunctionImpl",
      "com.google.common.cache.CacheBuilderSpec$AccessDurationParser",
      "com.google.common.collect.ComputationException",
      "com.google.common.collect.RegularImmutableList",
      "com.google.common.cache.CacheLoader$1",
      "org.apache.jackrabbit.mk.model.tree.NodeStore",
      "com.google.common.cache.CacheBuilder$OneWeigher",
      "com.google.common.base.Optional",
      "com.google.common.cache.CacheBuilderSpec$DurationParser",
      "org.apache.jackrabbit.mk.model.StoredCommit",
      "com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException",
      "com.google.common.cache.LocalCache$EntryFactory",
      "com.google.common.cache.CacheLoader$InvalidCacheLoadException",
      "com.google.common.collect.RegularImmutableBiMap$NonTerminalBiMapEntry",
      "org.apache.jackrabbit.mk.store.Binding$BytesEntry",
      "com.google.common.cache.LocalCache$Values",
      "com.google.common.cache.LocalCache$WeakValueReference",
      "org.apache.jackrabbit.mk.htree.HashBucket",
      "com.google.common.collect.ImmutableMapKeySet",
      "com.google.common.cache.CacheBuilderSpec$RecordStatsParser",
      "com.google.common.base.CharMatcher$NegatedFastMatcher",
      "com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets$1",
      "com.google.common.base.Ticker",
      "com.google.common.cache.CacheBuilderSpec$IntegerParser",
      "com.google.common.collect.RegularImmutableSet",
      "com.google.common.collect.MapMaker$NullConcurrentMap",
      "com.google.common.collect.LexicographicalOrdering",
      "com.google.common.collect.ImmutableListMultimap",
      "com.google.common.collect.ImmutableMultimap$1",
      "org.apache.jackrabbit.mk.store.Binding$BytesEntryIterator",
      "com.google.common.base.Present",
      "com.google.common.base.Equivalence$1",
      "com.google.common.cache.CacheStats",
      "com.google.common.collect.ImmutableMultimap$2",
      "com.google.common.cache.RemovalCause$4",
      "org.apache.jackrabbit.mk.store.Binding",
      "com.google.common.cache.RemovalCause$5",
      "org.apache.jackrabbit.mk.persistence.H2Persistence",
      "com.google.common.cache.RemovalCause$1",
      "com.google.common.collect.ImmutableAsList",
      "com.google.common.cache.RemovalCause$2",
      "com.google.common.cache.RemovalCause$3",
      "com.google.common.collect.ImmutableSet$Builder",
      "com.google.common.base.MoreObjects$ToStringHelper$ValueHolder",
      "com.google.common.base.MoreObjects$ToStringHelper",
      "com.google.common.collect.RegularImmutableAsList",
      "com.google.common.cache.LocalCache$HashIterator",
      "com.google.common.collect.SingletonImmutableSet",
      "com.google.common.collect.ImmutableSetMultimap$Builder",
      "com.google.common.collect.ImmutableMapEntrySet",
      "com.google.common.collect.MapMaker$RemovalCause$4",
      "com.google.common.cache.CacheLoader$SupplierToCacheLoader",
      "com.google.common.collect.MapMaker$RemovalCause$5",
      "com.google.common.base.Suppliers$ThreadSafeSupplier",
      "com.google.common.collect.MapMaker$RemovalCause$2",
      "com.google.common.collect.MapMaker$RemovalCause$3",
      "com.google.common.collect.ImmutableMultiset",
      "com.google.common.collect.UsingToStringOrdering",
      "com.google.common.collect.ImmutableMultimap$Keys",
      "org.apache.jackrabbit.mk.store.Binding$StringEntry",
      "com.google.common.collect.MapMaker$RemovalCause$1",
      "org.apache.jackrabbit.mk.model.ChildNodeEntries",
      "com.google.common.util.concurrent.Uninterruptibles",
      "com.google.common.collect.ObjectArrays",
      "com.google.common.collect.AbstractIterator",
      "com.google.common.base.MoreObjects",
      "com.google.common.base.CharMatcher$NegatedMatcher",
      "com.google.common.collect.GenericMapMaker",
      "com.google.common.cache.LocalCache$WriteQueue",
      "com.google.common.base.CharMatcher$And",
      "com.google.common.collect.ImmutableList$1",
      "org.apache.jackrabbit.mk.htree.HashDirectory$1",
      "com.google.common.collect.RegularImmutableBiMap$Inverse$InverseEntrySet",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore",
      "com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets",
      "com.google.common.collect.SortedIterable",
      "com.google.common.cache.RemovalListener",
      "com.google.common.collect.UnmodifiableIterator",
      "org.apache.jackrabbit.mk.htree.HashDirectory$BucketEntry",
      "com.google.common.collect.RegularImmutableBiMap$Inverse$InverseEntrySet$1",
      "com.google.common.collect.MapMaker$RemovalNotification",
      "com.google.common.base.CharMatcher$12",
      "com.google.common.collect.MapMaker$RemovalCause",
      "org.apache.jackrabbit.mk.model.MutableCommit",
      "com.google.common.base.CharMatcher$11",
      "com.google.common.collect.ImmutableMapKeySet$1",
      "com.google.common.base.CharMatcher$10",
      "com.google.common.collect.EmptyImmutableSortedSet",
      "org.apache.jackrabbit.mk.model.Id",
      "com.google.common.base.CharMatcher$15",
      "com.google.common.base.CharMatcher$14",
      "com.google.common.base.CharMatcher$13",
      "com.google.common.base.Platform",
      "com.google.common.collect.ImmutableMultiset$EntrySet",
      "com.google.common.cache.CacheBuilderSpec$ValueParser",
      "com.google.common.collect.ImmutableList$ReverseImmutableList",
      "com.google.common.base.CharMatcher$RangesMatcher",
      "com.google.common.collect.SingletonImmutableList",
      "com.google.common.base.Splitter",
      "org.apache.jackrabbit.mk.store.Binding$StringEntryIterator",
      "com.google.common.base.Function",
      "com.google.common.collect.ImmutableMap",
      "com.google.common.collect.ComparatorOrdering",
      "org.apache.jackrabbit.mk.persistence.Persistence",
      "com.google.common.collect.AbstractIndexedListIterator",
      "com.google.common.collect.MapMakerInternalMap$Strength$2",
      "com.google.common.collect.MapMakerInternalMap$Strength$1",
      "com.google.common.collect.CollectPreconditions",
      "org.h2.Driver",
      "com.google.common.collect.Multiset$Entry",
      "com.google.common.base.Suppliers$SupplierFunction",
      "com.google.common.collect.MapMakerInternalMap$Strength$3",
      "com.google.common.collect.ExplicitOrdering",
      "org.apache.jackrabbit.mk.persistence.GCPersistence",
      "com.google.common.cache.CacheBuilder",
      "com.google.common.cache.LocalCache$AccessQueue$1",
      "com.google.common.cache.LocalCache$AccessQueue$2",
      "com.google.common.collect.ImmutableList$Builder",
      "com.google.common.cache.LocalCache$Strength",
      "com.google.common.cache.LocalCache$NullEntry",
      "com.google.common.collect.ForwardingObject",
      "com.google.common.cache.LocalCache$WeakAccessEntry",
      "com.google.common.base.Stopwatch",
      "com.google.common.cache.LocalCache$LoadingSerializationProxy",
      "com.google.common.collect.Multimap",
      "com.google.common.collect.Iterators",
      "com.google.common.collect.CompoundOrdering",
      "com.google.common.collect.ImmutableBiMap",
      "com.google.common.cache.LocalCache$AbstractCacheSet",
      "com.google.common.base.Splitter$1$1",
      "com.google.common.collect.ImmutableMultiset$Builder",
      "com.google.common.cache.LocalCache$LocalLoadingCache",
      "com.google.common.collect.SingletonImmutableBiMap",
      "com.google.common.base.PairwiseEquivalence",
      "org.apache.jackrabbit.mk.htree.ChildNodeEntriesHTree",
      "com.google.common.collect.Ordering$IncomparableValueException",
      "com.google.common.collect.RegularImmutableBiMap$1",
      "com.google.common.collect.ImmutableMultiset$1",
      "com.google.common.base.Suppliers",
      "org.apache.jackrabbit.mk.store.IdFactory$1",
      "com.google.common.collect.MapMakerInternalMap$2",
      "com.google.common.collect.MapMakerInternalMap$1",
      "com.google.common.cache.LocalCache$AbstractReferenceEntry",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$6",
      "org.apache.jackrabbit.mk.store.CacheObject",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$5",
      "com.google.common.cache.RemovalNotification",
      "com.google.common.cache.CacheBuilderSpec$InitialCapacityParser",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$7",
      "com.google.common.collect.RegularImmutableMap$EntrySet",
      "com.google.common.base.Equivalence",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$2",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$1",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$4",
      "com.google.common.cache.LocalCache$LoadingValueReference",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$3",
      "com.google.common.cache.LocalCache$ValueIterator",
      "org.apache.jackrabbit.mk.store.RevisionStore",
      "org.apache.jackrabbit.mk.store.Binding$Entry",
      "com.google.common.collect.ReverseNaturalOrdering",
      "com.google.common.cache.LocalCache$StrongWriteEntry",
      "com.google.common.base.CharMatcher$Or",
      "com.google.common.base.Splitter$1",
      "com.google.common.collect.SetMultimap",
      "com.google.common.collect.DescendingImmutableSortedSet",
      "com.google.common.cache.RemovalCause",
      "com.google.common.base.Splitter$SplittingIterator",
      "com.google.common.base.AbstractIterator",
      "com.google.common.cache.CacheBuilder$1",
      "org.apache.jackrabbit.mk.util.AbstractFilteringIterator",
      "com.google.common.cache.CacheBuilder$3",
      "com.google.common.collect.Iterators$MergingIterator",
      "com.google.common.cache.CacheBuilder$2",
      "com.google.common.collect.RegularImmutableSortedSet",
      "com.google.common.util.concurrent.AbstractFuture"
    );
  } 
  private static void initMocksToAvoidTimeoutsInTheTests() throws ClassNotFoundException { 
    mock(Class.forName("org.apache.jackrabbit.mk.model.tree.NodeStateDiff", false, DefaultRevisionStore_ESTest_scaffolding.class.getClassLoader()));
  }

  private static void resetClasses() {
    org.evosuite.runtime.classhandling.ClassResetter.getInstance().setClassLoader(DefaultRevisionStore_ESTest_scaffolding.class.getClassLoader()); 

    org.evosuite.runtime.classhandling.ClassStateSupport.resetClasses(
      "org.apache.jackrabbit.mk.store.AbstractRevisionStore",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$1",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$2",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$3",
      "org.apache.jackrabbit.mk.store.RevisionStore$PutToken",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$PutTokenImpl",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$4",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$5",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$6",
      "org.apache.jackrabbit.mk.store.DefaultRevisionStore$7",
      "org.apache.jackrabbit.mk.persistence.InMemPersistence",
      "org.apache.jackrabbit.mk.store.IdFactory",
      "org.apache.jackrabbit.mk.store.IdFactory$1",
      "org.apache.jackrabbit.mk.store.NotFoundException",
      "org.apache.jackrabbit.mk.persistence.H2Persistence",
      "org.apache.jackrabbit.mk.model.AbstractCommit",
      "org.apache.jackrabbit.mk.model.MutableCommit",
      "org.apache.jackrabbit.mk.model.Id",
      "org.apache.jackrabbit.mk.model.StoredCommit",
      "org.apache.jackrabbit.mk.util.StringUtils",
      "org.apache.jackrabbit.mk.model.tree.AbstractNodeState",
      "org.apache.jackrabbit.mk.store.StoredNodeAsState",
      "org.apache.jackrabbit.mk.store.BinaryBinding",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap$4",
      "org.apache.jackrabbit.mk.util.IOUtils",
      "org.apache.jackrabbit.mk.store.BinaryBinding$2",
      "org.apache.jackrabbit.mk.model.AbstractNode",
      "org.apache.jackrabbit.mk.model.MutableNode",
      "org.apache.jackrabbit.mk.model.StoredNode",
      "org.apache.jackrabbit.mk.model.AbstractNode$1",
      "org.apache.jackrabbit.mk.util.AbstractFilteringIterator",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap$1",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap$2",
      "org.apache.jackrabbit.mk.model.ChildNodeEntriesMap$3",
      "org.apache.jackrabbit.mk.store.StoredNodeAsState$2",
      "org.h2.Driver",
      "com.google.common.cache.CacheBuilder$1",
      "com.google.common.base.Suppliers",
      "com.google.common.base.Suppliers$SupplierOfInstance",
      "com.google.common.cache.CacheStats",
      "com.google.common.base.Preconditions",
      "com.google.common.cache.CacheBuilder$2",
      "com.google.common.base.Ticker$1",
      "com.google.common.base.Ticker",
      "com.google.common.cache.CacheBuilder$3",
      "com.google.common.cache.CacheBuilder",
      "com.google.common.cache.LocalCache$LocalManualCache",
      "com.google.common.cache.LocalCache$1",
      "com.google.common.cache.LocalCache$2",
      "com.google.common.cache.LocalCache",
      "com.google.common.cache.LocalCache$Strength",
      "com.google.common.base.MoreObjects",
      "com.google.common.base.Equivalence",
      "com.google.common.base.Equivalence$Equals",
      "com.google.common.cache.CacheBuilder$OneWeigher",
      "com.google.common.cache.CacheBuilder$NullListener",
      "com.google.common.cache.LocalCache$EntryFactory",
      "com.google.common.cache.LocalCache$Segment",
      "com.google.common.cache.LocalCache$AccessQueue",
      "com.google.common.cache.LocalCache$AbstractReferenceEntry",
      "com.google.common.cache.LocalCache$AccessQueue$1",
      "com.google.common.cache.LocalCache$StrongEntry",
      "com.google.common.cache.LocalCache$StrongAccessEntry",
      "com.google.common.cache.LocalCache$NullEntry",
      "com.google.common.cache.LocalCache$StrongValueReference",
      "com.google.common.cache.LocalCache$WeightedStrongValueReference",
      "com.google.common.cache.CacheLoader",
      "com.google.common.cache.LocalCache$LocalManualCache$1",
      "org.apache.jackrabbit.mk.htree.ChildNodeEntriesHTree",
      "org.apache.jackrabbit.mk.htree.HashDirectory",
      "org.apache.jackrabbit.mk.htree.HashDirectory$1",
      "org.apache.jackrabbit.mk.store.Binding$Entry",
      "org.apache.jackrabbit.mk.store.Binding$StringEntry",
      "org.apache.jackrabbit.mk.util.UnmodifiableIterator",
      "org.apache.jackrabbit.mk.store.StoredNodeAsState$1",
      "org.apache.jackrabbit.mk.store.StoredNodeAsState$1$1",
      "com.google.common.cache.LocalCache$LoadingValueReference",
      "com.google.common.util.concurrent.AbstractFuture",
      "com.google.common.util.concurrent.SettableFuture",
      "com.google.common.util.concurrent.AbstractFuture$Sync",
      "com.google.common.util.concurrent.ExecutionList",
      "com.google.common.base.Stopwatch",
      "com.google.common.base.Platform",
      "com.google.common.util.concurrent.Uninterruptibles",
      "org.apache.jackrabbit.mk.store.BinaryBinding$1",
      "com.google.common.util.concurrent.UncheckedExecutionException",
      "org.apache.jackrabbit.mk.store.StoredNodeAsState$2$1",
      "com.google.common.cache.RemovalCause",
      "com.google.common.collect.UnmodifiableIterator",
      "com.google.common.collect.AbstractSequentialIterator",
      "com.google.common.cache.LocalCache$AccessQueue$2",
      "com.google.common.collect.ImmutableCollection",
      "com.google.common.collect.ImmutableSet",
      "com.google.common.collect.EmptyImmutableSet",
      "com.google.common.collect.UnmodifiableListIterator",
      "com.google.common.collect.Iterators$1",
      "com.google.common.collect.Iterators$2",
      "com.google.common.collect.Iterators"
    );
  }
}
