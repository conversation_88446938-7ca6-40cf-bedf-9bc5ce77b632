/*
 * This file was automatically generated by EvoSuite
 * Sat Dec 28 17:06:12 GMT 2019
 */

package org.apache.jackrabbit.mk.store;

import org.junit.Test;
import static org.junit.Assert.*;
import static org.evosuite.shaded.org.mockito.Mockito.*;
import static org.evosuite.runtime.EvoAssertions.*;
import java.util.concurrent.ExecutionException;
import org.apache.jackrabbit.mk.htree.ChildNodeEntriesHTree;
import org.apache.jackrabbit.mk.model.ChildNodeEntries;
import org.apache.jackrabbit.mk.model.ChildNodeEntriesMap;
import org.apache.jackrabbit.mk.model.Id;
import org.apache.jackrabbit.mk.model.MutableCommit;
import org.apache.jackrabbit.mk.model.MutableNode;
import org.apache.jackrabbit.mk.model.StoredCommit;
import org.apache.jackrabbit.mk.model.StoredNode;
import org.apache.jackrabbit.mk.model.tree.NodeState;
import org.apache.jackrabbit.mk.model.tree.NodeStateDiff;
import org.apache.jackrabbit.mk.persistence.H2Persistence;
import org.apache.jackrabbit.mk.persistence.InMemPersistence;
import org.apache.jackrabbit.mk.persistence.Persistence;
import org.apache.jackrabbit.mk.store.DefaultRevisionStore;
import org.apache.jackrabbit.mk.store.RevisionStore;
import org.apache.jackrabbit.mk.store.StoredNodeAsState;
import org.evosuite.runtime.EvoRunner;
import org.evosuite.runtime.EvoRunnerParameters;
import org.evosuite.runtime.ViolatedAssumptionAnswer;
import org.junit.runner.RunWith;

@RunWith(EvoRunner.class) @EvoRunnerParameters(mockJVMNonDeterminism = true, useVFS = true, useVNET = true, resetStaticState = true, separateClassLoader = true, useJEE = true) 
public class DefaultRevisionStore_ESTest extends DefaultRevisionStore_ESTest_scaffolding {

  @Test(timeout = 4000)
  public void test00()  throws Throwable  {
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0);
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      StoredNode storedNode0 = new StoredNode(id0, defaultRevisionStore0);
      StoredNodeAsState storedNodeAsState0 = new StoredNodeAsState(storedNode0, defaultRevisionStore0);
      StoredNodeAsState storedNodeAsState1 = new StoredNodeAsState(storedNode0, defaultRevisionStore0);
      NodeStateDiff nodeStateDiff0 = mock(NodeStateDiff.class, new ViolatedAssumptionAnswer());
      defaultRevisionStore0.compare(storedNodeAsState0, storedNodeAsState1, nodeStateDiff0);
      assertTrue(storedNodeAsState1.equals((Object)storedNodeAsState0));
  }

  @Test(timeout = 4000)
  public void test01()  throws Throwable  {
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl1 = new DefaultRevisionStore.PutTokenImpl();
      boolean boolean0 = defaultRevisionStore_PutTokenImpl1.equals(defaultRevisionStore_PutTokenImpl0);
      assertFalse(boolean0);
      assertFalse(defaultRevisionStore_PutTokenImpl0.equals((Object)defaultRevisionStore_PutTokenImpl1));
  }

  @Test(timeout = 4000)
  public void test02()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      defaultRevisionStore0.initialize();
      defaultRevisionStore0.verifyInitialized();
      assertEquals(33554432, DefaultRevisionStore.DEFAULT_CACHE_SIZE);
  }

  @Test(timeout = 4000)
  public void test03()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      StoredCommit storedCommit0 = defaultRevisionStore0.getHeadCommit();
      defaultRevisionStore0.markCommit(storedCommit0);
      assertEquals("", storedCommit0.getChanges());
  }

  @Test(timeout = 4000)
  public void test04()  throws Throwable  {
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0);
      defaultRevisionStore0.gc();
      assertEquals(33554432, DefaultRevisionStore.DEFAULT_CACHE_SIZE);
  }

  @Test(timeout = 4000)
  public void test05()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      defaultRevisionStore0.initialize();
      Id id0 = defaultRevisionStore0.markCommits();
      StoredNode storedNode0 = defaultRevisionStore0.getRootNode(id0);
      assertNotNull(storedNode0);
  }

  @Test(timeout = 4000)
  public void test06()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      MutableNode mutableNode0 = new MutableNode(defaultRevisionStore0);
      Id id0 = defaultRevisionStore0.putNode(defaultRevisionStore_PutTokenImpl0, mutableNode0);
      StoredNode storedNode0 = defaultRevisionStore0.getNode(id0);
      assertNotNull(storedNode0);
  }

  @Test(timeout = 4000)
  public void test07()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      Id id0 = defaultRevisionStore0.getHeadCommitId();
      StoredCommit storedCommit0 = defaultRevisionStore0.getCommit(id0);
      assertEquals(1392409281320L, storedCommit0.getCommitTS());
  }

  @Test(timeout = 4000)
  public void test08()  throws Throwable  {
      int int0 = DefaultRevisionStore.determineInitialCacheSize();
      assertEquals(33554432, int0);
  }

  @Test(timeout = 4000)
  public void test09()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      // Undeclared exception!
      try { 
        defaultRevisionStore0.verifyInitialized();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test10()  throws Throwable  {
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0);
      RevisionStore.PutToken revisionStore_PutToken0 = defaultRevisionStore0.createPutToken();
      MutableNode mutableNode0 = new MutableNode(defaultRevisionStore0);
      try { 
        defaultRevisionStore0.putNode(revisionStore_PutToken0, mutableNode0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test11()  throws Throwable  {
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0, h2Persistence0);
      RevisionStore.PutToken revisionStore_PutToken0 = defaultRevisionStore0.createPutToken();
      MutableCommit mutableCommit0 = new MutableCommit();
      try { 
        defaultRevisionStore0.putCommit(revisionStore_PutToken0, mutableCommit0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test12()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      try { 
        defaultRevisionStore0.putCNEMap(defaultRevisionStore_PutTokenImpl0, (ChildNodeEntries) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.persistence.InMemPersistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test13()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      RevisionStore.PutToken revisionStore_PutToken0 = defaultRevisionStore0.createPutToken();
      ChildNodeEntriesHTree childNodeEntriesHTree0 = new ChildNodeEntriesHTree(defaultRevisionStore0);
      try { 
        defaultRevisionStore0.putCNEMap(revisionStore_PutToken0, childNodeEntriesHTree0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test14()  throws Throwable  {
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore((Persistence) null);
      try { 
        defaultRevisionStore0.markCommits();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test15()  throws Throwable  {
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0, h2Persistence0);
      try { 
        defaultRevisionStore0.markCommit((StoredCommit) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test16()  throws Throwable  {
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0);
      Id id0 = Id.fromLong(211L);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 1L, id0, "mk.cacheSize", "mk.cacheSize", id0);
      // Undeclared exception!
      try { 
        defaultRevisionStore0.markCommit(storedCommit0);
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test17()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      ChildNodeEntriesMap childNodeEntriesMap0 = new ChildNodeEntriesMap();
      Id id0 = inMemPersistence0.writeCNEMap(childNodeEntriesMap0);
      MutableCommit mutableCommit0 = new MutableCommit();
      StoredCommit storedCommit0 = new StoredCommit(id0, mutableCommit0);
      try { 
        defaultRevisionStore0.markCommit(storedCommit0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test18()  throws Throwable  {
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore((Persistence) null);
      try { 
        defaultRevisionStore0.initialize();
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test19()  throws Throwable  {
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0, h2Persistence0);
      // Undeclared exception!
      try { 
        defaultRevisionStore0.initialize();
        fail("Expecting exception: NoClassDefFoundError");
      
      } catch(NoClassDefFoundError e) {
         //
         // org/h2/jdbcx/JdbcConnectionPool
         //
         verifyException("org.apache.jackrabbit.mk.persistence.H2Persistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test20()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      defaultRevisionStore0.initialize();
      Id id0 = Id.fromLong(0L);
      try { 
        defaultRevisionStore0.getRootNode(id0);
        fail("Expecting exception: ExecutionException");
      
      } catch(ExecutionException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: 0000000000000000
         //
         verifyException("com.google.common.util.concurrent.AbstractFuture$Sync", e);
      }
  }

  @Test(timeout = 4000)
  public void test21()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      defaultRevisionStore0.initialize();
      try { 
        defaultRevisionStore0.getRootNode((Id) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.common.base.Preconditions", e);
      }
  }

  @Test(timeout = 4000)
  public void test22()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      ChildNodeEntriesHTree childNodeEntriesHTree0 = new ChildNodeEntriesHTree(defaultRevisionStore0);
      Id id0 = defaultRevisionStore0.putCNEMap(defaultRevisionStore_PutTokenImpl0, childNodeEntriesHTree0);
      try { 
        defaultRevisionStore0.getRootNode(id0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.jackrabbit.mk.htree.ChildNodeEntriesHTree cannot be cast to org.apache.jackrabbit.mk.model.StoredCommit
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test23()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      Id id0 = defaultRevisionStore0.getHeadCommitId();
      try { 
        defaultRevisionStore0.getNode(id0);
        fail("Expecting exception: RuntimeException");
      
      } catch(RuntimeException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: deserialization failed
         //
         verifyException("com.google.common.cache.LocalCache$Segment", e);
      }
  }

  @Test(timeout = 4000)
  public void test24()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      Id id0 = Id.fromLong((-4503L));
      try { 
        defaultRevisionStore0.getNode(id0);
        fail("Expecting exception: ExecutionException");
      
      } catch(ExecutionException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: ffffffffffffee69
         //
         verifyException("com.google.common.util.concurrent.AbstractFuture$Sync", e);
      }
  }

  @Test(timeout = 4000)
  public void test25()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      ChildNodeEntriesHTree childNodeEntriesHTree0 = new ChildNodeEntriesHTree(defaultRevisionStore0);
      Id id0 = defaultRevisionStore0.putCNEMap(defaultRevisionStore_PutTokenImpl0, childNodeEntriesHTree0);
      try { 
        defaultRevisionStore0.getNode(id0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.jackrabbit.mk.htree.ChildNodeEntriesHTree cannot be cast to org.apache.jackrabbit.mk.model.StoredNode
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test26()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      inMemPersistence0.sweep();
      try { 
        defaultRevisionStore0.getHeadCommit();
        fail("Expecting exception: ExecutionException");
      
      } catch(ExecutionException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: 0000000000000001
         //
         verifyException("com.google.common.util.concurrent.AbstractFuture$Sync", e);
      }
  }

  @Test(timeout = 4000)
  public void test27()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      try { 
        defaultRevisionStore0.getHeadCommit();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test28()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      ChildNodeEntriesHTree childNodeEntriesHTree0 = new ChildNodeEntriesHTree(defaultRevisionStore0);
      Id id0 = defaultRevisionStore0.putCNEMap(defaultRevisionStore_PutTokenImpl0, childNodeEntriesHTree0);
      defaultRevisionStore0.gc();
      try { 
        defaultRevisionStore0.getCommit(id0);
        fail("Expecting exception: ExecutionException");
      
      } catch(ExecutionException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: 91c135af86923ebef7f7b1d974ae2da1f7d069b2
         //
         verifyException("com.google.common.util.concurrent.AbstractFuture$Sync", e);
      }
  }

  @Test(timeout = 4000)
  public void test29()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      defaultRevisionStore0.initialize();
      try { 
        defaultRevisionStore0.getCommit((Id) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.common.base.Preconditions", e);
      }
  }

  @Test(timeout = 4000)
  public void test30()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      ChildNodeEntriesHTree childNodeEntriesHTree0 = new ChildNodeEntriesHTree(defaultRevisionStore0);
      Id id0 = defaultRevisionStore0.putCNEMap(defaultRevisionStore_PutTokenImpl0, childNodeEntriesHTree0);
      try { 
        defaultRevisionStore0.getCommit(id0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.jackrabbit.mk.htree.ChildNodeEntriesHTree cannot be cast to org.apache.jackrabbit.mk.model.StoredCommit
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test31()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      try { 
        defaultRevisionStore0.getCNEMap((Id) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("com.google.common.base.Preconditions", e);
      }
  }

  @Test(timeout = 4000)
  public void test32()  throws Throwable  {
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0);
      byte[] byteArray0 = new byte[2];
      Id id0 = new Id(byteArray0);
      try { 
        defaultRevisionStore0.getCNEMap(id0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test33()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      ChildNodeEntriesHTree childNodeEntriesHTree0 = new ChildNodeEntriesHTree(defaultRevisionStore0);
      Id id0 = defaultRevisionStore0.putCNEMap(defaultRevisionStore_PutTokenImpl0, childNodeEntriesHTree0);
      try { 
        defaultRevisionStore0.getCNEMap(id0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.jackrabbit.mk.htree.ChildNodeEntriesHTree cannot be cast to org.apache.jackrabbit.mk.model.ChildNodeEntriesMap
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test34()  throws Throwable  {
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore((Persistence) null);
      // Undeclared exception!
      try { 
        defaultRevisionStore0.close();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test35()  throws Throwable  {
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      StoredNode storedNode0 = defaultRevisionStore_PutTokenImpl0.getLastModified();
      assertNull(storedNode0);
  }

  @Test(timeout = 4000)
  public void test36()  throws Throwable  {
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0);
      byte[] byteArray0 = new byte[3];
      Id id0 = new Id(byteArray0);
      StoredNode storedNode0 = new StoredNode(id0, defaultRevisionStore0);
      defaultRevisionStore_PutTokenImpl0.updateLastModifed(storedNode0);
  }

  @Test(timeout = 4000)
  public void test37()  throws Throwable  {
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0, h2Persistence0);
      Id id0 = Id.fromLong(33554432);
      try { 
        defaultRevisionStore0.getNode(id0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test38()  throws Throwable  {
      byte[] byteArray0 = new byte[8];
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore((Persistence) null);
      Id id0 = new Id(byteArray0);
      try { 
        defaultRevisionStore0.getCommit(id0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test39()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      try { 
        defaultRevisionStore0.getHeadCommitId();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test40()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      defaultRevisionStore0.gc();
      Id id0 = defaultRevisionStore0.markCommits();
      assertNotNull(id0);
  }

  @Test(timeout = 4000)
  public void test41()  throws Throwable  {
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore((Persistence) null);
      defaultRevisionStore0.gc();
      assertEquals(33554432, DefaultRevisionStore.DEFAULT_CACHE_SIZE);
  }

  @Test(timeout = 4000)
  public void test42()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      StoredCommit storedCommit0 = defaultRevisionStore0.getHeadCommit();
      Id id0 = defaultRevisionStore0.markCommits();
      MutableCommit mutableCommit0 = new MutableCommit(storedCommit0);
      mutableCommit0.setBranchRootId(id0);
      try { 
        defaultRevisionStore0.putCommit(defaultRevisionStore_PutTokenImpl0, mutableCommit0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test43()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      ChildNodeEntriesHTree childNodeEntriesHTree0 = new ChildNodeEntriesHTree(defaultRevisionStore0);
      Id id0 = defaultRevisionStore0.putCNEMap(defaultRevisionStore_PutTokenImpl0, childNodeEntriesHTree0);
      MutableCommit mutableCommit0 = new MutableCommit();
      mutableCommit0.setRootNodeId(id0);
      Id id1 = defaultRevisionStore0.putCommit(defaultRevisionStore_PutTokenImpl0, mutableCommit0);
      assertNotSame(id1, id0);
  }

  @Test(timeout = 4000)
  public void test44()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      ChildNodeEntriesHTree childNodeEntriesHTree0 = new ChildNodeEntriesHTree(defaultRevisionStore0);
      Id id0 = defaultRevisionStore0.putCNEMap(defaultRevisionStore_PutTokenImpl0, childNodeEntriesHTree0);
      defaultRevisionStore0.lockHead();
      MutableCommit mutableCommit0 = new MutableCommit();
      mutableCommit0.setRootNodeId(id0);
      defaultRevisionStore0.putHeadCommit(defaultRevisionStore_PutTokenImpl0, mutableCommit0, id0, id0);
      StoredCommit storedCommit0 = defaultRevisionStore0.getHeadCommit();
      assertEquals(0L, storedCommit0.getCommitTS());
  }

  @Test(timeout = 4000)
  public void test45()  throws Throwable  {
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      defaultRevisionStore0.initialize();
      Id id0 = defaultRevisionStore0.markCommits();
      MutableCommit mutableCommit0 = new MutableCommit();
      try { 
        defaultRevisionStore0.putHeadCommit(defaultRevisionStore_PutTokenImpl0, mutableCommit0, id0, id0);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // putHeadCommit called without holding write lock.
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test46()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      ChildNodeEntriesHTree childNodeEntriesHTree0 = new ChildNodeEntriesHTree(defaultRevisionStore0);
      Id id0 = defaultRevisionStore0.putCNEMap(defaultRevisionStore_PutTokenImpl0, childNodeEntriesHTree0);
      defaultRevisionStore0.lockHead();
      MutableCommit mutableCommit0 = new MutableCommit();
      try { 
        defaultRevisionStore0.putHeadCommit(defaultRevisionStore_PutTokenImpl0, mutableCommit0, id0, id0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.model.AbstractCommit", e);
      }
  }

  @Test(timeout = 4000)
  public void test47()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      defaultRevisionStore0.initialize();
      ChildNodeEntriesMap childNodeEntriesMap0 = new ChildNodeEntriesMap();
      Id id0 = defaultRevisionStore0.putCNEMap((RevisionStore.PutToken) null, childNodeEntriesMap0);
      StoredCommit storedCommit0 = new StoredCommit(id0, id0, 33554432, id0, "xee", "xee", id0);
      try { 
        defaultRevisionStore0.markCommit(storedCommit0);
        fail("Expecting exception: ClassCastException");
      
      } catch(ClassCastException e) {
         //
         // org.apache.jackrabbit.mk.model.ChildNodeEntriesMap cannot be cast to org.apache.jackrabbit.mk.model.StoredNode
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test48()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      try { 
        defaultRevisionStore0.putNode(defaultRevisionStore_PutTokenImpl0, (MutableNode) null);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.persistence.InMemPersistence", e);
      }
  }

  @Test(timeout = 4000)
  public void test49()  throws Throwable  {
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl1 = new DefaultRevisionStore.PutTokenImpl();
      boolean boolean0 = defaultRevisionStore_PutTokenImpl0.equals(defaultRevisionStore_PutTokenImpl1);
      assertFalse(boolean0);
      assertFalse(defaultRevisionStore_PutTokenImpl1.equals((Object)defaultRevisionStore_PutTokenImpl0));
  }

  @Test(timeout = 4000)
  public void test50()  throws Throwable  {
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      boolean boolean0 = defaultRevisionStore_PutTokenImpl0.equals(defaultRevisionStore_PutTokenImpl0);
      assertTrue(boolean0);
  }

  @Test(timeout = 4000)
  public void test51()  throws Throwable  {
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      boolean boolean0 = defaultRevisionStore_PutTokenImpl0.equals((Object) null);
      assertFalse(boolean0);
  }

  @Test(timeout = 4000)
  public void test52()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      defaultRevisionStore0.close();
      assertEquals(33554432, DefaultRevisionStore.DEFAULT_CACHE_SIZE);
  }

  @Test(timeout = 4000)
  public void test53()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      defaultRevisionStore0.initialize();
      try { 
        defaultRevisionStore0.initialize();
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // already initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test54()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      DefaultRevisionStore.PutTokenImpl defaultRevisionStore_PutTokenImpl0 = new DefaultRevisionStore.PutTokenImpl();
      MutableNode mutableNode0 = new MutableNode(defaultRevisionStore0);
      defaultRevisionStore0.putNode(defaultRevisionStore_PutTokenImpl0, mutableNode0);
      defaultRevisionStore0.gc();
      assertEquals(33554432, DefaultRevisionStore.DEFAULT_CACHE_SIZE);
  }

  @Test(timeout = 4000)
  public void test55()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      RevisionStore.PutToken revisionStore_PutToken0 = defaultRevisionStore0.createPutToken();
      defaultRevisionStore0.initialize();
      ChildNodeEntriesMap childNodeEntriesMap0 = new ChildNodeEntriesMap();
      Id id0 = defaultRevisionStore0.putCNEMap(revisionStore_PutToken0, childNodeEntriesMap0);
      ChildNodeEntriesMap childNodeEntriesMap1 = defaultRevisionStore0.getCNEMap(id0);
      assertSame(childNodeEntriesMap1, childNodeEntriesMap0);
  }

  @Test(timeout = 4000)
  public void test56()  throws Throwable  {
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore((Persistence) null);
      // Undeclared exception!
      try { 
        defaultRevisionStore0.unlockHead();
        fail("Expecting exception: IllegalMonitorStateException");
      
      } catch(IllegalMonitorStateException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("java.util.concurrent.locks.ReentrantReadWriteLock$Sync", e);
      }
  }

  @Test(timeout = 4000)
  public void test57()  throws Throwable  {
      H2Persistence h2Persistence0 = new H2Persistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(h2Persistence0);
      Id id0 = Id.fromLong(0L);
      StoredNode storedNode0 = new StoredNode(id0, defaultRevisionStore0);
      StoredNodeAsState storedNodeAsState0 = new StoredNodeAsState(storedNode0, defaultRevisionStore0);
      NodeStateDiff nodeStateDiff0 = mock(NodeStateDiff.class, new ViolatedAssumptionAnswer());
      // Undeclared exception!
      try { 
        defaultRevisionStore0.compare((NodeState) null, storedNodeAsState0, nodeStateDiff0);
        fail("Expecting exception: NullPointerException");
      
      } catch(NullPointerException e) {
         //
         // no message in exception (getMessage() returned null)
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test58()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0, inMemPersistence0);
      try { 
        defaultRevisionStore0.getRootNode((Id) null);
        fail("Expecting exception: IllegalStateException");
      
      } catch(IllegalStateException e) {
         //
         // not initialized
         //
         verifyException("org.apache.jackrabbit.mk.store.DefaultRevisionStore", e);
      }
  }

  @Test(timeout = 4000)
  public void test59()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.lockHead();
      defaultRevisionStore0.unlockHead();
      assertEquals(33554432, DefaultRevisionStore.DEFAULT_CACHE_SIZE);
  }

  @Test(timeout = 4000)
  public void test60()  throws Throwable  {
      InMemPersistence inMemPersistence0 = new InMemPersistence();
      DefaultRevisionStore defaultRevisionStore0 = new DefaultRevisionStore(inMemPersistence0);
      defaultRevisionStore0.initialize();
      Id id0 = Id.fromString("");
      try { 
        defaultRevisionStore0.getCNEMap(id0);
        fail("Expecting exception: ExecutionException");
      
      } catch(ExecutionException e) {
         //
         // org.evosuite.runtime.mock.java.lang.MockThrowable: 
         //
         verifyException("com.google.common.util.concurrent.AbstractFuture$Sync", e);
      }
  }
}
