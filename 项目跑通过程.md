# ODS项目跑通过程

## 项目概述

这是一个基于静态提取代码特征的过拟合补丁自动分类项目（Automated Classification of Overfitting Patches with Statically Extracted Code Features）。该项目使用XGBoost机器学习模型来预测代码补丁是否为过拟合补丁。

## 环境准备

### 1. 创建Conda环境

```bash
conda create -n ods-experiment python=3.8 -y
```

### 2. 激活环境

```bash
conda activate ods-experiment
```

### 3. 安装依赖包

```bash
pip install pandas numpy scikit-learn xgboost imbalanced-learn matplotlib
```

## 项目结构

```
ODSExperiment/
├── Experiment/                 # 实验数据和脚本
│   ├── data/                  # 实验数据文件
│   ├── RQ1-902-patches.ipynb # 研究问题1的notebook
│   ├── RQ2-PatchSim-Comparison.ipynb
│   └── RQ3-large-dataset-projects.ipynb
├── Features/                  # ODS特征数据
│   ├── Bears/
│   ├── Bugs.jar/
│   └── Defects4J/
├── Source/                    # 源代码文件
├── Tests/                     # 测试文件
├── RawRepairThemAllPatches/   # 原始补丁数据
├── train.csv                  # 训练数据 (102MB)
├── test_example.csv           # 测试样例数据
├── predict.py                 # 预测脚本
└── prediction.csv             # 预测结果
```

## 数据说明

### 训练数据 (train.csv)
- 大小: 102,142,036 bytes (~102MB)
- 形状: (训练样本数, 4499列)
- 格式: id, label, 特征0, 特征1, ..., 特征4496
- 编码: latin1

### 测试数据 (test_example.csv)
- 大小: 30,378 bytes
- 形状: (1, 4498列)
- 格式: id, 特征0, 特征1, ..., 特征4496
- 包含一个测试样本 "test_1"

## 运行步骤

### 1. 激活环境
```bash
conda activate ods-experiment
```

### 2. 运行预测脚本
```bash
python predict.py
```

### 3. 查看预测结果
```bash
cat prediction.csv
```

## 代码修复

在运行过程中遇到XGBoost API变化问题，已修复：

**原代码 (predict.py 第50-53行):**
```python
model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5)
eval_set=[(X_train,Y_train)]
model.fit(X_train,Y_train, early_stopping_rounds=30, eval_metric="mae", eval_set=eval_set)
```

**修复后:**
```python
model = xgb.XGBClassifier(random_state=42, max_depth=6, gamma=0.5, early_stopping_rounds=30, eval_metric="mae")
eval_set=[(X_train,Y_train)]
model.fit(X_train,Y_train, eval_set=eval_set)
```

## 运行结果

### 模型训练过程
模型使用XGBoost分类器，训练过程显示验证集MAE从0.29215逐步降低到0.15028，共进行了100轮训练。

### 预测结果
```csv
,patch,prediction_label
0,test_1,1
```

- 测试样本 "test_1" 被预测为标签 1（可能表示过拟合补丁）

## 模型参数

- **算法**: XGBoost分类器
- **参数设置**:
  - random_state: 42
  - max_depth: 6
  - gamma: 0.5
  - early_stopping_rounds: 30
  - eval_metric: "mae"

## 特征说明

项目使用4496个静态代码特征，这些特征通过Coming工具从代码补丁中提取，包括：
- 代码描述特征 (Code features)
- 修复模式特征 (Pattern features)  
- 上下文特征 (Context features)

## 验证测试

创建了测试脚本 `test_project.py` 来验证项目功能：

```bash
python test_project.py
```

测试结果显示：
- ✓ 所有依赖包已正确安装
- ✓ 数据文件存在且格式正确
- ✓ 预测功能正常工作
- ✓ 预测结果已生成

## 总结

项目已成功跑通，主要完成了以下工作：

1. **环境搭建**: 创建了专用的conda环境并安装了所有必要依赖
2. **代码修复**: 修复了XGBoost API兼容性问题
3. **功能验证**: 成功运行预测脚本并生成预测结果
4. **测试验证**: 创建测试脚本验证项目各项功能正常

项目现在可以正常使用，能够对新的代码补丁进行过拟合分类预测。
